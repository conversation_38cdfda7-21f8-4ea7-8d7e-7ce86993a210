import React from 'react'
import { loginApi, transferManageApi } from '@src/pages/oo-client/api'
import { permsMap } from '@oo/config/perms/perms-config'
import { routeConfig } from '@oo/routes/route-config'
import { Tooltip } from 'antd'
import { getCurAppConfig } from '@utils/format'
import { filterCouOnChainData } from '@src/globalComponents/chainInfoComponents/chain-data-handle-json'
import commonModuleApi from '@src/pages/oo-client/api/common'

//storage的命名空间
const storageKeyName = 'vena:oo'
type storeType = 'session' | 'local'

//sessionStorage存储类型定义
interface SessionStorageType {
	menuState: {
		openKeys: string[]
		selectedKeys: string[]
	}
	lastSelectedMenuKeys: string[]
	lastOpenKeys: string[]
	pageParams: Record<string, unknown>
	testFieldAge: number
	testCountMemory: number
	breadcrumbItems: (string | { name: string; path: string })[]
	roleId: string
}

//LocalStorageType存储类型定义
interface LocalStorageType {
	originMenuList: Record<string, unknown>[]
	menuList: Record<string, unknown>[]
	urlList: string[]
	permList: string[]
	frontCodeList: string[]
	userInfo: Record<string, any>
	selectRoleList: Record<string, any>[]
	lastSelectedMenuItem: Record<string, any>
	innerPagesRoutePermsMap: Record<string, any>
	roleTypeList: string[]
	roleList: string[]
	token: string
	testFieldAge: number
	language: string
	curRoleItem: { [k: string]: any }
	paramsFromRoleManage: { [k: string]: any }
	roleCode: string
}

type storgeType = keyof SessionStorageType | keyof LocalStorageType

//Record<string, unknown>
type storageParams = Partial<SessionStorageType> | Partial<LocalStorageType>

type storageKeyType = keyof storageParams

const getObjFromStorage = (type: storeType) => {
	if (type === 'session') {
		return JSON.parse(sessionStorage.getItem(storageKeyName) || '{}')
	} else if (type === 'local') {
		return JSON.parse(localStorage.getItem(storageKeyName) || '{}')
	}
}

export const clearStorage = (type?: storeType): boolean => {
	if (type === 'session') {
		sessionStorage.clear()
	} else if (type === 'local') {
		localStorage.clear()
	} else {
		sessionStorage.clear()
		localStorage.clear()
	}
	return true
}

//默认是local
export const setStorage = (param: storageParams, type: storeType = 'local'): boolean => {
	try {
		const objFromStorage: SessionStorageType | LocalStorageType = getObjFromStorage(type)
		let key: storageKeyType
		for (key in param) {
			if (param.hasOwnProperty(key)) {
				objFromStorage[key] = param[key]
			}
		}
		const objStr = JSON.stringify(objFromStorage)
		if (type === 'session') {
			sessionStorage.setItem(storageKeyName, objStr)
		} else {
			localStorage.setItem(storageKeyName, objStr)
		}
	} catch (e) {
		return false
	}
	return true
}

//默认是local
export const getStorage = (key: storgeType, type: storeType = 'local'): any => {
	const objFromStorage = getObjFromStorage(type)
	return objFromStorage[key]
}

export const inputTrim = (form, keyName, e) => {
	let value = (e.target.value + '').trim()
	form.setFieldsValue({
		[keyName]: value,
	})
}

export const hasAuth = curPerms => {
	let frontCodeList = getStorage('frontCodeList') || []
	return frontCodeList.includes(curPerms)
}

export const inputForbiddenWhiteSpace = (form, keyName, e) => {
	let value = (e.target.value + '').replace(/\s/g, '')
	form.setFieldsValue({
		[keyName]: value,
	})
}

export const setTooltip = value => {
	return (
		<Tooltip title={value} placement="topLeft">
			<span className="ellipsis">{value}</span>
		</Tooltip>
	)
}

export const copyText = (text: string) => {
	let oInput = document.createElement('input')
	oInput.value = text
	document.body.appendChild(oInput)
	oInput.select() // 选择对象;
	document.execCommand('Copy') // 执行浏览器复制命令
	oInput.remove()
}

export const handleRequestPath = (path: string, useMock = false) => {
	const env = (process_env + '').toLocaleLowerCase()
	//注意keyName可能写错
	if (env === 'production' && window.config) {
		const { serverAddress } = getCurAppConfig()
		if (path.indexOf('http') === -1) {
			if (serverAddress) {
				path = serverAddress + path
			}
		}
	}
	// development下进入 mock模式
	if (env === 'development' && useMock) {
		if (path.indexOf('/mock') === -1) {
			path = `/mock${path}`
		}
	}
	return path
}

export const getPageHash = (): string => {
	return window.location.hash.slice(1)
}

export const findMatchItemByPageHash = (): MenuType => {
	let pageHash = getPageHash()
	let menuList = getStorage('menuList') || []
	const findItemsByHash = (initMenuList: MenuType[], inItFindedItems: MenuType[] = []) => {
		if (Array.isArray(initMenuList)) {
			for (let i = 0; i < initMenuList.length; i++) {
				let item = initMenuList[i]
				if (item.path) {
					if (pageHash.includes(item.path)) {
						inItFindedItems.push(item)
					}
				}
				if (item.children) {
					findItemsByHash(item.children, inItFindedItems)
				}
			}
		}
		return inItFindedItems
	}
	let findedItems = findItemsByHash(menuList)
	findedItems.sort((a, b) => {
		return b.path.length - a.path.length
	})
	return findedItems[0]
}

//获取当前项目，真实menuList
export const getRealMenuList = (frontCodeList, originMenuList) => {
	const getMenuOrderNumMap = () => {
		let menuOrderNumMap = {}
		const setPermsOrderNumMap = list => {
			list.forEach(item => {
				//有排序用排序的顺序，没有排序时用最大值
				if (item.orderNum || item.orderNum === 0) {
					menuOrderNumMap[item.perms] = item.orderNum
				} else {
					menuOrderNumMap[item.perms] = 99999
				}
				if (Array.isArray(item.children) && item.children.length > 0) {
					setPermsOrderNumMap(item.children)
				}
			})
		}
		setPermsOrderNumMap(originMenuList)
		return menuOrderNumMap
	}

	const addMenuItem = (initFrontCodeList, routeList, initMenuList, breadcrumb, pId = '') => {
		const menuOrderNumMap = getMenuOrderNumMap()
		routeList.forEach((item, index) => {
			let { permsKey } = item
			if (!permsKey) {
				return
			}
			if (permsKey && !permsMap[permsKey]) {
				console.error(`${permsKey} - 该资源码不存在，请查证后再试`)
				return
			}
			//needValid 默认是true
			let { name, path, showOnMenu = true, icon, needValid = true } = item
			// 是否展示在菜单上
			// 1、有权限
			// 2、不需要校验
			if (showOnMenu && (initFrontCodeList.includes(permsKey) || !needValid)) {
				let menuItem = {
					name,
					path,
					icon,
					pId,
					perms: permsKey,
					breadcrumb: [...breadcrumb, name],
					id: pId ? `${pId}_${index}` : index + '',
					children: null,
					orderNum: menuOrderNumMap[permsKey],
				}
				initMenuList.push(menuItem)
				if (Array.isArray(item.children) && item.children.length > 0) {
					menuItem.children = addMenuItem(initFrontCodeList, item.children, [], [...menuItem.breadcrumb], menuItem.id) || []
					//children需排序
					menuItem.children.sort((a, b) => {
						return a.orderNum - b.orderNum
					})
				}
			}
		})
		//增加排序功能,需同步到脚手架里
		initMenuList.sort((a, b) => {
			return a.orderNum - b.orderNum
		})
		return initMenuList
	}

	const getMenuList = () => {
		let initMenuList = []
		try {
			const curAppRoutes = routeConfig.routes[0].children
			const authRoute = curAppRoutes.find(item => {
				return item.path === '/content'
			})
			if (authRoute) {
				let menuRouteList = authRoute.children
				let breadcrumb = []
				let pId = null
				addMenuItem(frontCodeList, menuRouteList, initMenuList, breadcrumb, pId)
			}
			setStorage({ menuList: initMenuList })
			return initMenuList
		} catch (e) {
			return initMenuList
		}
	}
	return getMenuList()
}

// 获取超级管理员的所有资源码
export const getFrontCodeListForAdmin = () => {
	let allPermsList = Object.keys(permsMap)
	let adminFrontCodeList = []
	allPermsList.forEach(item => {
		adminFrontCodeList.push(item)
	})
	return adminFrontCodeList
}

export const getFirstPagePath = menuList => {
	let getPathFromArr = list => {
		let targetPath = ''
		if (Array.isArray(list)) {
			for (let i = 0, len = list.length; i < len; i++) {
				let item = list[i]
				if (item.path) {
					targetPath = item.path
					return targetPath
				}
				if (item.children) {
					return getPathFromArr(item.children)
				}
			}
		}
		return targetPath
	}
	return getPathFromArr(menuList)
}

export const getChildrenRouteConfig = (moduleConfig: Record<string, PermItem>) => {
	let routeConfigs = []
	for (let key of Object.keys(moduleConfig)) {
		if (!moduleConfig[key].path) {
			continue
		}
		routeConfigs.push(moduleConfig[key])
	}
	return routeConfigs
}

export const invalidVal = '--'

export const switchRole = (params: { projectCode: string; roleName: string }) => {
	return loginApi.switchRole(params).then(res => {
		//做权限的事
		let { frontCodeList, menuList, roleList, roleTypeList } = res
		let realMenuList = getRealMenuList(frontCodeList, menuList)
		// 存储登录的一般信息
		setStorage({ frontCodeList, originMenuList: menuList, menuList: realMenuList, roleTypeList, roleList })
		return {
			frontCodeList,
			realMenuList,
		}
	})
}

//为内页定义面包屑名称
export const initInnerPagesName = () => {
	let innerPagesRoutePermsMap = {}
	for (let k in permsMap) {
		let item = permsMap[k]
		//内页的判断依据
		if (item.name && item.path && item.showOnMenu === false) {
			innerPagesRoutePermsMap[item.path] = item
		}
	}
	setStorage({ innerPagesRoutePermsMap })
}

//处理cou数据存证上链
export const couDataOnChain = couNoList => {
	let onChainData = ''
	let proofBeans = []
	transferManageApi
		.queryCouListByNoList({ couNoList })
		.then(res => {
			if (res.data && res.data.length > 0) {
				res.data.forEach(element => {
					onChainData = JSON.stringify(filterCouOnChainData(element))
					proofBeans.push({
						couNo: element.couNo,
						originData: onChainData,
					})
				})
				commonModuleApi.setCouProof({ proofBeans }).catch(err => console.log(err))
			}
		})
		.catch(err => console.log(err))
}

//获取当前用户roleItem
export const getCurrentRole = () => {
	const currentRoleId = getStorage('roleId')
	// const currentRoleId = '12';
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')
	return ext?.loginPerm?.roleList?.find(i => i?.roleId === currentRoleId)
}

export const getImageUrl = async (data: string) => {
	return await commonModuleApi.downloadFileUrl({ fileUrl: data })
}
