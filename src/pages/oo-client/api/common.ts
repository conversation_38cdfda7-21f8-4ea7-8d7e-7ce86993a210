import { get, post } from './apiMethod'

const commonModuleApi = {
	// 得到文件的url
	downloadFileUrl(params: { fileUrl: any }) {
		return post('/pledge-config/anybody/downloadFileUrl', params)
	},
	downloadFile(params: { fileUrl: any }) {
		return post('/pledge-config/anybody/downloadFile', params)
	},
	//获取服务器时间
	syncTime() {
		return get('/pledge/gw/server-time')
	},
	// 获取开立、支付、退款审核流程图的数据
	getTransferChartData(params: any) {
		return get('/pledge-asset/process/progress?handleResponse', params)
	},
	//获取最新版本号
	getVersion() {
		return post('/pledge-config/anybody/version/get')
	},
	//查询链上融信存证接口
	queryCouProof(params: any) {
		return post('/pledge-asset/cou/proof/query', params)
	},
	//上传文件
	uploadFile(params: { file: any; type: string; [str: string]: any }, noNeedMessage: boolean) {
		const paramsData = new FormData()
		for (const key in params) {
			paramsData.append(key, params[key])
		}
		const url = '/pledge-config/common/uploadFile'
		return post(url, paramsData)
	},
	//审核（内审）
	loadCurrentUserTask(params: any) {
		return post('/pledge-asset/process/task/loadCurrentUserTask', params)
	},
	doTask(params: any) {
		return post('/pledge-asset/process/task/doTask?handleResponse', params)
	},
	//查询当前登录者是否是本流程的最后一个审核者
	isLastFlowPerson(params: any) {
		return post('/pledge-asset/flow-audit/flowLastAuditor', params)
	},
	//融信存证数据接口
	setCouProof(params: any) {
		return post('/pledge-asset/cou/proof/set?handleResponse', params)
	},
}

export default commonModuleApi
