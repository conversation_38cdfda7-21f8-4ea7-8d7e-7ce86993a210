import { post } from './apiMethod'

/**
 * 运营设置页面 API module
 */

interface PlatFormProtocol {
	protocolType: string
	protocolHtml: string
	protocolDesc: string
	ooUuid: string
}

export default {
	// 新增平台协议
	AddPlatformProtocol(params: PlatFormProtocol) {
		let data = new FormData()
		for (const key in params) {
			data.append(key, params[key])
		}
		return post('/pledge-config/oo/save/protocol', data)
	},
	// 更新平台协议
	ModifyPlatformProtocol(params: PlatFormProtocol) {
		let data = new FormData()
		for (const key in params) {
			data.append(key, params[key])
		}
		return post('/pledge-config/oo/update/protocol', data)
	},

	//得到协议(企业相关协议)
	getProtocol(params: { protocolTypes: string[]; ooUuid: string }) {
		return post('/pledge-config/anybody/user/protocol', params)
	},
	// 平台协议配置的接口
	getProtocolList(params: { protocolTypes: string[]; ooUuid: string }) {
		return post('/pledge-config/anybody/user/protocolList', params)
	},
}
