import { post, get, postBlob } from './apiMethod'

const statisticsApi = {
	//获取统计列表
	getStatisticsList(params: any) {
		return post('/pledge-asset/op/pledge/financing/statistics', params)
	},
	// 导出统计列表
	getStatisticsExport(params: any) {
		return postBlob('/pledge-asset/op/pledge/financing/statistics-export?getblob', params)
	},
	//获取开立统计列表
	getOpeningStatistics(params: any) {
		return post('/pledge-asset/oo/cou/create/statistics', params)
	},
	// 导出开立统计
	getOpeningStatisticsExport(params: any) {
		return postBlob('/pledge-asset/oo/cou/create/statistics/export?getblob', params)
	},
	//获取持有融信统计
	getHolderStatistics(params: any) {
		return post('/pledge-asset/oo/cou/holder/statistics', params)
	},
	// 导出持有融信统计
	getHolderStatisticsExport(params: any) {
		return postBlob('/pledge-asset/oo/cou/holder/statistics/export?getblob', params)
	},
}

export default statisticsApi
