import { get, post, postBlob } from './apiMethod'

/**
 * @description c 密钥确认函 p 平台服务章程 u 平台服务以及用户操作规则
 */
type protocolType = 'companyKey' | 'platformServer' | 'userRule' | 'finance' | 'transfer' | 'pledgeFinance'

const financeSearchApi = {
	getFinanceList(params: any) {
		return post('/pledge-asset/op/pledge/financing/list', params)
	},
	getInfo(params: any) {
		return post('/pledge-asset/specialdate/list', params)
	},
	getFinanceDetail(params: any) {
		return post('/pledge-asset/op/pledge/financing/detail', params)
	},

	//导出融资列表
	exportFinanceList(params: any) {
		return postBlob('/pledge-asset/finance/export?getblob', params)
	},
	getProtocolTemplate(params: any) {
		return post('/pledge-asset/protocol/get', params)
	},
	// 根据融信uuid下载全量流转合同文件
	allContractDownload(params: { couUuid: string }) {
		return post('/pledge-asset/finance/allContract/download', params)
	},
	// 根据融信uuid下载融信浏转单（全路径）
	allTransferDownload(params: { couUuid: string }) {
		return post('/pledge-asset/finance/allTransferProtocol/download', params)
	},
	getProtoUrl(params: { file: string; type: protocolType }) {
		const data = new FormData()
		data.append('file', params.file)
		data.append('type', params.type)
		return post('/pledge-config/anybody/uploadHtmlFile', data)
	},
	getCompanyInfo(params: any) {
		return post('/pledge-config/company/get', params)
	},
	//获取服务器时间
	syncTime() {
		return get('/pledge/gw/server-time')
	},
	// 融资二次提交接口
	secondSubmit(params: { uuidOrNumber: string }) {
		return post('/pledge-asset/finance/second/submit?handleResponse', params)
	},
	// 金融机构列表
	getFinanceCreditList() {
		return post('/pledge-asset/finance/credit/list')
	},
	// 获取开立、支付融信流程图的数据
	getTransferChartData(params: any) {
		return get('/pledge-asset/process/progress?handleResponse', params)
	},
	getTransferNotice(params) {
		return post('/pledge-asset/oo/receivable/transfer/notice/query', params)
	},
	// 单据一键下载
	billDownload(params: { financeNo: string; fileUrl: string; couUuids: any[] }) {
		return post('/pledge-asset/finance/allIn/download', params)
	},

	// 历史应收账款备份

	getHistory(params: any) {
		return post('/pledge-asset/finance/receivable/history/backup', params)
	},
	transferPdf(params: any) {
		let data = new FormData()
		data.append('htmlStr', params.htmlStr)
		return post('/pledge-config/anybody/transfer/pdf', data)
	},
	assetCheck(params: any) {
		return post('/pledge-asset/pledge-finance/asset/check', params)
	},

	manualPushImages(params: any) {
		return post('/pledge-asset/anybody/send/finance/images', params)
	},

	//导出OO融资列表
	exportOoFinanceList(params: any) {
		return postBlob('/pledge-asset/op/pledge/financing/export?getblob', params)
	},
}

export default financeSearchApi
