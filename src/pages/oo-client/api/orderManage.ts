/*
 * @Descripttion:
 * @Version: 1.0
 * @Author: chenyan
 * @Date: 2023-01-28 17:46:10
 * @LastEditors: chenyan
 * @LastEditTime: 2023-03-29 18:20:18
 */
import { post, get } from './apiMethod'

const orderManageApi = {
	// 获取订单列表
	getOrderList(params: any) {
		return post('/pledge-config/cc/order/list', params)
	},
	// 修改订单
	updateOrder(params: any) {
		return post('/pledge-config/cc/order/modify', params)
	},
	// 修改资产包订单
	modifyPackageOrder(params: any) {
		return post('/pledge-asset/asset/order/modify', params)
	},
	// 创建订单
	createOrder(params: any) {
		return post('/pledge-config/cc/order/create', params)
	},
	// 创建资产包的订单
	createAssetOrder(params: any) {
		return post('/pledge-asset/asset/order/create', params)
	},
	// 查询资产包订单
	getAssetOrderList(params: any) {
		return post('/pledge-asset/asset/order/list', params)
	},
	// 获取订单详情
	getOrderDetail(params: any) {
		return get('/pledge-config/cc/order/detail', params)
	},
	// 渤商所登录
	bsLogin(params: any) {
		return post('/pledge-config/cc/bs/member/order/in', params)
	},
	// 渤商所订单信息
	bsMemberOrder(params: any) {
		return post('/pledge-config/cc/bs/member/order', params)
	},
}

export default orderManageApi
