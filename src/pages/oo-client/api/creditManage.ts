/*
 * @Date: 2022-07-29 13:42:49
 * @LastEditors: QI
 * @FilePath: \frontend-web\src\pages\oo-client\api\creditManage.ts
 * @Description: 授信管理api
 */
import { get, post, postBlob } from './apiMethod'

const creditManageApi = {
	// 获取开立列表
	getCreditCList(params: { companyName?: string; interestPayWay?: string; status?: string; pageNum: number; pageSize: number }) {
		return post(`/pledge-config/oo/corer/credit/list`, params)
	},
	// 获取授信列表
	getCreditSList(params: { companyName?: string; interestPayWay?: string; status?: string; pageNum: number; pageSize: number }) {
		return post(`/pledge-config/oo/provider/credit/list`, params)
	},
	getCreditSDetail(creditId: string) {
		return get(`/pledge-config/oo/provider/credit/${creditId}`)
	},
	// 授信审核
	creditAuditApi(params: any) {
		return post('/pledge-config/credit/audit?handleResponse', params)
	},
	// 导出授信列表
	exportFinanceCreditList(params: any) {
		return postBlob('/pledge-config/oo/credit/export?getblob', params)
	},
	//授信列表修改授信状态
	modifyCreditStatus(params: any) {
		return post('/pledge-config/credit/enable/modify', params)
	},
	//获取签署协议列表
	getSignedProtocols(params: any) {
		return post('/pledge-asset/protocol-sign/list', params)
	},

	/**获取签署协议列表
	 * @param params {id:授信id,signStatus:签署状态}
	 */
	updateProtocolStatus(params: any) {
		return post('/pledge-config/oo/credit/sign-status/modify', params)
	},
}

export default creditManageApi
