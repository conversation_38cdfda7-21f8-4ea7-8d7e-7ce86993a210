import { post, get } from './apiMethod'
const operatingOrganizationApi = {
	/**
	 * 运营机构列表
	 * 1. 获取运营机构列表
	 * 2. 创建运营机构
	 * 3. 修改运营机构
	 * 4. 根据id获取运营机构详情
	 * 5. 校验公司是否存在
	 */
	getOOList(params: { pageNum: number; pageSize: number }) {
		return post('/rhine3Admin/sp/oo/list', params)
	},
	createOO(params: any) {
		return post('/rhine3Admin/sp/oo/create', params)
	},
	modifyOO(params: any) {
		return post('/rhine3Admin/sp/oo/modify', params)
	},
	getOODetailById(params: { id: number }) {
		return post('/rhine3Admin/sp/oo/detail', params)
	},
	checkCompanyIsExist(params: { adminEmail: string; companyName: string; socialCreditCode: string; uuid?: string }) {
		return post('/rhine3Admin/sp/oo/exist', params)
	},
	/**
	 * 运营机构印章管理
	 * 1. 获取印章列表
	 * 2. 制作印章
	 * 3. 删除印章
	 */
	getSealList(params: { pageNum: number; pageSize: number }) {
		return post('/rhine3Admin/oo/cfcasignature/querySealListByProject', params)
	},
	makeSeal(params: any) {
		return post('/rhine3Admin/oo/cfcasignature/makeSeal', params)
	},
	deleteSeal(params: { companyUuid: string }) {
		return post('/rhine3Admin/oo/cfcasignature/deleteSealByCompany', params)
	},
	//运营人员编辑公告
	getLinkNameList(params) {
		// 模拟地址 临时测试使用
		return post('/rhine3Admin/cfcasignature/getLinkNameList', params)
	},
	getBusinessList(param) {
		return get('/pledge-config/lob/list', param)
	},
}

export default operatingOrganizationApi
