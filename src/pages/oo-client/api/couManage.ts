import { post, postBlob } from './apiMethod'

const couManageApi = {
	//查看原始cou列表
	getOriginalList(params: any) {
		return post('/pledge-asset/oo/cou/originalList', params)
	},
	//导出原始cou列表
	OriginalListExport(params: any) {
		return postBlob('/pledge-asset/oo/cou/originalList/export?getblob', params)
	},
	//查询持有人明细
	getHolderDetailList(params: any) {
		return post('/pledge-asset/oo/cou/holderList', params)
	},
	//导出持有人明细
	exportHolderDetailList(params: any) {
		return postBlob('/pledge-asset/oo/holderList/export?getblob', params)
	},
}

export default couManageApi
