/**
 * 中登查重管理模块接口API
 */
import { post, postBlob } from './apiMethod'

const financeCheckManageModuleApi = {
	//运营端查询融资查重记录列表接口
	financeCheckOoList(params) {
		return post('/pledge-asset/zd/check-task/finance/oo/list', params)
	},
	//运营端重新发起查重任务接口
	financeCheckOoRelaunch(params) {
		return post('/pledge-asset/zd/oo/check-task/relaunch?handleResponse', params)
	},
	getOoCheckTaskListByFinanceUuid(params) {
		return post('/pledge-asset/zd/oo/check-task/query-list-by-finance-uuid', params)
	},
	//下载
	downloadFiles(params: any) {
		return postBlob('/pledge-asset/zd/oo/check-task/downloadZip?getblob', params)
	},
	//运营端导出
	financeCheckTaskOoExport(params) {
		return postBlob('/pledge-asset/common/file/export?getblob', params)
	},
	//查询资产信息和查重部分信息
	getCheckTaskDetail(params: any) {
		return post('/pledge-asset/zd/oo/check-task/query-task-detail', params)
	},
	//查重任务详情的统计值
	getCheckTaskCount(params: any) {
		return post('/pledge-asset/zd/oo/check-task/count', params)
	},
	//重任务详情的根据type查询不同的任务详情tab页接口(本接口会返回所有数据id)
	getCheckTaskIdListByType(params: { taskNo: string; type: string }) {
		return post('/pledge-asset/zd/oo/check-task/tab', params)
	},
	//查询重任务详情的根据id查询对应命中和非命中详细信息接口
	getCheckTaskItemById(params: any) {
		return post('/pledge-asset/zd/oo/check-task/tab/item/byId?handleResponse', params)
	},
	//下载查重证明附件zip
	downloadRegisterFileZip(params: any) {
		return postBlob('/pledge-asset/zd/oo/check-task/download-support-file?getblob', params)
	},
	//下载报告(若报告附件未上传，点击下载会重新生成报告在下载)
	downloadReport(params: any) {
		return postBlob('/pledge-asset/zd/oo/check-task/download-report-file?getblob', params)
	},
	// objection 插叙异议列表
	getObjectionList(params: { originRegisterNo: string; taskId: string }) {
		return post('/pledge-asset/zd/oo/check-task/objection/regNo', params)
	},
	//预览地址
	pdfViewer(params: any) {
		return postBlob('/pledge-asset/zd/oo/check-task/preview?getblob', params)
	},
}
export default financeCheckManageModuleApi
