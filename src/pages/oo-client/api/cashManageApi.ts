/*
 * @Author: ya<PERSON><PERSON><PERSON> yanh<PERSON><PERSON>@qq.com
 * @Date: 2022-11-09 15:42:23
 * @LastEditors: yanh<PERSON><PERSON> yanheng<PERSON>@qq.com
 * @LastEditTime: 2022-11-10 14:46:27
 * @FilePath: \frontend-web\src\pages\oo-client\api\cashManageApi.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { get, post, postBlob } from './apiMethod'

export default {
	// 获取异常兑付列表
	getAbnormalCashList(params: any) {
		return post('/pledge-asset/oo/settlement/stranded/list', params)
	},
	// 获取历史异常兑付列表
	getHistoryAbnormalCashList(params: any) {
		return post('/pledge-asset/oo/settlement/history/stranded/list', params)
	},
	// 导出历史异常兑付列表
	exportHistoryAbnormalCashList(params: any) {
		return postBlob('/pledge-asset/oo/settlement/history/stranded/export?getblob', params)
	},
	// 清分异常列表导出
	exportAbnormalCashList(params: any) {
		return postBlob('/pledge-asset/oo/settlement/stranded/export?getblob', params)
	},
	// 运营机构手动发送清分命令
	sendJxBank(params: any) {
		return post('/pledge-asset/oo/settlement/send/jxBank', params)
	},
	// 根据融信uuid清分记录查询
	getAbnormalCashListByCouUuid(params: any) {
		return post('/pledge-asset/oo/settlement/list/couUuid', params)
	},

	// 根据融信uuid清分记录查询
	getAbnormalCashDelete(params: { uuid: string }) {
		return post('/pledge-asset/oo/settlement/stranded/del', params)
	},
	refreshAbnormalCashResult() {
		return get('/pledge-asset/settlement/query')
	},

	querySettlementNo(param: { settleNo: string }) {
		return get('/pledge-asset/oo/settlement/query/byNo', param)
	},
}
