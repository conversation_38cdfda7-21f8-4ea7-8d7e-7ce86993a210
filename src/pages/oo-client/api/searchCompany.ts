import { post, get, postBlob } from './apiMethod'

const searchCompanyModuleApi = {
	//获取企业列表
	getCompanyListGroup(params: { lob: string }) {
		return get('/pledge-config/lob/company-type/group', params)
	},
	//获取企业列表
	getCompanyListByLob(params: { lob: string; includeAll?: boolean }) {
		return get('/pledge-config/lob/company-type-list', params)
	},
	// 获取用户基本信息
	getUserInfo(params: any) {
		return get(`/pledge-user-service/user/${params.id}`)
	},
	//审核公司信息
	modifyCompany(params: any) {
		// return post('/pledge-config/oo/certify/audit', params)
		return post('/pledge-user-service/org/op/audit', params)
	},
	//获取企业列表
	getCompanyInfoList(params: any) {
		return get('/pledge-user-service/org/list', params)
	},
	//导出企业列表
	exportCompanyInfoList(params: any) {
		return postBlob('/pledge-config/company/export?getblob', params)
	},
	//根据id获取普通企业信息
	getCompanyDetailByUuid(params: any) {
		// return post('/pledge-config/company/detail', params)
		return get(`/pledge-user-service/org/${params.id}`)
	},
	//根据企业名称模糊搜索企业
	searchCompanyByName(params: any) {
		// return post('/rhine3Admin/client/company/searchByName', params)
		return post('/pledge-user-service/org/searchByName', params)
	},
	//文件url 获取可预览的链接 链接转换
	downloadFileUrl(params: { fileUrl: any }) {
		return post('/pledge-config/anybody/downloadFileUrl', params)
	},
	//链接转换
	downloadFile(params: { fileUrl: any }) {
		return post('/pledge-config/anybody/downloadFile', params)
	},
	//检查公司是否存在
	checkCompanyIsExist(params: { adminEmail: string; companyName: string; companyShortName: string; socialCreditCode: string; uuid?: string }) {
		return post('/pledge-config/anybody/company/exist?handleResponse', params)
	},
	//查询所有用户
	getUerList(params: any) {
		// 原用户列表接口
		// get('/pledge-user-service/oo/user/list', params)
		return post('/pledge-user-service/user/org-relation/list', params)
	},
	// 查询 用户关联企业列表
	getAffiliatedEnterpriseList(params: any) {
		return post('/pledge-user-service/user/org/list', params)
	},
	//导出OO端企业列表
	exportOoCompanyInfoList(params: any) {
		return postBlob('/pledge-user-service/org/list/export?getblob', params)
	},
	getBankcardByCompanyId(params: any) {
		return get(`/pledge-config/bankcard/account/by-org-id`, params)
	},
	getCompanyMemberInfo(params: any) {
		return post(`/pledge-user-service/org/list/user`, params)
	},
	searchCompanyByNameisAudit(params: any) {
		return post('/pledge-user-service/org/searchByName', {
			...params,
			filterAudit: true,
		})
	},
	searchOOByNameisAudit(params: any) {
		return post('/rhine3Admin/company/oo/searchByName', {
			...params,
		})
	},
	resetAuth(params: any) {
		return post('/pledge-user-service/user/verification/reset', {
			...params,
		})
	},
}

export default searchCompanyModuleApi
