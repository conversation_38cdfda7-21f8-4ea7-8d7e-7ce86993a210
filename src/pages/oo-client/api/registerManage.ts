/*
 * @Descripttion:
 * @Version: 1.0
 * @Author: chenyan
 * @Date: 2023-03-31 15:42:54
 * @LastEditors: chenyan
 * @LastEditTime: 2023-04-04 17:21:19
 */
import { post, postBlob } from './apiMethod'

const registerManageModuleApi = {
	//待补登记列表查询
	getPendRegistration(params: any) {
		return post('/pledge-asset/zdw/pend-registration/list', params)
	},
	//补登记（线下、线上、不登记）
	supplementRegister(params: any) {
		return post('/pledge-asset/zdw/supplement/register', params)
	},
	//登记列表
	getRegisterList: params => {
		return post('/pledge-asset/zdw/init-registration/list', params)
	},
	//获取登记详情
	getRegisterDetail: params => {
		return post('/pledge-asset/zdw/init-registration/detail', params)
	},
	//下载登记证明
	downloadRegisterAttachment: params => {
		return postBlob('/pledge-asset/zdw/registration/attachment/download?getblob', params)
	},
	//下载财产附件
	downloadPledgeAttachment: params => {
		return postBlob('/pledge-asset/zdw/pledge-attachment/download?getblob', params)
	},
	//待注销登记列表
	getWaitTerminateList: params => {
		return post('/pledge-asset/zdw/wait/terminate/reg/list', params)
	},
	//线下注销登记
	offlineTerminateRegister: params => {
		return post('/pledge-asset/zdw/terminate/reg/offline', params)
	},
	//线上注销登记
	onlineTerminateRegister: params => {
		return post('/pledge-asset/zdw/terminate/reg/online', params)
	},
	//初始登记变更记录列表
	changeRecordList: params => {
		return post('/pledge-asset/zdw/init-registration/change/list', params)
	},
	//查询注销登记详情
	terminateDetail: params => {
		return post('/pledge-asset/zdw/terminate/detail', params)
	},
}

export default registerManageModuleApi
