import { post, postBlob } from './apiMethod'

export default {
	// 获取当前运营机构下的合同列表
	getContractList(params: any) {
		return post('/pledge-asset/oo/query/contract/list', params)
	},

	// 获取当前运营机构下的发票列表
	getInvoiceList(params: any) {
		return post('/pledge-asset/oo/query/invoice/list', params)
	},
	//运营机构新增当前运营机构下的合同列表导出接口
	exportContractList(params: any) {
		return postBlob('/pledge-asset/oo/export/contract/list?getblob', params)
	},
	// 导出当前运营机构下的发票列表
	exportInvoiceList(params: any) {
		return postBlob('/pledge-asset/oo/export/invoice/list?getblob', params)
	},
}
