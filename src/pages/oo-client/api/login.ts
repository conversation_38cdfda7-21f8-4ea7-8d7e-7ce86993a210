import { get, post } from './apiMethod'
//保证头部有 OO 字段值
import { IloginApiType } from '../@types/login'

const loginModuleApi = {
	//登录接口
	/*
	login(params: IloginApiType) {
		return post('/rhine3/gw/account/login?handleResponse', params)
	},
	*/
	login(params: IloginApiType) {
		return post('/pledge-config/account/login?handleResponse', params)
	},
	//登出接口
	logout() {
		return post('/logout')
	},
	//获取当前用户权限
	getAuthInfo(params: { loginCompanyType: string }) {
		return post('/pledge-config/user/loginCompanyType', params)
	},
	//获取手机验证码
	getPhoneCode(params: { mobile: string; type?: string }) {
		return post('/pledge-config/anybody/user/sendPhoneCode?handleResponse', params)
	},
	//激活接口
	activateAccount(params: any) {
		return post('/pledge-config/anybody/oo/activateAccount?handleResponse', params)
	},
	getUser() {
		return post('/pledge-config/getUser')
	},
	switchRole(params) {
		return post('/pledge-config/user/role/switch', params)
	},
	checkReckeyUpdate() {
		return post('/pledge-config/user/checkReckeyUpdate')
	},
	saveReckey(params: any) {
		return post('/pledge-config/user/saveReckey', params)
	},
	//获取公司上有的FI企业
	getRelatedFICompany() {
		return post('/pledge-config/company/getRelatedFICompany')
	},
	//获取图形验证码
	getCaptcha() {
		return get('/rhine3/gw/captcha')
	},
	//更新版本号
	setVersion(params: any) {
		return post('/pledge-config/anybody/version/set', params)
	},

	//忘记密码相关接口

	//忘记密码 重置密码接口
	newResetPassword(params: { email: string; mobile: string; mobileCode: string; password: string; protocolVersionId?: number }) {
		return post('/pledge-config/anybody/oo/resetPassword?handleResponse', params)
	},

	// 获取baseInfo
	getNewBaseInfo() {
		return get('/pledge-user-service/account/ext-base-info')
	},
}

export default loginModuleApi
