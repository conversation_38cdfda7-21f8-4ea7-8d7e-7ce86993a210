import { post } from './apiMethod'

enum DestroyStatus {
	WAITING_AUDIT = 'WAITING_AUDIT',
	REJECTED = 'REJECTED',
	CONFIRMED = 'CONFIRMED',
}

const destroyModuleApi = {
	// 核销列表
	getDestroyList(params: { pageNum: number; pageSize: number; couNo?: string; destroyNo?: string; destroyStatus?: DestroyStatus }) {
		return post('/pledge-asset/destroy/list4Oo', params)
	},
	getDetail(params: { uuid: string }) {
		return post('/pledge-asset/destroy/detail', params)
	},
}

export default destroyModuleApi
