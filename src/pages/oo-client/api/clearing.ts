import { getBlob, post, get } from './apiMethod'

const clearingApi = {
	getSettlementList(params: any) {
		return post('/pledge-asset/oo/settlement/list', params)
	},
	getSettlementFileList(params: any) {
		return post('/pledge-asset/oo/settlementPushHistory/list', params)
	},
	downloadSettlementFile(params: any) {
		return getBlob('/pledge-asset/oo/settlementPushHistory/fileDownload?getblob', params)
	},
	manualSettlementFile(params: any) {
		return post('/pledge-asset/oo/settlementPushHistory/pushManual', params)
	},
	chekcSettlement(params: any) {
		return get('/pledge-asset/oo/settlement/query/one', params)
	},
	getSettlementFileDetails(params: any) {
		return get(`/pledge-asset/oo/settlement/detail/${params}`)
	},
	getRefundInfoDetail(params: any) {
		return get(`/pledge-asset/oo/cou/findRefundInfo/${params}`)
	},
	/**
	 *
	 * @param params  :{idList:['1']}
	 * @returns
	 */
	pullSettlementData(params: any) {
		return post('/pledge-asset/oo/settlementPushHistory/pullSettlementData', params)
	},
	/**
	 *
	 * @param params  :{idList:['1']}
	 * @returns
	 */
	resendSettlementPushHistory(params: any) {
		return post('/pledge-asset/oo/settlementPushHistory/resendSettlementPushHistory', params)
	},
}
export default clearingApi
