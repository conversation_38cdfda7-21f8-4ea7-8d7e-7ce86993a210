/*
 * @Descripttion: your project
 * @Author: QI
 * @Date: 2022-11-07 13:53:10
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-01-31 11:33:10
 */
import { get, post } from './apiMethod'

const orderFinanceApi = {
	//获取资产包列表
	getAssetList(params: any) {
		return post('/pledge-asset/asset/package/list', params)
	},
	//获取融资列表
	getFinanceList(params: any) {
		return post('/pledge-asset/order/finance/list', params)
	},
	//获取融资详情
	getFinanceDetail(params: any) {
		return get('/pledge-asset/order/finance/detail', params)
	},
	//获取订单融资中的卡片列表
	getFinanceCardList() {
		return get('/pledge-asset/cc/finance/product/list')
		// return post('/pledge-config/cc/credit/list');
	},
	//获取订单列表
	getOrderList(params: any) {
		return post('/pledge-config/cc/order/list', params)
	},
	// 获取待审核列表
	getAuditList(params: any) {
		return post('/pledge-asset/process/task/loadCurrentUserTask', params)
	},
	// 审核任务
	doTask(params: any) {
		return post('/pledge-asset/process/task/doTask?handleResponse', params)
	},
	createOrderFinance(params: any) {
		return post('/pledge-asset/order/finance/create', params)
	},
	getChainInfo(params: any) {
		return get('/pledge-asset/order/finance/deposit-detail', params)
	},
	// 资产包订单
	getAssetsOrderList(params: any) {
		return post('/pledge-asset/asset/order/list', params)
	},
	// 资产包合同列表
	getAssetContracts(params: any) {
		return post('/pledge-asset/asset/contract/list', params)
	},
	// 资产包发票列表
	getAssetInvoices(params: any) {
		return post('/pledge-asset/asset/invoice/list', params)
	},
	// 资产包物流列表
	getAssetLogistics(params: any) {
		return post('/pledge-asset/asset/logistics/list', params)
	},
	preCheck(params: any) {
		return post('/pledge-asset/order/finance/pre-check', params)
	},
	createFinance(params: any) {
		return post('/pledge-asset/order/finance/create', params)
	},
}

export default orderFinanceApi
