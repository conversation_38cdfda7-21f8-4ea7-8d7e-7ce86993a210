import { post, postBlob } from './apiMethod'

const refundModuleApi = {
	// 退款申请列表
	getRefundList(params: any) {
		return post('/pledge-asset/oo/refund/list', params)
	},
	// 查询退款详情
	getRefundDetail(params: any) {
		return post('/pledge-asset/refund/detail', params)
	},
	//查询开立退款链上明细
	getCreateRefoundChainInfo(params: { refundUuid: string }) {
		return post('/pledge-asset/refund/createChainInfo', params)
	},
	//sp导出退款列表
	exportWxRefundList(params: any) {
		return postBlob('/pledge-asset/refund/list/oo/export?getblob', params)
	},
}

export default refundModuleApi
