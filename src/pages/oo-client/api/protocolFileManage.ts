import { post, postBlob } from './apiMethod'

const protocolFileManageApi = {
	//运营机构查询平台服务费协议列表
	getProtocolList(params) {
		return post('/pledge-asset/oo/platform/fee/protocol/list', params)
	},
	//运营机构查询平台服务费协议文件列表下载
	downloadProtocol(
		params: {
			relationNo?: string
			signBeginTime?: string
			signCompanyName?: string
			signEndTime?: string
		} = {}
	) {
		return postBlob('/pledge-asset/oo/platform/fee/protocol/download?getblob', params)
	},
}

export default protocolFileManageApi
