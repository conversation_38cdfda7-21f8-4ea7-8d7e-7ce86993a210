import { post, postBlob } from './apiMethod'

const rateModuleApi = {
	// 得到平台服务费率列表
	getServiceFeeRateList(params: { pageNum: number; pageSize: number; companyType: number; centerUuid?: string }) {
		return post('/pledge-config/oo/serviceFeeRate/list', params)
	},
	createAndUpdateRate(params: {
		centerUuid: string
		fiUuid: string
		serviceFeeRate: number
		companyType: number
		supplierUuid?: string
		special?: number
		id?: number
	}) {
		return post('/pledge-config/oo/serviceFeeRate/modify', params)
	},
	deleteRate(params: { centerUuid: string; fiUuid: string; serviceFeeRate: number; companyType: number; supplierUuid?: string; special?: number }) {
		return post('/pledge-config/oo/serviceFeeRate/delete', params)
	},
	//导出平台服务费率
	exportServiceFeeRate(params: any) {
		return postBlob('/pledge-config/oo/serviceFeeRate/export?getblob', params)
	},

	// 新增订单平台服务费率
	createChainRate(params: any) {
		return post('/pledge-config/oo/order/service/fee/add', params)
	},
	// 修改订单平台服务费率
	modifyChainRate(params: any) {
		return post('/pledge-config/oo/order/service/fee/modify', params)
	},
	// 查询订单平台服务费率(产品)
	getChainRateListForProduct(params: any) {
		return post('/pledge-config/oo/order/service/productFee/list', params)
	},
	// 查询订单平台服务费率(公司)
	getChainRateListForCompany(params: any) {
		return post('/pledge-config/oo/order/service/companyFee/list', params)
	},
	// 删除订单平台服务费率
	deleteChainRate(params: any) {
		return post('/pledge-config/oo/order/service/fee/del', params)
	},

	// 模糊搜索金融产品
	getProductList(params: any) {
		return post('/pledge-asset/oo/finance/product/list', params)
	},
}

export default rateModuleApi
