import { post } from './apiMethod'

const cfcaManageApi = {
	getSealList(params: any) {
		return post('/pledge-config/cfcasignature/querySealListByProject', params)
	},

	//调试html转pdf
	uploadHtmlFile(params: { file: any; type: string }) {
		const paramsData = new FormData()
		for (const key in params) {
			paramsData.append(key, params[key])
		}
		const url = '/pledge-config/anybody/uploadHtmlFile'
		return post(url, paramsData)
	},
	makeSeal(params: any) {
		return post('/pledge-config/cfcasignature/makeSeal', params)
	},
	deleteSeal(params: { companyUuid: string }) {
		console.log(params)
		return post('/pledge-config/cfcasignature/deleteSealByCompany', params)
	},
}

export default cfcaManageApi
