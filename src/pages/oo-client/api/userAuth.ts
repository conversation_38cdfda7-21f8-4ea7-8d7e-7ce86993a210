import { get, post, postBlob } from './apiMethod'

interface UserInterface {
	username: string
	email: string
	roleIds: number[]
	uuid?: string
	mobile: number
}

const userAuthModuleApi = {
	//获取用户列表
	getUserList(params: any) {
		return post('/pledge-user-service/oo/user/list', params)
	},
	//新增用户
	createUser(params: UserInterface) {
		return post('/pledge-user-service/account/oo/add-account-role', params)
	},
	//更新用户信息
	updateUser(params: UserInterface) {
		return post('/pledge-user-service/rabia/account/updateAccountWithRole', params)
	},
	//解除关联
	removeUser(params: any) {
		return post('/pledge-user-service/rabia/account/removeAccount', params)
	},

	//查询角色id列表可创建角色
	searchRoleList(params: any) {
		return post('/pledge-user-service/role/list/by/support-org-type', params)
	},
	//导出用户列表
	exportUserList(params: any) {
		return postBlob('/pledge-user-service/oo/user/list/export?getblob', params)
	},
}

export default userAuthModuleApi
