import { post } from './apiMethod'

/**
 * 运营设置页面 API module
 */

type newListType = {}
type pageConfigType = {
	pageSize: number
	pageNum: number
}

export default {
	// 新增平台协议
	getNewsList(data: newListType) {
		return post('/pledge-config/oo/info/list', data)
	},
	getDomainList(data: pageConfigType) {
		return post('/pledge-config/oo/domain/list', data)
	},
	/*
	getDomainList(data: pageConfigType) {
		return post('/pledge-config/sp/domain/list', data)
	},
	*/
	addNews: data => {
		return post('/pledge-config/oo/info/add', data)
	},
	modifyNews: data => {
		return post('/pledge-config/oo/info/modify', data)
	},
	deleteNews: data => {
		return post('/pledge-config/oo/info/delete', data)
	},
	///oo/info/add
}
