import { get, post, postBlob } from './apiMethod'

const transferManageApi = {
	//流转管理
	getTransferList(params: any) {
		return post('/pledge-asset/transfer/list', params)
	},
	transferExport(params: any) {
		return postBlob('/pledge-asset/transfer/export?getblob', params)
	},
	// 获取开立、支付融信流程图的数据
	getTransferChartData(params: any) {
		return get('/pledge-asset/process/progress?handleResponse', params)
	},
	//审核流程
	getTransferPath(params: { couUuidList: any }) {
		return post('/pledge-asset/cou/transferPath', params)
	},
	// 查看流转详情
	getTransferDetail(params: { transferUuid: string }) {
		return post('/pledge-asset/transfer/detail', params)
	},
	//查看链上信息
	getChainInfo(params: { transferUuid: string }) {
		return post('/pledge-asset/transfer/chainInfo', params)
	},
	//跟据融信编号查询融信接口
	queryCouListByNoList(params: any) {
		return post('/pledge-asset/cou/list/byNoList?handleResponse', params)
	},
	// 获取开立的时候的发票
	getTransferInvoices(params: any) {
		return post('/pledge-asset/transfer/getInvoices', params)
	},
}

export default transferManageApi
