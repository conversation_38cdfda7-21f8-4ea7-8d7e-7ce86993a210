/**
 * 开票管理模块接口API
 */
import { post, postBlob } from './apiMethod'

const invoicingManageModuleApi = {
	//保理运营查询企业开票抬头信息列表
	queryInvoicingTitleInfo(params = {}) {
		return post('/pledge-config/oo/company-invoicing-info/list', params)
	},
	//保理运营查询本运营机构上传的发票数据列表
	addInvoice(params = {}) {
		return post('/pledge-config/oo/company-invoice/add?handleResponse', params)
	},
	//保理运营查询本运营机构上传的发票数据列表
	queryInvoiceList(params = {}) {
		return post('/pledge-config/oo/company-invoice/list', params)
	},
	//发票识别
	ocr(params: { fileName: string; invoiceBase64Str: string }) {
		return post('/pledge-asset/ocr/async/invoice?handleResponse', params)
	},
	//获取发票结果
	ocrInvoiceResult(params: string[]) {
		return post('/pledge-asset/ocr/async/result', params)
	},
	//保理运营删除发票
	deleteInvoice(params = {}) {
		return post('/pledge-config/oo/company-invoice/delete', params)
	},
	//保理运营推送发票
	notifyInvoiceEmail(params = {}) {
		return post('/pledge-config/oo/company-invoice/notify', params)
	},
	//保理运营导出企业开票信息列表
	exportInvoice(params = {}) {
		return postBlob('/pledge-config/oo/company-invoicing-info/export?getblob', params)
	},
	getInvoiceAlarmList(params = {}) {
		return post('/pledge-asset/invoice-alarm/oo/query', params)
	},
	exportInvoiceAlarm(params = {}) {
		return postBlob('pledge-asset/common/file/export?getblob', params)
	},
	//发票任务管理 1.8.7.1
	taskCreate(params = {}) {
		return post('/pledge-asset/invoice-alarm/task/create', params)
	},
	taskDelete(params = {}) {
		return post('/pledge-asset/invoice-alarm/task/delete', params)
	},
	taskStop(params = {}) {
		return post('/pledge-asset/invoice-alarm/task/stop', params)
	},
	taskList(params = {}) {
		return post('/pledge-asset/invoice-alarm/task/list', params)
	},
}
export default invoicingManageModuleApi
