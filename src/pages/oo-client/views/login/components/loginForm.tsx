import React, { useEffect } from 'react'
import { Button, Form, Input, message } from 'antd'
import { MailOutlined, LockOutlined } from '@ant-design/icons'
import { IloginInfoType, IcaptchaType } from '@src/pages/oo-client/@types/login'
import { loginApi } from '@src/pages/oo-client/api'
import SmEnde from '@utils/crypto/SmEnde'
import styled from 'styled-components'
import { setStorage, getRealMenuList, getFrontCodeListForAdmin, getFirstPagePath } from '@oo/biz/bizIndex'
import { uiData } from '@oo/store/index'
import { useNavigate } from 'react-router-dom'
import refreshTokenHelper from '@utils/request/refreshTokenHelper'

const sm = new SmEnde()

function LoginForm() {
	const navigate = useNavigate()
	const [form] = Form.useForm()
	const [loading, setLoading] = React.useState<boolean>(false)
	const [passwordErrorDesc, setPasswordErrorDesc] = React.useState<string>('')
	const [captchaInfo, setcaptcha] = React.useState<IcaptchaType>({
		img: '',
		captchaNo: '',
		captcha: '',
	})
	const Ref = React.useRef<any>()

	useEffect(() => {
		getCaptcha()
		const timer = setInterval(() => {
			getCaptcha()
		}, 120000)
		Ref.current = timer
		return () => {
			clearInterval(Ref.current)
		}
	}, [])

	const getCaptcha = async () => {
		const res = await loginApi.getCaptcha().catch(err => {
			console.log('err', err)
		})
		setcaptcha(res)
	}
	//登录逻辑
	const handleLogin = async (values: IloginInfoType) => {
		const { email, password, captcha } = values
		const params = {
			userName: email,
			password: sm.getHash(password), //对passwd取sm3的hash
			captcha,
			captchaNo: captchaInfo?.captchaNo,
			projectCode: 'oo',
			fromOo: true,
		}
		if (!loading) {
			setLoading(true) //登录按钮loading开
			const res = await loginApi.login(params).catch(err => {
				console.log('err', err)
			})
			if (res && res.returnCode % 1000 === 0) {
				const token = res.data.token.accessToken
				const expireIn = res.data.token?.expireIn
				const loginData: LoginResp = res.data
				const { account } = loginData
				const { ext } = account
				const loginPerm = ext.loginPerm
				const user = ext.user //获取用户信息

				setPasswordErrorDesc('')

				localStorage.setItem('token', token)
				refreshTokenHelper.setExpireTimeAndToken(expireIn, token)
				// 缓存ext
				localStorage.setItem('ext', JSON.stringify(ext))
				storeUserData(user) // 持久化存储用户信息

				if (user?.roleList?.length === 0) {
					message.info({
						content: '您没有设置任何业务线，请联系运营人员处理',
						duration: 30,
					})
					refresh()
					setLoading(false)
					return
				}
				//判断是否是多角色身份
				if (ext.multiRole) {
					setStorage({
						selectRoleList: user.roleList || [],
					})
					//跳转到角色选择页面
					navigate('/selectRole', {
						//state:
						state: user.roleList || [],
					})
				} else if (loginPerm) {
					let { menuList: originMenuList, frontCodeList } = loginPerm
					if (account.adminFlag) {
						frontCodeList = getFrontCodeListForAdmin()
					}
					let realMenuList = getRealMenuList(frontCodeList, originMenuList)
					let roleList = user?.roleList || []
					let curRoleItem = roleList.length > 0 ? roleList[0] : {}
					// 存储登录的一般信息
					setStorage({ frontCodeList, originMenuList, menuList: realMenuList, selectRoleList: user.roleList || [], curRoleItem })
					// 更新路由
					uiData.updateFrontCodeList(frontCodeList)

					let targetPath = getFirstPagePath(realMenuList)
					navigate(targetPath || '/content')
					message.success('登录成功')
				}
			} else {
				if (res && res.returnDesc && res.returnDesc.includes('40304')) {
					let descList = res.returnDesc.split('-')
					setPasswordErrorDesc(descList[0])
				} else if (res && res.returnDesc) {
					// 处理响应拦截器中没有设置的状态码
					message.error(res.returnDesc)
					setPasswordErrorDesc('')
				}
				getCaptcha()
				setLoading(false) //登录按钮loading关
			}
		}
	}
	const refresh = () => {
		clearInterval(Ref.current)
		getCaptcha()
		const timer = setInterval(() => {
			getCaptcha()
		}, 120000)
		Ref.current = timer
	}
	//格式化用户信息
	const storeUserData = (rawData: any) => {
		const companyInfo = {
			...rawData['company'],
		}
		const userInfo = {
			roles: rawData['roles'],
			uuid: rawData['uuid'],
			phoneNum: rawData['mobile'],
			email: rawData['email'],
			username: rawData['username'],
		}
		localStorage.setItem('userInfo', JSON.stringify(userInfo))
		localStorage.setItem('companyInfo', JSON.stringify(companyInfo))
	}
	const onFinish = (values: IloginInfoType) => {
		handleLogin(values)
	}

	return (
		<LoginFormWrapper>
			<Form form={form} name="login_form" onFinish={onFinish}>
				<Form.Item
					name="email"
					rules={[
						{
							required: true,
							type: 'email',
							message: '请输入邮箱，并确认格式正确',
							whitespace: true,
						},
					]}
				>
					<Input prefix={<MailOutlined />} placeholder="邮箱" />
				</Form.Item>
				<Form.Item
					name="password"
					rules={[
						{
							validator: (_, value) => {
								if (!value) {
									setPasswordErrorDesc('')
									return Promise.reject(new Error('请输入密码'))
								}
								return Promise.resolve()
							},
						},
					]}
				>
					<Input.Password prefix={<LockOutlined />} placeholder="密码" visibilityToggle />
				</Form.Item>
				{passwordErrorDesc ? <div style={{ color: '#f04134', position: 'absolute', top: '102px' }}>{passwordErrorDesc}</div> : null}
				<div className="capItem">
					<Form.Item name="captcha" rules={[{ required: true, message: '请输入验证码', whitespace: false }]}>
						<Input type="text" placeholder="验证码" className="capInput" />
					</Form.Item>
					<div className="captcha" onClick={refresh}>
						<img src={'data:image/png;base64,' + captchaInfo?.img} alt="看不清？刷新一下" title="看不清？刷新一下" />
					</div>
				</div>

				<Form.Item>
					<Button className="oper-btn" type="primary" htmlType="submit" loading={loading}>
						登 录
					</Button>
				</Form.Item>
			</Form>
		</LoginFormWrapper>
	)
}

const LoginFormWrapper = styled.div`
	position: relative;
	.oper-btn {
		width: 100%;
	}
	.capItem {
		display: flex;
		flex-wrap: wrap;
		.ant-form-item-control-input-content {
			display: flex;
			margin-right: 10px;
			width: auto !important;
		}
		.ant-form-item {
			flex: 1;
		}
		.captcha {
			background-color: #e9eefb;
			background-image: none;
			border: 1px solid transparent;
			border-radius: 4px;
			color: blue;
			text-align: center;
			width: 100px;
			height: 38px;
			display: flex;
			align-items: center;
			img {
				width: 100%;
				height: 100%;
			}
		}
	}
`

export default LoginForm
