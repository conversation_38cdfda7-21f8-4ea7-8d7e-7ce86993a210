import React, { useEffect } from 'react'
import styled from 'styled-components'
import { ConfigProvider, Space, Button, message } from 'antd'
//引入子组件
import LoginForm from './components/loginForm'
import ActiveForm from './components/activeForm'
import ForgetForm from './components/forget'
import { setStorage } from '../../biz/bizIndex'
import { useNavigate } from 'react-router-dom'
import { getFrontCodeListForAdmin, getRealMenuList } from '../../biz/bizIndex'
import { uiData } from '../../store'
import { getStorage, getFirstPagePath } from '../../biz/bizIndex'
import { loginApi } from '../../api'
import { formatLoginUrl } from '@src/utils/jy-helper'

function Login() {
	const searchParams = new URLSearchParams(window.location.search || window.location.href.split('?')[1] || '')
	const navigate = useNavigate()

	//清空数据
	useEffect(() => {
		setStorage({
			frontCodeList: [],
			menuList: [],
			originMenuList: [],
			selectRoleList: [],
		})
	}, [])

	const handleSetPerm = ext => {
		let { loginPerm, user } = ext
		let { menuList: originMenuList, frontCodeList, roleCode } = loginPerm?.roleList?.[0] || {} // 默认选中第一个角色
		if (ext?.user?.adminFlag) {
			frontCodeList = getFrontCodeListForAdmin()
		}
		originMenuList = originMenuList || []
		frontCodeList = frontCodeList || []
		let realMenuList = getRealMenuList(frontCodeList, originMenuList)

		// 存储登录的一般信息
		setStorage({ userInfo: user, frontCodeList, originMenuList, menuList: realMenuList, roleCode })
		// 更新路由
		uiData.updateFrontCodeList(frontCodeList)
	}

	const storeUserData = (rawData: any) => {
		const companyInfo = {
			...rawData['company'],
		}
		const userInfo = {
			roles: rawData['roles'],
			uuid: rawData['uuid'],
			phoneNum: rawData['mobile'],
			email: rawData['email'],
			username: rawData['username'],
		}
		localStorage.setItem('userInfo', JSON.stringify(userInfo))
		localStorage.setItem('companyInfo', JSON.stringify(companyInfo))
	}

	// 检查token进行登录
	const handleLogin = () => {
		// 检查是否有token
		const token = searchParams.get('token')
		if (token) {
			localStorage.setItem('token', token)
			loginApi
				.getNewBaseInfo()
				.then(res => {
					if (res) {
						localStorage.setItem('ext', JSON.stringify(res))
						handleSetPerm(res)
						storeUserData(res?.user) // 持久化存储用户信息
						message.success('登录成功')
						let realMenuList = getStorage('menuList')
						let targetPath = getFirstPagePath(realMenuList)
						window.history.replaceState({}, '', window.location.origin)
						navigate(targetPath || '/content')
					}
				})
				.catch(res => {
					if (res.returnCode === 72010) {
						// message.warning(res.returnDesc)
					}
				})
		}
	}

	useEffect(() => {
		handleLogin()
	}, [window.location.href])

	const returnUrl = encodeURIComponent(`${window.location.origin}${window.location.pathname}`)

	// 跳转登录页
	const handleClickLogin = () => {
		window.location.href = formatLoginUrl({ returnUrl })
	}

	// 跳转注册页
	const handleClickRegister = () => {
		window.location.href = formatLoginUrl({ returnUrl, step: 'register' })
	}

	return (
		<ConfigProvider componentSize="large">
			<LoginWrapper>
				<div className="page-content">
					<div className="opera-icon"></div>
					<div className="title">
						<div>运营管理后台</div>
					</div>
					<div style={{ textAlign: 'center', marginTop: 20 }}>
						{/* <Space> */}

						<Button type="primary" onClick={handleClickRegister} style={{ marginRight: '22px' }}>
							注册
						</Button>
						<Button type="primary" onClick={handleClickLogin}>
							登录
						</Button>
						{/* </Space> */}
					</div>
				</div>
				<div className="spLogin">
					<div className="top" style={{ padding: '20px', boxSizing: 'border-box', height: '50px' }}>
						<div className="fr">
							<Space>
								<img src={require('@src/assets/images/logos/jiangyinlogo.png')} alt="" />
								{/* <span>壹链通供应链金融平台</span> */}
							</Space>
						</div>
					</div>
				</div>
				{/* <div className="information">
					<p> ©1692021 万向区块链 沪ICP备17019413号-1 沪公网安备31010902002886号</p>
				</div> */}
			</LoginWrapper>
		</ConfigProvider>
	)
}
const LoginWrapper = styled.div`
	width: 100%;
	height: 100vh;
	position: relative;
	min-height: 600px;
	display: flex;
	justify-content: center;
	align-items: center;
	background-image: url(${require('@src/assets/images/logos/opbg.png')});
	background-size: 100% 100%;
	background-position: center;
	.spLogin {
		.top {
			position: absolute;
			left: 100px;
			top: 20px;
			.fr {
				img {
					width: 108px;
					height: 80px;
					vertical-align: baseline;
				}
				span {
					font-weight: 800;
				}
			}
		}
	}
	.page-content {
		width: 360px;
		height: auto;
		padding: 40px 20px;
		background: #fff;
		border: 1px solid #ccc;
		border-radius: 4px;
		.opera-icon {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 40px;
			-webkit-app-region: drag;
		}
		.title {
			font-weight: 600;
			color: rgba(21, 35, 81, 1);
			text-align: center;
			line-height: 30px;
			font-size: 20px;
		}
		.form-area {
			margin-top: 30px;
		}
		.bottom-btn {
			color: rgb(173, 173, 173);
			margin-top: 20px;
			.left,
			.right {
				font-size: 14px;
				cursor: pointer;
				transition: color 0.1s ease;
				&:active {
					color: #007aea;
				}
				&:hover {
					color: #1890ff;
				}
			}
			.right {
				float: right;
			}
		}
	}
	.information {
		position: absolute;
		bottom: 0;
		height: 40px;
		font-size: 18px;
		font-weight: 400;
		color: rgb(173, 173, 173);
		line-height: 40px;
		margin-bottom: 20px;
		text-align: center;
	}
	// 修改AntDesign样式
	.ant-input {
		font-size: 14px;
		color: #a3a3a3;
		background-color: transparent;
	}
	.ant-form-item-explain.ant-form-item-explain-error {
		font-size: 12px !important;
	}
	.ant-form-item-explain.ant-form-item-explain-success {
		font-size: 12px !important;
	}
`

export default Login
