import React, { useState, useEffect } from 'react'
import { Modal, Form, Checkbox, Row, Col, Select, message } from 'antd'
import { verifyTypeList, verifyCompanyInfo } from '../searchCompanyConfig'
import { searchCompanyApi } from '@src/pages/oo-client/api'
import SmallTitle from '@src/globalComponents/SmallTitle'
import styled from 'styled-components'

interface PropsStruct {
	data: any //用于企业展示的数据，录入企业不需要
	visible: boolean
	parentCompanyList: Array<any>
	onOk: () => void
	onCancel: () => void
}

const layout = {
	labelCol: { span: 11 },
	wrapperCol: { span: 13 },
}

type companyTypeListType = {
	key: string
	value: string
	disabled?: boolean
}[]

function EditCompInfoModal(props: PropsStruct) {
	const [form] = Form.useForm()
	const [confirmLoading, setConfirmLoading] = useState<boolean>(false)
	const [companyType, setCompanyType] = useState<string>('CAndS')
	const [groupLimit, setGroupList] = useState<{
		[k: string]: string[]
	}>({})

	const [companyList, setCompanyList] = useState<companyTypeListType>([])
	const { data, visible, parentCompanyList, onOk, onCancel } = props

	const filterAndPreviewData = async initData => {
		const curCompanyList: companyTypeListType = await searchCompanyApi.getCompanyListByLob({ lob: initData.bizLine, includeAll: false }).catch(err => {
			console.log('err', err)
		})
		const companyListGroup = await searchCompanyApi.getCompanyListGroup({ lob: initData.bizLine }).catch(err => {
			console.log('err', err)
		})
		let _groupLimit = {}
		companyListGroup.forEach(gItem => {
			let curGroup = gItem.companyTypeList
			curGroup.forEach(compName => {
				//以该公司名为key,该公司可共存的列表为value
				_groupLimit[compName] = curGroup
			})
		})
		setGroupList(_groupLimit)
		let record = initData
		let type: any[] = []
		// 设置类型
		if (record.type) {
			let getCompanyType = record.type.split(',')
			if (initData.bizLine == 'bill') {
				// 票据修改，有B的情况下只能包含B
				if (record?.type?.indexOf('B') !== -1) {
					type = ['B']
				}
				checkBoxOnChange(type, curCompanyList, _groupLimit)
			} else if (initData.bizLine == 'order') {
				// 随便设置一个订单端的类型即可
				if (record?.type?.indexOf('OCC') !== -1) {
					type = ['OCC']
				} else if (record?.type?.indexOf('OFI') !== -1) {
					type = ['OFI']
				}
				checkBoxOnChange(type, curCompanyList, _groupLimit)
			} else {
				//保理修改
				// 把B、OCC、OFI过滤
				type = getCompanyType.filter(i => {
					return i !== 'B' && i !== 'OCC' && i !== 'OFI'
				})
				checkBoxOnChange(type, curCompanyList, _groupLimit)
			}
		} else if (initData.bizLine == 'bill') {
			setCompanyType('B')
			setCompanyList(curCompanyList)
		} else {
			setCompanyList(curCompanyList)
		}

		let matchStatus = undefined
		let matchBillStatus = undefined
		let matchOrderStatus = undefined
		if (record.businessStatus !== 'INIT') {
			matchStatus = record.businessStatus
		}
		if (record.billBusinessStatus !== 'INIT') {
			matchBillStatus = record.billBusinessStatus
		}
		if (record.orderBusinessStatus !== 'INIT') {
			matchOrderStatus = record.orderBusinessStatus
		}
		// 如果没有母公司，则设置parentCompanyUuid为''，与不关联对应
		if (record.parentCompanyUuid === null) {
			record.parentCompanyUuid = ''
		}
		form.setFieldsValue({
			...record,
			companyType: type,
			businessStatus: matchStatus,
			billBusinessStatus: matchBillStatus,
			orderBusinessStatus: matchOrderStatus,
		})
	}

	useEffect(() => {
		if (visible && data) {
			filterAndPreviewData(data)
		} else {
			// 关闭弹窗时设置默认值
			setCompanyType('CAndS')
			form.resetFields()
		}
	}, [visible])

	const formOnFinish = values => {
		setConfirmLoading(true)
		submitData(values)
			.finally(() => {
				setConfirmLoading(false)
			})
			.catch(err => {
				console.log('err', err)
			})
	}

	const onConfirm = async () => {
		form.submit()

		/*
		form
			.validateFields()
			.then(info => {
				submitData(info)
			})
			.finally(() => {
				setConfirmLoading(false)
			})
			.catch(err => {
				console.log('err', err)
			})
		*/
	}

	const getStatus = values => {
		if (values['businessStatus']) {
			return values['businessStatus']
		} else if (values['billBusinessStatus']) {
			return values['billBusinessStatus']
		} else {
			return values['orderBusinessStatus']
		}
	}

	const submitData = async values => {
		values['uuid'] = data.uuid
		let params: any = {
			type: values['companyType'].join(','),
			companyUuid: values['uuid'],
			lob: data?.bizLine,
			bizStatus: getStatus(values),
		}
		if (values['companyType'][0] === 'FI') {
			if (values['parentCompanyUuid']) {
				params['parentCompanyUuid'] = values['parentCompanyUuid']
			} else {
				params['parentCompanyUuid'] = null
			}
		}
		const res = await searchCompanyApi.modifyCompany(params).catch(err => {
			console.log('err', err)
		})
		if (res !== undefined) {
			message.success('修改成功')
			onCancel()
			onOk()
		}
		setConfirmLoading(false)
	}

	const checkBoxOnChange = (type, curCompanyList, checkGroupLimit) => {
		let curSelectedType = ''
		if (Array.isArray(type)) {
			if (type.length > 0) {
				curSelectedType = type[0]
			}
			type = type.join(',')
		}

		//当前允许共存的列表
		let curGroupList = checkGroupLimit[curSelectedType] || []
		//设置禁用状态
		let newCompanyList = curCompanyList.map(companyItem => {
			if (curGroupList.length === 0) {
				companyItem.disabled = false
			} else {
				if (curGroupList.includes(companyItem.key)) {
					companyItem.disabled = false
				} else {
					companyItem.disabled = true
				}
			}

			return companyItem
		})
		setCompanyList([...newCompanyList])

		matchCompanyType(type)
	}
	//判断公司类型页面显示
	const matchCompanyType = type => {
		const operateType = data.bizLine
		if (operateType === 'bill') {
			setCompanyType('B')
		} else if (operateType === 'order') {
			setCompanyType('O')
		} else if (operateType === 'factor') {
			let newType = 'CAndS'
			if (type.includes('C') || type.includes('S')) {
				newType = 'CAndS'
			} else if (type.includes('FI')) {
				newType = 'FI'
			} else if (type.includes('PFI')) {
				newType = 'PFI'
			}
			setCompanyType(newType)
		}
	}

	const infoList = (data: any) => {
		let infoItems: any = {
			companyType: function (itemConfig) {
				return (
					<Col key="companyType" span={itemConfig.span}>
						<Form.Item name="companyType" label="企业类型" rules={[{ required: true, message: '请选择企业类型' }]}>
							<Checkbox.Group style={{ width: '100%' }} onChange={type => checkBoxOnChange(type, companyList, groupLimit)}>
								{companyList.map(item => {
									return (
										<Col span={16}>
											<Checkbox value={item.key} disabled={item.disabled}>
												{item.value}
											</Checkbox>
										</Col>
									)
								})}
							</Checkbox.Group>
						</Form.Item>
					</Col>
				)
			},
			parentCompanyUuid: function (itemConfig) {
				return (
					<Col key="parentCompanyUuid" span={itemConfig.span}>
						<Form.Item name="parentCompanyUuid" label="关联母公司">
							<Select getPopupContainer={triggerNode => triggerNode.parentElement}>
								{parentCompanyList &&
									parentCompanyList.map(item => {
										return (
											<Select.Option value={item.companyUuid} key={item.companyUuid}>
												{item.companyName}
											</Select.Option>
										)
									})}
							</Select>
						</Form.Item>
					</Col>
				)
			},
			status: function (itemConfig) {
				return (
					<Col key="status" span={itemConfig.span}>
						<Form.Item name="businessStatus" label="业务状态" rules={[{ required: true, message: '请选择审核状态' }]}>
							<Select placeholder="未审核" getPopupContainer={triggerNode => triggerNode.parentElement}>
								{verifyTypeList.map((fi: any) => {
									return (
										<Select.Option value={fi.key} key={fi.key}>
											{fi.name}
										</Select.Option>
									)
								})}
							</Select>
						</Form.Item>
					</Col>
				)
			},
			billStatus: function (itemConfig) {
				return (
					<Col key="billBusinessStatus" span={itemConfig.span}>
						<Form.Item name="billBusinessStatus" label="业务状态" rules={[{ required: true, message: '请选择审核状态' }]}>
							<Select placeholder="请选择审核状态" getPopupContainer={triggerNode => triggerNode.parentElement}>
								{verifyTypeList.map((fi: any) => {
									return (
										<Select.Option value={fi.key} key={fi.key}>
											{fi.name}
										</Select.Option>
									)
								})}
							</Select>
						</Form.Item>
					</Col>
				)
			},
			orderStatus: function (itemConfig) {
				return (
					<Col key="orderBusinessStatus" span={itemConfig.span}>
						<Form.Item name="orderBusinessStatus" label="业务状态" rules={[{ required: true, message: '请选择审核状态' }]}>
							<Select placeholder={'请选择审核状态'} getPopupContainer={triggerNode => triggerNode.parentElement}>
								{verifyTypeList.map((fi: any) => {
									return (
										<Select.Option value={fi.key} key={fi.key}>
											{fi.name}
										</Select.Option>
									)
								})}
							</Select>
						</Form.Item>
					</Col>
				)
			},
		}

		const getItems = () => {
			let result: any = []
			let spanCalc = 0
			let rowCols: any = []
			let rowKey = 0
			data.forEach(item => {
				let { key = '', span = 12 } = item
				const ItemRender: Function = infoItems[key]
				if (ItemRender) {
					if (spanCalc >= 24) {
						let row = <Row key={rowKey}>{rowCols}</Row>
						rowKey++
						result.push(row)
						spanCalc = 0
						rowCols = []
					}
					if (spanCalc + span > 24) {
						span = 24 - spanCalc
					}
					spanCalc = spanCalc + span
					rowCols.push(<ItemRender key={key} span={span}></ItemRender>)
				}
			})
			if (rowCols.length > 0) {
				let row = <Row key={rowKey}>{rowCols}</Row>
				result.push(row)
			}
			return result
		}

		return getItems()
	}

	return (
		<Modal
			forceRender
			width="860px"
			okText={'确定'}
			maskClosable={false}
			title={'修改企业信息'}
			open={visible}
			onOk={onConfirm}
			onCancel={() => {
				onCancel()
			}}
			confirmLoading={confirmLoading}
		>
			<EditWrapper>
				<Form {...layout} form={form} onFinish={formOnFinish} name="add_form" validateTrigger="onChange" scrollToFirstError={true}>
					<SmallTitle text="运营机构审核" />
					{infoList(verifyCompanyInfo[companyType])}
				</Form>
			</EditWrapper>
		</Modal>
	)
}
const EditWrapper = styled.div`
	.ant-form {
		padding: 20px;
	}
`
export default EditCompInfoModal
