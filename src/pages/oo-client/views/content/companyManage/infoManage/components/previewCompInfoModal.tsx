import React, { useState, useEffect } from 'react'
import { Mo<PERSON>, Spin } from 'antd'
import { previewBaseInfoType, previewAdminInfo, previewVerifyCompanyInfo, previewUploadFiles, previewAuthInfo, postAddressList } from '../searchCompanyConfig'
import { searchCompanyApi } from '@src/pages/oo-client/api'
import { b64toBlob } from '@src/utils/util'
import { getInfoList } from '@src/globalConfig/previewCompanyRender'
import SmallTitle from '@src/globalComponents/SmallTitle'
import PDFViewer from '@src/globalComponents/PDFViewer'
import FileSaver from 'file-saver'
import styled from 'styled-components'

interface PropsStruct {
	data: any
	visible: boolean
	onCancel: () => void
}

function PreviewCompInfoModal(props: PropsStruct) {
	const [companyType, setCompanyType] = useState<string>('FI')
	const [protocolSigns, setProtocolSigns] = useState<any[]>([])
	const [fileData, setFileData] = useState<{ fileName: string; fileUrl: string }>({ fileName: '', fileUrl: '' })
	const [fileVisible, setFileVisible] = useState<boolean>(false)
	const [viewLoading, setViewLoading] = React.useState<boolean>(false)
	const [companyDetail, setCompanyDetail] = useState<any>({})
	const { data, visible, onCancel } = props

	useEffect(() => {
		if (visible) {
			if (data.id) {
				getCompanyDetailByUuid(data.id)
			}
		}
	}, [visible])

	const getCompanyDetailByUuid = async (id: string) => {
		setViewLoading(true)
		const res = await searchCompanyApi.getCompanyDetailByUuid({ id }).catch(err => {
			console.log('err', err)
		})
		setViewLoading(false)
		// 处理返回的数据，表单回填
		if (res !== undefined) {
			console.log('res', res)
			const userInfo = await searchCompanyApi.getUserInfo({ id: res?.managerId }) // 管理员手机通过接口获取
			//FI,B
			let typeArr = res?.type?.split(',') || []
			if (typeArr.includes('FI')) {
				setCompanyType('FI')
			} else if (typeArr.includes('PFI')) {
				setCompanyType('PFI')
			} else {
				setCompanyType('CAndS')
			}
			if (res.protocolSigns) setProtocolSigns(res.protocolSigns)
			setCompanyDetail({ ...res, ...userInfo })
		}
	}

	const cancelModal = () => {
		onCancel()
		setProtocolSigns([])
		setCompanyDetail({})
	}
	const previewClick = previewData => {
		console.log(previewData)
		setFileData(previewData)
		setFileVisible(true)
	}

	//基本信息
	const BaseInfo = () => {
		if (previewBaseInfoType.length) {
			return (
				<PreviewWrapper className="card-wrapper" style={{ padding: 0 }}>
					<div className="boxContentWrap">
						<SmallTitle text="基本信息" />
						<div className="boxContent">
							<div className="baseInfo">
								{getInfoList(previewBaseInfoType, companyDetail)}
								{getInfoList(previewUploadFiles, companyDetail, previewClick)}
							</div>
						</div>
					</div>
				</PreviewWrapper>
			)
		} else return null
	}
	//送达地址
	const PostAddressInfo = () => {
		if (previewBaseInfoType.length) {
			return (
				<PreviewWrapper className="card-wrapper" style={{ padding: 0 }}>
					<SmallTitle text="送达地址" />
					<div className="baseInfo">{getInfoList(postAddressList, companyDetail)}</div>
				</PreviewWrapper>
			)
		} else return null
	}

	//法律信息
	const PreviewAdminInfo = () => {
		if (previewAdminInfo.length) {
			return (
				<PreviewWrapper className="card-wrapper" style={{ padding: 0 }}>
					<div className="boxContentWrap">
						<SmallTitle text="管理员（认证申请人）" />
						<div className="boxContent">{getInfoList(previewAdminInfo, companyDetail, previewClick)}</div>
					</div>
				</PreviewWrapper>
			)
		} else return null
	}

	//企业认证
	const AuthInfo = () => {
		if (previewAuthInfo.length) {
			return (
				<PreviewWrapper className="card-wrapper" style={{ padding: 0 }}>
					<div className="boxContentWrap">
						<SmallTitle text="企业认证" />
						<div className="boxContent">{getInfoList(previewAuthInfo, companyDetail)}</div>
					</div>
				</PreviewWrapper>
			)
		} else return null
	}

	//运营机构审核
	const VerifyInfo = () => {
		const verifyCompanyInfo = previewVerifyCompanyInfo[companyType]
		if (verifyCompanyInfo.length) {
			return (
				<PreviewWrapper className="card-wrapper" style={{ padding: 0 }}>
					<div className="boxContentWrap">
						<SmallTitle text="运营机构审核" />
						<div className="boxContent">{getInfoList(verifyCompanyInfo, companyDetail)}</div>
					</div>
				</PreviewWrapper>
			)
		} else return null
	}

	//协议信息
	const ProtocolSigns = () => {
		let fileList = protocolSigns.map((item: any) => {
			let fileName = ''
			if (item.protocolType === 'companyKey') fileName = '密钥确认函.pdf'
			if (item.protocolType === 'platformServer') fileName = '平台服务章程.pdf'
			if (item.protocolType === 'userRule') fileName = '平台服务及用户操作规则.pdf'
			return {
				fileType: item.protocolType,
				fileUrl: item.protocolUrl,
				fileName,
				projectCode: item.projectCode,
			}
		})
		const downloadFile = fileObj => {
			searchCompanyApi.downloadFile({ fileUrl: fileObj.fileUrl }).then(url => {
				const fileblob = b64toBlob(url, 'application/pdf')
				FileSaver.saveAs(fileblob, fileObj.fileName)
			})
		}
		let venaFileList = fileList.filter(item => item.projectCode === 'factor')
		let billFileList = fileList.filter(item => item.projectCode === 'bill')
		let orderFileList = fileList.filter(item => item.projectCode === 'order_cc' || item.projectCode === 'order_fi')
		return (
			<>
				{venaFileList && venaFileList.length > 0 && (
					<PreviewWrapper className="card-wrapper" style={{ padding: 0 }}>
						<div className="boxContentWrap">
							<SmallTitle text="企业平台协议" />
							<div className="boxContent">
								<div className="protocolSigns">
									{venaFileList.map(file => {
										return (
											<div className="protocol">
												<span onClick={() => previewClick(file)}>{file.fileName}</span>
												<span onClick={() => downloadFile(file)}>下载</span>
											</div>
										)
									})}
								</div>
							</div>
						</div>
					</PreviewWrapper>
				)}
				{billFileList && billFileList?.length > 0 && (
					<PreviewWrapper className="card-wrapper">
						<SmallTitle text="票据平台协议" />
						<div className="protocolSigns">
							{billFileList.map(file => {
								return (
									<div className="protocol">
										<span onClick={() => previewClick(file)}>{file.fileName}</span>
										<span onClick={() => downloadFile(file)}>下载</span>
									</div>
								)
							})}
						</div>
					</PreviewWrapper>
				)}
				{orderFileList.length > 0 ? (
					<PreviewWrapper className="card-wrapper">
						<SmallTitle text="订单平台协议" />
						<div className="protocolSigns">
							{orderFileList.map(file => {
								return (
									<div className="protocol">
										<span onClick={() => previewClick(file)}>{file.fileName}</span>
										<span onClick={() => downloadFile(file)}>下载</span>
									</div>
								)
							})}
						</div>
					</PreviewWrapper>
				) : null}
			</>
		)
	}

	return (
		<Modal title="企业信息详情" open={visible} onCancel={cancelModal} footer={null} width="860px" className="company-preview">
			<Spin tip="加载中..." spinning={viewLoading}>
				<div className="main-wrapper">
					<BaseInfo />
					{/* <PostAddressInfo /> */}
					<PreviewAdminInfo />
					<AuthInfo />
					<VerifyInfo />
					{protocolSigns && protocolSigns.length > 0 ? <ProtocolSigns /> : null}
				</div>
				<PDFViewer title={fileData.fileName} pdfUrl={fileData.fileUrl} visible={fileVisible} onCancel={() => setFileVisible(false)} />
			</Spin>
		</Modal>
	)
}

const PreviewWrapper = styled.div`
	.baseInfo {
		.card-list {
			padding: 0 20px;
		}
	}
	.protocolSigns {
		.protocol {
			width: 300px;
			height: 50px;
			margin: 10px 30px;
			background: #f2f2f2;
			line-height: 50px;
			span {
				margin-left: 20px;
				color: #49a9ee;
				cursor: pointer;
			}
			span:last-child {
				margin-right: 50px;
				float: right;
			}
		}
	}
`

export default React.memo(PreviewCompInfoModal)
