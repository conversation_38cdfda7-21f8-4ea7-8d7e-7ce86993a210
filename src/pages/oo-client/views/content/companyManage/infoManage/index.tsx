import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, message } from 'antd'
import { searchCompanyApi } from '@src/pages/oo-client/api'
import { formatTableData } from '@utils/format'
import { deepExtend, getUserData } from '@utils/util'
import { billBusinessStatusToSearch, businessStatusToSearch, projectCodeSearchList } from './searchCompanyConfig'
import { companyTypeList } from '@ooContent/companyManage/enterpriseManage/searchCompanyConfig'
import { stampToTime, timeToStamp } from '@src/utils/timeFilter'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import commonModuleApi from '@src/pages/oo-client/api/common'
import BaseTable from '@globalComponents/BaseTable'
import LayoutSlot from '@globalComponents/LayoutSlot'
import SearchBar from '@ooClientComponents/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import PreviewCompInfoModal from './components/previewCompInfoModal'
import EditCompInfoModal from './components/editCompInfoModal'
import styled from 'styled-components'
import moment from 'moment'

function SearchCompany() {
	const { uuid = null } = getUserData().companyInfo
	const [dialogData, setDialogData] = useState<any>(null)
	//预览弹框相关
	const [previewDialogVisible, setPreviewDialogVisible] = useState(false)
	const [editCompanyVisible, setEditCompanyVisible] = useState(false)
	//table相关
	const [searchParams, setSearchParams] = useState({})
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [dataSource, setDataSource] = useState([])
	const [tableLoading, setTableLoading] = useState(false)
	const [parentCompanyList, setpPrentCompanyList] = useState([])
	const [exportLoading, setExportLoading] = useState(false)

	useEffect(() => {
		getCompanyInfoList()
	}, [pagination.current, pagination.pageSize, searchParams])

	useEffect(() => {
		// getPFICompanyList()
	}, [])

	const getPFICompanyList = async () => {
		let result: any = []
		const res = await searchCompanyApi.getCompanyInfoList({ pageNum: 1, pageSize: 9999999, type: 'PFI' }).catch(err => {
			console.log('err', err)
		})
		const companyList = deepExtend([], res?.list)
		result.push({
			companyName: '不关联',
			companyUuid: '',
			companyPubKey: '',
		})
		companyList &&
			companyList.map(item => {
				if (item.pubKey) {
					result.push({
						companyName: item.companyName,
						companyUuid: item.uuid,
						companyPubKey: item.pubKey,
					})
				}
			})
		setpPrentCompanyList(result)
		return result
	}

	const getColumns = () => {
		const columnsDic = {
			orgName: { render: renderCompanyName },
			// operation: { render: renderOperation, width: 120 },
		}
		return getColumnsByPageName('companyList', columnsDic)
	}

	//获取企业信息列表
	const getCompanyInfoList = async (values?: any) => {
		let params = {
			current: pagination.current,
			size: pagination.pageSize,
			...searchParams,
			status: 'CONFIRM',
			operatingOrganizationUuid: uuid,
			desc: 'create_time',
		}
		if (values) {
			params = Object.assign(params, values)
		}
		setTableLoading(true)
		const res = await searchCompanyApi.getCompanyInfoList(params).catch(() => {
			setDataSource([])
			setTableLoading(false)
			pagination.total = 0
			setPagination({ ...pagination })
		})
		if (res) {
			if (res.list && res.list.length > 0) {
				setTableLoading(false)
				setDataSource(formatTableData.addKey(res.list))
				pagination.total = res.total
				setPagination({ ...pagination })
			} else {
				setDataSource([])
				setTableLoading(false)
				pagination.total = 0

				setPagination({ ...pagination })
			}
		}
	}

	//开启预览弹框
	const openPreviewDialog = (record: any) => {
		setDialogData(record)
		setPreviewDialogVisible(true)
	}

	//开启录入、编辑弹框
	const openEditDialog = (record: any, type: string) => {
		//record.compType = type
		// 传业务线，获取公司类型
		record.bizLine = type
		setDialogData(record)
		setEditCompanyVisible(true)
	}

	//处理搜索
	const handleSearch = (values: any) => {
		if (values && values.createDate && values.createDate.length === 2) {
			if (values.createDate[1]) values.endDate = moment(values.createDate[1]).format('YYYY-MM-DD')
			if (values.createDate[0]) values.startDate = moment(values.createDate[0]).format('YYYY-MM-DD')
			delete values.createDate
		}
		//点击查询后，带入参数页码置为1, 记录当前查询参数
		pagination.current = 1
		setSearchParams(values)
		setPagination({ ...pagination })
	}

	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}

	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}

	const handleReset = () => {
		pagination.current = 1
		setSearchParams({})
		setPagination({ ...pagination })
	}

	//渲染企业名称
	const renderCompanyName = (text: string, record: any) => (
		<Tooltip title={text} placement="topLeft">
			<span className="link" onClick={() => openPreviewDialog(record)}>
				{' '}
				{text}
			</span>
		</Tooltip>
	)

	const handleEditCompany = () => {
		getCompanyInfoList()
		setEditCompanyVisible(false)
	}

	//渲染操作列
	// const renderOperation = (text: string, record: any) => {
	// 	return (
	// 		<div>
	// 			<a className="link left-btn" onClick={() => openEditDialog(record, 'bill')}>
	// 				票据修改
	// 			</a>
	// 			<a className="link left-btn" style={{ whiteSpace: 'nowrap' }} onClick={() => openEditDialog(record, 'factor')}>
	// 				保理修改
	// 			</a>
	// 			<a className="link left-btn" style={{ whiteSpace: 'nowrap' }} onClick={() => openEditDialog(record, 'order')}>
	// 				订单修改
	// 			</a>
	// 		</div>
	// 	)
	// }

	//导出公司列表信息
	const exportCouCsv = async () => {
		setExportLoading(true)
		const fileName = `Download_${stampToTime(new Date().getTime(), 9)}.xlsx`
		let type = 'application/vnd.ms-excel'
		// let type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
		searchCompanyApi
			.exportOoCompanyInfoList({
				...searchParams,
				pageNum: 1,
				pageSize: 2147483646,
				// status: 'CONFIRM',
				// operatingOrganizationUuid: uuid,
			})
			.then((response: any) => {
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}

	return (
		<LayoutSlot>
			<SearchCompanyStyle className="card-wrapper">
				<div style={{ marginBottom: '20px' }}></div>
				<OperatingArea>
					<SearchBar
						pageName="companyInfoList"
						optionData={{
							orgType: companyTypeList,
							// billBusinessStatus: billBusinessStatusToSearch,
							// orderBusinessStatus: billBusinessStatusToSearch,
							// businessStatus: businessStatusToSearch,
							// projectCode: projectCodeSearchList,
						}}
						onSubmit={handleSearch}
						onClear={handleReset}
					/>
					<Button style={{ marginLeft: '5px' }} type="primary" onClick={exportCouCsv} loading={exportLoading}>
						导出
					</Button>
				</OperatingArea>
				<BaseTable
					className="card-table"
					loading={tableLoading}
					dataSource={dataSource}
					columns={getColumns()}
					total={pagination.total}
					current={pagination.current}
					onPageChange={handlePageChange}
					onSizeChange={handleSizeChange}
				/>
			</SearchCompanyStyle>
			<PreviewCompInfoModal data={dialogData} visible={previewDialogVisible} onCancel={() => setPreviewDialogVisible(false)} />
			<EditCompInfoModal
				data={dialogData}
				visible={editCompanyVisible}
				onCancel={() => setEditCompanyVisible(false)}
				onOk={handleEditCompany}
				parentCompanyList={parentCompanyList}
			/>
		</LayoutSlot>
	)
}

const SearchCompanyStyle = styled.div`
	.left-btn {
		margin-right: 5px;
	}
`

export default SearchCompany
