// 预览部分
const previewBaseInfoType: string[] = [
	// 'companyName',
	'orgName',
	'socialCreditCode',
	'industry',
	'corporationScale',
	'provinceCity',
	'address',
	'managerNo',
	'_blank',
	'legalPerson',
	'certificateType',
	'legalPersonIdCode',
	'fiCode',
]
const previewUploadFiles: string[] = [
	'idPortraitoad',
	// 'idNationalEmblem',
	'_blank',
	'businessLicense',
	'openingPermit',
	'CFCACertificateApplicationForm',
	'CFCACertificateOfAuthorization',
	'idCardOfHandler',
]
const previewVerifyCompanyInfo = {
	PFI: ['type', 'businessStatus', 'billBusinessStatus', 'orderBusinessStatus'],
	FI: ['type', 'parentCompanyName', 'businessStatus', 'billBusinessStatus', 'orderBusinessStatus'],
	CAndS: [
		'type',
		// 'businessStatus',
		// 'billBusinessStatus',
		// 'orderBusinessStatus'
	],
}
const previewAdminInfo: string[] = ['mobile', 'realName', 'idCard', 'profilePicture']
const previewAuthInfo: string[] = ['approveStatus', 'registerSystemCode', 'authRemark']

const postAddressList: string[] = ['postAddress', 'postAddressDetail', 'postCode', 'postPerson', 'postPersonMobile']

// 修改部分
const verifyCompanyInfo = {
	PFI: [
		{ key: 'companyType', span: 12 },
		{ key: 'status', span: 12 },
	],
	FI: [
		{ key: 'companyType', span: 12 },
		{ key: 'parentCompanyUuid', span: 12 },
		{ key: 'status', span: 12 },
	],
	CAndS: [
		{ key: 'companyType', span: 12 },
		{ key: 'status', span: 12 },
	],
	B: [
		{ key: 'companyType', span: 12 },
		{ key: 'billStatus', span: 12 },
	],
	O: [
		{ key: 'companyType', span: 12 },
		{ key: 'orderStatus', span: 12 },
	],
}

// searchBar部分
const companyTypeList = [
	{ key: 'FI', name: '金融机构' },
	{ key: 'C', name: '核心企业' },
	{ key: 'S', name: '供应商' },
	{ key: 'PFI', name: '金融机构母公司' },
	{ key: 'B', name: '票据平台' },
	{ key: 'OCC', name: '订单企业' },
	{ key: 'OFI', name: '订单金融机构' },
]
const verifyTypeList = [
	{ key: 'CONFIRM', name: '通过' },
	{ key: 'REJECT', name: '不通过' },
]
const billBusinessStatusToSearch = [
	{ key: 'INIT', name: '未审核' },
	{ key: 'CONFIRM', name: '通过' },
	{ key: 'REJECT', name: '不通过' },
]
const businessStatusToSearch = [
	{ key: 'INIT', name: '未审核' },
	{ key: 'CONFIRM', name: '通过' },
	{ key: 'REJECT', name: '不通过' },
]
const projectCodeSearchList: any[] = [
	{ key: 'factor', name: '保理平台' },
	{ key: 'bill', name: '票据平台' },
	{ key: 'order_cc', name: '订单企业平台' },
	{ key: 'order_fi', name: '订单金融机构平台' },
]

export {
	previewBaseInfoType,
	previewUploadFiles,
	previewVerifyCompanyInfo,
	previewAdminInfo,
	previewAuthInfo,
	verifyCompanyInfo,
	companyTypeList,
	verifyTypeList,
	billBusinessStatusToSearch,
	businessStatusToSearch,
	projectCodeSearchList,
	postAddressList,
}
