import React from 'react'
import styled from 'styled-components'
import { Button, message, Tooltip, Modal } from 'antd'
import BaseTable from '@globalComponents/BaseTable'
// import SearchBar from '@spClientComponents/SearchBar'
import SearchBar from '@oo/components/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import { cfcaManageApi } from '@oo/api'
import { formatTableData } from '@utils/format'
// import { getColumnsByPageName } from '@spClientComponents/../config/TableColumnsConfig'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { history } from '@src/utils/router'
import CFCADataModal from '@oo/components/CFCADataModal'

function CFCAManage() {
	const [dataSource, setDataSource] = React.useState([])
	const [searchParams, setSearchParams] = React.useState({})
	const [pagination, setPagination] = React.useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [modalVisible, setModalVisible] = React.useState(false)
	const [tableLoading, setTableLoading] = React.useState(false)
	const [previewCompany, setPreviewCompany] = React.useState({})
	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}

	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}

	React.useEffect(() => {
		getInfoList()
	}, [pagination.current, pagination.pageSize, searchParams])

	React.useEffect(() => {
		if (history.location.state) {
			let params = history.location.state
			setModalVisible(params.modalVisible)
			setPreviewCompany(params.companyInfo)
		}
	}, [history.location.state])

	const getInfoList = async () => {
		setTableLoading(true)
		let params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...searchParams,
		}
		const CFCAInfoList = await cfcaManageApi.getSealList(params).catch(() => {
			setTableLoading(false)
		})
		setTableLoading(false)
		if (CFCAInfoList) {
			// 处理查询结果没有数据时 CFCAInfoList.list 值是null（不够优雅）
			setDataSource(formatTableData.addKey(CFCAInfoList.list ? CFCAInfoList.list : []))
			pagination.total = CFCAInfoList.total
			setPagination({ ...pagination })
		}
	}
	const addSeal = () => {
		setModalVisible(true)
	}
	const deleteSeal = value => {
		Modal.confirm({
			title: '提示',
			content: <div style={{ marginTop: '20px' }}>是否确认删除印章？</div>,
			closable: true,
			icon: () => null,
			onOk: () => {
				cfcaManageApi
					.deleteSeal({ companyUuid: value.companyUuid })
					.then(res => {
						message.success('删除成功')
						getInfoList()
					})
					.catch(err => message.error('删除失败'))
			},
		})
	}
	const getColumns = () => {
		const columnsDic = {
			companyName: { render: renderTooltip },
			socialCreditCode: { render: renderTooltip },
			sealImg: { render: renderSealImg },
			operation: {
				render: (_, record) => {
					return (
						<span className="link" onClick={() => deleteSeal(record)}>
							删除印章
						</span>
					)
				},
			},
		}
		return getColumnsByPageName('cfcaManageList', columnsDic)
	}
	//渲染企业名称
	const renderTooltip = (text: string, record: any) => (
		<Tooltip title={text} placement="topLeft">
			<span>{text}</span>
		</Tooltip>
	)
	const renderSealImg = (text: string, record: any) => {
		return <img src={record.sealImageUrl} style={{ width: '80px' }} />
	}
	//处理搜索
	const handleSearch = (values: any) => {
		//点击查询后，带入参数页码置为1, 记录当前查询参数
		pagination.current = 1
		setSearchParams(values)
		setPagination({ ...pagination })
	}
	const handleReset = () => {
		pagination.current = 1
		setSearchParams({})
		setPagination({ ...pagination })
	}
	const handleConfirm = () => {
		getInfoList()
	}
	const onCancelVisible = () => {
		setModalVisible(false)
		setPreviewCompany({})
	}
	return (
		<CFCAManageStyle className="card-wrapper">
			<OperatingArea>
				<div className="btn-area" style={{ marginBottom: 10 }}>
					<Button onClick={addSeal}>创建印章</Button>
				</div>
				<SearchBar pageName="cfcaDataList" onSubmit={handleSearch} onClear={handleReset} />
			</OperatingArea>
			<BaseTable
				className="card-table"
				loading={tableLoading}
				dataSource={dataSource}
				columns={getColumns()}
				total={pagination.total}
				current={pagination.current}
				onPageChange={handlePageChange}
				onSizeChange={handleSizeChange}
			/>
			<CFCADataModal type="commonCompany" visible={modalVisible} onCancel={onCancelVisible} onConfirm={handleConfirm} previewCompany={previewCompany} />
		</CFCAManageStyle>
	)
}

const CFCAManageStyle = styled.div`
	.ant-table-cell {
		display: table-cell;
		vertical-align: middle;
	}
`

export default CFCAManage
