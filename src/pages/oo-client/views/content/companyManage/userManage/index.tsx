import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import OperatingArea from '@src/globalComponents/OperatingArea'
import SearchBar from '@src/pages/oo-client/components/SearchBar'
import PreviewAffiliatedCompanies from './components/previewAffiliatedCompanies'
import styled from 'styled-components'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import { clearingApi, searchCompanyApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { formatTableData } from '@utils/format'
import { Modal } from 'antd'
import { hasAuth } from '@oo/biz/bizIndex'
import { message } from 'antd'

const UserManage = () => {
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [searchParams, setSearchParams] = useState({})
	const [dataSource, setDataSource] = useState<any[]>([])
	const [loading, setLoading] = useState(false)
	const [userItem, setUserItem] = useState<any>(null)
	const [enterpriseVisible, setEnterpriseVisible] = useState<any>(false)

	useEffect(() => {
		getOriginanlList()
	}, [pagination.current, pagination.pageSize, searchParams])

	const getOriginanlList = () => {
		setLoading(true)
		searchCompanyApi
			.getUerList({
				current: pagination.current,
				size: pagination.pageSize,
				...searchParams,
			})
			.then(result => {
				if (result) {
					pagination.total = result['total']
					if (result.list) {
						setDataSource(formatTableData.addKey(result['list']))
					}
				}
				setLoading(false)
			})
			.catch(() => {
				setLoading(false)
			})
	}

	//search 提交 事件
	const onSubmit = (params: { companyName?: string; email?: string }) => {
		pagination.current = 1
		setSearchParams({
			...params,
		})
	}
	//Search 重置
	const onClear = () => {
		pagination.current = 1
		setSearchParams({})
	}
	//render 用户角色解析，如果公司的type和用户的type对上才展示，以防sp改动公司类型
	const getColumns = () => {
		const columnsDic = {
			orgCount: {
				title: '关联企业',
				dataIndex: 'orgCount',
				render: (text: any, record: any) => {
					return text ? (
						<span
							className="link"
							onClick={() => {
								setUserItem(record)
								setEnterpriseVisible(true)
							}}
						>
							{text}
						</span>
					) : (
						'- -'
					)
				},
			},
			verified: {
				title: '实名认证',
				dataIndex: 'verified',
				render: (text: any, record: any) => {
					return record.verified && record.faceAuthResult == 1 ? '已认证' : '未认证'
				},
			},
			operate: {
				title: '操作',
				dataIndex: 'operate',
				render: (text: any, record: any) => {
					return (
						<span
							className="link"
							onClick={() => {
								handleResetAuth(record)
							}}
						>
							{record.faceAuthResult == 1 && hasAuth('oo_companyManage:userManage:resetAuth') ? '重置认证' : ''}
						</span>
					)
				},
			},
		}
		return evenlyTableColunms(getColumnsByPageName('userManagementList', columnsDic))
	}

	function handleChange(current: number) {
		pagination.current = current
		setPagination({ ...pagination })
	}
	function handleSizeChange(size: number) {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	const handleResetAuth = record => {
		let modalRef = Modal.confirm({
			icon: false,
			title: '确认重置',
			content: <p>重置后 ，认证状态将为未认证，用户需要重新进行实名认证。</p>,
			closable: false,
			onOk: async () => {
				const res = await searchCompanyApi.resetAuth({ userId: record.id }).catch(e => {
					console.log('e', e)
					message.error('重置失败')
				})
				console.log('%%%%', res)
				if (res) {
					message.success('重置成功')
				} else {
					message.error('重置失败')
				}

				modalRef.destroy()
				getOriginanlList()
			},
		})
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar showLabel={false} pageName={'userManage'} onClear={onClear} onSubmit={onSubmit} />
					</OperatingArea>
					<BaseTable
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
					/>
					<PreviewAffiliatedCompanies data={userItem} visible={enterpriseVisible} onCancel={() => setEnterpriseVisible(false)} />
				</div>
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div`
	.options_btn {
		span {
			display: inline-block;
			margin: 0 20px;
		}
	}
`

export default UserManage
