import React, { memo, useEffect, useState } from 'react'
import styled from 'styled-components'
import { Modal, Spin } from 'antd'
import BaseTable from '@globalComponents/BaseTable'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { searchCompanyApi } from '@src/pages/oo-client/api'
import { formatTableData } from '@utils/format'

interface PropsStruct {
	data: any
	visible: boolean
	onCancel: () => void
}

function previewAffiliatedCompanies(props: PropsStruct) {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [tableLoading, setTableLoading] = useState<boolean>(false)
	const { data, visible, onCancel } = props

	useEffect(() => {
		if (visible) {
			if (data?.id) {
				getEnterpriseUserList(data.id)
			}
		}
	}, [visible])

	useEffect(() => {
		if (visible) {
			if (data?.id) {
				getEnterpriseUserList(data.id)
			}
		}
	}, [pagination.current, pagination.pageSize])

	const getColumns = () => {
		return getColumnsByPageName('enterpriseInfor')
	}

	const getEnterpriseUserList = async (id: string) => {
		setTableLoading(true)
		const res = await searchCompanyApi
			.getAffiliatedEnterpriseList({
				userId: id,
				current: pagination.current,
				size: pagination.pageSize,
			})
			.catch(() => {
				setDataSource([])
				setTableLoading(false)
				pagination.total = 0
				setPagination({ ...pagination })
			})
		if (res) {
			if (res.list && res.list.length > 0) {
				setTableLoading(false)
				setDataSource(formatTableData.addKey(res.list))
				pagination.total = res.total
				setPagination({ ...pagination })
			} else {
				setDataSource([])
				setTableLoading(false)
				pagination.total = 0
				setPagination({ ...pagination })
			}
		}
	}

	const cancelModal = () => {
		onCancel()
	}

	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}

	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}

	return (
		<Modal title="关联企业" open={visible} onCancel={cancelModal} footer={null} width="960px">
			<div className="main-wrapper" style={{ padding: 0 }}>
				<PreviewWrapper className="card-wrapper">
					<div className="boxContentWrap">
						<BaseTable
							className="user-table"
							loading={tableLoading}
							dataSource={dataSource}
							columns={getColumns()}
							total={pagination.total}
							current={pagination.current}
							onPageChange={handlePageChange}
							onSizeChange={handleSizeChange}
						/>
					</div>
				</PreviewWrapper>
			</div>
		</Modal>
	)
}

const PreviewWrapper = styled.div`
	.boxContentWrap {
		margin-bottom: 10px;
	}
`

export default memo(previewAffiliatedCompanies)
