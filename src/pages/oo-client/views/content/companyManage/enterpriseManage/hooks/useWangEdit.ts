import { useState, useEffect } from 'react'
import { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'
import { commonApi } from '@oo/api'
import { getImageUrl } from '@oo/biz/bizIndex'

type InsertFnType = (url: string, poster: string) => void

export default function useWangEdit() {
	const [editor, setEditor] = useState<IDomEditor | null>(null)
	const [editorHtml, setEditorHtml] = useState(``)
	const [uploadFileKeyList, setUploadFileKeyList] = useState<any>([])
	const toolbarConfig: Partial<IToolbarConfig> = {
		toolbarKeys: [
			'headerSelect',
			'blockquote',
			'|',
			'bold',
			'underline',
			'italic',
			{
				key: 'group-more-style',
				title: '更多',
				iconSvg:
					'<svg viewBox="0 0 1024 1024"><path d="M204.8 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M505.6 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M806.4 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path></svg>',
				menuKeys: ['through', 'code', 'sup', 'sub', 'clearStyle'],
			},
			'color',
			'bgColor',
			'|',
			'fontSize',
			'fontFamily',
			'lineHeight',
			'|',
			'bulletedList',
			'numberedList',
			'todo',
			{
				key: 'group-justify',
				title: '对齐',
				iconSvg:
					'<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',
				menuKeys: ['justifyLeft', 'justifyRight', 'justifyCenter', 'justifyJustify'],
			},
			{
				key: 'group-indent',
				title: '缩进',
				iconSvg:
					'<svg viewBox="0 0 1024 1024"><path d="M0 64h1024v128H0z m384 192h640v128H384z m0 192h640v128H384z m0 192h640v128H384zM0 832h1024v128H0z m0-128V320l256 192z"></path></svg>',
				menuKeys: ['indent', 'delIndent'],
			},
			'|',
			'emotion',
			'insertLink',
			{
				key: 'group-image',
				title: '图片',
				iconSvg:
					'<svg viewBox="0 0 1024 1024"><path d="M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z"></path></svg>',
				menuKeys: ['insertImage', 'uploadImage'],
			},
			'insertTable',
			'codeBlock',
			'divider',
			'|',
			'undo',
			'redo',
			'|',
			'fullScreen',
		],
	}

	let fileUrlKey: string[] = []

	// 编辑器配置
	let editorConfig: Partial<IEditorConfig> = {
		placeholder: '请输入内容...',
		MENU_CONF: {
			uploadImage: {
				// form-data fieldName ，默认值 'wangeditor-uploaded-image'
				fieldName: 'file',
				// 单个文件的最大体积限制，默认为 2M
				maxFileSize: 10 * 1024 * 1024, // 1M
				// 最多可上传几个文件，默认为 100
				maxNumberOfFiles: 100,
				// 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
				allowedFileTypes: [],
				// 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。

				// 将 meta 拼接到 url 参数中，默认 false
				metaWithUrl: false,

				withCredentials: true,
				// 超时时间，默认为 10 秒
				timeout: 30 * 1000, // 5 秒,

				// 单个文件上传失败
				onFailed(file: File, res: any) {
					console.log(`${file.name} 上传失败`, res)
				},
				// 上传错误，或者触发 timeout 超时
				onError(file: File, err: any, res: any) {
					// JS 语法
					console.log(`${file.name} 上传出错`, err, res)
				},
				// 自定义上传
				async customUpload(file: File, insertFn: InsertFnType) {
					// const formData = new FormData()
					// formData.append('file', file)
					// // 使用fiannce 做图片目录
					// formData.append('type', 'finance')
					const param = { file: file, type: 'finance' }
					commonApi.uploadFile(param, true).then(async res => {
						let path = await getImageUrl(res)
						fileUrlKey.push(res)
						setUploadFileKeyList(fileUrlKey)
						insertFn(path, '这是一张上传图片')
					})
				},
			},
		},
	}

	useEffect(() => {
		return () => {
			if (editor == null) return
			editor.destroy()
			setEditor(null)
		}
	}, [editor])

	return {
		editor,
		setEditor,
		toolbarConfig,
		editorConfig,
		editorHtml,
		setEditorHtml,
		uploadFileKeyList,
	}
}
