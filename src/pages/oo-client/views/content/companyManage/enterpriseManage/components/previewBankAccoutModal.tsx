import React, { CSSProperties, memo, useEffect, useState } from 'react'
import { Descriptions, Modal, Spin } from 'antd'
import SmallTitle from '@globalComponents/SmallTitle'
import styled from 'styled-components'
import { searchCompanyApi } from '@oo/api'

interface PropsStruct {
	data: any
	visible: boolean
	onCancel: () => void
}

function PreviewBankAccoutModal(props: PropsStruct) {
	const [viewLoading, setViewLoading] = React.useState<boolean>(false)
	const [bankcardDetail, setBankcardDetail] = useState<any>({})
	const { data, visible, onCancel } = props

	useEffect(() => {
		if (visible) {
			if (data?.id) {
				getBankcardByCompanyId(data.id)
			}
		}
	}, [visible])

	const descriptionsLabelStyle: CSSProperties = {
		width: '84px',
		height: '30px',
		lineHeight: '30px',
		display: 'inline',
		textAlignLast: 'justify',
	}

	const descriptionsContentStyle: CSSProperties = {
		height: '30px',
		textAlign: 'left',
		alignItems: 'center',
	}

	const getBankcardByCompanyId = async (id: string) => {
		setViewLoading(true)
		const res = await searchCompanyApi.getBankcardByCompanyId({ orgId: id })
		if (res != null && res.length > 0) {
			setBankcardDetail(res[0])
		} else {
			setBankcardDetail({})
		}
		setViewLoading(false)
	}

	const cancelModal = () => {
		onCancel()
	}

	return (
		<Modal title="银行账户" open={visible} onCancel={cancelModal} footer={null}>
			<Spin tip="加载中..." spinning={viewLoading}>
				<div className="main-wrapper">
					<PreviewWrapper className="card-wrapper">
						<SmallTitle text="银行账户" />
						<div className="boxContentWrap">
							<div className="boxContent">
								<div className="bankInfo">
									<Descriptions column={1} colon={false} labelStyle={descriptionsLabelStyle} contentStyle={descriptionsContentStyle}>
										<Descriptions.Item label={'账号:'}>{bankcardDetail.accountNum || '--'}</Descriptions.Item>
										<Descriptions.Item label={'户名:'}>{bankcardDetail.accountName || '--'}</Descriptions.Item>
										<Descriptions.Item label={'开户行:'}>{bankcardDetail.accountBank || '--'}</Descriptions.Item>
										<Descriptions.Item label={'江阴开户:'}>{bankcardDetail.jrcbFlag ? '是' : bankcardDetail.jrcbFlag == false ? '否' : '--'}</Descriptions.Item>
									</Descriptions>
								</div>
							</div>
						</div>
					</PreviewWrapper>
				</div>
			</Spin>
		</Modal>
	)
}

const PreviewWrapper = styled.div`
	.bankInfo {
		background-color: #fafafa;
		padding: 10px 20px;
	}
	.boxContentWrap {
		margin-bottom: 10px;
		position: relative;
		.boxContent {
			border: 1px solid #e9e9e9;
			border-top: 0;
			padding: 20px 10px;
		}
	}
`

export default memo(PreviewBankAccoutModal)
