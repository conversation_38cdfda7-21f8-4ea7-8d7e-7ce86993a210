import React, { useState, useEffect } from 'react'
import { Modal, Form, message, Spin } from 'antd'
import { verifyInfo, baseInfo, fileInfo, adminInfo } from '../searchCompanyConfig'
import { searchCompanyApi } from '@oo/api'
import GetFormList from '@src/globalComponents/companyInfoForm/GetFormList'
import SmallTitle from '@src/globalComponents/SmallTitle'
import styled from 'styled-components'
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { Editor, Toolbar } from '@wangeditor/editor-for-react'
import useWangEdit from '../hooks/useWangEdit'
import xss from 'xss'
import { substringsBetween } from '@utils/util'
import { observer } from 'mobx-react-lite'
import fileUploading from '@src/globalStore/fileUploading'
import { validatePersonIdCode } from '@src/globalBiz/gBiz'

interface PropsStruct {
	data: any //用于企业展示的数据，录入企业不需要
	visible: boolean
	onOk: (dataInfo) => void
	onCancel: () => void
}

const layout = {
	labelCol: { span: 11 },
	wrapperCol: { span: 13 },
}

function EditCompInfoModal(props: PropsStruct) {
	const [form] = Form.useForm()
	const [viewLoading, setViewLoading] = React.useState<boolean>(false)
	const [confirmLoading, setConfirmLoading] = useState<boolean>(false)
	const [creator, setCreator] = useState()
	const [operatingOrganizationUuid, seToperatingOrganizationUuid] = useState<any>()
	const { data, visible, onOk, onCancel } = props

	//编辑器相关

	const { editor, setEditor, toolbarConfig, editorConfig, editorHtml, setEditorHtml } = useWangEdit()

	useEffect(() => {
		if (visible) {
			if (data.id) {
				queryCompanyData(data.id)
			}
			//初始化loading
			fileUploading.init()
		} else {
			form.resetFields()
		}
	}, [visible])

	const queryCompanyData = async (id: number) => {
		setViewLoading(true)
		const res = await searchCompanyApi.getCompanyDetailByUuid({ id })
		const userInfo = await searchCompanyApi.getUserInfo({ id: res?.managerId })
		setViewLoading(false)
		// 处理返回的数据，表单回填
		if (res !== undefined) {
			seToperatingOrganizationUuid(null)
			setEditorHtml(res.remark || '')
			// 处理回显数据
			handleDisplayData(res, userInfo)
		}
	}

	const handleDisplayData = (record: any, userInfo) => {
		seToperatingOrganizationUuid(record.operatingOrganizationUuid)
		//处理图像回显
		let getFileContent = JSON.parse(record.fileContent)
		let filterFiles = {}
		if (getFileContent) {
			getFileContent.forEach((item, index) => {
				if (item.fileUid === 'openingPermit') {
					item.fileUid = 'openingPermit' //转换
					filterFiles[item.fileUid] = filterFiles[item.fileUid] || []
					filterFiles[item.fileUid].push({
						name: item.fileName,
						response: { data: item.fileUrl },
						status: 'done',
						uid: item.fileUid + index,
					})
				} else {
					filterFiles[item.fileUid] = filterFiles[item.fileUid] || []
					filterFiles[item.fileUid].push({
						name: item.fileName,
						response: { data: item.fileUrl },
						status: 'done',
						uid: item.fileUid,
					})
				}
			})
			if (filterFiles['idNationalEmblem']) {
				filterFiles['idPortraitoad'].push(...filterFiles['idNationalEmblem'])
			}
		}
		//组装地址信息回显数据字段
		let getProvinceCity: any[] = []
		if (record.province && record.city) {
			const getProvinceCode = record.province.substring(1)
			const getCityCode = record.city.substring(1)
			//将省份信息拼接好回显展示
			getProvinceCity = [getProvinceCode, getCityCode]
		}

		if (record.status === 'INIT') {
			record.status = undefined
		}

		if (!record.fiCode) {
			record.fiCode = '--'
		}
		if (!record.paperSubmitMark) {
			record.paperSubmitMark = '0'
		} else {
			record.paperSubmitMark = String(record.paperSubmitMark)
		}

		form.setFieldsValue({ ...record, provinceCity: getProvinceCity, ...filterFiles, ...userInfo })
	}

	const onConfirm = async () => {
		form
			.validateFields()
			.then(info => {
				const certificateType = info['certificateType']
				const legalPersonIdCode = info['legalPersonIdCode']
				//校验证件类型，及证件号码，给提示
				const codeResult = validatePersonIdCode(certificateType, legalPersonIdCode, message)
				if (codeResult) {
					doCheckCompanyIsExist(info)
				}
			})
			.catch(error => {
				console.log('error', error)
			})
	}

	// 检查公司是否存在
	const doCheckCompanyIsExist = async values => {
		setConfirmLoading(true)
		values['id'] = data.id
		values['fileContent'] = formatFileContent(values)
		if (editorHtml.length > 5000) {
			setConfirmLoading(false)
			// 不提交
			return
		}

		//截取图片key替换图片url（后端生成的图片url是临时url，会过期，所以不把临时url进行存储）
		let auditCompanyRemark = xss(editorHtml)
		let imgSrcList = substringsBetween(auditCompanyRemark, 'src="', '" alt')
		let urlKeyList = substringsBetween(auditCompanyRemark, 'key=', '&')
		if (Array.isArray(imgSrcList) && Array.isArray(urlKeyList)) {
			imgSrcList.forEach(itemUrl => {
				urlKeyList.forEach(urlKey => {
					if (itemUrl.includes(urlKey)) {
						auditCompanyRemark = auditCompanyRemark.replace(itemUrl, urlKey)
					}
				})
			})
		}
		const submitParams = {
			mobile: values['mobile'],
			// remark 从状态里取
			remark: auditCompanyRemark,
			approveStatus: values['approveStatus'],
			address: values['address'],
			city: 'P' + values['provinceCity'][1],
			corporationScale: values['corporationScale'],
			industry: values['industry'],
			nationality: 'CHN',
			province: 'P' + values['provinceCity'][0],
			orgName: values['orgName'].replace(/\s*/g, ''),
			fiCode: values['fiCode'],
			fileContent: values['fileContent'],
			socialCreditCode: values['socialCreditCode'],
			id: values['id'],
			legalPerson: values['legalPerson'],
			certificateType: values['certificateType'],
			legalPersonIdCode: values['legalPersonIdCode'],
			supportOrgType: 'C,S',
			managerNo: values['managerNo'],
			paperSubmitMark: values['paperSubmitMark'],
		}
		submitData(values, submitParams)
	}

	const submitData = (values, submitParams) => {
		searchCompanyApi
			.modifyCompany(submitParams)
			.then(() => {
				message.success('修改成功')
				onCancel()
				onOk(values)
				setConfirmLoading(false)
			})
			.catch(() => {
				setConfirmLoading(false)
			})
	}

	// 格式化需要上传的文件列表
	const formatFileContent = info => {
		if (info['idPortraitoad'].length > 1) {
			let pre = info['idPortraitoad'][0]
			let next = info['idPortraitoad'][1]
			info['idPortraitoad'] = []
			info['idPortraitoad'].push(pre)
			info['idNationalEmblem'] = []
			info['idNationalEmblem'].push(next)
		}
		info['idPortraitoad'][0]['type'] = 'idPortraitoad'
		if (!!info['idNationalEmblem']) {
			info['idNationalEmblem'][0]['type'] = 'idNationalEmblem'
		}
		info['businessLicense'][0]['type'] = 'businessLicense'
		info['CFCACertificateApplicationForm'][0]['type'] = 'CFCACertificateApplicationForm'

		info['idCardOfHandler'][0]['type'] = 'idCardOfHandler' //转换
		if (info['CFCACertificateOfAuthorization'] && info['CFCACertificateOfAuthorization'].length > 0) {
			info['CFCACertificateOfAuthorization'][0]['type'] = 'CFCACertificateOfAuthorization' // 这是企业授权书的字段
		}
		if (info['openingPermit']?.length > 0) {
			info['openingPermit'][0]['type'] = 'openingPermit'
			;(info['openingPermit'] || []).forEach(item => {
				item.type = 'openingPermit'
			})
		}

		/*
		转换
		企业证件==>其他附件
		info['enterpriseCertificate'][0]['type'] = 'openingPermit'
		*/

		let arr: any = [
			...info['idPortraitoad'],
			...(info['idNationalEmblem'] || []),
			...info['businessLicense'],
			...(info['openingPermit'] || []),
			...info['CFCACertificateApplicationForm'],
			...(info['CFCACertificateOfAuthorization'] || []),
			...info['idCardOfHandler'],
		]
		let newArr = []
		arr.forEach((file, index) => {
			if (file?.response?.data) {
				newArr.push({ fileName: file.name, fileUrl: file.response.data, fileUid: file.type })
			}
		})
		return JSON.stringify(newArr)
	}

	return (
		<Modal
			forceRender
			width="860px"
			okText={'确定'}
			maskClosable={false}
			title={'修改企业信息'}
			open={visible}
			onOk={onConfirm}
			onCancel={() => {
				onCancel()
			}}
			confirmLoading={confirmLoading || fileUploading.uploadingNum > 0}
		>
			<Spin tip="加载中..." spinning={viewLoading}>
				<EditWrapper>
					<Form {...layout} form={form} name="add_form" validateTrigger="onChange" scrollToFirstError={true}>
						<div className="boxContentWrap">
							<SmallTitle text="基本信息" />
							<div className="boxContent">
								<GetFormList configArr={baseInfo}></GetFormList>
								<GetFormList form={form} configArr={fileInfo}></GetFormList>
							</div>
						</div>
						<div className="boxContentWrap">
							<SmallTitle text="管理员（认证申请人）" />
							<div className="boxContent">
								<GetFormList configArr={adminInfo}></GetFormList>
							</div>
						</div>
						<div className="boxContentWrap">
							<SmallTitle text="企业认证" />
							<div className="boxContent">
								<GetFormList configArr={verifyInfo}></GetFormList>
								<div className="editor-item">
									<span className="title">认证意见</span>
									<div className="editor-form-item">
										<Form.Item wrapperCol={{ span: 24 }}>
											<div className="editor-container">
												<Toolbar editor={editor} defaultConfig={toolbarConfig} mode="default" className="tool-bar" />
												<Editor
													defaultConfig={editorConfig}
													value={editorHtml}
													onCreated={setEditor}
													onChange={onChangeEditor => setEditorHtml(onChangeEditor.getHtml())}
													mode="default"
													className="editor"
												/>
												<div className="editor-tip">{editorHtml.length}/5000</div>
											</div>
											{editorHtml.length > 5000 && <p style={{ color: 'red' }}>最大可允许提交字符5000,当前字符{editorHtml.length},请注意控制字符长度</p>}
										</Form.Item>
									</div>
								</div>
							</div>
						</div>
					</Form>
				</EditWrapper>
			</Spin>
		</Modal>
	)
}
const EditWrapper = styled.div`
	.ant-form {
		padding: 20px;
	}
	.boxContentWrap {
		margin-bottom: 10px;
		position: relative;
		.boxContent {
			border: 1px solid #e9e9e9;
			border-top: 0;
			padding: 20px 10px;
		}
	}
	.editor-item {
		display: flex;
		margin-top: 15px;
		.title {
			width: 23%;
			text-align: right;
			::after {
				content: ':';
				position: relative;
				top: -0.5px;
				margin: 0 8px 0 2px;
			}
		}

		.editor-form-item {
			width: 76.9%;
		}
	}
	.editor-container {
		border: 1px solid #ccc;
		z-index: 100;
		.tool-bar {
			border-bottom: 1px solid #ccc;
			.w-e-bar-item {
				button {
					text-align: center;
					padding-right: 30px;
				}
			}
		}
		.editor {
			height: 500px;
			overflow-y: hidden;
		}
		.editor-tip {
			text-align: right;
			margin-top: 20px;
		}
	}
`
export default observer(EditCompInfoModal)
