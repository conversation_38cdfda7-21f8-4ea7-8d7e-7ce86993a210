import OperatingArea from '@globalComponents/OperatingArea'

import {
	companyTypeList,
	industrialVerificationList,
	operatingList,
	projectCodeSearchList,
	verifyTypeListToSearch,
} from '@ooContent/companyManage/enterpriseManage/searchCompanyConfig'
import { history } from '@src/utils/router'
import { CompanyNameStatusList, CompanyNameStatusMap } from '@globalConfig/codeMaps'
import BaseTable from '@globalComponents/BaseTable'
import PreviewCompInfoModal from '@ooContent/companyManage/enterpriseManage/components/previewCompInfoModal'
import EditCompInfoModal from '@ooContent/companyManage/enterpriseManage/components/editCompInfoModal'
import PreviewBankAccoutModal from '@ooContent/companyManage/enterpriseManage/components/previewBankAccoutModal'
import PreviewEnterpriseUserModal from '@ooContent/companyManage/enterpriseManage/components/previewEnterpriseUserModal'
import PDFViewer from '@globalComponents/PDFViewer'
import LayoutSlot from '@globalComponents/LayoutSlot'
import React, { useEffect, useState } from 'react'
import { searchCompanyApi } from '@src/pages/oo-client/api'
import { formatTableData } from '@utils/format'
import moment from 'moment'
import styled from 'styled-components'
import { Button, message, Tag, Tooltip } from 'antd'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons'
import SearchBar from '@oo/components/SearchBar'
import { stampToTime } from '@utils/timeFilter'
import { downloadFileByFileFlow } from '@utils/exportBlob'

function EnterpriseManage() {
	//table相关
	const [searchParams, setSearchParams] = useState({})
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [tableLoading, setTableLoading] = useState<boolean>(false)
	const [dataSource, setDataSource] = useState<any[]>([])
	const [dialogData, setDialogData] = useState<any>(null)
	//预览弹框相关
	const [previewDialogVisible, setPreviewDialogVisible] = useState<boolean>(false)
	//pdf预览弹框控制
	const [pdfVisible, setPdfVisible] = useState<boolean>(false)
	const [pdfUrl, setPdfUrl] = useState<any>(null)
	const [pdfTitle, setPdfTitle] = useState<any>(null)
	const [editCompanyVisible, setEditCompanyVisible] = useState<boolean>(false)
	const [bankModalVisible, setBankModalVisible] = useState<boolean>(false)
	const [enterpriseModalVisible, setEnterpriseModalVisible] = useState<boolean>(false)
	const [exportLoading, setExportLoading] = useState(false)

	useEffect(() => {
		getCompanyInfoList()
	}, [pagination.current, pagination.pageSize, searchParams])

	//获取企业信息列表
	const getCompanyInfoList = async (values?: any) => {
		let params = {
			current: pagination.current,
			size: pagination.pageSize,
			...searchParams,
			desc: 'create_time',
		}
		if (values) {
			params = Object.assign(params, values)
		}
		setTableLoading(true)
		const res = await searchCompanyApi.getCompanyInfoList(params).catch(() => {
			setDataSource([])
			setTableLoading(false)
			pagination.total = 0
			setPagination({ ...pagination })
		})
		if (res) {
			if (res.list && res.list.length > 0) {
				setTableLoading(false)
				setDataSource(formatTableData.addKey(res.list))
				pagination.total = res.total
				setPagination({ ...pagination })
			} else {
				setDataSource([])
				setTableLoading(false)
				pagination.total = 0

				setPagination({ ...pagination })
			}
		}
	}

	//开启预览弹框
	const openPreviewDialog = (record: any) => {
		console.log(record)
		setDialogData(record)
		setPreviewDialogVisible(true)
	}

	//渲染企业名称
	const renderCompanyName = (text: string, record: any) => (
		<Tooltip title={text} placement="topLeft">
			<span className="link" onClick={() => openPreviewDialog(record)}>
				{record?.companyNameStatus === CompanyNameStatusMap.normal ? (
					<CheckCircleOutlined style={{ color: '#FFF', background: '#52C41A', borderRadius: '50%', marginRight: '5px' }} />
				) : (
					''
				)}
				{record?.companyNameStatus === CompanyNameStatusMap.anomaly ? (
					<ExclamationCircleOutlined style={{ color: '#FFF', background: '#FAAD14', borderRadius: '50%', marginRight: '5px' }} />
				) : (
					''
				)}
				{text}
			</span>
		</Tooltip>
	)

	//渲染操作列
	const renderOperation = (text: string, record: any) => {
		return (
			<div>
				<a className="link left-btn" onClick={() => openEditDialog(record)}>
					修改
				</a>
				<a className="link left-btn" onClick={() => openBankModal(record)}>
					银行账户
				</a>
				<a className="link left-btn" onClick={() => openEnterpriseModal(record)}>
					企业用户
				</a>
			</div>
		)
	}

	const getColumns = () => {
		const columnsDic = {
			orgName: { render: renderCompanyName },
			status: { width: 100 },
			commerceStatus: {
				render: text => {
					const currentStatusObj = industrialVerificationList.find(item => item['key'] === text)
					return !!currentStatusObj ? <Tag color={currentStatusObj.style.color}>{currentStatusObj.name}</Tag> : '- -'
				},
			},
			operation: { render: renderOperation, width: 200 },
		}
		return getColumnsByPageName('companyManagerList', columnsDic)
	}

	//开启录入、编辑弹框
	const openEditDialog = (record: any) => {
		setDialogData(record)
		setEditCompanyVisible(true)
	}

	const openBankModal = (record: any) => {
		setDialogData(record)
		setBankModalVisible(true)
	}

	const openEnterpriseModal = (record: any) => {
		setDialogData(record)
		setEnterpriseModalVisible(true)
	}

	//处理搜索
	const handleSearch = (values: any) => {
		if (values && values.createDate && values.createDate.length === 2) {
			const [startDate, endDate] = values?.createDate
			values.endDate = isNaN(endDate) ? '' : moment(endDate).format('YYYY-MM-DD')
			values.startDate = isNaN(startDate) ? '' : moment(startDate).format('YYYY-MM-DD')
			delete values.createDate
		}

		//点击查询后，带入参数页码置为1, 记录当前查询参数
		pagination.current = 1
		setSearchParams(values)
		setPagination({ ...pagination })
	}

	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}

	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}

	const handleReset = () => {
		pagination.current = 1
		setSearchParams({})
		setPagination({ ...pagination })
	}

	const handleEditCompany = dataInfo => {
		getCompanyInfoList()
		setEditCompanyVisible(false)
		toCFCAManagePage(dataInfo)
	}

	//判断是否跳转到CFCA管理
	const toCFCAManagePage = dataInfo => {
		if (dataInfo['approveStatus'] === 'CONFIRM' && !dialogData.hasSeal) {
			history.push('/content/companyManage/CFCAManager', {
				modalVisible: true,
				companyInfo: { id: dataInfo.id, orgName: dataInfo.orgName },
			})
		}
	}

	//导出公司列表信息
	const exportCouCsv = async () => {
		setExportLoading(true)
		const fileName = `Download_${stampToTime(new Date().getTime(), 9)}.xlsx`
		let type = 'application/vnd.ms-excel'
		// let type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
		searchCompanyApi
			.exportOoCompanyInfoList({
				...searchParams,
				pageNum: 1,
				pageSize: 2147483646,
				// status: 'CONFIRM',
				// operatingOrganizationUuid: uuid,
			})
			.then((response: any) => {
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}

	return (
		<LayoutSlot>
			<SearchCompanyStyle className="card-wrapper">
				<OperatingArea>
					<SearchBar
						pageName="companyList"
						optionData={{
							orgType: companyTypeList,
							status: verifyTypeListToSearch,
							projectCode: projectCodeSearchList,
							orgNameStatus: CompanyNameStatusList,
							// 工商校验
							industrialVerificationList: industrialVerificationList,
							operatingList: operatingList,
						}}
						onSubmit={handleSearch}
						onClear={handleReset}
						needFold={true}
					/>
					<Button style={{ marginLeft: '5px' }} type="primary" onClick={exportCouCsv} loading={exportLoading}>
						导出
					</Button>
				</OperatingArea>
				<BaseTable
					className="card-table"
					loading={tableLoading}
					dataSource={dataSource}
					columns={getColumns()}
					total={pagination.total}
					current={pagination.current}
					onPageChange={handlePageChange}
					onSizeChange={handleSizeChange}
				/>
			</SearchCompanyStyle>
			<PreviewCompInfoModal data={dialogData} visible={previewDialogVisible} onCancel={() => setPreviewDialogVisible(false)} />
			<EditCompInfoModal data={dialogData} visible={editCompanyVisible} onCancel={() => setEditCompanyVisible(false)} onOk={handleEditCompany} />
			<PreviewBankAccoutModal data={dialogData} visible={bankModalVisible} onCancel={() => setBankModalVisible(false)} />
			<PreviewEnterpriseUserModal data={dialogData} visible={enterpriseModalVisible} onCancel={() => setEnterpriseModalVisible(false)} />
			<PDFViewer title={pdfTitle} pdfUrl={pdfUrl} visible={pdfVisible} onCancel={() => setPdfVisible(false)} />
		</LayoutSlot>
	)
}

const SearchCompanyStyle = styled.div`
	.left-btn {
		margin-right: 5px;
	}
`

export default EnterpriseManage
