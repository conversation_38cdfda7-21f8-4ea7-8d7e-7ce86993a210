// 预览
const previewBaseInfoType: string[] = [
	'orgName',
	'socialCreditCode',
	'industry',
	'corporationScale',
	'provinceCity',
	'address',
	'managerNo',
	'_blank',
	'legalPerson',
	'certificateType',
	'legalPersonIdCode',
	'fiCode',
	// 'keyUpgrade',
]
const previewUploadFiles: string[] = [
	'idPortraitoad',
	// 'idNationalEmblem',
	'_blank',
	'businessLicense',
	'openingPermit',
	'CFCACertificateApplicationForm',
	'CFCACertificateOfAuthorization',
	'idCardOfHandler',
]
const previewVerifyInfo = {
	PFI: ['type', 'businessStatus', 'billBusinessStatus', 'orderBusinessStatus'],
	FI: ['type', 'parentCompanyName', 'businessStatus', 'billBusinessStatus', 'orderBusinessStatus'],
	// CAndS: ['type', 'businessStatus', 'billBusinessStatus', 'orderBusinessStatus'],
	CAndS: ['type'], //江阴银行注释掉保理，票据，订单
}
const previewAdminInfo: string[] = ['mobile', 'realName', 'idCard', 'profilePicture']
// const previewAuthInfo: string[] = ['approveStatus', 'operatingOrganizationName', 'projectCode', 'authRemark']
const previewAuthInfo: string[] = ['approveStatus', 'registerSystemCode', 'paperSubmitMark', '_blank', 'authRemark'] //江阴银行注释关联运营机构

// 修改
const baseInfo: any[] = [
	{ key: 'orgName', span: 12 },
	{ key: 'SocialCreditCode', span: 12 },
	{ key: 'Industry', span: 12 },
	{ key: 'CorporationScale', span: 12 },
	{ key: 'ProvinceCity', span: 12 },
	{ key: 'Address', span: 12 },
	{ key: 'LegalPerson', span: 12 },
	{ key: 'CertificateType', span: 12 },
	{ key: 'LegalPersonIdCode', span: 12 },
	{ key: 'ManagerNo', span: 12, isLook: true },
	// { key: 'FiCode', span: 12 },
]
const fileInfo: any[] = [
	{ key: 'IDPortrait', span: 12, hiddenExtra: true }, //人像
	// { key: 'IDNationalEmblem', span: 12, hiddenExtra: true }, //国徽
	{ key: '_blank', span: 12, label: '-' },
	{ key: 'BusinessLicense', span: 12, hiddenExtra: true }, //营业执照
	// 2、企业证件可支持上传多个文件,最多5个,非必填  1.8.6 迭代需求
	// { key: 'EnterpriseCertificate', span: 12, hiddenExtra: true }, // 企业证件
	{ key: 'OpeningPermit', span: 12, hiddenExtra: true, label: '其他证件' }, // 企业证件
	{ key: 'CFCACertificateApplicationForm', span: 12, hiddenExtra: true }, // CFCA证书申请表
	{ key: 'CFCACertificateOfAuthorization', span: 12, hiddenExtra: true }, // CFCA证书授权书
	{ key: 'IDCardOfHandler', span: 12, hiddenExtra: true }, // 经办人身份证复印件
]
const adminInfo: any[] = [
	{ key: 'Mobile', span: 12 },
	{ key: 'RealName', span: 12 },
	{ key: 'IdCard', span: 12 },
	{ key: 'ProfilePicture', span: 12 },
]
const verifyInfo: any[] = [
	{ key: 'approveStatus', span: 12, label: '认证状态' },
	{ key: 'registerSystemCode', span: 12, label: '注册来源' },
	{ key: 'paperSubmitMark', span: 12, label: '纸质材料' },
	// { key: 'operatingOrganizationName', span: 12 },
]
// const verifyInfo: any[] = [
// 	{ key: 'status', span: 12, label: '认证状态' },
// 	{ key: 'projectCode', span: 12, label: '注册来源1' },
// 	// { key: 'operatingOrganizationName', span: 12 },
// ]

// searchBar
const companyTypeList: any[] = [
	// { key: 'FI', name: '金融机构' },
	{ key: 'C', name: '核心企业' },
	{ key: 'S', name: '一般企业' },
	// { key: 'PFI', name: '金融机构母公司' },
	// { key: 'OCC', name: '订单企业' },
	// { key: 'OFI', name: '订单金融机构' },
]
const verifyTypeList: any[] = [
	{ key: 'CONFIRM', name: '通过' },
	{ key: 'REJECT', name: '不通过' },
]
const verifyTypeListToSearch: any[] = [
	{ key: 'INIT', name: '未审核' },
	{ key: 'CONFIRM', name: '通过' },
	{ key: 'REJECT', name: '不通过' },
]

const industrialVerificationList: any[] = [
	{ key: 'CONFIRM', name: '通过', style: { color: 'rgb(135, 208, 104)' } },
	{ key: 'REJECT', name: '不通过', style: { color: 'rgb(245, 154, 35)' } },
	{ key: 'ERROR', name: '校验异常', style: { color: 'rgb(255, 85, 0)' } },
]
const operatingList: any[] = [
	{ key: 'wx', name: '万向区块链' },
	{ key: 'test1', name: 'test1' },
	{ key: 'test2', name: 'test2' },
]
const projectCodeSearchList: any[] = [
	{ key: 'factor', name: '企业端' },
	{ key: 'bill', name: '票据平台' },
	{ key: 'order_cc', name: '订单企业平台' },
	{ key: 'order_fi', name: '订单金融机构平台' },
]

export {
	previewBaseInfoType,
	previewUploadFiles,
	previewVerifyInfo,
	previewAdminInfo,
	previewAuthInfo,
	baseInfo,
	fileInfo,
	adminInfo,
	verifyInfo,
	companyTypeList,
	verifyTypeList,
	verifyTypeListToSearch,
	industrialVerificationList,
	operatingList,
	projectCodeSearchList,
}
