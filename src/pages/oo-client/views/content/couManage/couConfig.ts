import { renderPayStatus } from '@src/pages/oo-client/config/TableColumnsRender'
import { numberToThousands, stampToTime } from '@src/utils/timeFilter'
const couMeaning = '融信'
const couStatusList = [
	{
		key: 'NO_CASH',
		name: '未兑付',
	},
	{
		key: 'WAIT_CASH',
		name: '待兑付',
	},
	{
		key: 'CASH_OK',
		name: '已兑付',
	},
]

const TransferStatus = [
	{ code: 'all', name: '全部' },
	{ code: 'WAITING', name: '审核中' },
	{ code: 'CONFIRMED', name: '支付成功' },
	{ code: 'REJECTED', name: '已拒绝' },
	{ code: 'WATING_SUBMIT', name: '系统提交文件失败' },
]

const cashStatus: any = {
	//兑付状态 couCashStatus
	NO_CASH: '未兑付',
	WAIT_CASH: '待兑付',
	CASH_OK: '已兑付',
	CASH_FAIL: '兑付失败',
	WAIT_PAY: '待支付',
	PAYING: '支付待确认',
	PAY_OK: '已支付',
	TO_GENERATED: '生成中',
	AVAILABLE: '可流转',
	SPLIT: '已拆分',
	TRANSFERRING: '流转交易中',
}
const cashCouStatusList = [
	{
		key: 'NO_CASH', // 兑付到期日 前7天  都是 待兑付
		name: '未兑付',
	},
	{
		key: 'WAIT_CASH', // 兑付到期日 前7天  都是 待兑付
		name: '待兑付',
	},
	{
		key: 'CASH_OK',
		name: '已兑付',
	},
]
const pledgeStatusList = [
	{
		key: '0',
		name: '未质押',
	},
	{
		key: '1',
		name: '质押中',
	},
]

const RefoundStatus = [
	{ code: 'WAITING_AUDIT', name: '审核中' },
	{ code: 'CONFIRMED', name: '退款成功' },
	{ code: 'REJECTED', name: '已拒绝' },
]

const searchCouPageList = [
	{ key: 'CONFIRMED', name: '支付成功' },
	{ key: 'WAITING', name: '审核中' },
	{ key: 'REJECTED', name: '已拒绝' },
]

const destroyStatus = [
	{
		key: 'WAITING_AUDIT',
		name: '审核中',
	},
	{
		key: 'REJECTED',
		name: '已拒绝',
	},
	{
		key: 'CONFIRMED',
		name: '核销成功',
	},
]

const payStatus = {
	CONFIRMED: '支付成功',
	WAITING: '审核中',
	REJECTED: '已拒绝',
}
const clearingType = {
	0: '持有清分',
	1: '质押清分',
}
const clearingFilePushType = {
	0: '未推送',
	1: '已推送',
	2: '推送失败',
}
const clearingFileResType = {
	0: '初始',
	1: '失败',
	3: '超时',
	9: '成功',
}
const clearingSuccessFlagType = {
	0: '初始',
	1: '失败',
	3: '超时',
	9: '成功',
}

const clearingKkFlagType = {
	0: '初始',
	1: '失败',
	3: '超时',
	4: '处理中',
	9: '成功',
}
const clearingBatchStatusType = {
	'00': '初始',
	'22': '专户到内部户转账失败',
	'23': '专户到内部户转账超时',
	'24': '专户到内部户转账成功',
	'43': '行内代发处理完成',
	'51': '跨行记账处理完成',
}
//导出CSV文件的信息配置
const exportCSVCfg = [
	{
		code: 'couNo',
		name: `${couMeaning}编号`,
		format: function (record) {
			return record.couNo
		},
	},
	{
		code: 'hoderDetialcouNo',
		name: `${couMeaning}编号`,
		format: function (record) {
			return record.couNo
		},
	},
	{
		code: 'originalCouNo',
		name: `原始${couMeaning}编号`,
		format: function (record) {
			return record.originalCouNo
		},
	},
	{
		code: 'originalCouAmountInCent',
		name: `原始${couMeaning}金额（￥）`,
		format: function (record) {
			return numberToThousands((parseFloat(record.originalCouAmountInCent) / 100).toFixed(2))
		},
	},
	{
		code: 'supplierCouNo',
		name: `${couMeaning}编号`,
		format: function (record) {
			return record.couNo
		},
	},
	{
		code: 'cashStatus',
		name: '兑付状态',
		format: function (record) {
			return cashStatus[record.cashStatus]
		},
	},
	{
		code: 'couAmountInCent',
		name: `金额（￥）`,
		format: function (record) {
			return numberToThousands((parseFloat(record.couAmountInCent) / 100).toFixed(2))
		},
	},
	{ code: 'toCompanyName', name: '收款方', format: record => record['toCompanyName'] },
	{ code: 'publishName', name: '开立方', format: record => record['publishName'] },
	{ code: 'creditName', name: '授信方', format: record => record['creditName'] },
	{ code: 'holderName', name: '持有方', format: record => record['holderName'] },
	{
		code: 'dueDate',
		name: `兑付到期日`,
		format: function (record) {
			return stampToTime(record.dueDate, 5)
		},
	},
	{
		code: 'createTime',
		name: '创建日期',
		format: function (record) {
			return stampToTime(record.createTime, 5)
		},
	},
	{
		code: 'transferNo',
		name: `${couMeaning}支付编号`,
		format: function (record) {
			return record.transferNo
		},
	},
	{
		code: 'couCreateTime',
		name: '接收日期',
		format: function (record) {
			return stampToTime(record.couCreateTime, 5)
		},
	},
	//流转管理相关的导出字段 start
	{
		code: 'transferNumber',
		name: `支付编号`,
		format: function (record) {
			return record.transferNo
		},
	},
	{
		code: 'peeOrPay',
		name: `支付类型`,
		format: function (record) {
			return record.peeOrPay
		},
	},
	{
		code: 'transferStatus',
		name: `状态`,
		format: function (record) {
			return renderPayStatus(record.status)
		},
	},
	{
		code: 'transferAmount',
		name: `支付金额`,
		format: function (record) {
			return numberToThousands((parseFloat(record.sumTransferAmountInCent) / 100).toFixed(2))
		},
	},
	{
		code: 'fromName',
		name: `付款方`,
		format: function (record) {
			return record.fromName
		},
	},
	{
		code: 'toName',
		name: `收款方`,
		format: function (record) {
			return record.toName
		},
	},
	{
		code: 'contractCode',
		name: `关联合同编号`,
		format: function (record) {
			let contractCode = ''
			if (record.contracts && record.contracts[0] && record.contracts[0].contractCode) {
				contractCode = record.contracts[0].contractCode
			}
			return contractCode
		},
	},
	{
		code: 'contractName',
		name: `合同名称`,
		format: function (record) {
			let name = ''
			if (record.contracts && record.contracts[0] && record.contracts[0].name) {
				name = record.contracts[0].name
			}
			return name
		},
	},
	{
		code: 'contractAmount',
		name: `合同金额（￥）`,
		format: function (record) {
			let amount: any = ''
			if (record.contracts && record.contracts[0] && record.contracts[0].amount) {
				amount = numberToThousands(parseFloat(record.contracts[0].amount).toFixed(2))
			}
			return amount
		},
	},
	{
		code: 'contractSignDate',
		name: `签订日期`,
		format: function (record) {
			let signDate: any = ''
			if (record.contracts && record.contracts[0] && record.contracts[0].signDate) {
				signDate = stampToTime(record.contracts[0].signDate, 5)
			}
			return signDate
		},
	},
]
const companyTypeExportCfg = companyType => {
	let companyTypeCfg = {
		cCompany: ['couNo', 'cashStatus', 'couAmountInCent', 'toCompanyName', 'creditName', 'dueDate', 'createTime', 'transferNo'],
		supplier: ['supplierCouNo', 'couAmountInCent', 'creditName', 'publishName', 'dueDate', 'couCreateTime'],
		oo_originCou: ['couNo', 'cashStatus', 'couAmountInCent', 'holderName', 'creditName', 'publishName', 'dueDate', 'createTime'],
		holderDetail: [
			'hoderDetialcouNo',
			'holderName',
			'publishName',
			'creditName',
			'couAmountInCent',
			'dueDate',
			'cashStatus',
			'originalCouNo',
			'originalCouAmountInCent',
			'createTime',
		],
		transferList: [
			'transferNumber',
			'transferStatus',
			'transferAmount',
			'fromName',
			'toName',
			'createTime',
			'contractCode',
			'contractName',
			'contractAmount',
			'contractSignDate',
		],
	}
	let cfgInfoList: Array<any> = []
	companyTypeCfg[companyType].forEach(key => {
		exportCSVCfg.forEach(cfgData => {
			if (key === cfgData.code) cfgInfoList.push(cfgData)
		})
	})
	return cfgInfoList
}

const searchTransferTypeList = [
	{ key: 'PAY', name: '流转' },
	{ key: 'CREATE', name: '开立' },
]

export {
	RefoundStatus,
	cashStatus,
	TransferStatus,
	couStatusList,
	destroyStatus,
	cashCouStatusList,
	searchCouPageList,
	payStatus,
	pledgeStatusList,
	clearingType,
	clearingFilePushType,
	clearingFileResType,
	clearingSuccessFlagType,
	clearingKkFlagType,
	clearingBatchStatusType,
	companyTypeExportCfg,
	searchTransferTypeList,
}
