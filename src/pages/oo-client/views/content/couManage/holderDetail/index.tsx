import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import styled from 'styled-components'
import SearchBar from '@src/pages/oo-client/components/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import { Button, message, Tooltip } from 'antd'
import { couManageApi, commonApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { formatTableData } from '@utils/format'
import { cashCouStatusList, pledgeStatusList } from '@src/pages/oo-client/views/content/couManage/couConfig'
import { stampToTime, timeToStamp } from '@utils/timeFilter'
import CouDeatail from './detail/CouDetailModal'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import ChainInfoModal from '@src/globalComponents/chainInfoComponents/ChainInfoModal'

const options = {
	cashStatusList: cashCouStatusList,
	pledgeStatusList: pledgeStatusList,
}

const Cash = () => {
	const urlParams = new URLSearchParams(window.location.search || window.location.href.split('?')[1] || '')
	const holderPubKey = urlParams.get('holderPubKey')
	const orgName = urlParams.get('orgName')
	const cashStatus = urlParams.get('cashStatus')
	const cashStatusList = cashStatus && cashStatus !== '1' ? (cashStatus === 'availableCouAmountInYuan' ? ['NO_CASH'] : [cashStatus]) : null
	const pledgeStatus = cashStatus == 'availableCouAmountInYuan' ? '0' : cashStatus == '1' ? '1' : null

	const startDate = urlParams.get('startDate') ? Number(urlParams.get('startDate')) : null
	const endDate = urlParams.get('endDate') ? Number(urlParams.get('endDate')) : null

	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [searchParams, setSearchParams] = useState<any>({
		startDate,
		endDate,
		holderPubKey,
		cashStatusList,
		pledgeStatus,
		statusList: cashStatus && cashStatus == 'availableCouAmountInYuan' ? ['AVAILABLE'] : null,
	})
	const [dataSource, setDataSource] = useState<any[]>([])
	const [loading, setLoading] = useState(false)
	const [exportLoading, setExportLoading] = React.useState(false)
	const [CouDeatailVisible, setCouDeatailVisible] = React.useState<boolean>(false)
	const [detailData, setDetilaData] = React.useState<any>(null)
	const [chainInfoModalVisible, setChainInfoModalVisible] = useState<boolean>(false)
	const [onChainData, setOnChainData] = useState({})

	const initValues = { holderPubKey: { id: holderPubKey, name: orgName }, catchDate: [startDate, endDate], cashStatusList, pledgeStatus, needClear: true }
	useEffect(() => {
		getOriginanlList()
	}, [pagination.current, pagination.pageSize, searchParams])
	const getOriginanlList = () => {
		setLoading(true)
		couManageApi
			.getHolderDetailList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...searchParams,
			})
			.then(result => {
				if (result) {
					pagination.total = result['total']
					if (result.list) {
						setDataSource(formatTableData.addKey(result['list']))
					}
				}
				setLoading(false)
			})
			.catch(() => {
				setLoading(false)
			})
	}

	//search 提交 事件
	const onHolderSubmit = (params: { catchDate: any; cashStatusList: any; couNo: [string] }) => {
		pagination.current = 1
		if (params['cashStatusList']) {
			params['cashStatusList'] =
				Object.prototype.toString.call(params['cashStatusList']) === '[object Array]'
					? params['cashStatusList']
					: params['cashStatusList'].includes(',')
					? params['cashStatusList'].split(',')
					: [params['cashStatusList']]
		}
		let endDate, startDate
		if (params && params.catchDate && params.catchDate.length === 2) {
			endDate = initValues?.catchDate[1] === params.catchDate[1] ? params.catchDate[1] : timeToStamp(params.catchDate[1], 'end')
			startDate = params.catchDate[0]
			delete params.catchDate
		}
		setSearchParams({
			...params,
			startDate,
			endDate,
		})
	}
	//Search 重置
	const onHolderClear = () => {
		pagination.current = 1
		setSearchParams({})
	}
	const onOperCallback = value => {
		setDetilaData(value)
		setCouDeatailVisible(true)
	}
	const getColumns = () => {
		const columnsDic = {
			couNo: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text} className="link">
							<span
								onClick={() => {
									onOperCallback(record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			originalCouNo: {
				dataIndex: 'originalCouNo',
				title: '原始融信编号',
			},

			couAmountInCent: {
				title: '金额(￥)',
			},
			onChainCertificate: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => showCertificate(record)}>
							查看证书
						</span>
					)
				},
			},
		}
		return getColumnsByPageName('holderDetail', columnsDic)
	}

	function handleHolderChange(current: number) {
		pagination.current = current
		setPagination({ ...pagination })
	}
	function handleHolderSizeChange(size: number) {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	const showCertificate = record => {
		commonApi
			.queryCouProof({ couNo: record.couNo })
			.then(data => {
				if (data && data.status === 'DONE') {
					setOnChainData(data)
					setChainInfoModalVisible(true)
				} else {
					message.warning('证书未生成，完成审批且上链后生成证书')
				}
			})
			.catch(err => {
				console.log(err)
			})
	}
	//导出融信信息
	const exportHolderCouCsv = async () => {
		setExportLoading(true)
		let serverTime = await commonApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`
		couManageApi
			.exportHolderDetailList({
				pageNum: 1,
				pageSize: 2147483646,
				...searchParams,
			})
			.then((response: any) => {
				let type = 'application/vnd.ms-excel'
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar
							showLabel={false}
							pageName={'holderDetailSearch'}
							optionData={options}
							onClear={onHolderClear}
							onSubmit={onHolderSubmit}
							initValues={initValues}
						/>
						<Button style={{ marginLeft: '5px' }} type="primary" onClick={exportHolderCouCsv} loading={exportLoading}>
							导出
						</Button>
					</OperatingArea>
					<BaseTable
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						onPageChange={handleHolderChange}
						onSizeChange={handleHolderSizeChange}
					/>
					<CouDeatail
						data={detailData}
						visible={CouDeatailVisible}
						onOk={() => {
							return
						}}
						onCancel={() => {
							setCouDeatailVisible(false)
							setDetilaData(null)
						}}
					/>
				</div>
			</LayoutSlot>
			<ChainInfoModal visible={chainInfoModalVisible} onCancel={() => setChainInfoModalVisible(false)} chainData={onChainData} />
		</Box>
	)
}

const Box = styled.div`
	.options_btn {
		span {
			display: inline-block;
			margin: 0 20px;
		}
	}
`

export default Cash
