import * as React from 'react'
import styled from 'styled-components'
import { message, Button } from 'antd'
import { numberToThousands } from '@src/utils/timeFilter'
let G6 = require('@antv/g6')
interface P {
	data: {
		nodes: Array<object>
		edges: Array<object>
	}
}
export default class TransferPath extends React.Component<P, {}> {
	private isNeedFitView = false
	private hasException: any = false
	private box: any = { offsetWidth: 0, offsetHeight: 0 }
	private graph: any = null

	componentDidMount() {
		setTimeout(() => {
			let main: any = document.getElementById('box')
			let offsetWidth = main?.offsetWidth
			let offsetHeight = main?.offsetHeight
			this.box = { offsetWidth, offsetHeight }

			if (this.props.data.nodes && this.props.data.nodes.length) {
				this.init(this.props.data)
				this.bindEvents()
			}
		}, 1000)
	}
	componentDidUpdate() {
		let main: any = document.getElementsByTagName('canvas')[0]
		let offsetWidth = main?.style?.width
		offsetWidth = Number(offsetWidth.slice(0, offsetWidth.indexOf('px')))
		let offsetHeight = main.style.height
		offsetHeight = Number(offsetHeight.slice(0, offsetHeight.indexOf('px')))
		this.box = { offsetWidth, offsetHeight }
		if (this.props.data.nodes && this.props.data.nodes.length) {
			this.init(this.props.data)
		}
	}
	componentWillUnmount() {
		if (this.graph) {
			this.graph.destroy()
		}
	}

	componentWillReceiveProps(nextProps: Readonly<P>, nextContext: any): void {
		this.hasException = this.catchException(nextProps)
		this.isNeedFitView = this.getIsNeedFitView(nextProps)
	}

	//流转路径小于等于 4 行的时候使用 fitView
	getIsNeedFitView = props => {
		const { edges } = props.data
		let sourceArray: any = []
		edges.forEach((item: any) => {
			sourceArray.push(item.source)
		})
		//去重
		sourceArray = Array.from(new Set(sourceArray))
		return sourceArray.length + 1 <= 4
	}
	//捕获后端返回数据为空值时,导致 g6 报的错
	catchException = props => {
		const { data } = props
		if (Array.isArray(data.nodes) && Array.isArray(data.edges)) {
			if (data.nodes.length > 0) {
				if (data.nodes.includes(null) || data.nodes.includes(undefined)) {
					message.error('返回数据nodes有误')
					return true
				}
			}
			if (data.edges.length > 0) {
				if (data.edges.includes(null) || data.edges.includes(undefined)) {
					message.error('返回数据edges有误')
					return true
				}
			}
		}
	}

	moveCenter = event => {
		let that = this
		const item = event.item
		// 聚焦当前点击的节点（把节点放到视口中心）

		const matrix = item.get('group').getMatrix()
		const point = {
			x: matrix[6],
			y: matrix[7],
		}
		const width = that.graph.get('width')
		const height = that.graph.get('height')
		// 找到视口中心
		const viewCenter = {
			x: width / 2,
			y: height / 2,
		}
		const modelCenter = that.graph.getPointByCanvas(viewCenter.x, viewCenter.y)
		const viewportMatrix = that.graph.get('group').getMatrix()
		// 画布平移的目标位置，最终目标是graph.translate(dx, dy);
		const dx = (modelCenter.x - point.x) * viewportMatrix[0]
		const dy = (modelCenter.y - point.y) * viewportMatrix[4]
		let lastX = 0
		let lastY = 0
		let newX: any = void 0
		let newY: any = void 0
		// 动画每次平移一点，直到目标位置
		that.graph.get('canvas').animate(
			{
				onFrame: ratio => {
					newX = dx * ratio
					newY = dy * ratio
					that.graph.translate(newX - lastX, newY - lastY)
					lastX = newX
					lastY = newY
				},
			},
			300,
			'easeCubic'
		)
	}

	bindEvents = () => {
		this.graph.on('node:click', event => {
			this.moveCenter(event)
		})
	}

	registerShape = () => {
		/**
		 * 计算字符串的长度
		 * @param {string} str 指定的字符串
		 */
		let calcStrLen = str => {
			var len = 0
			for (var i = 0; i < str.length; i++) {
				if (str.charCodeAt(i) > 0 && str.charCodeAt(i) < 128) {
					len++
				} else {
					len += 2
				}
			}
			return len
		}
		let foldString = (str, maxWidth, fontSize) => {
			var fontWidth = fontSize * 1.5 //字号+边距
			var newMaxWidth = maxWidth * 2 // 需要根据自己项目调整
			var width = calcStrLen(str) * fontWidth
			//这里是超出后面显示的省略号
			var ellipsis = '\n'
			var total = ''
			if (width > newMaxWidth) {
				var actualLen = Math.floor((newMaxWidth - 10) / fontWidth)
				var result = str.substring(0, actualLen) + ellipsis
				var restStr = str.substring(actualLen, str.length)
				total = result + foldString(restStr, maxWidth, fontSize)
				return total
			}
			return str
		}
		G6.registerNode(
			'sql',
			{
				drawShape(cfg, group) {
					const width = cfg.style.width
					const height = cfg.style.height
					const stroke = cfg.style.stroke
					const startPoint = {
						x: -width / 2,
						y: -height / 2,
					}
					const fontSize = 14
					const fontWeight = 300
					const labelLeftOffset = 90
					const labelTopOffset = 30
					//文本上下边距
					const textTopOffset = fontSize / 3
					const rect = group.addShape('rect', {
						attrs: {
							/**
							 * 起始节点坐标若设为(0,0),节点中心则为(width/2,height/2),计算偏移不方便,为了使节点中心落在(0,0)起始坐标应该为(-width/2,-height/2)
							 */
							x: -width / 2,
							y: -height / 2,
							width,
							height,
							radius: 3,
							stroke,
							lineWidth: 0.6,
							fillOpacity: 1,
							fill: '#fff',
						},
						// name: 'rect-shape',
					})
					if (cfg) {
						const couNoText = group.addShape('text', {
							attrs: {
								text: `融信编号:`,
								x: startPoint.x + labelLeftOffset,
								y: -height / 2 + labelTopOffset,
								fontSize,
								fontWeight,
								textAlign: 'right',
								textBaseline: 'top',
								fill: '#000000D9',
							},
						})
						// couAmount
						const couAmountText = group.addShape('text', {
							attrs: {
								text: `融信金额:`,
								x: startPoint.x + labelLeftOffset,
								y: -height / 2 + (labelTopOffset + couNoText.getBBox().height + textTopOffset),
								fontSize,
								fontWeight,
								textAlign: 'right',
								textBaseline: 'top',
								fill: '#000000D9',
							},
						})
						group.addShape('text', {
							attrs: {
								text: '持有方:',
								x: startPoint.x + labelLeftOffset,
								y: -height / 2 + (labelTopOffset + couNoText.getBBox().height + couAmountText.getBBox().height + 2 * textTopOffset),
								fontSize,
								fontWeight,
								textAlign: 'right',
								textBaseline: 'top',
								fill: '#000000D9',
							},
						})
						// couNo
						const couNo = group.addShape('text', {
							attrs: {
								text: cfg.data.couNo,
								x: startPoint.x + labelLeftOffset + 5,
								y: -height / 2 + labelTopOffset,
								fontSize,
								fontWeight,
								textAlign: 'left',
								textBaseline: 'top',
								fill: '#000000D9',
							},
						})
						// couAmount
						const couAmount = group.addShape('text', {
							attrs: {
								text: '￥' + numberToThousands(cfg.data.couAmountInCent / 100),
								x: startPoint.x + labelLeftOffset + 5,
								y: -height / 2 + (labelTopOffset + couNo.getBBox().height + textTopOffset),
								fontSize,
								fontWeight,
								textAlign: 'left',
								textBaseline: 'top',
								fill: '#000000D9',
							},
						})
						group.addShape('text', {
							attrs: {
								text: foldString(cfg.data.holderCompanyName, width / 2 - 5, 14),
								width,
								x: startPoint.x + labelLeftOffset + 5,
								y: -height / 2 + (labelTopOffset + couNo.getBBox().height + couAmount.getBBox().height + 2 * textTopOffset),
								fontSize,
								fontWeight,
								textAlign: 'left',
								textBaseline: 'top',
								fill: '#000000D9',
							},
						})
					}
					return rect
				},
			},
			'single-node'
		)
	}

	init(data) {
		if (this.hasException) {
			return
		}
		if (!this.graph) {
			this.registerShape()
			this.graph = new G6.Graph({
				container: 'box',
				width: this.box.offsetWidth,
				height: this.box.offsetHeight,
				pixelRatio: 1,
				modes: {
					default: ['drag-canvas', 'zoom-canvas'],
				},
				fitView: this.isNeedFitView,
				fitViewPadding: this.isNeedFitView ? 100 : 0,
				minZoom: 0.5,
				maxZoom: 1.1,
				layout: {
					type: 'dagre',
					nodesep: 150,
					ranksep: 10,
				},
				defaultNode: {
					type: 'sql',
					size: 150,
					labelCfg: {
						style: {
							fill: '#000',
							fontSize: 10,
						},
					},
					style: {
						stroke: '#9EAFF5',
						fill: '#fff',
						width: 290,
						height: 120,
					},
				},
				defaultEdge: {
					// type: 'polyline',
					style: {
						lineWidth: 2,
						endArrow: true,
						stroke: '#ccc',
						offset: 11,
					},
				},
			})
		}
		this.customFitView()
		this.graph.data({ ...data })
		this.graph.render()
		this.goToZoom()
	}

	//将 graph 缩小到指定大小
	goToZoom() {
		//计算节点高度
		//设上下间隙为高度的 2 分之一
		//4 个 h,5*0.5 h
		//一个屏幕显示 4 行的节点高度
		const toHeight = this.box.offsetHeight / (4 + 5 * 0.2)
		//原始节点高度
		const nodeHeight = this.graph.get('defaultNode').style.height
		//缩放比例
		const zoom = toHeight / nodeHeight
		this.graph.zoom(zoom, { x: this.box.offsetWidth / 2, y: 0 })
	}
	//将 graph第一个节点为中心居中
	customFitView() {
		const nodes = this.graph.getNodes()
		const firstNode = nodes[0]
		if (firstNode) {
			//获取第一个根节点坐标
			const { x, y } = firstNode._cfg.model
			//计算根节点需要移动的到中间的偏移量
			const startPoint = {
				x,
				y,
			}
			const endPoint = {
				x: this.box.offsetWidth / 2,
				y,
			}
			this.graph.translate(endPoint.x - startPoint.x, endPoint.y - startPoint.y)
		}
	}

	download2() {
		if (this.props.data.nodes.length > 150) {
			message.warning('图片过大，暂不支持导出')
		} else {
			this.graph.downloadFullImage('融信流转路径图', 'image/png', { backgroundColor: '#dfe2f1' })
		}
	}

	render() {
		return (
			<Style>
				<Button type="primary" className="exportButton" onClick={this.download2.bind(this)}>
					导出
				</Button>
				{/* <h3 className="small-title">流转路径</h3> */}
				<div id="box"></div>
			</Style>
		)
	}
}

const Style = styled.div`
	position: absolute;
	background-color: #fff;
	z-index: 999;
	height: 94%;
	width: 100%;
	h1 {
		line-height: 64px;
		font-weight: 700;
		font-size: 16px;
	}
	#box {
		height: calc(100% - 64px);
		/* box-shadow: 0px 2px 4px 0px rgba(40, 120, 255, 0.08), 0px 0px 4px 1px rgba(0, 0, 0, 0.12); */
		border-radius: 4px;
	}
	.exportButton {
		float: right;
		margin: 10px 15px -42px 0;
	}
`
