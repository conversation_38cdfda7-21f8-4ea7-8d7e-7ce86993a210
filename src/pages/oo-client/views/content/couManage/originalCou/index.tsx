import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import styled from 'styled-components'
import SearchBar from '@ooClientComponents/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import { Button, Modal, message } from 'antd'
import { couManageApi, transferManageApi, commonApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { formatTableData } from '@utils/format'
import TransferPath from './components/transferTree'
import { cashCouStatusList, pledgeStatusList } from '@src/pages/oo-client/views/content/couManage/couConfig'
import { stampToTime, timeToStamp } from '@utils/timeFilter'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import ChainInfoModal from '@src/globalComponents/chainInfoComponents/ChainInfoModal'

const options = {
	cashStatusList: cashCouStatusList,
	pledgeStatusList: pledgeStatusList,
}

const Cash = () => {
	const urlParams = new URLSearchParams(window.location.search || window.location.href.split('?')[1] || '')
	const publishPubKey = urlParams.get('publishPubKey')
	const orgName = urlParams.get('orgName')
	const startDate = urlParams.get('startDate') ? Number(urlParams.get('startDate')) : null
	const endDate = urlParams.get('endDate') ? Number(urlParams.get('endDate')) : null
	const cashStatusList = urlParams.get('cashStatusList')
		? urlParams.get('cashStatusList') === 'totalCouAmountInYuan'
			? ['NO_CASH', 'WAIT_CASH', 'CASH_OK']
			: [urlParams.get('cashStatusList')]
		: null

	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [searchParams, setSearchParams] = useState<any>({ startDate, endDate, publishPubKey, cashStatusList })
	const [dataSource, setDataSource] = useState<any[]>([])
	const [loading, setLoading] = useState(false)
	const [transferTreeVisible, setTransferTreeVisible] = useState(false)
	const [pathData, setPathData] = useState<any>({})
	const [exportLoading, setExportLoading] = React.useState(false)
	const [chainInfoModalVisible, setChainInfoModalVisible] = useState<boolean>(false)
	const [onChainData, setOnChainData] = useState({})

	const initValues = { publishPubKey: { id: publishPubKey, name: orgName }, createdTime: [startDate, endDate], cashStatusList }
	useEffect(() => {
		getOriginanlList()
	}, [pagination.current, pagination.pageSize, searchParams])
	const getOriginanlList = () => {
		setLoading(true)
		couManageApi
			.getOriginalList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				cashStatusList: ['NO_CASH', 'WAIT_CASH', 'CASH_OK'],
				couStatusList: ['AVAILABLE', 'SPLIT', 'TRANSFERRING'],
				...searchParams,
			})
			.then(result => {
				if (result) {
					pagination.total = result['total']
					if (result.list) {
						setDataSource(formatTableData.addKey(result['list']))
					} else {
						setDataSource([])
					}
				}
				setLoading(false)
			})
			.catch(() => {
				setLoading(false)
			})
	}

	//search 提交 事件
	const onSubmit = (params: { createdTime: any; cashStatusList: any; couNo: [string] }) => {
		let endDate, startDate
		if (params && params.createdTime && params.createdTime.length === 2) {
			endDate = params?.createdTime[1] === initValues.createdTime[1] ? params.createdTime[1] : timeToStamp(params.createdTime[1], 'end')
			startDate = params.createdTime[0]
			delete params.createdTime
		}
		if (params['cashStatusList']) {
			params['cashStatusList'] =
				Object.prototype.toString.call(params['cashStatusList']) === '[object Array]'
					? params['cashStatusList']
					: params['cashStatusList'].includes(',')
					? params['cashStatusList'].split(',')
					: [params['cashStatusList']]
		}
		pagination.current = 1
		setSearchParams({
			...params,
			startDate,
			endDate,
		})
	}
	//Search 重置
	const onClear = () => {
		pagination.current = 1
		setSearchParams({})
	}

	const showTransferTree = record => {
		transferManageApi.getTransferPath({ couUuidList: [record.uuid] }).then(
			result => {
				let data = {
					nodes: result[0].nodes,
					edges: result[0].edges,
				}
				setPathData(data)
				setTransferTreeVisible(true)
			},
			err => {
				console.log('err', err)
			}
		)
	}
	const getColumns = () => {
		const columnsDic = {
			couAmountInCent: {
				title: '金额(￥)',
			},
			operation: {
				title: '查看',
				render: (text, record) => {
					return (
						<React.Fragment>
							<span
								className="link"
								style={{ marginRight: '20px' }}
								onClick={() => {
									showTransferTree(record)
								}}
							>
								流转路径
							</span>
						</React.Fragment>
					)
				},
			},
			onChainCertificate: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => showCertificate(record)}>
							查看证书
						</span>
					)
				},
			},
		}
		return getColumnsByPageName('opCouOriginalList', columnsDic)
	}

	const showCertificate = record => {
		commonApi
			.queryCouProof({ couNo: record.couNo })
			.then(data => {
				if (data && data.status === 'DONE') {
					setOnChainData(data)
					setChainInfoModalVisible(true)
				} else {
					message.warning('证书未生成，完成审批且上链后生成证书')
				}
			})
			.catch(err => {
				console.log(err)
			})
	}

	function handleChange(current: number) {
		pagination.current = current
		setPagination({ ...pagination })
	}
	function handleSizeChange(size: number) {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	//导出融信信息
	const exportCouCsv = async () => {
		setExportLoading(true)
		let serverTime = await commonApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`
		couManageApi
			.OriginalListExport({
				cashStatusList: ['NO_CASH', 'WAIT_CASH', 'CASH_OK'],
				couStatusList: ['AVAILABLE', 'SPLIT', 'TRANSFERRING'],
				...searchParams,
				pageNum: 1,
				pageSize: 2147483646,
			})
			.then((response: any) => {
				let type = 'application/vnd.ms-excel'
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar showLabel={false} pageName={'opCouOriginalList'} optionData={options} onClear={onClear} onSubmit={onSubmit} initValues={initValues} />
						<Button style={{ marginLeft: '5px' }} type="primary" onClick={exportCouCsv} loading={exportLoading}>
							导出
						</Button>
					</OperatingArea>
					<BaseTable
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
					/>
				</div>
			</LayoutSlot>
			<Modal
				title="查看流转路径"
				open={transferTreeVisible}
				onCancel={() => {
					setTransferTreeVisible(false)
				}}
				footer={null}
				bodyStyle={{ height: '70vh', overflow: 'scroll' }}
				width="900px"
			>
				<div className="chart">
					<TransferPath data={pathData} />
				</div>
			</Modal>
			<ChainInfoModal visible={chainInfoModalVisible} onCancel={() => setChainInfoModalVisible(false)} chainData={onChainData} />
		</Box>
	)
}

const Box = styled.div`
	.options_btn {
		span {
			display: inline-block;
			margin: 0 20px;
		}
	}
`

export default Cash
