/*
 * @Date: 2022-09-13 13:59:40
 * @LastEditors: yanh<PERSON><PERSON> <EMAIL>
 * @FilePath: \frontend-web\src\pages\oo-client\views\content\cashManage\abnormalCash\index.tsx
 * @Description: 异常兑付列表
 */

import React, { useState, useEffect } from 'react'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { cashManageApi } from '@src/pages/oo-client/api'
import { formatTableData } from '@utils/format'
import { history } from '@src/utils/router'
import { stampToTime, timeToStamp } from '@src/utils/timeFilter'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import { Alert, Button, message, Modal, Tag } from 'antd'
import commonModuleApi from '@src/pages/oo-client/api/common'
import BaseTable from '@globalComponents/BaseTable'
import LayoutSlot from '@globalComponents/LayoutSlot'
import SearchBar from '@ooClientComponents/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import styled from 'styled-components'
import { getUserData } from '@src/utils/util'
import { renderOriginalCouStatus } from '@src/pages/oo-client/config/TableColumnsRender'
export default () => {
	const { uuid: operatingOrganizationUuid } = getUserData().companyInfo
	//table相关
	const [searchParams, setSearchParams] = useState({})
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [dataSource, setDataSource] = useState<any[]>([])
	const [tableLoading, setTableLoading] = useState<boolean>(false)
	const [exportLoading, setExportLoading] = useState<boolean>(false)
	const [refreshLoading, setRefreshLoading] = useState<boolean>(false)
	useEffect(() => {
		getAbnormalCashList()
	}, [pagination.current, pagination.pageSize, searchParams])

	const getColumns = () => {
		const columnsDic = {
			originalCouStatus: {
				dataIndex: 'couCashStatus',
				render: (text, record) => {
					if (record.couCashStatus === 'WAIT_CASH') {
						return <Tag color="#F59A23">{renderOriginalCouStatus(record.couCashStatus)}</Tag>
					} else if (record.couCashStatus === 'CASH_OK') {
						return <Tag color="#87d068">{renderOriginalCouStatus(record.couCashStatus)}</Tag>
					}
					return '- -'
				},
			},
			operation: { render: renderOperation, width: 150 },
		}
		return getColumnsByPageName('abnormalCash', columnsDic)
	}

	//获取企业信息列表
	const getAbnormalCashList = async (values?: any) => {
		let params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...searchParams,
		}
		if (values) {
			params = Object.assign(params, values)
		}
		setTableLoading(true)
		const res = await cashManageApi.getAbnormalCashList(params).catch(() => {
			pagination.total = 0
			setDataSource([])
			setTableLoading(false)
			setPagination({ ...pagination })
		})
		if (res) {
			if (res.list && res.list.length > 0) {
				pagination.total = res.total
				setDataSource(formatTableData.addKey(res.list))
				setPagination({ ...pagination })
				setTableLoading(false)
			} else {
				setDataSource([])
				pagination.total = 0
				setTableLoading(false)
				setPagination({ ...pagination })
			}
		}
	}

	//处理搜索
	const handleSearch = (values: any) => {
		if (values && values.dueDate && values.dueDate.length === 2) {
			values.dueEndDate = timeToStamp(values.dueDate[1], 'end')
			values.dueBeginDate = values.dueDate[0]
			delete values.dueDate
		}
		//点击查询后，带入参数页码置为1, 记录当前查询参数
		pagination.current = 1
		setSearchParams(values)
		setPagination({ ...pagination })
	}
	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}

	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}

	const handleReset = () => {
		pagination.current = 1
		setSearchParams({})
		setPagination({ ...pagination })
	}

	const handleDelete = uuid => {
		console.log(uuid)
		Modal.confirm({
			// icon: <InfoCircleFilled style={{ color: '#1890ff' }} />,
			title: '确认删除这条记录?',
			content: <p></p>,
			closable: false,
			onOk: async () => {
				cashManageApi
					.getAbnormalCashDelete({
						uuid,
					})
					.then(res => {
						message.success('删除成功')
						getAbnormalCashList()
					})
					.catch(e => {
						console.log('e', e)
					})
			},
		})
	}
	//渲染操作列
	const renderOperation = (text: string, record: any) => {
		return (
			<div>
				<span className="link" onClick={() => history.push('/content/cashManage/abnormalCashDetails', { couInfo: record })}>
					查看兑付信息
				</span>
				{record.couCashStatus === 'CASH_OK' && (
					<span className="link" style={{ color: 'red', marginLeft: '10px' }} onClick={() => handleDelete(record.uuid)}>
						删除
					</span>
				)}
			</div>
		)
	}

	//导出融信信息
	const exportCouCsv = async () => {
		setExportLoading(true)
		let serverTime = await commonModuleApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`
		let type = 'application/vnd.ms-excel'
		cashManageApi
			.exportAbnormalCashList({
				...searchParams,
				operatingOrganizationUuid,
				pageNum: 1,
				pageSize: 2147483646,
			})
			.then((response: any) => {
				console.log(response)
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}

	const refreshResult = async () => {
		setRefreshLoading(true)
		await cashManageApi.refreshAbnormalCashResult().catch(() => setRefreshLoading(false))
		message.success('执行成功')
		setRefreshLoading(false)
	}
	return (
		<LayoutSlot>
			<SearchCompanyStyle className="card-wrapper">
				<Alert
					style={{ marginBottom: '10px' }}
					message="关于异常兑付列表的说明"
					description={
						<ol style={{ listStyleType: 'decimal' }}>
							<li>本页面展示过了清分日期，但未清分成功的融信</li>
							<li>运营人员根据线下还款的实际情况，在本页面对相关异常兑付融信，进行手动清分等操作</li>
						</ol>
					}
					type="info"
					showIcon
				/>
				<OperatingArea>
					<SearchBar pageName="abnormalCash" onSubmit={handleSearch} onClear={handleReset} />
				</OperatingArea>
				<Button.Group>
					{/* <Button style={{ marginLeft: '20px', width: '80px' }} type="primary" onClick={exportCouCsv} loading={exportLoading}>
						导出
					</Button> */}
					<Button style={{ marginLeft: '20px', background: '#F59A23', borderColor: '#F59A23' }} type="primary" onClick={refreshResult} loading={refreshLoading}>
						批量获取清分结果
					</Button>
				</Button.Group>
				<BaseTable
					className="card-table"
					loading={tableLoading}
					dataSource={dataSource}
					columns={getColumns()}
					total={pagination.total}
					current={pagination.current}
					onPageChange={handlePageChange}
					onSizeChange={handleSizeChange}
				/>
			</SearchCompanyStyle>
		</LayoutSlot>
	)
}
const SearchCompanyStyle = styled.div``
