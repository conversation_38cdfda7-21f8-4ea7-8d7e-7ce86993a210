/*
 * @Date: 2022-09-14 11:08:58
 * @LastEditors: yanh<PERSON><PERSON> <EMAIL>
 * @FilePath: \frontend-web\src\pages\oo-client\views\content\cashManage\abnormalCashDetails\index.tsx
 * @Description: In User Settings Edit
 */
import React, { useState, useEffect } from 'react'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { cashManageApi } from '@src/pages/oo-client/api'
import { formatTableData } from '@utils/format'
import { history } from '@src/utils/router'
import { Descriptions, Modal } from 'antd'
import BaseTable from '@globalComponents/BaseTable'
import LayoutSlot from '@globalComponents/LayoutSlot'
import styled from 'styled-components'
import { renderAmount, renderDate_ } from '@src/pages/oo-client/config/TableColumnsRender'
import SmallTitle from '@src/globalComponents/SmallTitle'
const DescriptionsItem = Descriptions.Item
export default () => {
	const { couInfo } = history.location.state || { couInfo: {} }
	const [record, setRecord] = useState<any>({})
	const [tableLoading, setTableLoading] = useState<boolean>(false)
	const [dataSource, setDataSource] = useState<any[]>([])
	const [modalVisible, setModalVisible] = useState<boolean>(false)
	//table相关
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	useEffect(() => {
		getAbnormalCashList()
	}, [pagination.current, pagination.pageSize])

	const getColumns = () => {
		const columnsDic = {
			operation: { title: '清分结果', render: renderOperation, width: 120 },
		}
		return getColumnsByPageName('abnormalCashDetails', columnsDic)
	}

	//获取企业信息列表
	const getAbnormalCashList = async (values?: any) => {
		let params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			couUuid: couInfo?.couUuid,
		}
		if (values) {
			params = Object.assign(params, values)
		}
		setTableLoading(true)
		const res = await cashManageApi.getAbnormalCashListByCouUuid(params).catch(() => {
			pagination.total = 0
			setTableLoading(false)
			setDataSource([])
			setPagination({ ...pagination })
		})
		if (res) {
			if (res.list && res.list.length > 0) {
				pagination.total = res.total
				setDataSource(formatTableData.addKey(res.list))
				setPagination({ ...pagination })
				setTableLoading(false)
			} else {
				setTableLoading(false)
				setDataSource([])
				pagination.total = 0
				setPagination({ ...pagination })
			}
		}
	}

	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}

	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}

	//渲染操作列
	const renderOperation = (text: string, dataRecord: any) => {
		return (
			<div>
				<span
					className="link left-btn"
					onClick={() => {
						setRecord(dataRecord)
						setModalVisible(true)
					}}
				>
					查看清分结果
				</span>
			</div>
		)
	}

	return (
		<LayoutSlot>
			<Styled>
				<div className="card-wrapper">
					<SmallTitle text="融信信息" />
					<Descriptions column={2} labelStyle={{ width: '100px' }} contentStyle={{ width: '30%' }} bordered>
						<DescriptionsItem label="融信编号">{couInfo?.couNo}</DescriptionsItem>
						<DescriptionsItem label="金额（￥）">{renderAmount(couInfo?.couAmount)}</DescriptionsItem>
						<DescriptionsItem label="持有方">{couInfo?.couHolderName}</DescriptionsItem>
						<DescriptionsItem label="开立方">{couInfo?.couPublishName}</DescriptionsItem>
						<DescriptionsItem label="兑付到期日">{renderDate_(couInfo?.dueDate)}</DescriptionsItem>
						<DescriptionsItem label="兑付状态">{'已兑付'}</DescriptionsItem>
					</Descriptions>
				</div>
				<div className="card-wrapper">
					<SmallTitle>
						<div style={{ display: 'inline-block' }}>清分记录</div>
					</SmallTitle>
					<BaseTable
						className="card-table"
						dataSource={dataSource}
						loading={tableLoading}
						current={pagination.current}
						total={pagination.total}
						columns={getColumns()}
						onSizeChange={handleSizeChange}
						onPageChange={handlePageChange}
					/>
				</div>
				<Modal
					title="查看清分结果"
					bodyStyle={{ padding: '20px', minHeight: '180px' }}
					onCancel={() => setModalVisible(false)}
					open={modalVisible}
					footer={null}
				>
					<p>{record?.returnMsg}</p>
				</Modal>
			</Styled>
		</LayoutSlot>
	)
}
const Styled = styled.div``
