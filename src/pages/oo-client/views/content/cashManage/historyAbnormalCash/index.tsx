import React, { useState, useEffect } from 'react'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { cashManageApi } from '@src/pages/oo-client/api'
import { formatTableData } from '@utils/format'
import { history } from '@src/utils/router'
import { stampToTime, timeToStamp } from '@src/utils/timeFilter'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import { Button, message } from 'antd'
import commonModuleApi from '@src/pages/oo-client/api/common'
import BaseTable from '@globalComponents/BaseTable'
import LayoutSlot from '@globalComponents/LayoutSlot'
import SearchBar from '@ooClientComponents/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import styled from 'styled-components'

const index = () => {
	const [searchParams, setSearchParams] = useState({})
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [dataSource, setDataSource] = useState<any[]>([{}])
	const [tableLoading, setTableLoading] = useState<boolean>(false)
	const [exportLoading, setExportLoading] = useState<boolean>(false)

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	//获取企业信息列表
	const getList = async (values?: any) => {
		let params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...searchParams,
		}
		if (values) {
			params = Object.assign(params, values)
		}
		setTableLoading(true)
		const res = await cashManageApi.getHistoryAbnormalCashList(params).catch(() => {
			pagination.total = 0
			setTableLoading(false)
			setPagination({ ...pagination })
			setDataSource([])
		})
		if (res) {
			if (res.list && res.list.length > 0) {
				pagination.total = res.total
				setDataSource(formatTableData.addKey(res.list))
				setTableLoading(false)
				setPagination({ ...pagination })
			} else {
				pagination.total = 0
				setTableLoading(false)
				setDataSource([])
				setPagination({ ...pagination })
			}
		}
	}
	//处理搜索
	const handleSearch = (values: any) => {
		if (values && values.dueDate && values.dueDate.length === 2) {
			values.dueBeginDate = values.dueDate[0]
			values.dueEndDate = timeToStamp(values.dueDate[1], 'end')
			delete values.dueDate
		}
		//点击查询后，带入参数页码置为1, 记录当前查询参数
		pagination.current = 1
		setPagination({ ...pagination })
		setSearchParams(values)
	}
	const handleReset = () => {
		pagination.current = 1
		setSearchParams({})
		setPagination({ ...pagination })
	}
	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}
	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}

	const getColumns = () => {
		const columnsDic = {
			operation: {
				render: (text, record) => (
					<div>
						<span
							className="link"
							onClick={() => {
								history.push('/content/cashManage/historyAbnormalCashDetails', { couInfo: record })
							}}
						>
							查看兑付信息
						</span>
					</div>
				),
			},
		}
		return getColumnsByPageName('historyAbnormalCash', columnsDic)
	}

	//导出融信信息
	const exportCouCsv = async () => {
		setExportLoading(true)
		let serverTime = await commonModuleApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`
		let type = 'application/vnd.ms-excel'
		cashManageApi
			.exportHistoryAbnormalCashList({
				...searchParams,
				pageNum: 1,
				pageSize: 2147483646,
			})
			.then((response: any) => {
				console.log(response)
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}

	return (
		<div>
			<LayoutSlot>
				<SearchCompanyStyle className="card-wrapper">
					<OperatingArea>
						<SearchBar pageName="abnormalCash" onSubmit={handleSearch} onClear={handleReset} />
					</OperatingArea>
					<Button.Group>
						{/* <Button style={{ marginLeft: '20px', width: '80px' }} type="primary" onClick={exportCouCsv} loading={exportLoading}>
							导出
						</Button> */}
					</Button.Group>
					<BaseTable
						className="card-table"
						loading={tableLoading}
						dataSource={dataSource}
						columns={getColumns()}
						total={pagination.total}
						current={pagination.current}
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
					/>
				</SearchCompanyStyle>
			</LayoutSlot>
		</div>
	)
}

export default index

const SearchCompanyStyle = styled.div``
