/*
 * @Date: 2022-09-14 11:08:58
 * @LastEditors: yanh<PERSON><PERSON> yanheng<PERSON>@qq.com
 * @FilePath: \frontend-web\src\pages\oo-client\views\content\cashManage\abnormalCashDetails\index.tsx
 * @Description: In User Settings Edit
 */
import React, { useState, useEffect } from 'react'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { cashManageApi } from '@src/pages/oo-client/api'
import { formatTableData } from '@utils/format'
import { history } from '@src/utils/router'
import { Alert, Button, message, Descriptions, Spin, Modal } from 'antd'
import BaseTable from '@globalComponents/BaseTable'
import LayoutSlot from '@globalComponents/LayoutSlot'
import styled from 'styled-components'
import { renderAmount, renderDate_, renderOriginalCouStatus } from '@src/pages/oo-client/config/TableColumnsRender'
import SmallTitle from '@src/globalComponents/SmallTitle'
const DescriptionsItem = Descriptions.Item
export default () => {
	const { couInfo } = history.location.state || { couInfo: {} }
	console.log('abnormalCashDetails', couInfo)
	//table相关
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [modalVisible, setModalVisible] = useState<boolean>(false)
	const [record, setRecord] = useState<any>({})
	const [dataSource, setDataSource] = useState<any[]>([])
	const [tableLoading, setTableLoading] = useState<boolean>(false)
	const [pageLoading, setPageLoading] = useState<boolean>(false)
	useEffect(() => {
		getAbnormalCashList()
	}, [pagination.current, pagination.pageSize])

	const getColumns = () => {
		const columnsDic = {
			operation: {
				title: '清分结果',
				render: renderOperation,
				width: 220,
			},
		}
		return getColumnsByPageName('abnormalCashDetails', columnsDic)
	}

	//获取清分列表
	const getAbnormalCashList = async (values?: any) => {
		let params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			couUuid: couInfo?.couUuid,
		}
		if (values) {
			params = Object.assign(params, values)
		}
		setTableLoading(true)
		const res = await cashManageApi.getAbnormalCashListByCouUuid(params).catch(() => {
			setDataSource([])
			setTableLoading(false)
			pagination.total = 0
			setPagination({ ...pagination })
		})
		if (res) {
			if (res.list && res.list.length > 0) {
				setTableLoading(false)
				setDataSource(formatTableData.addKey(res.list))
				pagination.total = res.total
				setPagination({ ...pagination })
			} else {
				setDataSource([])
				setTableLoading(false)
				pagination.total = 0

				setPagination({ ...pagination })
			}
		}
	}

	const settleQueryByNo = (settleNo: string) => {
		setTableLoading(true)

		cashManageApi
			.querySettlementNo({ settleNo })
			.then(res => {
				message.success('发送成功')
				getAbnormalCashList()
			})
			.catch(e => {
				console.log('e', e)
			})
	}

	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}

	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}

	//渲染操作列
	const renderOperation = (text: string, dataRecord: any) => {
		return (
			<div style={{ display: 'flex', justifyContent: 'space-between' }}>
				<span
					className="link"
					onClick={() => {
						setRecord(dataRecord)
						setModalVisible(true)
					}}
				>
					查看清分结果
				</span>
				<span
					className="link"
					onClick={() => {
						settleQueryByNo(dataRecord.settleNo)
					}}
				>
					发送查询请求
				</span>
			</div>
		)
	}

	//导出融信信息
	const sendJxBank = async () => {
		setPageLoading(true)
		cashManageApi
			.sendJxBank({
				uuid: couInfo?.uuid,
			})
			.then((response: any) => {
				message.success('金融机构受理成功')
				setPageLoading(false)
				getAbnormalCashList()
			})
			.catch(() => {
				setPageLoading(false)
			})
	}

	return (
		<LayoutSlot>
			<Styled>
				<Spin spinning={pageLoading}>
					<div className="card-wrapper">
						<SmallTitle text="融信信息" />
						<Descriptions column={2} labelStyle={{ width: '100px' }} contentStyle={{ width: '30%' }} bordered>
							<DescriptionsItem label="融信编号">{couInfo?.couNo}</DescriptionsItem>
							<DescriptionsItem label="金额（￥）">{renderAmount(couInfo?.couAmount)}</DescriptionsItem>
							<DescriptionsItem label="持有方">{couInfo?.couHolderName}</DescriptionsItem>
							<DescriptionsItem label="开立方">{couInfo?.couPublishName}</DescriptionsItem>
							<DescriptionsItem label="兑付到期日">{renderDate_(couInfo?.dueDate)}</DescriptionsItem>
							<DescriptionsItem label="兑付状态">{renderOriginalCouStatus(couInfo?.couCashStatus)}</DescriptionsItem>
						</Descriptions>
					</div>
					<div className="card-wrapper">
						<SmallTitle>
							<div style={{ display: 'inline-block' }}>清分记录</div>
						</SmallTitle>
						<Alert
							message={
								<p>
									若不存在银行处理中的清分记录，则可点击发送清分指令按钮，产生一笔新的清分记录 <br />
									若存在银行处理中的清分记录，请先手动查询银行清分结果；若提示处理中，则后续需要隔段时间继续查询；
									<br /> 若提示清分失败，则可重新发送清分指令；
									<br />
									若提示清分成功，表示正常完成清分，状态为已兑付；
								</p>
							}
							type="info"
							showIcon
						/>
						<div className="tips">重要提示：非融资融信，发送清分指令前，请查看历史清分流水的清分结果信息，并与相关方确认后再进行操作</div>
						<Button type="primary" onClick={sendJxBank} size="middle" style={{ width: '120px' }}>
							发送清分指令
						</Button>
						<BaseTable
							className="card-table"
							loading={tableLoading}
							dataSource={dataSource}
							columns={getColumns()}
							total={pagination.total}
							current={pagination.current}
							onPageChange={handlePageChange}
							onSizeChange={handleSizeChange}
						/>
					</div>
					<Modal
						bodyStyle={{ padding: '20px', minHeight: '180px' }}
						footer={null}
						title="查看清分结果"
						open={modalVisible}
						onCancel={() => setModalVisible(false)}
					>
						<p>{record?.returnMsg}</p>
					</Modal>
				</Spin>
			</Styled>
		</LayoutSlot>
	)
}
const Styled = styled.div`
	.tips {
		color: red;
		padding: 0 0 12px 0;
		font-size: 14px;
	}
`
