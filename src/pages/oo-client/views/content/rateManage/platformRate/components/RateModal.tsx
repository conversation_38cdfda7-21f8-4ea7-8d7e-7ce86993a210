import React, { useEffect } from 'react'
import { Modal, Form, Input, Radio } from 'antd'
import GetCompanyName from '@src/globalComponents/SearchComByName'
import { searchCompanyApi } from '@src/pages/oo-client/api'
import styled from 'styled-components'

const layout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 13 },
}

export interface PropsStruct {
	/**
	 * @description 标题
	 */
	title: string
	/**
	 * @description 显示/隐藏
	 */
	visible: boolean
	/**
	 * @description 核心企业还是供应商
	 */
	type: string
	/**
	 * @description 提交时加载
	 */
	confirmLoading: boolean
	/**
	 * @description 回显数据
	 */
	previewData?: any
	/**
	 * @description 点击关闭事件
	 */
	onCancel: () => void
	/**
	 * @description 点击提交事件
	 */
	onSubmit: (values) => void
}

export default function RateModal(props: PropsStruct) {
	const { visible, title, type, confirmLoading, previewData, onCancel, onSubmit } = props
	const [form] = Form.useForm()

	useEffect(() => {
		if (visible) {
			if (title === '修改') {
				let fieldsValue: any = {
					center: previewData.centerUuid,
					fi: previewData.fiUuid,
					rate: previewData.serviceFeeRate,
				}
				if (type === 'supplier') {
					fieldsValue.supplier = previewData.supplierUuid
					fieldsValue.special = previewData.special
				}
				// 如果有回显数据，则设置回显
				form.setFieldsValue(fieldsValue)
			} else if (title === '新增') {
				form.resetFields()
			}
		}
	}, [visible])

	const submitData = () => {
		// 动态添加需要验证的字段
		let validNameList: string[] = ['center', 'fi', 'rate']
		if (type === 'supplier') {
			validNameList.push('supplier')
			validNameList.push('special')
		}
		form.validateFields(validNameList).then(
			values => {
				onSubmit(values)
			},
			err => {
				console.log(err)
			}
		)
	}

	return (
		<Modal
			title={title}
			open={visible}
			onCancel={() => {
				onCancel()
			}}
			onOk={submitData}
			width="500px"
			bodyStyle={{
				padding: '12px',
			}}
			confirmLoading={confirmLoading}
		>
			<Box>
				<Form
					{...layout}
					form={form}
					initialValues={{
						special: 1,
					}}
				>
					<div style={{ display: type === 'center' ? 'none' : 'block' }}>
						<GetCompanyName
							dropdownMatchSelectWidth={320}
							name="supplier"
							label="供应商"
							placeholder="请选择供应商"
							getCompany={searchCompanyApi.searchCompanyByName}
							companyType="S"
							valuekey={'uuid'}
							requireMsg="请选择供应商"
							previewData={{ uuid: previewData.supplierUuid, companyName: previewData.supplierName }}
							disabled={title === '修改' ? true : false}
						/>
					</div>

					<GetCompanyName
						dropdownMatchSelectWidth={320}
						name="center"
						label="核心企业"
						placeholder="请选择核心企业"
						getCompany={searchCompanyApi.searchCompanyByName}
						companyType="C"
						valuekey={'uuid'}
						requireMsg="请选择核心企业"
						previewData={{ uuid: previewData.centerUuid, companyName: previewData.centerName }}
						disabled={title === '修改' ? true : false}
					/>
					<GetCompanyName
						dropdownMatchSelectWidth={320}
						name="fi"
						label="金融机构"
						placeholder="请选择金融机构"
						getCompany={searchCompanyApi.searchCompanyByName}
						companyType="FI"
						valuekey={'uuid'}
						requireMsg="请选择金融机构"
						previewData={{ uuid: previewData.fiUuid, companyName: previewData.fiName }}
						disabled={title === '修改' ? true : false}
					/>
					<Form.Item
						name="rate"
						label="服务费率"
						validateFirst
						rules={[
							{
								required: true,
								message: '请输入服务费率，0-50且最多5位小数',
							},
							{
								pattern: /^\d+(\.\d{1,5})?$/,
								message: '请输入服务费率，0-50且最多5位小数',
							},
							{
								type: 'number',
								min: 0,
								max: 50,
								transform: value => {
									return Number(value)
								},
								message: '请输入服务费率，0-50且最多5位小数',
							},
						]}
					>
						<Input placeholder="请输入服务费率" suffix="%" />
					</Form.Item>
					<div style={{ display: type === 'center' ? 'none' : 'block' }}>
						<Form.Item
							className="special"
							name="special"
							label="特殊标识"
							rules={[
								{
									required: true,
								},
							]}
						>
							<Radio.Group name="radiogroup">
								<Radio value={1}>特殊</Radio>
								<Radio value={0}>非特殊</Radio>
							</Radio.Group>
						</Form.Item>
					</div>
				</Form>
			</Box>
		</Modal>
	)
}

const Box = styled.div``
