import React, { useEffect, useState } from 'react'
import { message, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Mo<PERSON> } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { rateManageApi, commonApi } from '@src/pages/oo-client/api/index'
import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import SearchBar from '@ooClientComponents/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import RateModal from './components/RateModal'
import styled from 'styled-components'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import { stampToTime } from '@utils/timeFilter'

function platformRate() {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [searchParams, setSearchParams] = useState({})
	const [loading, setLoading] = useState<boolean>(false)
	const [companyType, setCompanyType] = useState<number>(1) // 公司类型 1代表核心企业，2代表供应商
	const [pageName, setPageName] = useState<string>('cRateList')
	const [currentKey, setCurrentKey] = useState<string>('center')
	const [rateVisible, setRateVisible] = useState(false)
	const [rateTitle, setRateTitle] = useState<string>('新增')
	const [confirmLoading, setConfirmLoading] = React.useState(false)
	const [previewData, setPreviewData] = React.useState<any>({})
	const [exportServiceFeeLoading, setExportServiceFeeLoading] = React.useState(false)

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams, currentKey])

	// 得到退款申请列表
	const getList = () => {
		setLoading(true)
		const companyTypeParams = {
			companyType,
		}
		rateManageApi
			.getServiceFeeRateList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...searchParams,
				...companyTypeParams,
			})
			.then(
				(res: any) => {
					pagination.total = res['total']
					if (res.list) {
						setDataSource([...res.list])
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => {
					setLoading(false)
					pagination.total = 0
					setDataSource([])
				}
			)
	}

	const submitData = initSubmitData => {
		setConfirmLoading(true)
		let params: any = {
			fiUuid: initSubmitData.fi,
			centerUuid: initSubmitData.center,
			serviceFeeRate: initSubmitData.rate,
			companyType,
		}
		if (currentKey === 'supplier') {
			params.supplierUuid = initSubmitData.supplier
			params.special = initSubmitData.special
		}
		if (rateTitle === '修改') {
			params.id = previewData.id
		}

		rateManageApi
			.createAndUpdateRate(params)
			.then(res => {
				getList()
				setRateVisible(false)
				setConfirmLoading(false)
				if (rateTitle === '新增') {
					message.success('创建成功')
				} else if (rateTitle === '修改') {
					message.success('修改成功')
				}
			})
			.catch(err => {
				setConfirmLoading(false)
			})
	}

	const handleMenuClick = e => {
		if (e.key === 'center') {
			setPageName('cRateList')
			setCompanyType(1)
		} else if (e.key === 'supplier') {
			setPageName('sRateList')
			setCompanyType(2)
		}
		// 查询参数清空
		setSearchParams({})
		setCurrentKey(e.key)
	}

	const handleCreateClick = () => {
		setRateTitle('新增')
		setPreviewData({})
		setRateVisible(true)
	}
	const updateClick = record => {
		setRateTitle('修改')
		setPreviewData(record)
		setRateVisible(true)
	}
	const deleteClick = record => {
		let params: any = {
			id: record.id,
			fiUuid: record.fiUuid,
			centerUuid: record.centerUuid,
			serviceFeeRate: record.serviceFeeRate,
			companyType,
		}
		if (currentKey === 'supplier') {
			params.supplierUuid = record.supplierUuid
			params.special = record.special
		}
		Modal.confirm({
			title: '确定删除该条记录',
			icon: <ExclamationCircleOutlined />,
			okText: '确定',
			onOk() {
				rateManageApi
					.deleteRate(params)
					.then(() => {
						message.success('删除成功')
						getList()
					})
					.catch(err => {
						console.log('err', err)
					})
			},
			onCancel() {
				console.log('Cancel')
			},
		})
	}

	const onClear = () => {
		setSearchParams({})
	}

	const onSubmit = (params: any) => {
		const getUuid = params[currentKey] ? params[currentKey] : ''
		const getParamsObj = {
			[currentKey + 'Uuid']: getUuid,
		}
		setSearchParams(getParamsObj)
		pagination.current = 1
	}

	const handlePageChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}

	const handleSizeChange = (size: number) => {
		pagination.pageSize = size
		pagination.current = 1
		setPagination({ ...pagination })
	}

	const getColumns = () => {
		const columnsDic = {
			special: {
				render: text => {
					return <span>{text === 0 ? '非特殊' : '特殊'}</span>
				},
			},
			operation: {
				render: (text, record) => {
					return (
						<div className="options_btn">
							<span
								className="link"
								onClick={() => {
									updateClick(record)
								}}
							>
								修改
							</span>
							<span
								className="red"
								onClick={() => {
									deleteClick(record)
								}}
							>
								删除
							</span>
						</div>
					)
				},
			},
		}
		return getColumnsByPageName(pageName, columnsDic)
	}

	const exportServiceFee = async () => {
		setExportServiceFeeLoading(true)
		let serverTime = await commonApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`
		const companyInfo = JSON.parse(localStorage.getItem('companyInfo') || '{}')
		const operatingOrganizationUuid = companyInfo.uuid

		rateManageApi
			.exportServiceFeeRate({
				...searchParams,
				pageNum: 1,
				pageSize: 2147483646,
				...{ companyType },
				operatingOrganizationUuid,
			})
			.then((response: any) => {
				let type = 'application/vnd.ms-excel'
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportServiceFeeLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportServiceFeeLoading(false)
			})
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<Alert
						message="关于平台服务费率的说明"
						description={
							<div>
								<p>1、核心企业服务费率：供应商申请融资，若为核心企业付息，则取相应核心企业的服务费率</p>
								<p>2、供应商服务费率：供应商申请融资，若为供应商付息，则取供应商相应的服务费率</p>
							</div>
						}
						type="info"
						showIcon
					/>
					<Menu onClick={handleMenuClick} selectedKeys={[currentKey]} mode="horizontal" style={{ marginBottom: '20px' }}>
						<Menu.Item key="center">核心企业服务费率</Menu.Item>
						<Menu.Item key="supplier">供应商服务费率</Menu.Item>
					</Menu>
					<OperatingArea>
						<Button onClick={handleCreateClick} type="primary">
							新增
						</Button>
						<SearchBar onSubmit={onSubmit} onClear={onClear} pageName={pageName} />
						<Button onClick={exportServiceFee} type="primary" style={{ marginLeft: '5px' }} loading={exportServiceFeeLoading}>
							导出
						</Button>
					</OperatingArea>
					<BaseTable
						onSizeChange={handleSizeChange}
						onPageChange={handlePageChange}
						columns={getColumns()}
						dataSource={dataSource}
						loading={loading}
						rowKey="id"
						{...pagination}
					/>
				</div>
				<RateModal
					visible={rateVisible}
					title={rateTitle}
					onSubmit={submitData}
					onCancel={() => setRateVisible(false)}
					confirmLoading={confirmLoading}
					previewData={previewData}
					type={currentKey}
				/>
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.options_btn {
		span {
			display: inline-block;
			margin-right: 10px;
		}
	}
`

export default platformRate
