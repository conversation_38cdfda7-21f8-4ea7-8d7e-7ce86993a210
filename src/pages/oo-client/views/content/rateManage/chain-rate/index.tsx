import React, { useEffect, useState } from 'react'
import { message, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Mo<PERSON> } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { rateManageApi } from '@src/pages/oo-client/api/index'
import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import SearchBar from '@ooClientComponents/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import RateModal from './components/rate-modal'
import styled from 'styled-components'
import { getUserData } from '@src/utils/util'

function platformRate() {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [searchParams, setSearchParams] = useState({})
	const [loading, setLoading] = useState<boolean>(false)
	const [pageName, setPageName] = useState<string>('financeProduct')
	const [currentKey, setCurrentKey] = useState<string>('product')
	const [rateVisible, setRateVisible] = useState(false)
	const [rateTitle, setRateTitle] = useState<string>('新增')
	const [confirmLoading, setConfirmLoading] = React.useState(false)
	const [previewData, setPreviewData] = React.useState<any>({})
	const { uuid = null } = getUserData().companyInfo

	useEffect(() => {
		if (currentKey === 'product') {
			getListForProduct()
		} else if (currentKey === 'company') {
			getListForCompany()
		}
	}, [pagination.current, pagination.pageSize, searchParams, currentKey])

	const getListForProduct = () => {
		setLoading(true)
		rateManageApi
			.getChainRateListForProduct({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				ooCompanyUuid: uuid,
			})
			.then(
				(res: any) => {
					pagination.total = res['total']
					if (res.list) {
						setDataSource([...res.list])
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => {
					setLoading(false)
					pagination.total = 0
					setDataSource([])
				}
			)
	}

	const getListForCompany = () => {
		setLoading(true)
		rateManageApi
			.getChainRateListForCompany({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				ooCompanyUuid: uuid,
				...searchParams,
			})
			.then(
				(res: any) => {
					pagination.total = res['total']
					if (res.list) {
						setDataSource([...res.list])
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => {
					setLoading(false)
					pagination.total = 0
					setDataSource([])
				}
			)
	}

	const submitData = initSubmitData => {
		const { uuid: operatingOrganizationUuid } = JSON.parse(localStorage.getItem('companyInfo') || '[]')
		setConfirmLoading(true)
		let params: any = {
			serialNo: initSubmitData.product,
			serviceFeeRate: initSubmitData.rate,
			type: initSubmitData.type,
			operatingOrganizationUuid,
		}
		if (currentKey === 'company') {
			params.companyUuid = initSubmitData.company
		}
		if (rateTitle === '修改') {
			params.id = previewData.id

			rateManageApi
				.modifyChainRate(params)
				.then(res => {
					setRateVisible(false)
					setConfirmLoading(false)
					message.success('修改成功')
					if (currentKey === 'product') {
						getListForProduct()
					} else if (currentKey === 'company') {
						getListForCompany()
					}
				})
				.catch(err => {
					setConfirmLoading(false)
				})
		} else if (rateTitle === '新增') {
			rateManageApi
				.createChainRate(params)
				.then(res => {
					setRateVisible(false)
					setConfirmLoading(false)
					message.success('创建成功')
					if (currentKey === 'product') {
						getListForProduct()
					} else if (currentKey === 'company') {
						getListForCompany()
					}
				})
				.catch(err => {
					setConfirmLoading(false)
				})
		}
	}

	const handleMenuClick = e => {
		if (e.key === 'product') {
			setPageName('financeProduct')
		} else if (e.key === 'company') {
			setPageName('companySetting')
		}
		// 查询参数清空
		setSearchParams({})
		setCurrentKey(e.key)
	}

	const handleCreateClick = () => {
		setRateTitle('新增')
		setPreviewData({})
		setRateVisible(true)
	}
	const handleUpdateClick = record => {
		setRateTitle('修改')
		setPreviewData(record)
		setRateVisible(true)
	}
	const handleDeleteClick = record => {
		let params: any = {
			id: record.id,
		}
		Modal.confirm({
			title: '确定删除该条记录',
			icon: <ExclamationCircleOutlined />,
			onOk() {
				rateManageApi
					.deleteChainRate(params)
					.then(() => {
						message.success('删除成功')
						if (currentKey === 'product') {
							getListForProduct()
						} else if (currentKey === 'company') {
							getListForCompany()
						}
					})
					.catch(err => {
						console.log('err', err)
					})
			},
			okText: '确定',
			onCancel() {
				console.log('Cancel')
			},
		})
	}

	const onSubmit = (params: any) => {
		const getUuid = params[currentKey] ? params[currentKey] : ''
		const paramsObj = {
			[currentKey + 'Uuid']: getUuid,
		}
		pagination.current = 1
		setSearchParams(paramsObj)
	}
	const onClear = () => {
		setSearchParams({})
	}

	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	const handlePageChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}

	const getColumns = () => {
		const columnsDic = {
			chainRateType: {
				render: text => {
					return <span>{text === 0 ? '单笔' : '年化'}</span>
				},
			},
			operation: {
				render: (text, record) => {
					return (
						<div className="options_btn">
							<span
								className="link"
								onClick={() => {
									handleUpdateClick(record)
								}}
							>
								修改
							</span>
							<span
								className="red"
								onClick={() => {
									handleDeleteClick(record)
								}}
							>
								删除
							</span>
						</div>
					)
				},
			},
		}
		return getColumnsByPageName(pageName, columnsDic)
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<Alert
						message="关于平台服务费率的说明"
						description={
							<div>
								<p>1、按金融产品：按照金融产品的维度设置平台服务费率</p>
								<p>2、按企业设置：按照企业+金融产品的维度设置平台服务费率</p>
								<p>若企业设置了服务费率，则融资时取企业的服务费率，若企业没有设置服务费率，则融资时取对应的金融产品的服务费率</p>
							</div>
						}
						type="info"
						showIcon
					/>
					<Menu onClick={handleMenuClick} selectedKeys={[currentKey]} mode="horizontal" style={{ marginBottom: '20px' }}>
						<Menu.Item key="product">按金融产品</Menu.Item>
						<Menu.Item key="company">按企业设置</Menu.Item>
					</Menu>
					<OperatingArea>
						<Button onClick={handleCreateClick} type="primary">
							新增
						</Button>
						{pageName === 'companySetting' ? <SearchBar onSubmit={onSubmit} onClear={onClear} pageName={pageName} /> : null}
					</OperatingArea>
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						rowKey="id"
					/>
				</div>
				<RateModal
					title={rateTitle}
					visible={rateVisible}
					onCancel={() => setRateVisible(false)}
					onSubmit={submitData}
					confirmLoading={confirmLoading}
					type={currentKey}
					previewData={previewData}
				/>
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.options_btn {
		span {
			display: inline-block;
			margin-right: 10px;
		}
	}
`

export default platformRate
