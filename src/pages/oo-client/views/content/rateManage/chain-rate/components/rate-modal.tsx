import React, { useEffect } from 'react'
import { Modal, Form, Input, Radio } from 'antd'
import GetCompanyName from '@src/globalComponents/SearchComByName'
import SearchProductByName from '@src/globalComponents/SearchProductByName'
import { rateManageApi, searchCompanyApi } from '@src/pages/oo-client/api'
import styled from 'styled-components'

const layout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 13 },
}

export interface PropsStruct {
	/**
	 * @description 显示/隐藏
	 */
	visible: boolean
	/**
	 * @description 标题
	 */
	title: string
	/**
	 * @description 提交时加载
	 */
	confirmLoading: boolean
	/**
	 * @description 回显数据
	 */
	previewData?: any
	/**
	 * @description 金融产品还是企业设置
	 */
	type: string
	/**
	 * @description 点击提交事件
	 */
	onSubmit: (values) => void
	/**
	 * @description 点击关闭事件
	 */
	onCancel: () => void
}

export default function RateModal(props: PropsStruct) {
	const [form] = Form.useForm()
	const { visible, title, type, confirmLoading, previewData, onCancel, onSubmit } = props

	useEffect(() => {
		if (visible) {
			if (title === '修改') {
				let fieldsValue: any = {
					product: previewData.serialNo,
					rate: previewData.serviceFeeRate,
					type: previewData.type,
				}
				if (type === 'company') {
					fieldsValue.company = previewData.companyUuid
				}
				// 如果有回显数据，则设置回显
				form.setFieldsValue(fieldsValue)
			} else if (title === '新增') {
				form.resetFields()
			}
		}
	}, [visible])

	const submitData = () => {
		// 动态添加需要验证的字段
		let validNameDataList: string[] = ['product', 'rate', 'type']
		if (type === 'company') {
			validNameDataList.push('company')
		}
		form.validateFields(validNameDataList).then(
			values => {
				onSubmit(values)
			},
			err => {
				console.log(err)
			}
		)
	}

	return (
		<Modal
			open={visible}
			title={title}
			confirmLoading={confirmLoading}
			onOk={submitData}
			onCancel={() => {
				onCancel()
			}}
			bodyStyle={{
				padding: '12px',
			}}
			width="500px"
		>
			<Box>
				<Form
					form={form}
					initialValues={{
						type: 0,
					}}
					{...layout}
				>
					<div style={{ display: type === 'product' ? 'none' : 'block' }}>
						<GetCompanyName
							dropdownMatchSelectWidth={320}
							name="company"
							label="企业名称"
							placeholder="请选择企业"
							getCompany={searchCompanyApi.searchCompanyByName}
							companyType="OCC"
							valuekey={'uuid'}
							requireMsg="请选择企业"
							previewData={{ uuid: previewData.companyUuid, companyName: previewData.companyName }}
							disabled={title === '修改' ? true : false}
						/>
					</div>

					<SearchProductByName
						dropdownMatchSelectWidth={320}
						name="product"
						label="金融产品名称"
						placeholder="请选择金融产品"
						getCompany={rateManageApi.getProductList}
						valuekey={'serialNo'}
						requireMsg="请选择金融产品"
						disabled={title === '修改' ? true : false}
						previewData={{ serialNo: previewData.serialNo, name: previewData.productName }}
					/>

					<Form.Item
						validateFirst
						label="服务费率"
						name="rate"
						rules={[
							{
								required: true,
								message: '请输入服务费率，0-50且最多5位小数',
							},
							{
								pattern: /^\d+(\.\d{1,5})?$/,
								message: '请输入服务费率，0-50且最多5位小数',
							},
							{
								type: 'number',
								min: 0,
								max: 50,
								transform: value => {
									return Number(value)
								},
								message: '请输入服务费率，0-50且最多5位小数',
							},
						]}
					>
						<Input placeholder="请输入服务费率" suffix="%" />
					</Form.Item>
					<div style={{ display: type === 'center' ? 'none' : 'block' }}>
						<Form.Item
							label="费率类型"
							name="type"
							className="type"
							rules={[
								{
									required: true,
								},
							]}
						>
							<Radio.Group name="radioGroup">
								<Radio value={0}>单笔</Radio>
								<Radio value={1}>年化</Radio>
							</Radio.Group>
						</Form.Item>
					</div>
				</Form>
			</Box>
		</Modal>
	)
}

const Box = styled.div``
