import React, { useEffect, useState } from 'react'
import { Modal, Form, Input, message } from 'antd'
import styled from 'styled-components'
import { newsManageApi } from '@src/pages/oo-client/api'
import SelectFormItem from '@globalComponents/form/select-form-item'
import { validUrl } from '@src/utils/util'

export type modalConfigType = {
	type?: 'add' | 'edit'
	title?: string
	open?: boolean
	updateTable?: () => void
	record?: Record<string, any>
}

type setModalCbType = (pre: modalConfigType) => modalConfigType

type PropsType = {
	modalConfig: modalConfigType
	setModalConfig: (value: setModalCbType) => void | modalConfigType
	link?: string
}

export default (props: PropsType) => {
	let { setModalConfig, modalConfig } = props
	let { type, open, title, updateTable, record } = modalConfig
	const [form] = Form.useForm()
	const [curLink, setCurLink] = useState(record?.link || '')
	const [confirmLoading, setConfirmLoading] = useState(false)
	const [selectOptions, setSelectOptions] = useState([])

	const linkInputChange = e => {
		setCurLink(e.target.value.trim())
	}

	const handleCancel = () => {
		setModalConfig(pre => {
			return {
				...pre,
				open: false,
			}
		})
	}

	const handleOk = () => {
		form.submit()
	}

	const submitForm = values => {
		setConfirmLoading(true)
		if (type == 'add') {
			newsManageApi
				.addNews(values)
				.then(res => {
					message.success('提交成功')
					setModalConfig(pre => {
						return {
							...pre,
							open: false,
						}
					})
					if (typeof updateTable === 'function') {
						updateTable()
					}
				})
				.finally(() => {
					setConfirmLoading(false)
				})
		} else if (type === 'edit') {
			newsManageApi
				.modifyNews(Object.assign(values, { id: record.id }))
				.then(res => {
					message.success('提交成功')
					setModalConfig(pre => {
						return {
							...pre,
							open: false,
						}
					})
					if (typeof updateTable === 'function') {
						updateTable()
					}
				})
				.finally(() => {
					setConfirmLoading(false)
				})
		}
	}

	const selectConfig = {
		formItemProps: {
			label: '发布平台',
			name: 'platformList',
			rules: [{ required: true, message: '请选择发布平台' }],
		},
		controlProps: {
			options: selectOptions,
			placeholder: '请选择发布平台',
		},
	}

	const getSelectOptions = () => {
		newsManageApi.getDomainList({ pageNum: 1, pageSize: 100000 }).then(res => {
			let options = []
			if (res && Array.isArray(res.list)) {
				res.list.forEach(item => {
					options.push({
						label: item.linkName,
						value: item.id,
						key: item.id,
					})
				})
			}
			setSelectOptions(options)
		})
	}
	// handleLinkClick
	// 触发格式报错
	const handleLinkClick = () => {
		form.validateFields(['link']).catch(e => {
			console.log(`e = ${e}`)
		})
	}

	const getLink = () => {
		if (validUrl(curLink)) {
			return (
				<a href={curLink} target="_blank">
					访问链接
				</a>
			)
		} else {
			return <a onClick={handleLinkClick}>访问链接</a>
		}
	}

	useEffect(() => {
		if (open) {
			if (type === 'add') {
				form.resetFields()
				//保证访问地址和填写地址
				setCurLink('')
			} else if (type == 'edit') {
				setCurLink(record?.link || '')
				let platformList = Object.keys(record.platformMap || {})
				form.setFieldsValue({ ...record, platformList })
			}
			getSelectOptions()
		}
	}, [open])

	const validUrlFn = (_, value) => {
		if (validUrl(value)) {
			return Promise.resolve(value)
		} else {
			return Promise.reject(false)
		}
	}

	return (
		<Modal width={600} title={title} maskClosable={false} open={open} onOk={handleOk} onCancel={handleCancel} confirmLoading={confirmLoading}>
			<ModalTag>
				<Form onFinish={submitForm} form={form} labelCol={{ span: 8 }} wrapperCol={{ span: 9 }}>
					<SelectFormItem {...selectConfig} />
					<Form.Item label="资讯标题" name="title" rules={[{ required: true, message: '请输入资讯标题' }]}>
						<Input placeholder="请输入，最多50个字符" maxLength={50} />
					</Form.Item>
					<Form.Item
						className="link-content"
						extra={getLink()}
						validateFirst={true}
						label="资讯链接地址"
						name="link"
						rules={[
							{ required: true, message: '请输入资讯链接地址' },
							{
								validator: validUrlFn,
								message: '链接地址不合法',
							},
						]}
					>
						<Input onChange={linkInputChange} placeholder="请输入，最多100个字符" maxLength={100} />
					</Form.Item>
				</Form>
			</ModalTag>
		</Modal>
	)
}

/*
link	string
platformList	[integer($int64)]
title	string
*/

const ModalTag = styled.div`
	padding-top: 30px;
	.link-content {
		position: relative;
		.ant-form-item-extra {
			position: absolute;
			right: -70px;
			top: 5px;
		}
	}
`

/*
<Form.Item wrapperCol={{ offset: 8, span: 16 }}>
	<Button type="primary" htmlType="submit">
	Submit
	</Button>
</Form.Item>
*/
