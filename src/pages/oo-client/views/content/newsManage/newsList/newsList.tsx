import React, { useEffect, useRef, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/new-widget/wx-table/baseTable'
import styled from 'styled-components'
import { Button, message, Modal } from 'antd'
import OperatingArea from '@src/globalComponents/OperatingArea'
// as 结构重命名
import { newsManageApi } from '@src/pages/oo-client/api'
import { setTooltip } from '@src/pages/oo-client/biz/bizIndex'
import { getTabTit } from '@globalBiz/gBiz'
import './newsList.less'
import AddOrEditNews, { type modalConfigType } from './components/addOrEditNews'
import dayjs from 'dayjs'

const NewsList = () => {
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 }) //表格 配置
	const [modalConfig, setModalConfig] = useState<modalConfigType>({
		title: '发布资讯',
		type: 'add',
		open: false,
	})
	const tabRef = useRef(null)

	const modifyNews = record => {
		setModalConfig({
			type: 'edit',
			title: '修改资讯',
			open: true,
			record: record,
			updateTable,
		})
	}

	const deleteNews = record => {
		const config = {
			title: '确定删除',
			content: '',
			onOk: () => {
				return newsManageApi
					.deleteNews({ id: record.id })
					.then(res => {
						message.success({
							key: record.id,
							content: '删除成功',
						})
						updateTable()
					})
					.catch(err => {
						console.log(err)
					})
			},
		}
		Modal.confirm(config)
	}

	//获取合同的表格列
	const getColumns = () => {
		let columns = [
			getTabTit(['资讯标题', 'title', 350, true]),
			getTabTit(['发布时间', 'createTime', 150, true], text => {
				return dayjs(text).format('YYYY/MM/DD HH:mm:ss') || '--'
			}),
			getTabTit(['发布平台', 'platformMap', 400], value => {
				//platformMap
				if (value) {
					return setTooltip(Object.values(value).join('，'))
				} else {
					return '--'
				}
			}),
			getTabTit(['操作', 'action'], (text, record) => {
				return (
					<span>
						<a className="mr10" onClick={modifyNews.bind(null, record)}>
							修改
						</a>
						<a className="red" onClick={deleteNews.bind(null, record)}>
							删除
						</a>
					</span>
				)
			}),
		]

		return columns
	}

	const updateTable = () => {
		tabRef?.current?.updateTable()
	}

	const publicNews = () => {
		setModalConfig({
			title: '发布资讯',
			type: 'add',
			open: true,
			updateTable,
		})
	}

	const baseTableConfig = {
		requestUrl: newsManageApi.getNewsList,
		columns: getColumns(),
	}

	return (
		<Box>
			<LayoutSlot className="newsLayout">
				<div>
					<OperatingArea>
						<Button type="primary" onClick={publicNews}>
							发布资讯
						</Button>
					</OperatingArea>
					<BaseTable ref={tabRef} {...baseTableConfig} />
				</div>
				<AddOrEditNews {...{ modalConfig, setModalConfig }} />
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div`
	.newsLayout {
		background: #fff;
		padding: 10px;
	}
`

export default NewsList
