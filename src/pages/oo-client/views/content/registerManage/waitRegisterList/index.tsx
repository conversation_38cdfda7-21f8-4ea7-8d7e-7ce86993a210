import React, { useState, useEffect } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import styled from 'styled-components'
import SearchBar from '@oo/components/SearchBar'
import { businessType, registerStatus, RegisterMethod } from '@globalConfig/registerManageConfig'
import OperatingArea from '@src/globalComponents/OperatingArea'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { message, Modal, Space } from 'antd'
import { InfoCircleFilled } from '@ant-design/icons'
import ReplenishRegisterModal from './components/replenishRegisterModal'
import { registerManageModuleApi } from '@oo/api'
import { formatTableData } from '@utils/format'

const WaitRegisterList: React.FC = () => {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [searchParams, setSearchParams] = useState({})
	const [dataInfo, setDataInfo] = useState({})
	const [loading, setLoading] = useState(false)
	const [replenishRegisterVisible, setReplenishRegisterVisible] = useState(false)
	const [replenishType, setReplenishType] = useState<string>(RegisterMethod.OFFLINE)

	useEffect(() => {
		getRegisterList()
	}, [pagination.current, pagination.pageSize, searchParams])
	const getRegisterList = () => {
		setLoading(true)
		registerManageModuleApi
			.getPendRegistration({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...searchParams,
			})
			.then(res => {
				pagination.total = res['total']
				if (res && res.list) {
					setDataSource(formatTableData.addKey(res.list, 'id'))
				} else {
					setDataSource([])
				}
				setLoading(false)
			})
			.catch(err => {
				setLoading(false)
				pagination.total = 0
				setDataSource([])
			})
	}
	const onPageChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const onSizeChange = (size: number) => {
		console.log(size)
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	const getColumns = () => {
		const columnsDic = {
			operation: {
				width: 250,
				render: (text: any, record: any) => {
					return (
						<Space>
							<span className="link" onClick={() => replenishRegister(RegisterMethod.OFFLINE, record)}>
								线下补登记
							</span>
							<span className="link" onClick={() => replenishRegister(RegisterMethod.ONLINE, record)}>
								线上补登记
							</span>
							<span className="link" onClick={() => noReplenish(record)}>
								不登记
							</span>
						</Space>
					)
				},
			},
		}
		return getColumnsByPageName('waitRegisterList', columnsDic)
	}
	const replenishRegister = (type: string, initDataInfo) => {
		setReplenishRegisterVisible(true)
		setReplenishType(type)
		setDataInfo(initDataInfo)
	}
	const noReplenish = record => {
		Modal.confirm({
			title: '确定不进行登记吗?',
			icon: <InfoCircleFilled color="#49a9ee" />,
			content: '点击确定后，该笔数据将会从待补登记列表移除',
			onOk() {
				setLoading(true)
				registerManageModuleApi
					.supplementRegister({
						financeNumber: record?.applicationNumber,
						financeType: 'PLEDGE',
						type: RegisterMethod.NON_REGISTRATION,
					})
					.then(() => {
						message.success('提交成功')
						setLoading(false)
						getRegisterList()
					})
					.catch(err => {
						console.log(err)
						setLoading(false)
					})
			},
		})
	}
	const onSubmit = (params: any) => {
		pagination.current = 1
		setSearchParams(params)
	}
	const onClear = () => {
		setSearchParams({})
	}
	const replenishRegisterOnOk = () => {
		setReplenishRegisterVisible(false)
		getRegisterList()
	}
	return (
		<BoxStyle>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar pageName="waitRegisterList" optionData={{ businessType, registerStatus }} onSubmit={onSubmit} onClear={onClear}></SearchBar>
					</OperatingArea>
					<BaseTable
						onPageChange={onPageChange}
						onSizeChange={onSizeChange}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						columns={getColumns()}
					></BaseTable>
				</div>
			</LayoutSlot>
			<ReplenishRegisterModal
				visible={replenishRegisterVisible}
				dataInfo={dataInfo}
				onCancel={() => {
					setReplenishRegisterVisible(false)
					setLoading(false)
				}}
				onOk={replenishRegisterOnOk}
				replenishType={replenishType}
			/>
		</BoxStyle>
	)
}

const BoxStyle = styled.div`
	width: 100%;
	height: 100%;
`

export default WaitRegisterList
