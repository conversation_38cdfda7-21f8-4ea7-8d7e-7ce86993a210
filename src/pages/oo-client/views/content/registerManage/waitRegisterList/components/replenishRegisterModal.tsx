import React, { ReactElement, useEffect } from 'react'
import { Modal, Form, Input, Row, Col, Alert, message, Tooltip } from 'antd'
import styled from 'styled-components'
import SmallTitle from '@src/globalComponents/SmallTitle'
import { RegisterMethod, businessTypeValue } from '@globalConfig/registerManageConfig'
import { registerManageModuleApi } from '@src/pages/oo-client/api'

const layout = {
	labelCol: { span: 8 },
	wrapperCol: { span: 14 },
}

interface Props {
	dataInfo: any
	visible: boolean
	onCancel: () => any
	onOk: () => any
	//判断线上还是线下
	replenishType: string
}

function ReplenishRegisterModal({ visible, onCancel, dataInfo, onOk, replenishType }: Props): ReactElement {
	const [form] = Form.useForm()
	const [confirmLoading, setConfirmLoading] = React.useState(false)
	useEffect(() => {
		if (!visible) form.resetFields()
	}, [visible])
	const submitData = async () => {
		setConfirmLoading(true)
		let params = {}
		if (replenishType === RegisterMethod.OFFLINE) {
			let res = await form.validateFields().catch(error => {
				setConfirmLoading(false)
				return
			})
			setConfirmLoading(false)
			params = {
				initRegistrationNo: res.initRegistrationNo,
				financeNumber: dataInfo?.applicationNumber,
				type: RegisterMethod.OFFLINE,
				financeType: 'PLEDGE',
			}
		} else {
			params = {
				financeNumber: dataInfo?.applicationNumber,
				type: RegisterMethod.ONLINE,
				financeType: 'PLEDGE',
			}
		}
		registerManageModuleApi
			.supplementRegister(params)
			.then(() => {
				form.resetFields()
				onOk()
				setConfirmLoading(false)
				message.success('提交成功')
			})
			.catch(err => {
				setConfirmLoading(false)
			})
	}
	return (
		<Modal
			open={visible}
			title={replenishType === RegisterMethod.OFFLINE ? '线下补登记' : '确定进行线上补登记?'}
			onCancel={onCancel}
			onOk={submitData}
			okText="确定"
			width={600}
			confirmLoading={confirmLoading}
		>
			<Wrapper className="main-wrapper">
				<SmallTitle text="融资信息" />
				<Row style={{ marginBottom: '15px' }}>
					<Col className="rowName" span={8}>
						融资申请编号：
					</Col>
					<Col span={14}>{dataInfo?.applicationNumber}</Col>
				</Row>
				<Row style={{ marginBottom: '15px' }}>
					<Col className="rowName" span={8}>
						融资企业：
					</Col>
					<Col span={14} className="finance-company-name">
						<Tooltip placement="topLeft" title={dataInfo?.companyName}>
							{dataInfo?.companyName}
						</Tooltip>
					</Col>
				</Row>
				<Row style={{ marginBottom: '15px' }}>
					<Col className="rowName" span={8}>
						融资创建日期：
					</Col>
					<Col span={14}>{dataInfo?.createDate}</Col>
				</Row>
				<SmallTitle text="登记信息" />
				{replenishType === RegisterMethod.ONLINE ? (
					<div>
						<Row style={{ marginBottom: '15px' }}>
							<Col className="rowName" span={8}>
								交易业务类型：
							</Col>
							<Col span={14}>{businessTypeValue?.A00200}</Col>
						</Row>
						<Row style={{ marginBottom: '15px' }}>
							<Col className="rowName" span={8}>
								登记期限（月）：
							</Col>
							<Col span={14}>12</Col>
						</Row>
					</div>
				) : null}
				{replenishType === RegisterMethod.OFFLINE ? (
					<Form {...layout} form={form}>
						<Form.Item
							name="initRegistrationNo"
							className="initRegistrationNo"
							label="初始登记编号"
							extra="提示：请输入线下登记时，中登网产生的登记证明编号"
							rules={[
								{
									required: true,
									// pattern: /^(?!-1+(?:\.0+)?$)(?:[1-9]\d{0,9}|0)(?:\.\d{1,2})?$/,
									message: '请输入初始登记编号',
									whitespace: true,
								},
							]}
						>
							<Input placeholder="请输入，最多输入20位" maxLength={20} />
						</Form.Item>
					</Form>
				) : null}
				{replenishType === RegisterMethod.ONLINE ? (
					<Alert message="系统将自动抓取该笔融资申请的出让人信息、受让人信息、财产信息，进行初始登记" type="info" showIcon />
				) : (
					''
				)}
			</Wrapper>
		</Modal>
	)
}

const Wrapper = styled.div`
	padding: 30px;
	.rowName {
		text-align: right;
	}
	.quotaInCent {
		margin-bottom: 20px;
		.ant-form-item-explain-error {
			position: absolute;
			top: 55px;
		}
	}
	.finance-company-name {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
`

export default ReplenishRegisterModal
