import React from 'react'
import { getColumnsByPageName } from '@oo/config/table-columns-config'
import { registerManageModuleApi } from '@oo/api'
import SearchBar from '@oo/components/SearchBar'
import CommonRegisterList from '@globalComponents/cross-platform-page/registerManage/registerList'

const RegisterList: React.FC = () => {
	const apiModule = {
		getRegisterList: registerManageModuleApi.getRegisterList,
		downloadRegisterAttachment: registerManageModuleApi.downloadRegisterAttachment,
	}
	return (
		<CommonRegisterList
			apiModule={apiModule}
			getColumnsByPageName={getColumnsByPageName}
			SearchBar={SearchBar}
			detailUrl={'/content/registerManage/registerDetail'}
			changRecordDetailUrl={'/content/registerManage/changeDetail'}
		></CommonRegisterList>
	)
}
export default RegisterList
