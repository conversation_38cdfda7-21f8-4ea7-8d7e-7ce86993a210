import React from 'react'
import CommonChangeDetail from '@globalComponents/cross-platform-page/registerManage/changeDetail'
import { registerManageModuleApi } from '@src/pages/oo-client/api'

interface IProps {
	changeRecordList: Function
	terminateDetail: Function
	downloadRegisterAttachment: Function
}
const ChangeDetail: React.FC = () => {
	const apiModule: IProps = {
		changeRecordList: registerManageModuleApi.changeRecordList,
		terminateDetail: registerManageModuleApi.terminateDetail,
		downloadRegisterAttachment: registerManageModuleApi.downloadRegisterAttachment,
	}
	return <CommonChangeDetail apiModule={apiModule}></CommonChangeDetail>
}
export default ChangeDetail
