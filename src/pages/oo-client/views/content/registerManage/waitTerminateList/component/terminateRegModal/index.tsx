import React, { forwardRef, useEffect, useState } from 'react'
import { WxModal, WxModalProps, useModalParams } from '@cnpm/wx-rc/components/index'
import SmallTitle from '@src/globalComponents/SmallTitle'
import WxDetailDisplay from '@src/globalComponents/wxDetailDisplay/wxDetailDisplay'
import { getNotNullVal } from '@src/utils/util'
import { renderDate_ } from '@oo/config/TableColumnsRender'
import { invalidVal } from '@src/globalBiz/gBiz'
import styled from 'styled-components'
import { Form, Input, Select } from 'antd'
import { useForm } from 'antd/lib/form/Form'
import { TerminateMethod, TerminateRegReasonCode, TerminateRegReasonList, TerminateRegReasonMap } from '@globalConfig/registerManageConfig'

const OfflineTerminateRegModal = forwardRef((props, modalRef: any) => {
	const [loading, setLoading] = useState(false)
	const { open, record, terminateMethod, terminateRequest, tableRef } = useModalParams(modalRef)
	const [form] = useForm()
	//监听注销原因字段，如果注销原因是其他导致所登记权利消灭的情形，需要填写注销原因
	const reason = Form.useWatch('reason', form)
	const displayFields: any = [
		{
			key: 'applicationNumber',
			note: '融资申请编号:',
			value: '',
			span: 24,
			format: getNotNullVal,
		},
		{
			key: 'companyName',
			note: '融资企业:',
			value: '',
			span: 24,
			format: getNotNullVal,
		},
		{
			key: 'createTime',
			note: '融资创建日期:',
			value: '',
			span: 24,
			format: renderDate_,
		},
	]
	const [fields, setFields] = useState(displayFields)

	useEffect(() => {
		if (open) {
			//组装登记基本信息数据
			for (let item of displayFields) {
				if (item.format) {
					item.value = item.format(record[item.key])
				} else {
					item.value = record[item.key] || invalidVal
				}
			}
			setFields([...displayFields])
		}
	}, [open])

	//请求 - 线上、线下注销
	const onOfflineTerminateReg = async () => {
		let values = await form.validateFields()
		if (values) {
			setLoading(true)
			let { fiUuid, financeUuid, applicationNumber, zdwRegNo } = record
			let params = Object.assign(
				{},
				{ ...values },
				{
					fiUuid: fiUuid,
					financeUuid: financeUuid,
					financeNumber: applicationNumber,
					zdwInitNo: zdwRegNo,
				}
			)
			let res = await terminateRequest(params).catch(err => {
				setLoading(false)
				console.log(err)
			})
			if (res) {
				setLoading(false)
				form.resetFields()
				modalRef?.current?.hide()
				tableRef?.current?.updateTable()
			}
		}
	}

	const modalProps: WxModalProps = {
		modalProps: {
			title: terminateMethod === TerminateMethod.OFFLINE ? '线下注销' : '线上注销',
			confirmLoading: loading,
			width: 600,
			onOk: onOfflineTerminateReg,
			onCancel: () => {
				form.resetFields()
				modalRef?.current?.hide()
			},
		},
	}
	return (
		<div>
			<WxModal {...modalProps} ref={modalRef}>
				<ModalWrapper>
					<SmallTitle text="融资信息"></SmallTitle>
					<WxDetailDisplay colNum={1} fields={fields}></WxDetailDisplay>
					<SmallTitle text="登记信息"></SmallTitle>
					{terminateMethod === TerminateMethod.OFFLINE ? (
						<Form labelCol={{ span: 6 }} wrapperCol={{ span: 12 }} form={form} autoComplete="off">
							<Form.Item
								label="登记证明编号："
								name="regNo"
								extra="提示：请输入线下注销登记产生的登记证明编号"
								rules={[{ required: true, message: '请输入登记证明编号' }]}
							>
								<Input placeholder="请输入，最多输入20位" maxLength={20} />
							</Form.Item>
							<Form.Item label="注销原因：" name="reason" rules={[{ required: true, message: '请选择注销原因' }]}>
								<Select placeholder="请选择" getPopupContainer={(triggerNode: any) => triggerNode.parentNode}>
									{TerminateRegReasonList.map(item => {
										return (
											<Select.Option value={item.value} key={item.value}>
												{item.label}
											</Select.Option>
										)
									})}
								</Select>
							</Form.Item>
							{reason === TerminateRegReasonCode['04'] ? (
								<Form.Item
									label="注销原因描述："
									name="reasonDesc"
									rules={[{ required: true, message: '请输入注销原因描述，长度不超过50字', whitespace: true }]}
								>
									<Input.TextArea rows={4} maxLength={50} placeholder="请输入，最多输入50位" />
								</Form.Item>
							) : (
								''
							)}
						</Form>
					) : (
						<Form labelCol={{ span: 6 }} wrapperCol={{ span: 12 }} form={form} autoComplete="off">
							<Form.Item label="注销原因：" name="reason" initialValue={TerminateRegReasonCode['03']} rules={[{ required: true, message: '请选择注销原因' }]}>
								<span>{TerminateRegReasonMap[TerminateRegReasonCode['03']]?.value}</span>
							</Form.Item>
						</Form>
					)}
				</ModalWrapper>
			</WxModal>
		</div>
	)
})

const ModalWrapper = styled.div`
	margin: 40px;
	.wxDetailDisplay {
		padding: 10px 20px 10px 20px;
	}
	.wxDetailDisplay .wxNote {
		font-weight: 400;
		color: rgba(0, 0, 0, 0.85);
	}
	.ant-form-item-extra {
		font-size: 12px;
	}
`

export default OfflineTerminateRegModal
