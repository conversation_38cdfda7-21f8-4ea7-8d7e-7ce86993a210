import React, { useRef } from 'react'
import SearchTable from '@src/globalComponents/new-widget/wx-table/searchTable'
import FormItem from 'antd/lib/form/FormItem'
import { getTabTit } from '@globalBiz/gBiz'
import { getNotNullVal } from '@src/utils/util'
import { registerManageModuleApi } from '@oo/api'
import { renderAmount, renderDate_, renderMapFinanceStatus } from '@src/pages/oo-client/config/TableColumnsRender'
import { Input, Space } from 'antd'
import TerminateRegModal from './component/terminateRegModal'
import { TerminateMethod } from '@globalConfig/registerManageConfig'

const WaitTerminateList = () => {
	const terminateRef = useRef(null)
	const tableRef = useRef(null)
	const columns = [
		getTabTit(['融资申请编号', 'applicationNumber', 220, true], getNotNullVal),
		getTabTit(['融资企业', 'companyName', null, true], getNotNullVal),
		getTabTit(['融资金额(￥)', 'inputFinanceAmountInYuan', null, true], renderAmount),
		getTabTit(['金融机构', 'fiName', null, true], getNotNullVal),
		getTabTit(['状态', 'status', null, true], renderMapFinanceStatus),
		getTabTit(['创建日期', 'createTime', null, true], renderDate_),
		getTabTit(['操作', 'operate'], (text, record) => {
			return (
				<div>
					<Space>
						<span
							className="link"
							onClick={() => {
								terminateRef?.current?.show({
									record,
									open: true,
									terminateMethod: TerminateMethod.OFFLINE,
									terminateRequest: registerManageModuleApi.offlineTerminateRegister,
									tableRef,
								})
							}}
						>
							线下注销
						</span>
						<span
							className="link"
							onClick={() => {
								terminateRef?.current?.show({
									record,
									open: true,
									terminateMethod: TerminateMethod.ONLINE,
									terminateRequest: registerManageModuleApi.onlineTerminateRegister,
									tableRef,
								})
							}}
						>
							线上注销
						</span>
					</Space>
				</div>
			)
		}),
	]
	const tableConfig = {
		table: {
			columns,
			requestUrl: registerManageModuleApi.getWaitTerminateList,
		},
		searchBar: {
			formItems: (
				<>
					<FormItem name="applicationNumber" label="">
						<Input placeholder="融资申请编号"></Input>
					</FormItem>
				</>
			),
		},
	}

	return (
		<div>
			<div className="card-wrapper">
				<SearchTable {...tableConfig} ref={tableRef}></SearchTable>
				<TerminateRegModal ref={terminateRef}></TerminateRegModal>
			</div>
		</div>
	)
}

export default WaitTerminateList
