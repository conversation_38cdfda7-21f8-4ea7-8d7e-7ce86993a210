import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import OperatingArea from '@src/globalComponents/OperatingArea'

import SearchBar from '@src/pages/oo-client/components/SearchBar'
import styled from 'styled-components'
import { billModuleApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { formatTableData } from '@utils/format'
import BillDetailsModal from './components/billDetailsModal'
import moment from 'moment'
import { Tooltip } from 'antd'
const BillList = () => {
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [searchParams, setSearchParams] = useState({})
	const [dataSource, setDataSource] = useState<any[]>([])
	const [loading, setLoading] = useState(false)
	const [billInfo, setbillInfo] = useState({
		visible: false,
		info: null,
	})
	useEffect(() => {
		getOriginanlList()
	}, [pagination.current, pagination.pageSize, searchParams])
	const getOriginanlList = () => {
		setLoading(true)
		billModuleApi
			.getBillList({
				current: pagination.current,
				size: pagination.pageSize,
				desc: ['id'],
				...searchParams,
			})
			.then(result => {
				if (result) {
					pagination.total = result['total']
					if (result.list) {
						setDataSource(formatTableData.addKey(result['list']))
					} else {
						setDataSource([])
					}
				}
				setLoading(false)
			})
			.catch(() => {
				setLoading(false)
			})
	}

	//search 提交 事件
	const onSubmit = (params: { dueDate: number[]; billNumber: string; uploader: string }) => {
		pagination.current = 1
		setSearchParams({
			billNumber: params?.billNumber,
			uploader: params?.uploader,
			beginDate: params?.dueDate[0] && moment(params?.dueDate[0]).format('YYYY-MM-DD'),
			endDate: params?.dueDate[1] && moment(params?.dueDate[1]).format('YYYY-MM-DD'),
		})
	}
	//Search 重置
	const onClear = () => {
		pagination.current = 1
		setSearchParams({})
	}
	const renderBillNumber = (text: string, record: any) => (
		<Tooltip title={text}>
			<span
				className="link"
				onClick={() => {
					setbillInfo({
						visible: true,
						info: record,
					})
				}}
			>
				{text.substring(0, 4) + '...' + text.substring(text.length - 6)}
			</span>
		</Tooltip>
	)
	const getColumns = () => {
		const columnsDic = {
			billNumber: { render: renderBillNumber },
		}
		return getColumnsByPageName('billList', columnsDic)
	}

	function handleChange(current: number) {
		pagination.current = current
		setPagination({ ...pagination })
	}
	function handleSizeChange(size: number) {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	const cancelModal = () => {
		setbillInfo({
			visible: false,
			info: null,
		})
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar showLabel={false} pageName={'billList'} onClear={onClear} onSubmit={onSubmit} />
					</OperatingArea>
					<BaseTable
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
					/>
					{billInfo?.visible && <BillDetailsModal {...billInfo} cancelModal={cancelModal} />}
				</div>
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div`
	.options_btn {
		span {
			display: inline-block;
			margin: 0 20px;
		}
	}
`

export default BillList
