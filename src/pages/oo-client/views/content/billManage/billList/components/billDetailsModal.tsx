import React, { useState, useEffect } from 'react'
import { Modal, Descriptions, Image } from 'antd'
import SmallTitle from '@src/globalComponents/SmallTitle'
import { billModuleApi } from '@src/pages/oo-client/api'
import styled from 'styled-components'
export default function BillDetailsModal(props) {
	const { visible, cancelModal } = props
	const [info, setInfo] = useState(props?.info)
	const getbillInfo = () => {
		billModuleApi
			.billdetails({
				id: info.id,
			})
			.then(result => {
				setInfo(result)
			})
	}
	useEffect(() => {
		getbillInfo()
	}, [])
	const detailsConfig = [
		{
			title: '基本信息',
			itemList: [
				{
					name: 'billNumber',
					label: '票据编号',
				},
				{
					name: 'billTypeValue',
					label: '票据类型',
				},
				{
					name: 'billBeginDate',
					label: '出票日期',
				},
				{
					name: 'billEndDate',
					label: '到期日期',
				},
			],
		},
		{
			title: '出票人',
			itemList: [
				{
					name: 'saleFullName',
					label: '全称',
				},
			],
		},
		{
			title: '承兑人',
			itemList: [
				{
					name: 'cdrFullName',
					label: '全称',
				},
				{
					name: 'cdrBankNum',
					label: '开户行行号',
				},
			],
		},
		{
			title: '收款人',
			itemList: [
				{
					name: 'payeeFullName',
					label: '全称',
				},
			],
		},
	]
	return (
		<Modal title="票据详情" open={visible} onCancel={cancelModal} footer={null} width="860px" className="company-preview">
			<Style className="card-wrapper">
				<SmallTitle>票据正面</SmallTitle>
				<div className="image">
					<Image src={info?.billUrlFullPath} width={500} />
				</div>
				<SmallTitle>风险评估</SmallTitle>
				<Descriptions title="异常信息">
					<Descriptions.Item label="">{info?.abnormalMessage || '-'}</Descriptions.Item>
				</Descriptions>
				<SmallTitle>识别结果</SmallTitle>
				{detailsConfig.map((descriptions, index) => {
					return (
						<Descriptions key={index} title={descriptions.title} column={2}>
							{descriptions.itemList.map((item, descriptionsIndex) => {
								return (
									<Descriptions.Item key={descriptionsIndex} label={item.label}>
										{info[item.name] || '-'}
									</Descriptions.Item>
								)
							})}
						</Descriptions>
					)
				})}
			</Style>
		</Modal>
	)
}
const Style = styled.div`
	.image {
		margin: 20px 0;
	}
`
