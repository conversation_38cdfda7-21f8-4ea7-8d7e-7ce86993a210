import React, { useEffect, useState } from 'react'
import WxDetailDisplay from '@globalComponents/wxDetailDisplay/wxDetailDisplay'
import { Modal } from 'antd'
import SmallTitle from '@src/globalComponents/SmallTitle'
import PDFViewer from '@globalComponents/PDFViewer'
import { renderAmount } from '@src/pages/oo-client/config/TableColumnsRender'
import { stampToTime } from '@src/utils/timeFilter'
import { invoiceCheckStatusMap } from '@src/globalBiz/invoiceBizModule'
import { invalidVal } from '@src/globalBiz/gBiz'
export default function DetailsModal(props) {
	const { visible, dataSource, onCancel } = props
	const [pdfVisible, setVisible] = useState(false)
	const [pdfPath, setPdfPath] = useState('')
	const showPDfView = (path: string) => {
		setPdfPath(path)
		setVisible(true)
	}
	const invoiceContentFields: DisplayField[] = [
		{
			key: 'invoiceType',
			note: '发票类型:',
			value: '',
			span: 12,
			format: (val: string) => {
				if (val) {
					const invoiceInfo = [
						{ code: 'VATSpecial', name: '增值税专用发票', ocrCode: ['01'] },
						{ code: 'VehicleSales', name: '机动车销售统一发票', ocrCode: ['03'] },
						{ code: 'VATCommon', name: '增值税普通发票', ocrCode: ['04', '10', '11', '14'] },
						{ code: 'ELEI_SVATI', name: '电子发票（增值税专用发票）', ocrCode: ['03'] },
						{ code: 'ELEI_OI', name: '电子发票（普通发票）', ocrCode: ['03'] },
					]
					const obj = invoiceInfo.find(i => i.code === val)
					return obj && obj.name
				}
				return '--'
			},
		},
		{
			key: 'checkFlag',
			note: '验真状态:',
			value: '',
			span: 12,
			format: value => {
				const type = ['验真失败', '验真成功', '全部']
				return type[value]
			},
		},
		{
			key: 'buyerName',
			note: '买方名称:',
			value: '',
			span: 12,
		},
		{
			key: 'supplierName',
			note: '卖方名称:',
			value: '',
			span: 12,
		},
		{
			key: 'invoiceCode',
			note: '发票代码:',
			value: '',
			span: 12,
		},
		{
			key: 'invoiceNumber',
			note: '发票号码:',
			value: '',
			span: 12,
		},
		{
			key: 'pretaxAmount',
			note: '含税金额:',
			value: '',
			span: 12,
			format: renderAmount,
		},
		{
			key: 'amount',
			note: '不含税金额:',
			value: '',
			span: 12,
			format: renderAmount,
		},
		{
			key: 'checkCode',
			note: '校验码后6位:',
			value: '',
			span: 12,
		},
		{
			key: 'invoiceDate',
			note: '开票日期:',
			value: '',
			span: 12,
			format: value => {
				return stampToTime(value, 6)
			},
		},
		{
			key: 'fileUrl',
			note: '发票附件:',
			value: '',
			span: 12,
			format: value => {
				if (!value) {
					return '--'
				}
				const data = JSON.parse(value)
				let name = ''
				let path = ''
				Object.keys(data).forEach(i => {
					name = i
					path = data[i]
				})
				return (
					<span className="link" onClick={() => showPDfView(path)}>
						{name}
					</span>
				)
			},
		},
		{
			key: 'checkState',
			note: '发票状态:',
			value: '',
			span: 12,
			format: value => {
				return invoiceCheckStatusMap[value] || invalidVal
			},
		},
	]
	const othersFields: DisplayField[] = [
		{
			key: 'createTime',
			note: '创建时间:',
			value: '',
			span: 12,
			format: value => {
				return stampToTime(value, 6)
			},
		},
		{
			key: 'operatorCompanyName',
			note: '创建企业:',
			value: '',
			span: 12,
		},
		{
			key: 'operatorUser',
			note: '创建用户:',
			value: '',
			span: 12,
		},
	]
	const [invoiceContentField, setInvoiceContentField] = useState<DisplayField[]>([])
	const [othersField, setOthersField] = useState<DisplayField[]>([])
	useEffect(() => {
		if (visible === true) {
			for (let item of invoiceContentFields) {
				item.value = dataSource[item.key]
				if (item.format) {
					item.value = item.format(dataSource[item.key])
				}
			}
			setInvoiceContentField([...invoiceContentFields])
			for (let item of othersFields) {
				item.value = dataSource[item.key]
				if (item.format) {
					item.value = item.format(dataSource[item.key])
				}
			}
			setOthersField([...othersFields])
		}
	}, [])
	return (
		<Modal title="查看发票" open={visible} onCancel={onCancel} footer={null} width="860px">
			<div className="main-wrapper" style={{ marginTop: '20px' }}>
				<SmallTitle text="发票信息"></SmallTitle>
				<WxDetailDisplay colNum={2} fields={invoiceContentField}></WxDetailDisplay>
				<SmallTitle text="创建者信息"></SmallTitle>
				<WxDetailDisplay colNum={2} fields={othersField}></WxDetailDisplay>
			</div>
			<PDFViewer title="" visible={pdfVisible} onCancel={() => setVisible(false)} pdfUrl={pdfPath} />
		</Modal>
	)
}
