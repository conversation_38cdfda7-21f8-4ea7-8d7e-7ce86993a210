import React, { useRef, useState } from 'react'
import SearchTable from '@globalComponents/new-widget/wx-table/searchTable'
import FormItem from 'antd/es/form/FormItem'
import { Button, DatePicker, Input, Select, Space, Tooltip, message } from 'antd'
import { getTabTit, invalidVal } from '@globalBiz/gBiz'
import { renderAmount } from '@src/pages/oo-client/config/TableColumnsRender'
import { stampToTime, timeToStamp } from '@src/utils/timeFilter'
import { couAssetsManageApi } from '@oo/api'
import styled from 'styled-components'
import DetailsModal from './components/detailsModal'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import { ViewReasonModal, getInvoicCheckStatusCols, invoiceCheckStatusOptions } from '@src/globalBiz/invoiceBizModule'

const { RangePicker } = DatePicker
const { Option } = Select

export default function Index() {
	const searchBarTableRef = useRef(null)
	const [visible, setVisible] = useState<boolean>(false)
	const [invoiceData, setInvoiceData] = useState()
	const [exportLoading, setExportLoading] = useState(false)
	const viewReasonModalRef = useRef(null)
	const invoiceColsObj = getInvoicCheckStatusCols(record => {
		//查看错误详情
		viewReasonModalRef?.current?.show({
			reason: record?.checkMsg,
		})
	})
	const columns = [
		getTabTit(['发票号码', 'invoiceNumber'], (text, record) => {
			if (text === undefined || text === null) {
				return invalidVal
			}
			return (
				<Tooltip title={text}>
					<span
						className="link"
						title={text}
						onClick={() => {
							setVisible(true)
							setInvoiceData(record)
						}}
					>
						{text}
					</span>
				</Tooltip>
			)
		}),
		getTabTit(['卖方名称', 'supplierName', null, true]),
		getTabTit(['买方名称', 'buyerName', null, true]),
		getTabTit(['含税金额（￥）', 'pretaxAmount'], renderAmount),
		getTabTit(['开票日期', 'invoiceDate'], text => {
			return stampToTime(text, 6)
		}),
		invoiceColsObj.checkFlagWithReason,
		invoiceColsObj.checkState,
		getTabTit(['创建时间', 'createTime'], text => {
			if ([undefined, null].includes(text)) {
				return invalidVal
			}
			let val = stampToTime(text, 4)
			return (
				<Tooltip title={val} placement="topLeft">
					<span className="ellipsis">{val}</span>
				</Tooltip>
			)
		}),
		getTabTit(['创建企业', 'operatorCompanyName'], text => {
			if ([undefined, null].includes(text)) {
				return invalidVal
			}
			return (
				<Tooltip title={text} placement="topLeft">
					<span className="ellipsis">{text}</span>
				</Tooltip>
			)
		}),
		getTabTit(['创建用户', 'operatorUser'], text => {
			if ([undefined, null].includes(text)) {
				return invalidVal
			}
			return (
				<Tooltip title={text} placement="topLeft">
					<span className="ellipsis">{text}</span>
				</Tooltip>
			)
		}),
	]
	const tableConfig = {
		table: {
			columns,
			requestUrl: couAssetsManageApi.getInvoiceList,
		},
		searchBar: {
			transform: param => {
				if (param && param.createDate && param.createDate.length === 2) {
					param.endCreateTime = timeToStamp(param.createDate[1], 'end')
					param.startCreateTime = timeToStamp(param.createDate[0], 'begin')
					delete param.createDate
				}
				return param
			},
			formItems: (
				<>
					<FormItem name="invoiceNumber" key="invoiceNumber">
						<Input placeholder="发票号码" />
					</FormItem>
					<FormItem name="sellerName" key="sellerName">
						<Input placeholder="卖方名称" />
					</FormItem>
					<FormItem name="buyerName" key="buyerName">
						<Input placeholder="买方名称" />
					</FormItem>
					<FormItem name="checkFlag" key="checkFlag">
						<Select placeholder="验真状态" allowClear>
							{/* <Option key="2">全部</Option> */}
							<Option key="1">验真成功</Option>
							<Option key="0">验真失败</Option>
							<Option key="9">验真中</Option>
						</Select>
					</FormItem>
					<FormItem name="createDate" label="" className="createDate">
						<RangePicker
							style={{ width: '100%' }}
							getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
							placeholder={['创建时间（开始)', '创建时间（结束)']}
							format="YYYY-MM-DD"
						/>
					</FormItem>
					<FormItem name="checkState" key="checkState">
						<Select
							placeholder="发票状态"
							options={invoiceCheckStatusOptions.map(item => {
								return {
									...item,
									value: item.key,
									label: item.name,
								}
							})}
							allowClear
						></Select>
					</FormItem>
				</>
			),
		},
	}
	const exportFile = async () => {
		const total = searchBarTableRef?.current?.getTableDataDetail()?.total
		if (total > 1000 || Number(total) > 1000) {
			message.error('单次最多导出1000条记录')
			return false
		} else if (total === 0 || Number(total) === 0) {
			message.error('可下载的数据为空，请检查您的查询条件')
			return false
		}
		const fileName = `发票数据.xlsx`
		const searchParams = searchBarTableRef?.current?.getValues()
		couAssetsManageApi
			.exportInvoiceList(searchParams)
			.then((response: any) => {
				let type = 'application/vnd.ms-excel'
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}
	return (
		<Box>
			<ViewReasonModal ref={viewReasonModalRef} />
			<div className="card-wrapper">
				<SearchTable {...tableConfig} ref={searchBarTableRef}>
					<div className="btnDom">
						<Space>
							{/* <Button type="primary" onClick={() => exportFile()} loading={exportLoading}>
								导出
							</Button> */}
						</Space>
					</div>
				</SearchTable>
			</div>
			{visible && <DetailsModal visible={visible} onCancel={() => setVisible(false)} dataSource={invoiceData} />}
		</Box>
	)
}
const Box = styled.div`
	width: 100%;
	height: 100%;
	.form .ant-form-item.createDate {
		width: 300px;
	}
	.btnDom {
		margin-bottom: 20px;
	}
`
