import LayoutSlot from '@src/globalComponents/LayoutSlot'
import React, { useRef, useState } from 'react'
import styled from 'styled-components'
import SearchTable from '@globalComponents/new-widget/wx-table/searchTable'
import { getTabTit, invalidVal } from '@globalBiz/gBiz'
import { couAssetsManageApi } from '@oo/api'
import { Button, DatePicker, Form, Input, Tooltip, message } from 'antd'
import { numberToThousands, stampToTime, timeToStamp } from '@src/utils/timeFilter'
const FormItem = Form.Item
const RangePicker = DatePicker.RangePicker
import ContractDetailModal from './components/contractDetailModal'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'

const Contract = () => {
	const modalRef = useRef(null)
	const handleShowModal = record => {
		modalRef.current.showModal(record)
	}
	const searchRef = useRef(null)
	const [loading, setLoading] = useState(false)
	const searchTableProps = {
		table: {
			columns: [
				getTabTit(['合同编号', 'contractCode'], (text, record) => {
					if ([undefined, null].includes(text)) {
						return invalidVal
					}
					return (
						<span className="link" onClick={() => handleShowModal(record)}>
							{text}
						</span>
					)
				}),
				getTabTit(['合同名称', 'name', null, true]),
				getTabTit(['买方名称', 'companyBuyerName', null, true]),
				getTabTit(['卖方名称', 'companySellerName', null, true]),
				getTabTit(['合同金额（￥）', 'amount'], text => {
					if ([undefined, null].includes(text)) {
						return invalidVal
					}
					let val = numberToThousands(parseFloat(text))
					return (
						<Tooltip title={val} placement="topLeft">
							<span className="ellipsis">{val}</span>
						</Tooltip>
					)
				}),
				getTabTit(['签订日期', 'signDate'], (text: number) => {
					if ([undefined, null].includes(text)) {
						return invalidVal
					}
					let val = stampToTime(text, 6)
					return (
						<Tooltip title={val} placement="topLeft">
							<span className="ellipsis">{val}</span>
						</Tooltip>
					)
				}),
				getTabTit(['创建时间', 'createTime'], (text: number) => {
					if ([undefined, null].includes(text)) {
						return invalidVal
					}
					let val = stampToTime(text, 4)
					return (
						<Tooltip title={val} placement="topLeft">
							<span className="ellipsis">{val}</span>
						</Tooltip>
					)
				}),
				getTabTit(['创建企业', 'operatorCompanyName'], text => {
					if ([undefined, null].includes(text)) {
						return invalidVal
					}
					return (
						<Tooltip title={text} placement="topLeft">
							<span className="ellipsis">{text}</span>
						</Tooltip>
					)
				}),
				getTabTit(['创建用户', 'operatorUser'], text => {
					if ([undefined, null].includes(text)) {
						return invalidVal
					}
					return (
						<Tooltip title={text} placement="topLeft">
							<span className="ellipsis">{text}</span>
						</Tooltip>
					)
				}),
			],
			requestUrl: couAssetsManageApi.getContractList,
		},
		searchBar: {
			transform: param => {
				if (param.createDate && param.createDate.length > 1) {
					param.endCreateTime = timeToStamp(param.createDate[1], 'end')
					param.startCreateTime = timeToStamp(param.createDate[0], 'begin')
					delete param.createDate
				}
				return param
			},
			formItems: (
				<>
					<FormItem name="contractCode" key="contractCode">
						<Input placeholder="合同编号" />
					</FormItem>
					<FormItem name="companyBuyerName" key="companyBuyerName">
						<Input placeholder="买方名称" />
					</FormItem>
					<FormItem name="companySellerName" key="companySellerName">
						<Input placeholder="卖方名称" />
					</FormItem>
					<FormItem name="createDate" label="" className="createDate">
						<RangePicker
							getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
							placeholder={['创建时间（开始)', '创建时间（结束)']}
							style={{ width: 300 }}
							format="YYYY-MM-DD"
						/>
					</FormItem>
				</>
			),
		},
	}

	/**
	 * 合同信息导出，单次最多导出1000条记录
	 */
	const onExport = () => {
		let tableDataDetail = searchRef.current.getTableDataDetail()
		if (tableDataDetail && Number(tableDataDetail?.total) === 0) {
			message.error('可下载的数据为空,请检查您的查询条件')
			return
		} else if (tableDataDetail && Number(tableDataDetail?.total) > 1000) {
			message.error('单次最多导出1000条记录')
			return
		}
		setLoading(true)
		let params = searchRef?.current?.getValues() || {}
		couAssetsManageApi
			.exportContractList(params)
			.then((response: any) => {
				downloadFileByFileFlow(response, 'application/vnd.ms-excel', '合同数据.xlsx', () => {
					setLoading(false)
				})
			})
			.catch(e => {
				message.error('下载失败')
				setLoading(false)
			})
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<SearchTable {...searchTableProps} ref={searchRef}>
						<div className="BtnBox">
							{/* <Button
								type="primary"
								loading={loading}
								onClick={() => {
									onExport()
								}}
							>
								导出
							</Button> */}
						</div>
					</SearchTable>
				</div>
				<ContractDetailModal ref={modalRef}></ContractDetailModal>
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.form .ant-form-item.createDate {
		width: 300px;
	}
	.BtnBox {
		margin-bottom: 20px;
	}
`

export default Contract
