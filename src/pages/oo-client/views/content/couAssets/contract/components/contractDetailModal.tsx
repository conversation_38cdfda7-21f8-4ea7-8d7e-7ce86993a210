import SmallTitle from '@src/globalComponents/SmallTitle'
import { Modal } from 'antd'
import React, { useState, forwardRef, useImperativeHandle } from 'react'
import WxDetailDisplay from '@globalComponents/wxDetailDisplay/wxDetailDisplay'
import styled from 'styled-components'
import { invalidVal } from '@src/pages/oo-client/biz/bizIndex'
import { numberToThousands, stampToTime } from '@src/utils/timeFilter'
import { fileList } from '@src/pages/oo-client/config/detailColumn'
import PDFViewer from '@src/globalComponents/PDFViewer'

const ContractDetailModal = (props, ref) => {
	const [isModalVisible, setIsModalVisible] = useState(false)
	const [visible, setVisible] = useState(false)
	const [pdfPath, setPdfPath] = useState('')
	const contractFields: DisplayField[] = [
		{
			note: '合同编号：',
			value: '',
			key: 'contractCode',
		},
		{
			note: '合同名称：',
			value: '',
			key: 'name',
		},
		{
			note: '买方名称：',
			value: '',
			key: 'companyBuyerName',
		},
		{
			note: '卖方名称：',
			value: '',
			key: 'companySellerName',
		},
		{
			note: '产品名称：',
			value: '',
			key: 'productName',
		},
		{
			note: '合同金额：',
			value: '',
			key: 'amount',
			format: text => {
				if ([undefined, null].includes(text)) {
					return invalidVal
				}
				return numberToThousands(parseFloat(text))
			},
		},
		{
			note: '开始日期：',
			value: '',
			key: 'startDate',
			format: text => {
				return stampToTime(text, 6)
			},
		},
		{
			note: '结束日期：',
			value: '',
			key: 'endDate',
			format: text => {
				return stampToTime(text, 6)
			},
		},
		{
			note: '签订日期：',
			value: '',
			key: 'signDate',
			format: text => {
				return stampToTime(text, 6)
			},
		},
		{
			note: '合同扫描件：',
			value: '',
			key: 'fileUrl',
			format: text => {
				return fileList(text, showPDfView)
			},
		},
	]
	const showPDfView = (path: string) => {
		setPdfPath(path)
		setVisible(true)
	}
	const createUserDisplayFields: DisplayField[] = [
		{
			note: '创建时间：',
			value: '',
			key: 'createTime',
			format: text => {
				return stampToTime(text, 4)
			},
		},
		{
			note: '创建企业：',
			value: '',
			key: 'operatorCompanyName',
		},
		{
			note: '创建用户：',
			value: '',
			key: 'operatorUser',
		},
	]
	const [contractInfo, setContractInfo] = useState(contractFields)
	const [userInfo, setUserInfo] = useState(createUserDisplayFields)
	useImperativeHandle(ref, () => {
		return {
			showModal,
			closeModal,
		}
	})
	/**
	 * 显示合同详情
	 */
	const showModal = contract => {
		for (let item of contractFields) {
			item.value = contract[item.key]
			if (item?.format) {
				item.value = item.format(contract[item.key])
			}
		}
		setContractInfo([...contractFields])
		for (let item of createUserDisplayFields) {
			item.value = contract[item.key]
			if (item?.format) {
				item.value = item.format(contract[item.key])
			}
		}
		setUserInfo([...createUserDisplayFields])
		setIsModalVisible(true)
	}
	/**
	 * 关闭合同详情
	 */
	const closeModal = () => {
		setIsModalVisible(false)
	}

	return (
		<Modal width={800} open={isModalVisible} footer={null} onCancel={closeModal} title="查看合同">
			<Box>
				<SmallTitle text="合同信息"></SmallTitle>
				<WxDetailDisplay fields={contractInfo} colNum={2}></WxDetailDisplay>
				<SmallTitle text="创建者信息"></SmallTitle>
				<WxDetailDisplay fields={userInfo} colNum={2}></WxDetailDisplay>
				<PDFViewer title="" visible={visible} onCancel={() => setVisible(false)} pdfUrl={pdfPath} />
			</Box>
		</Modal>
	)
}
const Box = styled.div`
	padding: 20px;
	.fileBox {
		overflow: hidden;
		white-space: break-spaces;
		word-break: break-all;
	}
`
export default forwardRef(ContractDetailModal)
