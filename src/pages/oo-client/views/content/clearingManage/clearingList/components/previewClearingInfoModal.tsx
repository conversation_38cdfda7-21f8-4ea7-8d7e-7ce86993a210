import React, { useState, useEffect } from 'react'
import { Descriptions, Modal, Spin, Row, Col } from 'antd'
import BaseTable from '@src/globalComponents/BaseTable'
import SmallTitle from '@globalComponents/SmallTitle'
import styled from 'styled-components'
import { clearingApi } from '@src/pages/oo-client/api'
import { renderCgzt, renderDfzt, renderHkzt } from '@oo/config/TableColumnsRender'
import { formatTableData } from '@utils/format'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import moment from 'moment'
import ClearingRepaymentInfo from '@ooContent/clearingManage/clearingList/components/clearingRepaymentInfo'

interface PropsStruct {
	data: any
	visible: boolean
	onCancel: () => void
}

function PreviewClearingInfoModal(props: PropsStruct) {
	const [viewLoading, setViewLoading] = React.useState<boolean>(false)
	const [clearingResultDetail, setClearingResultDetail] = useState<any>({})
	const [rongxinTabData, setRongxinTabData] = useState<any[]>([])
	const [clearingRepaymentInfo, setClearingRepaymentInfo] = useState<any>(null)
	const { data, visible, onCancel } = props

	useEffect(() => {
		if (visible) {
			if (data.id) {
				getDetails()
			}
		}
	}, [visible])

	const getDetails = async () => {
		setViewLoading(true)
		let res = await clearingApi.getSettlementFileDetails(data?.id).catch(err => {
			setViewLoading(false)
		})
		if (res) {
			let res1 = await clearingApi.getRefundInfoDetail(res.couUuid).catch(err => {})
			if (res1) {
				setClearingRepaymentInfo(res1)
			} else {
				setClearingRepaymentInfo(null)
			}
			setClearingResultDetail(res)
			setRongxinTabData(formatTableData.addKey(res?.couList || []))
		}
		setViewLoading(false)
	}

	const cancelModal = () => {
		onCancel()
		setClearingResultDetail({})
		setRongxinTabData([])
	}

	const clearingRecognitionInfo: any[] = [
		{ key: 'settleNo', label: '清分流水号:', labelSpan: 3, labelOffset: 0, valueSpan: 21, valueOffset: 0 },
		{ key: 'iouNo', label: '借据号:', labelSpan: 3, labelOffset: 0, valueSpan: 21, valueOffset: 0 },
		{ key: 'batchNo', label: '批次号:', labelSpan: 3, labelOffset: 0, valueSpan: 21, valueOffset: 0 },
	]

	const getInfoList = (infoList: any[], detailInfo: any) => {
		const itemRender = info => {
			let { key, label, labelSpan, labelOffset, valueSpan, valueOffset } = info
			return (
				<React.Fragment key={key}>
					<Col span={labelSpan} offset={labelOffset}>
						{label}
					</Col>
					<Col span={valueSpan} offset={valueOffset}>
						{detailInfo[key] || '--'}
					</Col>
				</React.Fragment>
			)
		}
		return (
			<>
				<Row>
					{infoList.map((item: any) => {
						return itemRender(item)
					})}
				</Row>
			</>
		)
	}

	const clearingResultInfo: any[] = [
		{ key: 'settleQueryTime', label: '查询时间', span: 1 },
		{ key: 'cgzt', label: '成功标志', span: 1 },
		{ key: 'deductionAccount', label: '扣款账号', span: 1 },
		{ key: 'deductionAccountNum', label: '扣款账号名称', span: 1 },
		{ key: 'collectionAccount', label: '收款账号', span: 1 },
		{ key: 'collectionAccountNum', label: '收款账号名称', span: 1 },
		{ key: 'payableAmount', label: '应付金额', span: 1 },
		{ key: 'paymentAmount', label: '代发金额', span: 1 },
		{ key: 'dfzt', label: '代发状态', span: 1 },
		{ key: 'repaymentAmount', label: '还款金额', span: 1 },
		{ key: 'hkzt', label: '还款状态', span: 1 },
		{ key: 'loanOrderNum', label: '流水号', span: 1 },
		{ key: 'loanOrder', label: '放款流水', span: 1 },
		{ key: 'serialNum', label: '序列号', span: 1 },
		{ key: 'responseMessage', label: '响应信息', span: 2 },
	]

	const getInfoGrid = (infoList: any[], detailInfo: any, column: number = 2, bordered: boolean = true) => {
		const itemRender = info => {
			let titleTxt = info['label'] || '--'
			let contentTxt = detailInfo[info['key']] || '--'
			if (info['key'] === 'cgzt') {
				contentTxt = renderCgzt(contentTxt)
			}
			if (info['key'] === 'dfzt') {
				contentTxt = renderDfzt(contentTxt)
			}
			if (info['key'] === 'hkzt') {
				contentTxt = renderHkzt(contentTxt)
			}
			if (info['key'] === 'settleQueryTime' && contentTxt !== '--') {
				contentTxt = moment(contentTxt).format('YYYY-MM-DD')
			}
			let spanCol = info['span'] || 1

			return (
				<Descriptions.Item key={info['key']} label={titleTxt} span={spanCol}>
					{contentTxt}
				</Descriptions.Item>
			)
		}
		return (
			<>
				<Descriptions column={column} bordered={bordered}>
					{infoList.map((item: any) => {
						return itemRender(item)
					})}
				</Descriptions>
			</>
		)
	}

	const rongxinColumns = () => {
		return evenlyTableColunms(getColumnsByPageName('rongxinInfor'))
	}

	const RongxinInfo = () => {
		return (
			<PreviewWrapper className="card-wrapper" style={{ padding: 0 }}>
				<div className="boxContentWrap">
					<SmallTitle text="融信信息" />
					<BaseTable columns={rongxinColumns()} dataSource={rongxinTabData} havePagination={false} />
				</div>
			</PreviewWrapper>
		)
	}

	const RepaymentInfo = () => {
		return (
			<PreviewWrapper className="card-wrapper" style={{ padding: 0 }}>
				<div className="boxContentWrap">
					<SmallTitle text="融资还款信息" />
					{/*<BaseTable columns={rongxinColumns()} dataSource={rongxinTabData} havePagination={false} />*/}
					<ClearingRepaymentInfo data={clearingRepaymentInfo} />
				</div>
			</PreviewWrapper>
		)
	}

	const ClearingResult = () => {
		return (
			<PreviewWrapper className="card-wrapper" style={{ padding: 0 }}>
				<div className="boxContentWrap">
					<SmallTitle text="清分查询结果" />
					<div className="boxContent">
						<div className="baseInfo">{getInfoGrid(clearingResultInfo, clearingResultDetail)}</div>
					</div>
				</div>
			</PreviewWrapper>
		)
	}

	const ClearingRecognition = () => {
		return (
			<PreviewWrapper className="card-wrapper" style={{ padding: 0 }}>
				<div className="boxContentWrap">{getInfoList(clearingRecognitionInfo, clearingResultDetail)}</div>
			</PreviewWrapper>
		)
	}

	return (
		<Modal title="清分信息" open={visible} onCancel={cancelModal} footer={null} width="860px" className="company-preview">
			<Spin tip="加载中..." spinning={viewLoading}>
				<div className="main-wrapper">
					<ClearingRecognition />
					<RongxinInfo />
					{clearingRepaymentInfo ? <RepaymentInfo /> : ''}
					<ClearingResult />
				</div>
			</Spin>
		</Modal>
	)
}

const PreviewWrapper = styled.div`
	.baseInfo {
		.card-list {
			padding: 0 20px;
		}
	}
	.protocolSigns {
		.protocol {
			width: 300px;
			height: 50px;
			margin: 10px 30px;
			background: #f2f2f2;
			line-height: 50px;
			span {
				margin-left: 20px;
				color: #49a9ee;
				cursor: pointer;
			}
			span:last-child {
				margin-right: 50px;
				float: right;
			}
		}
	}
`

export default React.memo(PreviewClearingInfoModal)
