import { Descriptions } from 'antd'
import { render2Fixed } from '@oo/config/TableColumnsRender'
import React from 'react'

const clearingRepaymentInfo = (props: { data: any }) => {
	const { data } = props

	return (
		<div style={{ marginBottom: '20px' }}>
			<Descriptions bordered column={2}>
				<Descriptions.Item label="融资编号">{data?.applicationNumber || '--'}</Descriptions.Item>
				<Descriptions.Item label="融资申请金额">
					{data?.principalAmount !== null ? `￥${render2Fixed(`${data?.principalAmount / 100}`)}` : '--'}
				</Descriptions.Item>
				<Descriptions.Item label="还款日期">{data?.refundDate || '--'}</Descriptions.Item>
				{/* <Descriptions.Item label="利息还款金额">
					{data?.interestAmount !== null && data?.interestAmount !== 0 ? `￥${render2Fixed(`${data?.interestAmount / 100}`)}` : '--'}
				</Descriptions.Item> */}
			</Descriptions>
		</div>
	)
}

export default clearingRepaymentInfo
