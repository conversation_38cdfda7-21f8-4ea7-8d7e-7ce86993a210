import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import OperatingArea from '@src/globalComponents/OperatingArea'
import SearchBar from '@src/pages/oo-client/components/SearchBar'
import { clearingApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { formatTableData } from '@src/utils/format'
import { timeToStamp } from '@src/utils/timeFilter'
import React, { useEffect, useReducer, useRef, useState } from 'react'
import styled from 'styled-components'
import { Button, message, Modal, Tooltip } from 'antd'
import PreviewClearingInfoModal from '@ooContent/clearingManage/clearingList/components/previewClearingInfoModal'
import { ModalFunc } from 'antd/es/modal/confirm'
import moment from 'moment/moment'
import { useLocation } from 'react-router-dom'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'

const ClearingFile = () => {
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [dataSource, setDataSource] = useState<any[]>([])
	const [tableLoading, setTableLoading] = useState<boolean>(false)
	const [exportLoading, setExportLoading] = useState<boolean>(false)
	const [refreshLoading, setRefreshLoading] = useState<boolean>(false)
	const [previewDialogVisible, setPreviewDialogVisible] = useState(false)
	const [dialogData, setDialogData] = useState<any>(null)
	const [timeCount, setTimeCount] = useState<number>(5)
	const intervalCallback = useRef<Function>()
	const [refreshVisible, setRefreshVisible] = useState<boolean>(false)
	const [checkFlag, setCheckFlag] = useState<boolean>(false)
	const [currentSettleNo, setCurrentSettleNo] = useState<string>('')
	const [currentBatchNo, setCurrentBatchNo] = useState<string>('')
	const location = useLocation()
	const state = location?.state as any
	const batchNo = state?.batchNo
	const [searchParams, setSearchParams] = useState<any>({ batchNo })

	function callback() {
		let value = timeCount
		setTimeCount(v => {
			value = v - 1
			return v - 1
		})
		return value
	}

	useEffect(() => {
		getClearingList()
		intervalCallback.current = callback
	}, [pagination.current, pagination.pageSize, searchParams])

	const getClearingList = async () => {
		let params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...searchParams,
		}
		setTableLoading(true)
		const res = await clearingApi.getSettlementList(params).catch(() => {
			setDataSource([])
			setTableLoading(false)
			pagination.total = 0
			setPagination({ ...pagination })
		})
		if (res) {
			if (res.list && res.list.length > 0) {
				setTableLoading(false)
				setDataSource(formatTableData.addKey(res.list))
				pagination.total = res.total
				setPagination({ ...pagination })
			} else {
				setDataSource([])
				setTableLoading(false)
				pagination.total = 0

				setPagination({ ...pagination })
			}
		}
	}
	const handleSearch = (values: any) => {
		if (values && values.dueDate && values.dueDate.length === 2) {
			values.dueEndDate = timeToStamp(values.dueDate[1], 'end')
			values.dueBeginDate = values.dueDate[0]
			delete values.dueDate
		}
		if (values && values.settleDate && values.settleDate.length === 2) {
			if (values.settleDate[0]) values.settleBeginDate = moment(values.settleDate[0]).format('YYYY-MM-DD')
			if (values.settleDate[1]) values.settleEndDate = moment(values.settleDate[1]).format('YYYY-MM-DD')
			delete values.settleDate
		}
		//点击查询后，带入参数页码置为1, 记录当前查询参数
		pagination.current = 1
		setSearchParams(values)
		setPagination({ ...pagination })
	}
	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}

	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}

	const handleReset = () => {
		pagination.current = 1
		setSearchParams({})
		setPagination({ ...pagination })
	}

	const renderSettleName = (text: string, record: any) => (
		<Tooltip title={text} placement="topLeft">
			<span className="link" onClick={() => openPreviewDialog(record)}>
				{' '}
				{text}
			</span>
		</Tooltip>
	)

	const openPreviewDialog = (record: any) => {
		setDialogData(record)
		setPreviewDialogVisible(true)
	}

	const countNum = () => {
		let timer = null
		function tick() {
			let timeCount_ = intervalCallback.current()
			if (timeCount_ < 1) {
				clearInterval(timer)
			}
		}
		timer = setInterval(tick, 1000)
	}

	const retransmission = record => {
		console.log('retransmission', record)
		let modalRef = Modal.confirm({
			icon: false,
			title: '确认重发',
			content: <p>重发后，行内系统将会执行该笔付款。</p>,
			closable: false,
			onOk: async () => {
				const res = await clearingApi.chekcSettlement({ settleNo: record?.settleNo }).catch(e => {
					console.log('e', e)
				})
				// const res = false
				setCurrentSettleNo(record?.settleNo)
				setCurrentBatchNo(record?.batchNo)
				setRefreshVisible(true)
				setCheckFlag(res)
				if (res) {
					countNum()
				}
				modalRef.destroy()
			},
		})
	}

	const refreshList = async () => {
		//todo 调用批次更新接口
		let params = {
			batchNoList: [currentBatchNo],
		}
		await clearingApi.pullSettlementData(params)
		getClearingList()
		setRefreshVisible(false)
		setTimeCount(5)
	}

	const getColumns = () => {
		const columnsDic = {
			couAmount: { title: '融信金额（¥）' },
			settleNo: { render: renderSettleName, unit: 10 },
			batchNo: { unit: 10 },
			createDate: { unit: 9 },
			operation: {
				title: '操作',
				render: (text, record) => {
					if (record.settleKkflag === 9 && (record.cgzt === '0' || record.cgzt === '1')) {
						return (
							<React.Fragment>
								<span
									className="link"
									style={{ marginRight: '20px' }}
									onClick={() => {
										retransmission(record)
									}}
								>
									重发
								</span>
							</React.Fragment>
						)
					} else {
						return <span>--</span>
					}
				},
			},
		}
		return evenlyTableColunms(getColumnsByPageName('clearing', columnsDic))
	}

	return (
		<LayoutSlot>
			<WrapperStyle className="card-wrapper">
				<OperatingArea>
					<SearchBar
						pageName="clearing"
						optionData={{
							cgzt: [
								{ key: 0, name: '初始' },
								{ key: 1, name: '失败' },
								{ key: 3, name: '超时' },
								{ key: 4, name: '处理中' },
								{ key: 9, name: '成功' },
							],
						}}
						onSubmit={handleSearch}
						onClear={handleReset}
						initValues={{ batchNo, needClear: true }}
					/>
				</OperatingArea>

				<BaseTable
					className="card-table"
					loading={tableLoading}
					dataSource={dataSource}
					columns={getColumns()}
					total={pagination.total}
					current={pagination.current}
					onPageChange={handlePageChange}
					onSizeChange={handleSizeChange}
				/>
				<PreviewClearingInfoModal data={dialogData} visible={previewDialogVisible} onCancel={() => setPreviewDialogVisible(false)} />
				<Modal title={''} open={refreshVisible} closable={false} centered={true} bodyStyle={{ textAlign: 'center' }} footer={null}>
					<p style={{ paddingBottom: '10%' }}>{checkFlag ? '重发成功！手动查询清分结果。' : '状态异常！请手动查询清分结果'}</p>
					<p>
						<Button type={'primary'} disabled={checkFlag && timeCount > 0} onClick={refreshList}>
							手动查询{checkFlag ? `(${timeCount}s)` : ''}
						</Button>
					</p>
				</Modal>
			</WrapperStyle>
		</LayoutSlot>
	)
}
const WrapperStyle = styled.div``
export default ClearingFile
