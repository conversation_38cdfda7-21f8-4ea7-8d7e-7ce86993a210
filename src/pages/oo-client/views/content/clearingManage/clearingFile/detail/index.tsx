import { Descriptions, Modal } from 'antd'
import React, { useEffect, useState } from 'react'
import { clearingBatchStatusType } from '../../../couManage/couConfig'
interface Props {
	visible: boolean
	data: any
	onCancel: () => void
	onOk: () => void
}
const ClearingBatchDetail = (props: Props) => {
	const { visible, onCancel, data } = props

	useEffect(() => {
		if (visible) {
		}
	}, [visible])

	return (
		<Modal
			width={600}
			open={visible}
			title="批次信息"
			maskClosable={false}
			onCancel={() => {
				onCancel()
			}}
			footer={null}
		>
			<Descriptions layout="horizontal" labelStyle={{ width: 180 }} size="small" column={{ xs: 1, sm: 1, md: 1 }} bordered>
				<Descriptions.Item label="批次号">{data.batchNo}</Descriptions.Item>
				<Descriptions.Item label="扣款账号">{data.payerAccount}</Descriptions.Item>
				<Descriptions.Item label="扣款账户">{data.payerAccountName}</Descriptions.Item>
				<Descriptions.Item label="批次状态">{clearingBatchStatusType[data.settlePczt] || '--'}</Descriptions.Item>
				<Descriptions.Item label="响应信息">{data.settleReturnMsg || '--'}</Descriptions.Item>
			</Descriptions>
		</Modal>
	)
}

export default ClearingBatchDetail
