import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import OperatingArea from '@src/globalComponents/OperatingArea'
import SearchBar from '@src/pages/oo-client/components/SearchBar'
import { clearingApi, commonApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { formatTableData } from '@src/utils/format'
import { timeToStamp } from '@src/utils/timeFilter'
import { b64toBlob } from '@src/utils/util'
import { Button, message, Modal, Tooltip } from 'antd'
import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import FileSaver from 'file-saver'
import ClearingBatchDetail from './detail'
import { clearingFilePushType, clearingKkFlagType } from '../../couManage/couConfig'
import { useNavigate } from 'react-router-dom'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'

const ClearingList = () => {
	const navigate = useNavigate()
	const [searchParams, setSearchParams] = useState({})
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [dataSource, setDataSource] = useState<any[]>([])
	const [tableLoading, setTableLoading] = useState<boolean>(false)
	const [loading, setLoading] = useState<boolean>(false)
	const [refreshLoading, setRefreshLoading] = useState<boolean>(false)
	const [showDetail, setShowDetail] = useState<boolean>(false)
	const [searchPopConfirm, setSearchPopConfirm] = useState<boolean>(false)
	const [batchDetail, setBatchDetail] = useState<any>({})
	const [currentId, setCurrentId] = useState<string>('')
	useEffect(() => {
		getClearingFileList()
	}, [pagination.current, pagination.pageSize, searchParams])

	const getClearingFileList = async () => {
		let params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...searchParams,
		}
		setTableLoading(true)
		const res = await clearingApi.getSettlementFileList(params).catch(() => {
			setDataSource([])
			setTableLoading(false)
			pagination.total = 0
			setPagination({ ...pagination })
		})
		if (res) {
			if (res.list && res.list.length > 0) {
				setTableLoading(false)
				setDataSource(formatTableData.addKey(res.list))
				pagination.total = res.total
				setPagination({ ...pagination })
			} else {
				setDataSource([])
				setTableLoading(false)
				pagination.total = 0

				setPagination({ ...pagination })
			}
		}
	}
	const handleSearch = (values: any) => {
		if (values && values.clearingTime && values.clearingTime.length === 2) {
			values.settleEndDate = timeToStamp(values.clearingTime[1], 'end')
			values.settleBeginDate = values.clearingTime[0]
			delete values.clearingTime
		}
		//点击查询后，带入参数页码置为1, 记录当前查询参数
		pagination.current = 1
		setSearchParams(values)
		setPagination({ ...pagination })
	}

	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}

	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}

	const handleReset = () => {
		pagination.current = 1
		setSearchParams({})
		setPagination({ ...pagination })
	}
	const downloadFile = record => {
		console.log(record)
		commonApi.downloadFile({ fileUrl: record?.minioUrl }).then(url => {
			const fileblob = b64toBlob(url)
			FileSaver.saveAs(fileblob, `${record.fileName}`)
			// setFinanceCouAllTableLoading(false)
		})
	}
	const manualPush = record => {
		console.log(record)
		Modal.confirm({
			icon: false,
			title: '确认推送',
			content: <p style={{ margin: '20px' }}>推送后，行内系统将会执行付款。</p>,
			closable: false,
			onOk: async () => {
				clearingApi
					.manualSettlementFile({ id: record?.id })
					.then(res => {
						message.success('推送成功')
						getClearingFileList()
					})
					.catch(e => {
						console.log('e', e)
					})
			},
		})
	}

	const reTrigerBatch = record => {
		Modal.confirm({
			icon: false,
			title: '确认重发批次',
			content: <p style={{ margin: '20px' }}>重发后，行内系统将会执行付款。</p>,
			closable: false,
			onOk: async () => {
				clearingApi
					.resendSettlementPushHistory({ idList: [record?.id] })
					.then(res => {
						message.success('重发成功')
						getClearingFileList()
					})
					.catch(e => {
						console.log('e', e)
					})
			},
		})
	}
	const searchAndUpdate = record => {
		setLoading(true)
		setCurrentId(record.id)
		clearingApi
			.pullSettlementData({ idList: [record.id] })
			.then(() => {
				setLoading(false)
				setSearchPopConfirm(true)
			})
			.catch(() => {})
			.finally(() => {
				setLoading(false)
				setCurrentId('')
			})
	}

	const renderObjIntoArrForSelect = obj => {
		const arr = []
		for (const key in obj) {
			arr.push({ key: key, name: obj[key] })
		}
		return arr
	}
	const getColumns = () => {
		const columnsDic = {
			batchNo: {
				width: 200,
				render: (val, record) => {
					return (
						<a
							onClick={() => {
								setBatchDetail(record)
								setShowDetail(true)
							}}
						>
							<Tooltip title={val}>{val}</Tooltip>
						</a>
					)
				},
			},
			settleDate: { width: 110 },
			payerCompany: { width: 200 },
			financeFileFlag: { width: 100 },
			couCount: { width: 110 },
			settleFileCouAmount: { width: 140 },
			settlePushStatus: { width: 100 },
			successStatus: { width: 80 },
			kkStatus: { width: 80 },
			operation: {
				width: 200,
				fixed: false,
				render: (value, record) => {
					return (
						<div>
							<a className="link left-btn" onClick={() => navigate('/content/oo_clearingManage/clearingList', { state: { batchNo: record?.batchNo } })}>
								明细
							</a>
							<a className="link left-btn" onClick={() => downloadFile(record)}>
								下载文件
							</a>
							{[0, 2, 3].includes(record?.settlePushStatus) && (
								<a className="link left-btn" onClick={() => manualPush(record)}>
									手动推送
								</a>
							)}
							{record.settlePushStatus == '1' && [0, 1, 3].includes(record.settleKkflag) && (
								<a className="link left-btn" onClick={() => reTrigerBatch(record)}>
									重发批次
								</a>
							)}
							{record.settlePushStatus == '1' && (!record.settleCgbz || [0, 1, 3].includes(record.settleCgbz)) && (
								<Button type="link" className="link" onClick={() => searchAndUpdate(record)} loading={record.id == currentId && loading}>
									查询
								</Button>
							)}
						</div>
					)
				},
			},
		}
		return getColumnsByPageName('clearingFile', columnsDic)
	}

	return (
		<LayoutSlot>
			<WrapperStyle className="card-wrapper">
				<OperatingArea>
					<SearchBar
						pageName="clearingFile"
						optionData={{
							financeFlag: [
								{ key: 0, name: '持有清分' },
								{ key: 1, name: '质押清分' },
							],
							settlePushStatus: renderObjIntoArrForSelect(clearingFilePushType),
							settleKkflag: renderObjIntoArrForSelect(clearingKkFlagType),
						}}
						onSubmit={handleSearch}
						onClear={handleReset}
					/>
				</OperatingArea>

				<BaseTable
					className="card-table"
					loading={tableLoading}
					dataSource={dataSource}
					columns={getColumns()}
					total={pagination.total}
					current={pagination.current}
					onPageChange={handlePageChange}
					onSizeChange={handleSizeChange}
					scroll={{ x: 1000 }}
				/>
			</WrapperStyle>
			<ClearingBatchDetail visible={showDetail} data={batchDetail} onCancel={() => setShowDetail(false)} onOk={() => setShowDetail(false)} />

			<Modal
				width={400}
				open={searchPopConfirm}
				title={false}
				maskClosable={false}
				onCancel={() => {
					setSearchPopConfirm(false)
				}}
				closable={false}
				footer={false}
			>
				<div style={{ textAlign: 'center' }}>
					<p style={{ marginTop: 30 }}>查询完成</p>
					<Button
						type="primary"
						style={{ marginTop: 50 }}
						onClick={() => {
							setSearchPopConfirm(false)
							getClearingFileList()
						}}
					>
						返回查看
					</Button>
				</div>
			</Modal>
		</LayoutSlot>
	)
}
const WrapperStyle = styled.div`
	.left-btn {
		margin-right: 8px;
	}
	.link {
		padding: 0;
		height: max-content;
	}
`
export default ClearingList
