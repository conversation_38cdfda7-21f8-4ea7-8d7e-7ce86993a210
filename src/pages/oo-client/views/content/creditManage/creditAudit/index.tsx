import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import styled from 'styled-components'
import AuditModal from '@src/globalComponents/AuditModal'
import { Tooltip, message } from 'antd'
import CreditInfoModal from '../components/creditInfoModal'
import { numberToThousands } from '@utils/timeFilter'
import { creditManageApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'

const flowManage = () => {
	const [pagination, setPagination] = React.useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [loading, setLoading] = useState(true) //表格加载
	const [dataSource, setDataSource] = useState<any[]>([]) //表格数据
	const [creditAuditInfoData, setCreditAuditInfoData] = React.useState<any>({})
	const [creditAuditInfoVisible, setCreditAuditInfoVisible] = React.useState<boolean>(false)
	const [transferAuditVisible, setTransferAuditVisible] = useState(false)
	const [creditAuditType, setCreditAuditType] = useState('agree')
	const [creditAuditTitle, setCreditAuditTitle] = useState('确定通过？')
	const [auditConfirmLoading, setAuditConfirmLoading] = React.useState(false)

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize])

	const getList = () => {
		setLoading(true)
		creditManageApi.getCreditCList({ pageNum: pagination.current, pageSize: pagination.pageSize, status: 'checking' }).then(
			res => {
				pagination.total = res?.total
				setPagination({ ...pagination })

				setDataSource(res?.list)
				setLoading(false)
			},
			reason => {
				setLoading(false)
			}
		)
	}

	//审核操作
	const auditOperate = async (value, title, type) => {
		setCreditAuditType(type)
		setCreditAuditTitle(title)
		setCreditAuditInfoData(value)
		setTransferAuditVisible(true)
	}

	const submitAuditData = async submitData => {
		setAuditConfirmLoading(true)
		let flag = 1
		if (creditAuditType !== 'agree') {
			flag = 0
		}
		let param = {}
		param = {
			id: creditAuditInfoData.id,
			flag,
			auditReason: submitData.comment,
		}
		creditManageApi
			.creditAuditApi(param)
			.then(item => {
				getList()
				setTransferAuditVisible(false)
				setAuditConfirmLoading(false)
				if (item.returnCode === 17000) {
					message.success('处理成功')
				} else if (item.returnCode === 30015) {
					message.error('重复操作，该笔授信已完成处理')
				} else {
					message.error(item.returnDesc)
				}
			})
			.catch(err => {
				setAuditConfirmLoading(false)
			})
	}

	//表格改变 改变 size
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	//表格改变 当前页面
	const handleChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}

	//获取合同的表格列
	const getColumns = () => {
		const columnsDic = {
			availableCredit: {
				dataIndex: 'availableCredit',
				render: (text: any, record: any) => {
					const availableCredit = (record.quotaInCent - record.usedQuotaInCent - record.frozenQuotaInCent) / 100
					if (!Number.isNaN(availableCredit)) {
						return numberToThousands(availableCredit)
					} else {
						return ''
					}
				},
			},
			creditCompany: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									setCreditAuditInfoVisible(true)
									setCreditAuditInfoData(record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			operation: {
				render: (text, record) => {
					return (
						<div className="options_btn">
							<span className="link" onClick={() => auditOperate(record, '确定通过？', 'agree')}>
								通过
							</span>
							<span className="red" onClick={() => auditOperate(record, '确定拒绝？', 'reject')}>
								拒绝
							</span>
						</div>
					)
				},
			},
		}
		return getColumnsByPageName('creditAuditList', columnsDic)
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<BaseTable
						{...pagination}
						columns={getColumns()}
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
						loading={loading}
						dataSource={dataSource}
						rowKey="transferUuid"
					/>
				</div>
			</LayoutSlot>
			<AuditModal
				title={creditAuditTitle}
				onCancel={() => setTransferAuditVisible(false)}
				onSubmit={submitAuditData}
				visible={transferAuditVisible}
				type={creditAuditType}
				confirmLoading={auditConfirmLoading}
			/>
			<CreditInfoModal
				visible={creditAuditInfoVisible}
				creditInfo={creditAuditInfoData}
				onCancel={() => {
					setCreditAuditInfoVisible(false)
				}}
			></CreditInfoModal>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.options_btn {
		span {
			display: inline-block;
			margin-right: 10px;
		}
	}
	.warnalert {
		position: fixed;
		top: 50px;
	}
	.auditTableInfoTest {
		width: 20px;
		height: 20px;
		background-color: rgb(64, 197, 133);
		color: white;
		line-height: 20px;
		text-align: center;
		border-radius: 4px;
	}
`

export default flowManage
