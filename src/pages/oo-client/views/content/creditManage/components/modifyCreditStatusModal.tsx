import { creditStatusList } from '@src/pages/oo-client/config/companyCreditManage'
import { Form, Modal, Select } from 'antd'
import React, { useEffect, useState } from 'react'
import { creditManageApi } from '@src/pages/oo-client/api'

interface PropsStruct {
	/**
	 * @description 授信信息
	 */
	creditInfo: any
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 取消操作
	 */
	onOk: () => void
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
}
const modifyCreditStatusModal = ({ visible, creditInfo, onOk, onCancel }: PropsStruct) => {
	const [form] = Form.useForm()
	const [confirmLoading, setConfirmLoading] = useState<boolean>(false)
	useEffect(() => {
		if (visible) {
			let { companyName, enable } = creditInfo
			form.setFieldsValue({ companyName, enable })
		}
	}, [visible])
	const onSubmit = () => {
		setConfirmLoading(true)
		form.validateFields().then(
			async values => {
				let { id } = creditInfo
				await creditManageApi.modifyCreditStatus({ id, ...values }).catch(e => {
					console.log(e)
				})
				setConfirmLoading(false)
				onOk && onOk()
			},
			err => {
				setConfirmLoading(false)
			}
		)
	}
	return (
		<Modal
			title="修改授信信息"
			open={visible}
			onCancel={() => {
				onCancel && onCancel()
			}}
			onOk={onSubmit}
			confirmLoading={confirmLoading}
			width={500}
		>
			<div className="card-wrapper">
				<Form form={form} onFinish={onSubmit} labelCol={{ span: 5 }} wrapperCol={{ span: 17 }}>
					<Form.Item label="授信企业" className="creditStatus">
						<span>{creditInfo?.companyName}</span>
					</Form.Item>
					<Form.Item label="授信状态" className="creditStatus" name="enable">
						<Select placeholder="选择授信状态" getPopupContainer={(triggerNode: any) => triggerNode.parentNode}>
							{creditStatusList.map(item => {
								return (
									<Select.Option value={item.key} key={item.key}>
										{item.value}
									</Select.Option>
								)
							})}
						</Select>
					</Form.Item>
				</Form>
			</div>
		</Modal>
	)
}

export default modifyCreditStatusModal
