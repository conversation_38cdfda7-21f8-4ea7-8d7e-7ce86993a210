import React from 'react'
import { Modal, Tooltip, Alert, Tag, Descriptions } from 'antd'
import { formatNumber } from '@utils/format'
import SmallTitle from '@src/globalComponents/SmallTitle'
import styled from 'styled-components'
import { renderAmountInCent, renderCreditStatus } from '@src/pages/oo-client/config/TableColumnsRender'

interface PropsStruct {
	/**
	 * @description 授信信息
	 */
	creditInfo: any
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
}

function OpeningCreditInfoModal(props: PropsStruct) {
	const { visible, creditInfo, onCancel } = props

	const formatListData = {
		checkValue(text: any) {
			if (!text) return '- -'
			return text
		},
		quota(quotaInCent: number) {
			const quotaInYuan = quotaInCent / 100
			return formatNumber.addThousandsSign(quotaInYuan)
		},
		calcCanUseQuote() {
			const { quotaInCent, usedQuotaInCent, frozenQuotaInCent } = creditInfo
			const calcCanUseQuoteInYuan = (quotaInCent - usedQuotaInCent - frozenQuotaInCent) / 100
			return formatNumber.addThousandsSign(calcCanUseQuoteInYuan)
		},
		interestPayWay(type: string) {
			switch (type) {
				case 'supplier':
					return '供应商付息'
				case 'center':
					return '核心企业付息'
			}
		},
		verifyStatus(text: string) {
			if (text === 'checking') {
				return <Tag color="#F59A23">审核中</Tag>
			}
			if (text === 'confirmed') {
				return <Tag color="#87d068">审核通过</Tag>
			}
			if (text === 'rejected') {
				return <Tag color="#f50">审核拒绝</Tag>
			}
		},
	}

	return (
		<Modal title="查看开立额度" open={visible} onCancel={onCancel} footer={null} width="860px" maskClosable={false}>
			<div className="main-wrapper">
				<CreditInfoWrapper className="card-wrapper" style={{ padding: 0 }}>
					<div className="boxContentWrap">
						<SmallTitle text="基本信息" />
						<div className="boxContent">
							<Descriptions className="card-list" column={2}>
								<Descriptions.Item label="核心企业">{creditInfo?.orgName}</Descriptions.Item>
								<Descriptions.Item label="到期日">{creditInfo?.creditDueDate}</Descriptions.Item>
								<Descriptions.Item label="开立额度">{renderAmountInCent(creditInfo?.quotaInCent)} 元</Descriptions.Item>
								<Descriptions.Item label="可用额度">{renderAmountInCent(creditInfo?.availableQuotaInCent)} 元</Descriptions.Item>
								<Descriptions.Item label="质押率">{(Number(creditInfo?.financeDiscount || 1) * 100).toFixed(2) || '--'}%</Descriptions.Item>
								{/* <Descriptions.Item label="单笔融资限额">{renderAmountInCent(creditInfo?.creditSingleLimitInCent)} 元</Descriptions.Item> */}
								<Descriptions.Item label="申请编号">{creditInfo?.creditApplicationNo} </Descriptions.Item>
								<Descriptions.Item label="单户累计限额">{renderAmountInCent(creditInfo?.creditCoreTotalLimitInCent)}元</Descriptions.Item>
							</Descriptions>
						</div>
					</div>
				</CreditInfoWrapper>
				<CreditInfoWrapper className="card-wrapper" style={{ padding: 0 }}>
					<div className="boxContentWrap">
						<SmallTitle text="金融机构信息" />
						<div className="boxContent">
							<Descriptions className="card-list" column={2}>
								<Descriptions.Item label="金融机构名称">{creditInfo?.subOrg?.orgName}</Descriptions.Item>
								<Descriptions.Item label="金融机构编码">{creditInfo?.subOrg?.orgNo}</Descriptions.Item>
								<Descriptions.Item label="统一社会信用代码">{creditInfo?.subOrg?.socialCreditCode}</Descriptions.Item>
								<Descriptions.Item label="企业负责人">{creditInfo?.subOrg?.legalPerson}</Descriptions.Item>
								<Descriptions.Item label="省份">{creditInfo?.subOrg?.province}</Descriptions.Item>
								<Descriptions.Item label="城市">{creditInfo?.subOrg?.city}</Descriptions.Item>
								<Descriptions.Item label="地址">{creditInfo?.subOrg?.address}</Descriptions.Item>
								<Descriptions.Item label="机构号">{creditInfo?.subOrg?.noInBank}</Descriptions.Item>
								<Descriptions.Item label="联系电话">{creditInfo?.subOrg?.orgPhone}</Descriptions.Item>
							</Descriptions>
						</div>
					</div>
				</CreditInfoWrapper>
				<CreditInfoWrapper className="card-wrapper" style={{ padding: 0 }}>
					<div className="boxContentWrap">
						<SmallTitle text="客户经理信息" />
						<div className="boxContent">
							<Descriptions className="card-list" column={2}>
								<Descriptions.Item label="客户经理姓名">{creditInfo?.manager?.managerName}</Descriptions.Item>
								<Descriptions.Item label="客户经理编号">{creditInfo?.manager?.managerNo}</Descriptions.Item>
								<Descriptions.Item label="客户经理手机号">{creditInfo?.manager?.mobile}</Descriptions.Item>
							</Descriptions>
						</div>
					</div>
				</CreditInfoWrapper>
			</div>
		</Modal>
	)
}

const CreditInfoWrapper = styled.div`
	.card-item {
		/* 覆盖全局的响应式width */
		width: 50% !important;
		padding: 8px 0;
		text-align: left;
		&:nth-child(2n + 1) {
			padding-right: 10px;
		}
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		.right {
			display: inline !important;
		}
	}
	.ant-descriptions-row > td {
		padding: 8px 0;
	}
	.ant-descriptions-item-label {
		font-weight: bold;
		color: #666;
	}
	.ant-descriptions-item-container {
		color: #666;
	}
	.main-wrapper .card-wrapper {
		padding: 0 !important;
	}
`

export default OpeningCreditInfoModal
