import React from 'react'
import { Toolt<PERSON>, message, Button } from 'antd'
import { creditManageApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { numberToThousands, stampToTime, timeToStamp } from '@utils/timeFilter'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import LayoutSlot from '@globalComponents/LayoutSlot'
import BaseTable from '@globalComponents/BaseTable'
import SearchBar from '@src/pages/oo-client/components/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import CreditInfoModal from '../components/creditInfoModal'
import commonModuleApi from '@src/pages/oo-client/api/common'
import ModifyCreditStatusModal from '../components/modifyCreditStatusModal'
import styled from 'styled-components'
import { creditStatusList } from '@src/pages/oo-client/config/companyCreditManage'
import moment from 'moment'

function CreditList() {
	//table相关
	const [searchParams, setSearchParams] = React.useState({})
	const [pagination, setPagination] = React.useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [dataSource, setDataSource] = React.useState([])
	const [tableLoading, setTableLoading] = React.useState<boolean>(false)
	const [exportLoading, setExportLoading] = React.useState(false)

	const [creditListInfoVisible, setCreditListInfoVisible] = React.useState<boolean>(false)
	const [creditListInfoData, setCreditListInfoData] = React.useState<any>({})
	const [creditStatusVisible, setCreditStatusVisible] = React.useState<boolean>(false)

	React.useEffect(() => {
		getFinanceCreditList()
	}, [pagination.current, pagination.pageSize, searchParams])

	// 获取融资授信列表
	const getFinanceCreditList = () => {
		setTableLoading(true)
		creditManageApi.getCreditSList({ pageNum: pagination.current, pageSize: pagination.pageSize, ...searchParams }).then(
			res => {
				pagination.total = res?.total
				setPagination({ ...pagination })

				setDataSource(res?.list)
				setTableLoading(false)
			},
			reason => {
				setTableLoading(false)
			}
		)
	}

	const handleExportClick = async () => {
		setExportLoading(true)
		let serverTime = await commonModuleApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const companyInfo = JSON.parse(localStorage.getItem('companyInfo') || '{}')
		const operatingOrganizationUuid = companyInfo.uuid
		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`
		creditManageApi
			.exportFinanceCreditList({
				...searchParams,
				pageNum: 1,
				pageSize: 2147483646,
				type: 2,
				operatingOrganizationUuid,
			})
			.then((response: any) => {
				let type = 'application/vnd.ms-excel'
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(e => {
				console.log('e', e)
				message.error('导出失败')
				setExportLoading(false)
			})
	}

	//处理搜索
	const handleSearch = (values: any) => {
		if (values && values.dueDate && values.dueDate.length === 2) {
			if (values.dueDate[1]) values.dueEndDate = moment(values.dueDate[1]).format('YYYY-MM-DD')
			if (values.dueDate[0]) values.dueStartDate = moment(values.dueDate[0]).format('YYYY-MM-DD')
			delete values.dueDate
		}
		//点击查询后，带入参数页码置为1, 记录当前查询参数
		pagination.current = 1
		setSearchParams(values)
		setPagination({ ...pagination })
	}
	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}
	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}
	const handleReset = () => {
		pagination.current = 1
		setSearchParams({})
		setPagination({ ...pagination })
	}

	const goDetail = record => {
		creditManageApi
			.getCreditSDetail(record.creditId)
			.then(res => {
				setCreditListInfoVisible(true)
				setCreditListInfoData(res)
				// setCreditListInfoData(record)
			})
			.catch(() => {})
	}

	const getColumns = () => {
		const columnsDic = {
			creditCompany: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									setCreditListInfoVisible(true)
									goDetail(record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			availableCredit: {
				dataIndex: 'availableCredit',
				render: (text: any, record: any) => {
					const availableCredit = (record.quotaInCent - record.usedQuotaInCent - record.frozenQuotaInCent) / 100
					if (!Number.isNaN(availableCredit)) {
						return numberToThousands(availableCredit)
					} else {
						return ''
					}
				},
			},
			operation: {
				width: 90,
				render: (text: string, record: any) => {
					return (
						<span className="link" onClick={() => openModifyCreditStatusModal(record)}>
							修改
						</span>
					)
				},
			},
		}
		return getColumnsByPageName('creditListS', columnsDic)
	}

	const openModifyCreditStatusModal = data => {
		setCreditListInfoData(data)
		setCreditStatusVisible(true)
	}

	const onModifyCreditStatusCallback = () => {
		setCreditStatusVisible(false)
		getFinanceCreditList()
	}

	return (
		<LayoutSlot>
			<CreditListStyle className="card-wrapper">
				<OperatingArea>
					<SearchBar
						pageName="creditListS"
						optionData={{
							interestPayWay: [
								{ name: '供应商付息', key: 'supplier' },
								{ name: '核心企业付息', key: 'center' },
							],
							enable: [
								{ name: '启用', key: '1' },
								{ name: '禁用', key: '0' },
							],
							creditStatusList,
						}}
						initValues={{
							companyType: { name: '授信方', key: 'relationFiName' },
						}}
						onSubmit={handleSearch}
						onClear={handleReset}
					/>
					<Button onClick={handleExportClick} type="primary" style={{ marginLeft: '5px' }} loading={exportLoading}>
						导出
					</Button>
				</OperatingArea>
				<BaseTable
					className="card-table"
					rowKey="id"
					loading={tableLoading}
					dataSource={dataSource}
					columns={getColumns()}
					total={pagination.total}
					current={pagination.current}
					onPageChange={handlePageChange}
					onSizeChange={handleSizeChange}
				/>
			</CreditListStyle>
			<CreditInfoModal
				visible={creditListInfoVisible}
				creditInfo={creditListInfoData}
				onCancel={() => {
					setCreditListInfoVisible(false)
				}}
			></CreditInfoModal>
			<ModifyCreditStatusModal
				visible={creditStatusVisible}
				creditInfo={creditListInfoData}
				onOk={onModifyCreditStatusCallback}
				onCancel={() => {
					setCreditStatusVisible(false)
				}}
			></ModifyCreditStatusModal>
		</LayoutSlot>
	)
}

const CreditListStyle = styled.div`
	.ant-table-cell {
		display: table-cell;
		vertical-align: middle;
	}
`

export default CreditList
