import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import PDFViewer from '@src/globalComponents/PDFViewer'
import { creditManageApi } from '@src/pages/oo-client/api'
import commonModuleApi from '@src/pages/oo-client/api/common'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import { stampToTime } from '@src/utils/timeFilter'
import { b64toBlob } from '@src/utils/util'
import { Button, message } from 'antd'
import React, { useEffect, useState } from 'react'
import { useLocation } from 'react-router-dom'

const CooperateProtocols = () => {
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [dataSource, setDataSource] = useState<any[]>([])
	const [loading, setLoading] = useState(false)
	const location = useLocation()
	const state = location?.state as any
	const creditId = state?.creditId
	const companyName = state?.companyName
	const [pdfPath, setPdfPath] = useState<string>('')
	const [exportLoading, setExportLoading] = useState<boolean>(false)
	const [visible, setVisible] = useState<boolean>(false)
	const [currentPdf, setCurrentPdf] = useState<string>('')

	useEffect(() => {
		if (creditId) {
			getProtocolList()
		}
	}, [])

	const getProtocolList = () => {
		setLoading(true)
		creditManageApi
			.getSignedProtocols({
				pagination,
				creditId: creditId,
			})
			.then(res => {
				if (res && Array.isArray(res.list)) {
					setDataSource(res.list)
					setPagination({ ...pagination, total: res?.total })
					setLoading(false)
				}
			})
			.catch(() => {})
			.finally(() => {
				setLoading(false)
			})
	}

	//表格改变 当前页面
	const handleChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	//表格改变 改变 size
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	const handleDownloadClick = async record => {
		setCurrentPdf(record?.protocolUrl)
		setExportLoading(true)
		const fileName = `Download_${stampToTime(new Date(), 9)}.pdf`
		const base64Url = await commonModuleApi.downloadFile({ fileUrl: record?.protocolUrl }).catch(() => {
			message.error('导出失败')
			setExportLoading(false)
		})
		const blob: Blob = b64toBlob(base64Url)
		let type = 'application/pdf'
		downloadFileByFileFlow(blob, type, fileName, () => {
			setExportLoading(false)
			message.success('导出成功')
		})
	}

	const getColumns = () => {
		const columnsDic = {
			order: { unit: 10 },
			createTime: { title: '签署时间' },
			operation: {
				render: (text, record) => {
					return (
						<div>
							<span
								className="link"
								onClick={() => {
									setPdfPath(record?.protocolUrl)
									setVisible(true)
								}}
							>
								预览
							</span>
							<Button
								type="link"
								onClick={() => {
									handleDownloadClick(record)
								}}
								loading={record?.protocolUrl == currentPdf && exportLoading}
							>
								下载
							</Button>
						</div>
					)
				},
			},
		}
		return evenlyTableColunms(getColumnsByPageName('cooperateProtocol', columnsDic))
	}

	return (
		<div>
			<LayoutSlot>
				<div className="card-wrapper">
					<h2 style={{ padding: '20px 0' }}>企业名称：{companyName}</h2>
					<BaseTable
						{...pagination}
						columns={getColumns()}
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
						loading={loading}
						dataSource={dataSource}
					/>
					{visible && <PDFViewer title="" visible={visible} onCancel={() => setVisible(false)} pdfUrl={pdfPath} />}
				</div>
			</LayoutSlot>
		</div>
	)
}
export default CooperateProtocols
