import React from 'react'
import { Toolt<PERSON>, message, Button, Modal, Radio, Form } from 'antd'
import { creditManageApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { numberToThousands, stampToTime, timeToStamp } from '@utils/timeFilter'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import LayoutSlot from '@globalComponents/LayoutSlot'
import BaseTable from '@globalComponents/BaseTable'
import SearchBar from '@src/pages/oo-client/components/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import commonModuleApi from '@src/pages/oo-client/api/common'
import ModifyCreditStatusModal from '../components/modifyCreditStatusModal'
import styled from 'styled-components'
import { creditStatus, creditStatusList, creditStatusMap } from '@src/pages/oo-client/config/companyCreditManage'
import OpeningCreditInfoModal from '../components/openningCredit'
import { useNavigate } from 'react-router-dom'
import { hasAuth } from '@src/pages/oo-client/biz/bizIndex'

function CreditList() {
	const navigate = useNavigate()
	//table相关
	const [searchParams, setSearchParams] = React.useState({})
	const [pagination, setPagination] = React.useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [dataSource, setDataSource] = React.useState([])
	const [tableLoading, setTableLoading] = React.useState<boolean>(false)
	const [exportLoading, setExportLoading] = React.useState(false)

	const [creditListInfoVisible, setCreditListInfoVisible] = React.useState<boolean>(false)
	const [creditListInfoData, setCreditListInfoData] = React.useState<any>({})
	const [creditStatusVisible, setCreditStatusVisible] = React.useState<boolean>(false)
	const [modal, contextHolder] = Modal.useModal()
	const [form] = Form.useForm()

	React.useEffect(() => {
		getFinanceCreditList()
	}, [pagination.current, pagination.pageSize, searchParams])

	// 获取融资授信列表
	const getFinanceCreditList = () => {
		setTableLoading(true)
		creditManageApi.getCreditCList({ pageNum: pagination.current, pageSize: pagination.pageSize, ...searchParams }).then(
			res => {
				pagination.total = res?.total
				setPagination({ ...pagination })

				setDataSource(res?.list)
				setTableLoading(false)
			},
			reason => {
				setTableLoading(false)
			}
		)
	}

	const goDetail = record => {
		creditManageApi
			.getCreditSDetail(record.creditId)
			.then(res => {
				setCreditListInfoVisible(true)
				setCreditListInfoData(res)
				// setCreditListInfoData(record)
			})
			.catch(() => {})
	}
	const handleExportClick = async () => {
		setExportLoading(true)
		let serverTime = await commonModuleApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const companyInfo = JSON.parse(localStorage.getItem('companyInfo') || '{}')
		const operatingOrganizationUuid = companyInfo.uuid
		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`
		creditManageApi
			.exportFinanceCreditList({
				...searchParams,
				pageNum: 1,
				pageSize: 2147483646,
				type: 1,
				operatingOrganizationUuid,
			})
			.then((response: any) => {
				let type = 'application/vnd.ms-excel'
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(e => {
				console.log('e', e)
				message.error('导出失败')
				setExportLoading(false)
			})
	}

	//处理搜索
	const handleSearch = (values: any) => {
		if (values && values.dueDate && values.dueDate.length === 2) {
			values.dueEndDate = timeToStamp(values.dueDate[1], 'end')
			values.dueStartDate = values.dueDate[0]
			delete values.dueDate
		}
		//点击查询后，带入参数页码置为1, 记录当前查询参数
		pagination.current = 1
		setSearchParams(values)
		setPagination({ ...pagination })
	}
	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}
	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}
	const handleReset = () => {
		pagination.current = 1
		setSearchParams({})
		setPagination({ ...pagination })
	}
	const changeProtocolStatus = item => {
		form.setFieldValue('signStatus', item?.signStatus)
		modal.confirm({
			icon: false,
			title: '修改',
			content: (
				<div>
					<Form form={form}>
						<Form.Item name="signStatus" label="协议签署状态：" rules={[{ required: true }]}>
							<Radio.Group
								options={[
									{ label: '待签署', value: 0 },
									{ label: '已签署', value: 1 },
								]}
							/>
						</Form.Item>
					</Form>
				</div>
			),
			okText: '确定',
			cancelText: '取消',
			onOk: () => {
				return new Promise((resolve, reject) => {
					creditManageApi
						.updateProtocolStatus({ id: item?.creditId, signStatus: form.getFieldValue('signStatus') })
						.then(res => {
							message.success('更新成功！')
							resolve(res)
							getFinanceCreditList()
						})
						.catch(err => {
							reject(err)
						})
				}).catch(() => console.log()) // 捕获错误
			},
		})
	}

	const getColumns = () => {
		const columnsDic = {
			creditCompany: {
				title: '核心企业名称',
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									setCreditListInfoVisible(true)
									// setCreditListInfoData(record)
									goDetail(record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			creditDueDate: {
				title: '到期日',
			},
			signStatus: {
				render: val => {
					if (val == 0) {
						return '待签署'
					} else if (val == 1) {
						return '已签署'
					} else {
						return '--'
					}
				},
			},
			availableCredit: {
				dataIndex: 'availableCredit',
				render: (text: any, record: any) => {
					const availableCredit = (record.quotaInCent - record.usedQuotaInCent - record.frozenQuotaInCent) / 100
					if (!Number.isNaN(availableCredit)) {
						return numberToThousands(availableCredit)
					} else {
						return ''
					}
				},
			},
		}

		const operation = {
			width: 100,
			render: (text: string, record: any) => {
				return (
					<>
						{/* <span
							className="link"
							onClick={() => navigate('/content/creditManage/protocolList', { state: { creditId: record?.creditId, companyName: record?.orgName } })}
						>
							查看协议
						</span> */}
						<span className="link" onClick={() => changeProtocolStatus(record)}>
							修改
						</span>
					</>
				)
			},
		}
		return getColumnsByPageName('creditListC', { ...columnsDic, operation: hasAuth('oo_creditManage:update') ? operation : null })
	}

	const openModifyCreditStatusModal = data => {
		setCreditListInfoData(data)
		setCreditStatusVisible(true)
	}

	const onModifyCreditStatusCallback = () => {
		setCreditStatusVisible(false)
		getFinanceCreditList()
	}

	return (
		<LayoutSlot>
			<CreditListStyle className="card-wrapper">
				<OperatingArea>
					<SearchBar
						pageName="creditListC"
						optionData={{
							enable: [
								{ name: creditStatusMap[creditStatus.disable], key: creditStatus.disable },
								{ name: creditStatusMap[creditStatus.enable], key: creditStatus.enable },
							],
							creditStatusList,
						}}
						initValues={{
							companyType: { name: '授信方', key: 'relationFiName' },
						}}
						onSubmit={handleSearch}
						onClear={handleReset}
						// needFold={true}
					/>
					<Button onClick={handleExportClick} type="primary" style={{ marginLeft: '5px' }} loading={exportLoading}>
						导出
					</Button>
				</OperatingArea>
				<BaseTable
					className="card-table"
					rowKey="id"
					loading={tableLoading}
					dataSource={dataSource}
					columns={getColumns()}
					total={pagination.total}
					current={pagination.current}
					onPageChange={handlePageChange}
					onSizeChange={handleSizeChange}
				/>
			</CreditListStyle>
			<OpeningCreditInfoModal
				visible={creditListInfoVisible}
				creditInfo={creditListInfoData}
				onCancel={() => {
					setCreditListInfoVisible(false)
				}}
			></OpeningCreditInfoModal>
			<ModifyCreditStatusModal
				visible={creditStatusVisible}
				creditInfo={creditListInfoData}
				onOk={onModifyCreditStatusCallback}
				onCancel={() => {
					setCreditStatusVisible(false)
				}}
			></ModifyCreditStatusModal>
			{/* modal.useModal 的配套context */}
			{contextHolder}
		</LayoutSlot>
	)
}

const CreditListStyle = styled.div`
	.ant-table-cell {
		display: table-cell;
		vertical-align: middle;
	}
`

export default CreditList
