import React, { useEffect, useState } from 'react'
import { message, Tooltip } from 'antd'
import { destroyModuleApi, commonApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { getFlowElementStatus } from '@utils/dataClean'
import { history } from '@src/utils/router'
import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import OperatingArea from '@src/globalComponents/OperatingArea'
import SearchBar from '@src/pages/oo-client/components/SearchBar'
import FlowChartModal from '@src/globalComponents/FlowChartModal'
import styled from 'styled-components'

enum DestroyStatus {
	WAITING_AUDIT = 'WAITING_AUDIT',
	REJECTED = 'REJECTED',
	CONFIRMED = 'CONFIRMED',
}
interface Pagination {
	current: number
	pageSize: number
	total: number
}
interface SearchParams {
	couNo?: string
	destroyNo?: string
	destroyStatus?: DestroyStatus
}

function DestroyList() {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [loading, setLoading] = useState<boolean>(false)
	const [searchParams, setSearchParams] = useState<SearchParams>({})
	const [pagination, setPagination] = useState<Pagination>({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [flowChartVisible, setFlowChartVisible] = useState<boolean>(false) // 审核流传图的弹窗
	const [flowChartData, setFlowChartData] = useState<any[]>([]) // 流转图数据

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	// 得到核销列表
	const getList = async () => {
		setLoading(true)
		const res = await destroyModuleApi
			.getDestroyList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...searchParams,
			})
			.catch(err => {
				console.log('err', err)
			})
		console.log('res', res)
		// console.log(res.list.flat())
		if (res !== undefined) {
			pagination.total = res['total']
			if (res.list) {
				// 数组扁平化
				let flatList: any[] = []
				res.list.forEach(item => {
					flatList.push({ ...item.destroyBasic4List, ...item.destroyCou4List })
				})
				setDataSource(flatList)
			} else {
				setDataSource([])
			}
		} else {
			pagination.total = 0
			setDataSource([])
		}
		setLoading(false)
	}
	// 获取融信流转的流程图数据
	const getDestroyFlowChartData = async record => {
		const businessKey = record.uuid
		const res = await commonApi.getTransferChartData({ businessKey }).catch(reason => {
			console.log('reason', reason)
		})
		if (res !== undefined) {
			if (res?.returnCode == 17000) {
				setFlowChartVisible(true)
				const result = getFlowElementStatus(res.data)
				setFlowChartData(result)
			} else {
				message.error('此项数据暂不支持查看流程图')
			}
		}
	}
	const getColumns = () => {
		const pageName = 'searchDestroyList'
		const columnsDic = {
			destroyNo: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									onOperaCallback(record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			operation: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => getDestroyFlowChartData(record)}>
							审核流程
						</span>
					)
				},
			},
		}
		return getColumnsByPageName(pageName, columnsDic)
	}
	const onSubmit = (params: any) => {
		pagination.current = 1
		console.log('params', params)
		setSearchParams(params)
	}
	const onClear = () => {
		setSearchParams({})
	}
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	const handlePageChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const onOperaCallback = (data: any) => {
		history.push('/content/couDestroy/detail', {
			uuid: data.uuid,
		})
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar
							optionData={{
								destroyStatus: [
									{ name: '审核中', key: 'WAITING_AUDIT' },
									{ name: '已拒绝', key: 'REJECTED' },
									{ name: '核销成功', key: 'CONFIRMED' },
								],
							}}
							onSubmit={onSubmit}
							onClear={onClear}
							pageName="destroyList"
						/>
					</OperatingArea>
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						rowKey="id"
					/>
				</div>
				<FlowChartModal
					type="destroy"
					data={flowChartData}
					visible={flowChartVisible}
					onCancel={() => {
						setFlowChartVisible(false)
					}}
				></FlowChartModal>
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
`

export default DestroyList
