import React, { ReactElement, useEffect, useState } from 'react'
import styled from 'styled-components'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import OperatingArea from '@src/globalComponents/OperatingArea'
import BaseTable from '@src/globalComponents/BaseTable'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { Button, Modal, Space } from 'antd'
import { userAuthModule<PERSON><PERSON> } from '@src/pages/oo-client/api'
import { formatTableData } from '@utils/format'
import AddOrEditUserInfoModal from './components/addOrEditUserInfoModal'
import UserDetailModal from './components/userDetailModal'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { onOk, TableOptions } from '@src/utils/util'

type FormModalType = 'add' | 'edit'
export default function SearchUser(): ReactElement {
	const [formModalType, setFormModalType] = useState<FormModalType>('add')
	const [userInfo, setUserInfo] = useState({})
	const [modalVisible, setModalVisible] = useState(false)
	const [addAndEditModalVisible, setAddAndEditModalVisible] = useState(false)
	const [tableLoading, setTableLoading] = useState(false)
	const [dataSource, setDataSource] = useState([])
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const { confirm } = Modal

	useEffect(() => {
		getInfoList()
	}, [pagination.current, pagination.pageSize])

	const getInfoList = async () => {
		const params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
		}
		setTableLoading(true)
		const res = await userAuthModuleApi.getUserList(params).catch(() => {
			setTableLoading(false)
		})
		setTableLoading(false)
		if (res) {
			const list = formatTableData.addKey(res.list)
			setDataSource(list)
			pagination.total = res.total
			setPagination({ ...pagination })
		}
	}

	const handleEditFinish = types => {
		setAddAndEditModalVisible(false)
		onOk(types, getInfoList)
	}

	const handleAddOrEditUserInfo = (type: FormModalType, record?: any) => {
		setFormModalType(type)
		if (record) {
			setUserInfo(record)
		} else setUserInfo({})
		setAddAndEditModalVisible(true)
	}

	const handleDeleteUser = async (record: any) => {
		const params = {
			accountId: record.accountId,
		}
		confirm({
			title: '确认删除用户',
			icon: <ExclamationCircleOutlined />,
			async onOk() {
				await userAuthModuleApi.removeUser(params)
				onOk(TableOptions.DELETE, getInfoList)
			},
			okText: '确定',
			onCancel() {
				console.log('Cancel')
			},
		})
	}

	//查看用户详情
	const handleUserDetial = (record: any) => {
		setModalVisible(true)
		setUserInfo(record)
	}

	const getColumns = () => {
		const columnsDic = {
			username: {
				render: (text: string, record: any) => {
					return (
						<span onClick={() => handleUserDetial(record)} className="username">
							{text}
						</span>
					)
				},
			},
			roleName: {
				//render 用户角色解析，如果公司的type和用户的type对上才展示，以防sp改动公司类型
				render: (text: string, record: any) => {
					let roleArr: string[] = []
					record?.roleList?.forEach(roleInfo => {
						roleArr.push(roleInfo.roleName)
					})
					let returnData = ''
					if (roleArr.length > 0) {
						returnData = roleArr.join('，')
					} else {
						returnData = '- -'
					}
					return returnData
				},
			},
			operation: {
				render: (text: string, record: any) => {
					if (record?.roleList?.[0]?.roleType !== 1) {
						return (
							<Space>
								<Button className={'edit_btn'} type="text" onClick={() => handleAddOrEditUserInfo('edit', record)}>
									修改
								</Button>
								<Button className={'delete_btn'} type="text" onClick={() => handleDeleteUser(record)}>
									删除
								</Button>
							</Space>
						)
					} else {
						return <></>
					}
				},
			},
		}
		return getColumnsByPageName('userInfoList', columnsDic)
	}

	function handleChange(current: number) {
		pagination.current = current
		setPagination({ ...pagination })
	}
	function handleSizeChange(size: number) {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	return (
		<LayoutSlot>
			<Wrapper className="card-wrapper">
				<OperatingArea>
					<Button onClick={() => handleAddOrEditUserInfo('add')} type="primary">
						新建用户
					</Button>
				</OperatingArea>
				<BaseTable
					loading={tableLoading}
					columns={getColumns()}
					dataSource={dataSource}
					{...pagination}
					onPageChange={handleChange}
					onSizeChange={handleSizeChange}
				/>
				<UserDetailModal userInfo={userInfo} visible={modalVisible} onCancel={() => setModalVisible(false)} />
				<AddOrEditUserInfoModal
					type={formModalType}
					userInfo={userInfo}
					visible={addAndEditModalVisible}
					onOk={handleEditFinish}
					onCancel={() => setAddAndEditModalVisible(false)}
					options={TableOptions}
				/>
			</Wrapper>
		</LayoutSlot>
	)
}

const Wrapper = styled.div`
	.edit_btn {
		color: #606eff;
		margin-right: 10px;
	}
	.delete_btn {
		color: #ff4d4f;
	}
	.username {
		color: #606eff;
		cursor: pointer;
	}
`
