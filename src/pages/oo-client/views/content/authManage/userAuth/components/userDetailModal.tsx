import React, { ReactElement } from 'react'
import { Modal, Descriptions } from 'antd'
import styled from 'styled-components'
interface Props {
	userInfo: any
	visible: boolean
	onCancel: () => any
}

function UserDetailModal({ visible, onCancel, userInfo }: Props): ReactElement {
	let roleArr: string[] = []
	let ext = JSON.parse(localStorage.getItem('ext') || '{}')
	let companyTypeList = ext.user && ext.user.company && ext.user.company.type ? ext.user.company.type.split(',') : []
	if (userInfo.roleList && userInfo.roleList.length > 0) {
		companyTypeList.forEach(item => {
			userInfo.roleList.forEach(roleInfo => {
				if (item === roleInfo.roleName.slice(0, item.length)) {
					roleArr.push(roleInfo.remark)
				}
			})
		})
	}
	let returnData = ''
	if (roleArr.length > 0) {
		returnData = roleArr.join('，')
	} else {
		returnData = '- -'
	}

	return (
		<Modal open={visible} title="查看用户详情" onCancel={onCancel} footer={null} width={600}>
			<Wrapper className="main-wrapper">
				<Descriptions layout="horizontal" column={{ xs: 1, sm: 1, md: 1 }}>
					<Descriptions.Item label="用户名">{userInfo.username}</Descriptions.Item>
					<Descriptions.Item label="用户邮箱">{userInfo.email}</Descriptions.Item>
					<Descriptions.Item label="手机号">{userInfo.mobile}</Descriptions.Item>
					<Descriptions.Item label="角色权限">{returnData}</Descriptions.Item>
				</Descriptions>
			</Wrapper>
		</Modal>
	)
}

const Wrapper = styled.div`
	padding: 10px;
	.ant-descriptions-item-label {
		width: 50%;
		display: block;
		text-align: right;
	}
`

export default UserDetailModal
