import styled from 'styled-components'
import React, { ReactElement, useEffect, useState } from 'react'
import { Modal, Form, Input, Checkbox, Tooltip } from 'antd'
import { userAuthModuleApi } from '@src/pages/oo-client/api'
import { QuestionCircleOutlined } from '@ant-design/icons'

interface Props {
	type: 'add' | 'edit'
	userInfo: any
	visible: boolean
	onOk: (types: string) => any
	onCancel: () => any
	options: any
}

function AddOrEditUserInfoModal({ type, visible, onCancel, onOk, userInfo, options }: Props): ReactElement {
	const [form] = Form.useForm()
	const [roleIdList, setRoleIdList] = useState<Array<any>>([])
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')

	useEffect(() => {
		if (visible) {
			getroleList()
		}
	}, [visible])

	//查询现有配置角色列表
	const getroleList = async () => {
		// 点击修改或新建获取的角色列表
		const roleList = await userAuthModuleApi.searchRoleList({
			roleType: 0, //0 是操作员，1是管理员
			supportOrgType: 'OO',
		})
		// 取到接口返回的角色id
		let roleIdList: string[] = []
		roleList?.forEach(item => {
			roleIdList.push(item.id)
		})

		if (type === 'add') {
			form.resetFields()
		}
		if (type === 'edit') {
			const rolelist: any[] = []
			userInfo.roleList?.forEach(item => {
				// 如果这个用户的角色在角色列表中没有，则不push（处理老用户的角色问题）
				if (roleIdList.indexOf(item.roleId) !== -1) {
					// rolelist.push({ id: item.id, remark: item.remark })
					rolelist.push(item.roleId)
				}
			})
			if (visible) roleChange(rolelist)
			userInfo.roleIdList = rolelist
			form.setFieldsValue(userInfo)
		}
		setRoleIdList(roleList || [])
	}

	//提交表单（新增 && 编辑）前置操作
	const handleSubmit = () => {
		form.validateFields().then(
			res => {
				console.log('表单验证后的res: ', res)
				handleAddOrEditUserInfo(res)
			},
			err => {
				console.log(err, 'err')
			}
		)
	}
	//提交表单（新增 && 编辑）提交数据
	const handleAddOrEditUserInfo = async (values: any) => {
		const params = {
			...values,
			orgId: ext?.user?.orgId,
			roleIdList: values?.roleIdList,
		}
		if (type === 'add') {
			await userAuthModuleApi.createUser(params)
			onOk(options.ADD)
		} else {
			params['accountId'] = userInfo?.accountId
			params['userId'] = userInfo?.id
			params['supportOrgType'] = ext?.user?.company?.type
			// 修改参数
			params['roleCodeList'] = values?.roleIdList
			await userAuthModuleApi.updateUser(params)
			onOk(options.EDIT)
		}
	}

	const roleChange = checkedValues => {
		let checkRoleList: any = []
		roleIdList.forEach(role => {
			checkedValues.forEach(id => {
				if (role.id == id) {
					checkRoleList.push(role)
				}
			})
		})
	}

	return (
		<Modal
			forceRender
			open={visible}
			title={type === 'add' ? '新建用户' : '修改用户'}
			onCancel={() => {
				onCancel()
			}}
			width={600}
			onOk={handleSubmit}
			getContainer={false}
		>
			<div className="card-wrapper">
				<Form form={form} name="addUserForm" onFinish={handleSubmit} labelCol={{ span: 5 }} wrapperCol={{ span: 17 }}>
					<Form.Item
						name="mobile"
						className="mobile"
						label={'用户手机号'}
						rules={[
							{
								required: true,
								message: '请输入用户手机号，并确认格式正确',
								whitespace: true,
							},
							{
								pattern: /^1\d{10}$/,
								message: '请输入用户手机号，并确认格式正确',
							},
						]}
					>
						<Input type="string" placeholder="请输入手机号" maxLength={11} />
					</Form.Item>

					<Form.Item className="accountName" label="用户名" name="accountName" rules={[{ required: true, message: '请输入用户名', whitespace: true }]}>
						<Input placeholder="请输入用户名" maxLength={20} />
					</Form.Item>

					<CheckboxStyle>
						<Form.Item className="roleIdList" label="角色名称" name="roleIdList" rules={[{ required: true, message: '请选择角色名称' }]}>
							<Checkbox.Group onChange={roleChange}>
								{/* defaultValue={['Apple']} */}
								{roleIdList.length > 0 &&
									roleIdList.map(item => {
										return (
											<Checkbox key={item.id} value={item.id} style={{ width: '200px' }}>
												{item.roleName}
												{item?.description ? (
													<Tooltip placement="top" title={item?.description}>
														<QuestionCircleOutlined className="explain" />
													</Tooltip>
												) : null}
											</Checkbox>
										)
									})}
							</Checkbox.Group>
						</Form.Item>
					</CheckboxStyle>
				</Form>
			</div>
		</Modal>
	)
}
const CheckboxStyle = styled.div`
	.roleIdList {
		.ant-checkbox-wrapper {
			margin: 0;
		}
		.explain {
			color: rgb(249, 163, 20);
			margin-left: 5px;
			cursor: pointer;
		}
	}
`

export default AddOrEditUserInfoModal
