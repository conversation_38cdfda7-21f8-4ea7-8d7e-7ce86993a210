import React, { useEffect } from 'react'
import { Layout, message } from 'antd'
import { Outlet } from 'react-router-dom'
import { getUserData } from '@utils/util'
import { history } from '@src/utils/router'
import NavLayout from '@src/globalComponents/NavLayout'
import BreadcrumbLayout from '@globalComponents/BreadcrumbLayout'
import LayoutTop from '@oo/components/layoutTop'
import loginApi from '@src/pages/oo-client/api/login'
import styled from 'styled-components'
import { getStorage, setStorage, initInnerPagesName } from '@oo/biz/bizIndex'
import uiData from '@oo/store/ui-data'

const { Content } = Layout

type userInfoType = { email: '' }

function MainContent() {
	useEffect(() => {
		if (!getUserData().userInfo) {
			message.warning('未登录')
			history.push('/login')
		}
	}, [])

	const userInfo: userInfoType = getUserData().userInfo || { email: '' }
	const userNavList = getStorage('menuList')

	const handleLogout = async (cb: () => void) => {
		await loginApi.logout().catch(err => {
			console.log('err', err)
		})
		message.success('退出成功')
		history.push('/login')
		cb()
	}

	return (
		<MainContentWrapper>
			<Layout style={{ height: '100%' }}>
				<NavLayout userNavList={userNavList} title="江阴农商银行" uiData={uiData} {...{ setStorage, getStorage, initInnerPagesName }} />

				<Layout>
					<div className="top-bar">
						<BreadcrumbLayout userNavList={userNavList} uiData={uiData} />
						<LayoutTop user={userInfo} account={userInfo.email} onLogout={handleLogout} />
					</div>
					<Content className="content-box">
						<Outlet />
					</Content>
				</Layout>
			</Layout>
		</MainContentWrapper>
	)
}

const MainContentWrapper = styled.div`
	position: relative;
	width: 100%;
	height: 100vh;
	// min-width: 1280px;
	/* user-select: none; */
	overflow-x: auto;
	.top-bar {
		/* position: absolute;
		top: 0;
		left: 240px; */
		height: 40px;
		line-height: 40px;
		/* width: calc(100% - 240px); */
		box-shadow: 2px 2px 6px #cfd6e0;
		text-align: right;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-left: 15px;
		padding-right: 15px;
		z-index: 999;
	}
	.content-box {
		padding: 15px;
		overflow-y: auto;
	}
`

export default MainContent
