import React, { useEffect } from 'react'
import { Modal, Form, Input } from 'antd'
import { ExclamationCircleOutlined, CheckCircleOutlined } from '@ant-design/icons'

const { TextArea } = Input
export interface PropsStruct {
	/**
	 * @description 标题
	 */
	title: string
	/**
	 * @description 显示/隐藏
	 */
	visible: boolean
	/**
	 * @description 通过还是拒绝
	 */
	type: string
	/**
	 * @description 提交时加载
	 */
	confirmLoading: boolean
	/**
	 * @description 发票状态和登记状态
	 */
	data: any
	/**
	 * @description 点击关闭事件
	 */
	onCancel: () => void
	/**
	 * @description 提交事件
	 */
	onSubmit: (values) => void
}

export default function AuditModalPage(props: PropsStruct) {
	const { visible, title, type, confirmLoading, onCancel, onSubmit } = props
	const [form] = Form.useForm()
	useEffect(() => {
		if (!confirmLoading) {
			form.resetFields()
		}
	}, [confirmLoading])

	const submitData = () => {
		form.validateFields().then(
			values => {
				// 1代表同意，2代表拒绝
				if (type === 'agree') {
					values.coutcome = '1'
				} else {
					values.coutcome = '2'
				}
				onSubmit(values)
			},
			err => {
				console.log(err)
				return
			}
		)
	}

	return (
		<Modal
			title={title}
			open={visible}
			onCancel={() => {
				onCancel()
				form.resetFields()
			}}
			onOk={submitData}
			width="500px"
			bodyStyle={{
				height: '135px',
				padding: '12px',
			}}
			confirmLoading={confirmLoading}
			className="wx_Modal wx_Modal_confirm"
		>
			<div className="wx_Modal_Main">
				{type === 'reject' ? (
					<Form form={form}>
						<Form.Item name="comment">
							<TextArea showCount maxLength={50} rows={4} placeholder="请描述处理意见，最多输入50个字符" />
						</Form.Item>
					</Form>
				) : (
					<p style={{ margin: '24px 50px 52px 50px', fontSize: 18 }} className="">
						<ExclamationCircleOutlined style={{ color: '#1785ff', marginRight: '5px' }} /> 确定通过后无法撤销
						{props.data?.invoice ? (
							<p style={{ margin: '10px 0', fontSize: 14 }}>
								<CheckCircleOutlined style={{ color: 'green', marginRight: '5px' }} />
								发票状态正常；
							</p>
						) : (
							<p style={{ margin: '10px 0', fontSize: 14 }}>
								<ExclamationCircleOutlined style={{ color: 'orange', marginRight: '5px' }} />
								存在异常状态发票，请在融资详情中查看；
							</p>
						)}
						{props.data?.register ? (
							<p style={{ margin: '10px 0', fontSize: 14 }}>
								<CheckCircleOutlined style={{ color: 'green', marginRight: '5px' }} />
								中登查重状态正常；
							</p>
						) : (
							<p style={{ margin: '10px 0', fontSize: 14 }}>
								<ExclamationCircleOutlined style={{ color: 'orange', marginRight: '5px' }} />
								存在命中中登登记，请在融资详情中查看；
							</p>
						)}
					</p>
				)}
			</div>
		</Modal>
	)
}
