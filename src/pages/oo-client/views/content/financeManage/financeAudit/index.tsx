import React, { useEffect, useState } from 'react'
import { message, Modal, Tooltip } from 'antd'
import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'

import styled from 'styled-components'
import { deepExtend } from '@utils/util'
import { history } from '@src/utils/router'
// import FioAuditModal from '../components/FioAuditModal'
import commonModuleApi from '@src/pages/oo-client/api/common'
import { couDataOnChain, getStorage, hasAuth } from '@src/pages/oo-client/biz/bizIndex'
import { InfoCircleFilled } from '@ant-design/icons'
import financeSearchApi from '@src/pages/oo-client/api/financeSearch'
import AuditModal from './modal/audit-modal'
import OperatingArea from '@src/globalComponents/OperatingArea'
import SearchBar from '@src/pages/oo-client/components/SearchBar'
import { timeToStamp } from '@src/utils/timeFilter'
interface IProps {
	isFIOperator?: boolean
}
function auditPage({ isFIOperator = false }: IProps) {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [auditFinancePagination, setAuditFinancePagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [loading, setLoading] = useState(false)
	const [auditVisible, setAuditVisible] = useState(false)
	const [auditType, setAuditType] = useState<any>('agree')
	const [auditTitle, setAuditTitle] = useState<any>('确定通过？')
	const [confirmLoading, setConfirmLoading] = React.useState(false)
	const [financeRowData, setFinanceRowData] = React.useState<any>({})
	//是否为 Fio 并且为非直连模式（润楼模式）
	// const [isRlFioAndDisDirect, setIsRlFioAndDisDirectRlFio] = useState<boolean>(false)
	//查看收款账号可见性
	const [receiveAccountVisible, setReceiveAccountVisible] = useState<boolean>(false)
	//查看收款账号信息
	const [receiveAccountData, setsetReceiveAccountData] = useState<any>(null)
	const [isNomalInvoice, setIsNomalInvoice] = useState<boolean>(false)
	const [isNomalRegister, setIsNomalRegister] = useState<boolean>(false)
	const [searchParams, setSearchParams] = useState<any>() //searchbar 字段

	const getIsLastAuditor = async (taskId: string): Promise<Boolean> => {
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let companyId: string = ext.user && ext.user.company ? ext.user.company.id : ''
		let isLastAuditor: any = await commonModuleApi
			.isLastFlowPerson({
				companyUuid: companyId,
				role: getStorage('roleCode') || '',
				taskId,
			})
			.catch(err => {
				console.log('err', err)
				return false
			})
		return isLastAuditor
	}

	useEffect(() => {
		getList()
	}, [auditFinancePagination.current, auditFinancePagination.pageSize, searchParams])

	// 得到融资申请列表
	function getList() {
		setLoading(true)
		commonModuleApi
			.loadCurrentUserTask({
				pageNum: auditFinancePagination.current,
				pageSize: auditFinancePagination.pageSize,
				processDefinitionKeyList: ['pledgeFinance'],
				roleNameList: [getStorage('roleCode')] || [],
				...searchParams,
			})
			.then(
				(res: any) => {
					let financeData = deepExtend({}, res)
					auditFinancePagination.total = financeData['count']
					if (financeData.data) {
						let filterFinanceData: Array<any> = []
						filterFinanceData = financeData['data'].map(item => {
							//数据结构调整
							let auditFinanceData = {
								assignee: item.assignee,
								businessKey: item.businessKey,
								createTime: item.createTime,
								taskId: item.id,
								name: item.name,
							}
							return { ...item.bizMap, ...{ auditFinanceData } }
						})
						setDataSource(filterFinanceData)
						setLoading(false)
					}
				},
				err => {
					setLoading(false)
					auditFinancePagination.total = 0
					setDataSource([])
				}
			)
	}
	const handleSizeChange = (size: number) => {
		auditFinancePagination.current = 1
		auditFinancePagination.pageSize = size
		setAuditFinancePagination({ ...auditFinancePagination })
	}
	const onOperCallback = (str: string, data: any, type?: string, title?: string) => {
		if (str === 'applicationNumber') {
			// 存储融资申请详情，防止刷新数据丢失
			//根据按钮权限来判断跳转至对应的详情页面
			let url = '/content/financeManage/financeDetail'
			// if (hasAuth('bl_couFinance:financeDetail:cDetails')) {
			// 	url = '/content/couFinance/cFinanceDetail'
			// }
			// 存储融资申请详情，防止刷新数据丢失
			url &&
				history.push(url, {
					uuidOrNumber: data.uuid,
				})
		} else if (str === 'auditFinance') {
			setFinanceRowData(data)
			setAuditVisible(true)
			setAuditType(type)
			setAuditTitle(title)
		}
	}
	const assetCheck = record => {
		Modal.confirm({
			icon: false,
			title: '资产校验',
			content: (
				<p>
					<InfoCircleFilled style={{ color: '#1890ff' }} />
					发起发票验真和中登查重，发起后可在详情中查看最新校验结果。
				</p>
			),
			closable: false,
			onOk: async () => {
				financeSearchApi
					.assetCheck({
						uuidOrNumber: record.applicationNumber,
					})
					.then(res => {
						message.success('已发起查验！')
					})
					.catch(e => {
						console.log('e', e)
					})
			},
		})
	}
	const getColumns = () => {
		const columnsDic = {
			applicationNumber: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									onOperCallback('applicationNumber', record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			operation: {
				width: 150,
				render: (text: any, record: any) => {
					return (
						<div>
							<span className="link" onClick={() => assetCheck(record)}>
								资产校验
							</span>
							<span className="link" onClick={() => onOperCallback('auditFinance', record, 'agree', '确定通过？')}>
								通过
							</span>
							<span className="red" onClick={() => onOperCallback('auditFinance', record, 'reject', '确定拒绝？')}>
								拒绝
							</span>
						</div>
					)
				},
			},
		}

		return getColumnsByPageName('auditFinanceList', columnsDic)
	}
	function handlePageChange(current: number) {
		auditFinancePagination.current = current
		setAuditFinancePagination({ ...auditFinancePagination })
	}

	const submitData = async (values: any) => {
		setConfirmLoading(true)
		let param = {}
		let jsonParamStr: any = {}
		if (auditType !== 'agree') {
			jsonParamStr.loanResult = 'REJECT_LOAN'
			jsonParamStr.applicationNumber = financeRowData.applicationNumber
		}
		param = {
			businessKey: financeRowData.auditFinanceData.businessKey,
			taskId: financeRowData.auditFinanceData.taskId,
			jsonParamStr: JSON.stringify(jsonParamStr),
			outcome: auditType,
			comment: values.comment,
		}
		const isLastAuditor = await getIsLastAuditor(financeRowData.auditFinanceData.taskId)
		commonModuleApi
			.doTask(param)
			.then(item => {
				setConfirmLoading(false)
				if (item.returnCode === 17000) {
					message.success('处理成功')
					//判断是不是最后节点，是需要cou存证上链
					if (isLastAuditor) {
						forCouDataOnChain()
					}
				} else {
					if (item.returnCode === 30015) {
						message.error('重复操作，该项融资已完成处理')
					} else {
						// 删除关键字（银行接口异常）
						let returnDesc = item.returnDesc.replace('银行接口异常', '')
						message.error(returnDesc)
					}
				}
				setAuditVisible(false)
				getList()
			})
			.catch(err => {
				setConfirmLoading(false)
				setAuditVisible(false)
			})
	}

	const forCouDataOnChain = () => {
		financeSearchApi
			.getFinanceDetail({
				uuidOrNumber: financeRowData.uuid,
			})
			.then((res: any) => {
				let couList = res.couList
				let couNoList = []
				couNoList = couList.map(cou => {
					return cou['couNo']
				})
				couDataOnChain(couNoList)
			})
	}

	//获取银行账户信息
	const getBankCard = async (companyUuid, relationFi) => {
		let params = {
			businessType: 'FINANCING_RECEIVE',
			companyUuid,
			relationFi,
		}
		// let querybankCard = await commonModuleApi.queryBankcard(params).catch(err => {
		// 	console.log('err', err)
		// })
		// if (querybankCard) setsetReceiveAccountData(querybankCard)
	}

	//search 提交 事件
	const onSubmit = (params: { createDate: any; status: string; transferNo: string }) => {
		let endDate, startDate
		if (params && params.createDate && params.createDate.length === 2) {
			endDate = timeToStamp(params.createDate[1], 'end')
			startDate = timeToStamp(params.createDate[0], 'begin')
			delete params.createDate
		}
		auditFinancePagination.current = 1
		// setPagination({ ...pagination })
		setSearchParams({ ...params, startDate, endDate })
	}
	//Search 重置
	const onClear = () => {
		auditFinancePagination.current = 1
		setSearchParams({})
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					{/* <OperatingArea>
						<SearchBar showLabel={false} pageName={'pledgeTransfer'} onClear={onClear} onSubmit={onSubmit} />
					</OperatingArea> */}
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...auditFinancePagination}
						rowKey="id"
					/>
				</div>
			</LayoutSlot>
			<AuditModal
				title={auditTitle}
				visible={auditVisible}
				onCancel={() => setAuditVisible(false)}
				onSubmit={submitData}
				confirmLoading={confirmLoading}
				type={auditType}
				data={{}}
			/>
			<Modal
				open={receiveAccountVisible}
				title="融资企业收款账户信息"
				footer={false}
				onCancel={() => {
					setReceiveAccountVisible(false)
					setsetReceiveAccountData(null)
				}}
			>
				{(receiveAccountData && (
					<div style={{ padding: '20px', lineHeight: '30px' }}>
						<p>收款账户户名：{receiveAccountData.accountName}</p>
						<p>收款账户账号：{receiveAccountData.accountNum}</p>
						<p>收款账户开户行：{receiveAccountData.accountBank}</p>
					</div>
				)) || <div>信息获取失败</div>}
			</Modal>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.link {
		margin-right: 5px;
	}
`

export default auditPage
