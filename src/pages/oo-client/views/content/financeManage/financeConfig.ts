import { stampToTime, timeCalculate, numberToThousands } from '@utils/timeFilter'
import { formatNumber } from '@utils/format'

//table简化版
const financeStatusObj = {
	AUDIT: '审核中',
	REJECT: '已拒绝',
	CONFIRM: '已放款',
	REPAID: '已还款',
}

const financeStatusList = [
	{ key: 'AUDIT', name: '审核中' },
	{ key: 'REJECT', name: '已拒绝' },
	{ key: 'CONFIRM', name: '已放款' },
	{ key: 'REPAID', name: '已还款' },
	// { key: 'CANCELED', name: '已取消' },
	{ key: 'PENDING', name: '待签署协议' },
	{ key: 'WAIT_CONFIRM', name: '待放款' },
]

const interestPayWayList = [
	{
		key: 'center',
		name: '核心企业付息',
	},
	{
		key: 'supplier',
		name: '供应商付息',
	},
]
const checkBoxOptions = [
	{ label: '系统提交文件失败', value: 'CREATE' },
	{ label: '待放款方审核', value: 'AUDIT' },
	{ label: '放款方已拒绝', value: 'REJECT' },
	{ label: '已放款', value: 'CONFIRM' },
]
const dateFormat = 'YYYY/MM/DD'
const defaultCheckedList = ['CREATE', 'AUDIT', 'REJECT', 'CONFIRM']

const invoiceTypeInfo = {
	VATSpecial: { code: 'VATSpecial', name: '增值税专用发票' },
	VehicleSales: { code: 'VehicleSales', name: '机动车销售统一发票' },
	VATCommon: { code: 'VATCommon', name: '增值税普通发票' },
	VATOfTransport: { code: 'VATOfTransport', name: '货物运输业增值税专用发票' },
}

const financeInfoObj = {
	financeInfoListC: [
		'applicationNumber',
		'status',
		'currentAmount',
		'interestPayWay',
		'interestRate',
		'financeCosts',
		'serviceFeeRate',
		'serviceFee',
		'factoringRate',
		'factoringCosts',
		'financeDueDate',
		'applicantCompanyName',
		'financeValueDate',
		'fiCompanyName',
		'zdwRegNo',
	],
	financeInfoListS: {
		center: [
			'applicationNumber',
			'status',
			'currentAmount',
			'interestPayWay',
			'financeDueDate',
			'applicantCompanyName',
			'financeValueDate',
			'fiCompanyName',
			'zdwRegNo',
		],
		supplier: [
			'applicationNumber',
			'status',
			'currentAmount',
			'interestRate',
			'financeCosts',
			'serviceFeeRate',
			'serviceFee',
			'factoringRate',
			'factoringCosts',
			'financeDueDate',
			'applicantCompanyName',
			'financeValueDate',
			'fiCompanyName',
			'zdwRegNo',
		],
	},
}

const exportCsvConfig = {
	applicationNumber: {
		name: '融资申请编号',
		format: function (record) {
			return record.applicationNumber
		},
	},
	status: {
		name: '状态',
		format: function (record) {
			return financeStatusObj[record.status]
		},
	},
	createTime: {
		name: '创建日期',
		format: function (record) {
			return stampToTime(record.createTime, 8)
		},
	},
	applicantCompanyName: {
		name: '融资企业',
		format: function (record) {
			return record.applicantCompanyName
		},
	},
	cCompanyName: {
		name: '核心企业',
		format: function (record) {
			return record.relationName
		},
	},
	fiCompanyName: {
		name: '金融机构',
		format: function (record) {
			return record.fiCompanyName
		},
	},
	inputFinanceAmountInYuan: {
		name: '融资金额（¥）',
		format: function (record) {
			return numberToThousands(parseFloat(record.inputFinanceAmountInYuan))
		},
	},
	interestPayWay: {
		name: '付息方式',
		format: function (record) {
			return record.interestPayWay === 'center' ? '核心企业付息' : '供应商付息'
		},
	},
	financeValueDate: {
		name: '起息日',
		format: function (record) {
			let date = ''
			if (record.status === 'CONFIRM') {
				date = stampToTime(record.financeValueDate, 8)!
			}
			return date
		},
	},
	financeDueDate: {
		code: 'financeDueDate',
		name: '融资到期日',
		format: function (record) {
			return stampToTime(record.financeDueDate, 8)
		},
	},
	interestRate: {
		code: 'interestRate',
		name: '年化融资利率',
		format: function (record) {
			let returnInfo = ''
			if (record && record.interestRate) {
				returnInfo = record.interestRate + '%'
			}
			return returnInfo
		},
	},
	financeCosts: {
		code: 'financeCosts',
		name: '融资利息（¥）',
		format: function (record) {
			let returnInfo = ''
			if (record && record.financeDueDate) {
				const day = timeCalculate(record['financeValueDate'], record.financeDueDate, 'day')!
				const financeCosts = formatNumber.toFix2((record.inputFinanceAmountInYuan * (record.interestRate / 100) * day) / 360)
				returnInfo = numberToThousands(financeCosts)
			}
			return returnInfo
		},
	},
	serviceFeeRate: {
		code: 'serviceFeeRate',
		name: '平台费率',
		format: function (record) {
			let returnInfo = ''
			if (record && record.serviceFeeRate) {
				returnInfo = record.serviceFeeRate + '%'
			}
			return returnInfo
		},
	},
	serviceFee: {
		code: 'serviceFee',
		name: '平台服务费（¥）',
		format: function (record) {
			let returnInfo = ''
			if (record && record.financeDueDate) {
				const day = timeCalculate(record['financeValueDate'], record.financeDueDate, 'day')!
				const serviceFee = formatNumber.toFix2((record.inputFinanceAmountInYuan * (record.serviceFeeRate / 100) * day) / 360)
				returnInfo = numberToThousands(serviceFee)
			}
			return returnInfo
		},
	},
	factoringRate: {
		code: 'factoringRate',
		name: '保理业务手续费率',
		format: function (record) {
			let returnInfo = ''
			if (record && record.factoringRate) {
				returnInfo = record.factoringRate + '%'
			}
			return returnInfo
		},
	},
	factoringCosts: {
		code: 'factoringCosts',
		name: '保理业务手续费（¥）',
		format: function (record) {
			let returnInfo = ''
			const factoringCosts = formatNumber.toFix2(record['inputFinanceAmountInYuan'] * (record['factoringRate'] / 100))
			if (factoringCosts) {
				returnInfo = numberToThousands(factoringCosts)
			}
			return returnInfo
		},
	},
	zdwRegNo: {
		code: 'zdwRegNo',
		name: '初始中登网登记编号',
		format: function (record) {
			return record.zdwRegNo ? record.zdwRegNo : ''
		},
	},
	zdwAmendNo: {
		code: 'zdwAmendNo',
		name: '变更中登网登记编号',
		format: function (record) {
			return record.zdwAmendNo ? record.zdwAmendNo : ''
		},
	},
}

export {
	invoiceTypeInfo,
	financeInfoObj,
	financeStatusList,
	interestPayWayList,
	financeStatusObj,
	checkBoxOptions,
	dateFormat,
	defaultCheckedList,
	exportCsvConfig,
}
