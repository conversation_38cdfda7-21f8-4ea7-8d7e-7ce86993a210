import React, { useEffect, useState } from 'react'
import { message, Tooltip, Alert } from 'antd'
import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import { financeSearchApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import styled from 'styled-components'
import { history } from '@src/utils/router'

function reSubmitPage() {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [loading, setLoading] = useState(false)

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize])

	// 得到融资申请列表
	const getList = () => {
		setLoading(true)
		financeSearchApi
			.getFinanceList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				sendBankStatus: 'WAIT',
				status: 'AUDIT',
			})
			.then(
				(res: any) => {
					pagination.total = res['total']
					if (res.list) {
						setDataSource([...res.list])
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => {
					setLoading(false)
					pagination.total = 0
					setDataSource([])
				}
			)
	}

	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	const onOperCallback = (str: string, data: any) => {
		if (str === 'applicationNumber') {
			// 存储融资申请详情，防止刷新数据丢失
			history.push('/content/financeManage/financeDetail', {
				uuidOrNumber: data.uuid,
			})
		} else if (str === 'operation') {
			setLoading(true)
			financeSearchApi
				.secondSubmit({
					uuidOrNumber: data.uuid,
				})
				.then(value => {
					if (value.returnCode === 17000) {
						message.success('提交成功')
						getList()
					} else {
						// 删除关键字（银行接口异常）
						let returnDesc = value.returnDesc.replace('银行接口异常', '')
						message.error(returnDesc)
						setLoading(false)
					}
				})
				.catch(err => {
					setLoading(false)
				})
		}
	}
	const getColumns = () => {
		const pageName = 'searchReSubmitFinance'
		const columnsDic = {
			applicationNumber: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									onOperCallback('applicationNumber', record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			operation: {
				width: 100,
				render: (text: any, record: any) => {
					return (
						<div>
							<span
								className="link"
								onClick={() => {
									onOperCallback('operation', record)
								}}
							>
								{record.sendBankStatus === 'WAIT' ? '提交到银行' : ''}
							</span>
						</div>
					)
				},
			},
		}
		return getColumnsByPageName(pageName, columnsDic)
	}

	function handlePageChange(current: number) {
		pagination.current = current
		setPagination({ ...pagination })
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<Alert message="本页面展示平台所有未成功推送至银行的数据记录，包括还处于审核流程中的数据、以及推送至银行失败的数据" type="info" showIcon />
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						rowKey="id"
					/>
				</div>
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
`

export default reSubmitPage
