import React from 'react'
import { Descriptions, Tooltip } from 'antd'
import { financeStatusObj } from '../../financeConfig'
import { numberToThousands, stampToTime } from '@src/utils/timeFilter'
import { renderTooltip } from '@src/pages/oo-client/config/TableColumnsRender'
import { pledgeFinanceStatusObj } from '@globalConfig/financeCheckManage'

const baseInfo = (props: { data: any }) => {
	const { data } = props
	return (
		<div style={{ marginBottom: '20px' }}>
			<Descriptions bordered column={2}>
				<Descriptions.Item label="融资申请编号">
					<Tooltip title={data.applicationNumber}>{data.applicationNumber}</Tooltip>
				</Descriptions.Item>
				<Descriptions.Item label="融资申请状态">{pledgeFinanceStatusObj[data.status]}</Descriptions.Item>
				<Descriptions.Item label="质押融信金额">{`￥${numberToThousands(data.inputFinanceTransferAmountInYuan)}`}</Descriptions.Item>
				{/* <Descriptions.Item label="融资比例">{data.discount * 100 + '%'}</Descriptions.Item> */}
				<Descriptions.Item label="实际质押率">
					{((Number(data.inputFinanceAmountInYuan) / Number(data.inputFinanceTransferAmountInYuan)) * 100).toFixed(2) + '%'}
				</Descriptions.Item>
				<Descriptions.Item label="融资申请金额">{`￥${numberToThousands(data.inputFinanceAmountInYuan)}`}</Descriptions.Item>
				<Descriptions.Item label="融资企业" contentStyle={{ maxWidth: '220px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
					{renderTooltip(data.applicantCompanyName)}
				</Descriptions.Item>
				{/* <Descriptions.Item label="金融机构" contentStyle={{ maxWidth: '220px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
					{renderTooltip(data.fiCompanyName)}
				</Descriptions.Item> */}
				<Descriptions.Item label="起息日">
					{data.status !== 'CONFIRM' && data.status !== 'REPAID' ? '- -' : stampToTime(data.financeValueDate, 6)}
				</Descriptions.Item>
				<Descriptions.Item label="融资到期日">{stampToTime(data.financeDueDate, 6)}</Descriptions.Item>
				<Descriptions.Item label="初始中登网登记编号">
					<Tooltip title={data.zdwRegNo}>{data.zdwRegNo ? data.zdwRegNo : '- -'}</Tooltip>
				</Descriptions.Item>
				<Descriptions.Item label="变更中登网登记编号">
					<Tooltip title={data.zdwAmendNo}>{data.zdwAmendNo ? data.zdwAmendNo : '- -'}</Tooltip>
				</Descriptions.Item>
				<Descriptions.Item label="创建日期">{stampToTime(data.createTime, 6)}</Descriptions.Item>
				{/* <Descriptions.Item label="付息方式">
					{data.interestPayWay === 'center' ? '核心企业付息' : data.interestPayWay === 'supplier' ? '供应商付息' : ''}
				</Descriptions.Item> */}
				<Descriptions.Item label="处理意见">{data.auditReason ? data.auditReason : '- -'}</Descriptions.Item>
			</Descriptions>
		</div>
	)
}

export default baseInfo
