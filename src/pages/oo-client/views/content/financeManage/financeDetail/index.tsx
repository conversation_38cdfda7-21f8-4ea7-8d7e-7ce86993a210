import React, { useEffect, useState } from 'react'
import { Table, message, Tooltip } from 'antd'
import { renderAmount } from '@src/pages/oo-client/config/TableColumnsRender'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { timeCalculate } from '@src/utils/timeFilter'
import { financeSearchApi, commonApi, financeCheckManageModuleApi, transferManageApi } from '@src/pages/oo-client/api'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { b64toBlob } from '@src/utils/util'
import { history } from '@src/utils/router'
import AttachmentTable from '@src/globalComponents/EditTable/AttachmentTable'
import PDFViewer from '@globalComponents/PDFViewer'
import SmallTitle from '@src/globalComponents/SmallTitle'
import MessageModal from './components/messageModal'
import BaseInfo from './components/baseInfo'
import CostInfo from './components/costInfo'
import RepaymentInfo from './components/repaymentInfo'
import FileSaver from 'file-saver'
import styled from 'styled-components'
import ChainInfoModal from '@src/globalComponents/chainInfoComponents/ChainInfoModal'
import { platformFeeModelEnum } from '@src/globalConfig/codeMaps'
import { getInvoicCheckStatusCols } from '@src/globalBiz/invoiceBizModule'
import { returnModalColumns } from '@src/globalComponents/cross-platform-page/financeCheckManage/financeCheckList/data-deal'
import { WxModal, WxModalProps } from '@src/globalComponents/@cnpm/wx-rc/components'
import SearchTable from '@src/globalComponents/new-widget/wx-table/searchTable'
import DetailModal from '@src/pages/oo-client/components/detailModal'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import moment from 'moment'
import BaseTable from '@src/globalComponents/BaseTable'

function financeDetail() {
	const attachmentTable = React.useRef(null as any)
	const [attachmentInfoList, setAttachmentInfoList] = useState<object[]>([])
	const [baseInfoDataSource, setBaseInfoDataSource] = useState<any>({}) // 基本信息表格中的数据
	const [costDataSource, setCostDataSource] = useState<any>({}) // 基本信息表格中的数据
	const [financeCouDataSource, setFinanceCouDataSource] = useState<any[]>([]) // 融信融信表格中的数据
	const [financingInvoiceDataSource, setFinancingInvoiceDataSource] = useState<any[]>([]) // 发票表格中的数据
	const [openingInvoiceDataSource, setOpeningInvoiceDataSource] = useState<any[]>([]) // 发票表格中的数据
	const [protocolList, setProtocolList] = useState<any[]>([]) // 协议数据
	const [financeContractInfo, setFinanceContractInfo] = useState<any>({}) // 融资合同
	const [financeCouAllTableLoading, setFinanceCouAllTableLoading] = useState<boolean>(false)
	const [billLoading, setBillLoading] = useState<boolean>(false)
	const [inDetailVisible, setInDetailVisible] = useState(false)
	const [invoiceData, setInvoiceData] = useState<any>({})
	const [refundInfo, setRefundInfo] = useState<any>({}) // 还款信息
	const [operatingList, setOperatingList] = useState([]) //操作记录

	// 处理pdf预览
	const [path, setPath] = useState('')
	const [pdfVisible, setPdfVisible] = useState(false)

	// 处理请求报文预览
	const [messageVisible, setMessageVisible] = useState(false)
	const [apiMessage, setApiMessage] = useState<any>('')

	const [chainInfoModalVisible, setChainInfoModalVisible] = useState<boolean>(false)
	const [onChainData, setOnChainData] = useState({})
	const [platformProtocol, setPlatformProtocol] = useState(null)
	const [zdCheckList, setZdCheckList] = useState([])
	const [failText, setFailText] = React.useState<string>('')
	const resultModalRef = React.useRef(null)
	const financeCostData = [
		{
			key: '1',
			project: '融资利息',
			rateType: '年化',
			rate: costDataSource.interestRate,
			cost: costDataSource.interestCost,
			platformFeeModel: costDataSource.platformFeeModel ? '银行扣取' : '--',
		},
		// {
		// 	key: '2',
		// 	project: '保理手续费',
		// 	rateType: '单笔',
		// 	rate: costDataSource.factoringRate,
		// 	cost: costDataSource.factoringCost,
		// 	platformFeeModel: costDataSource.platformFeeModel ? '银行扣取' : '--',
		// },
		// {
		// 	key: '3',
		// 	project: '平台服务费',
		// 	rateType: '年化',
		// 	rate: costDataSource.serviceFeeRate,
		// 	cost: costDataSource.serviceFeeCost,
		// 	platformFeeModel: costDataSource.platformFeeModel || '--',
		// },
	]

	const failReasonFun = (text, record) => {
		setFailText(text)
		resultModalRef?.current?.show()
	}

	const detailFun = record => {
		//跳转至查重任务详情
		history.push('/content/financeCheckManage/financeCheckDetail', {
			taskNo: record?.taskNo,
		})
	}

	const modalColumns = returnModalColumns({ failReasonFun, detailFun, specialColumn: false })

	useEffect(() => {
		const uuidOrNumber = history.location.state?.uuidOrNumber
		const applicationNumber = history.location.state?.applicationNumber

		if (uuidOrNumber) getFinanceDetail(uuidOrNumber)
		if (applicationNumber) getFlowChartData(applicationNumber)

		return () => {
			// 组件卸载时清除uuid
			localStorage.removeItem('uuidOrNumber')
		}
	}, [])

	const clickInvoiceNumber = record => {
		setInDetailVisible(true)
		setInvoiceData(record)
	}

	const getFlowChartData = businessKey => {
		transferManageApi.getTransferChartData({ businessKey }).then(
			res => {
				if (res?.data?.activityList) {
					res?.data?.activityList.map(item => {
						if (item?.activityType === 'userTask' && item?.assignee) {
							let { taskComments, endTime, assigneeInfo } = item
							setOperatingList(preOperatingList => [...preOperatingList, { taskComments, endTime, ...assigneeInfo }])
						}
					})
				}
			},
			reason => {
				console.log('reason', reason)
			}
		)
	}

	const getFinanceDetail = financeDetailUuid => {
		const hide = message.loading('加载中...', 0)
		financeSearchApi
			.getFinanceDetail({
				uuidOrNumber: financeDetailUuid,
			})
			.then(
				(res: any) => {
					// 设置api报文
					setApiMessage(res.apiMessage)
					setTimeout(hide, 0)
					//financeZdw该字段如果有，则使用该对象中的中登网数据，反之使用最外层数据,兼容老数据
					if (res?.financeZdw) {
						res.zdwRegNo = res?.financeZdw?.zdwRegNo
						res.zdwAmendNo = res?.financeZdw?.zdwAmendNo
					}
					let dateDiff = 0
					let m1 = moment(res.financeDueDate)
					let m2 = moment(res.financeValueDate)
					dateDiff = res.financeDueDate ? m1.diff(m2, 'day') : 0
					let _ratenow = res.factoringRate ? ((Number(res.inputFinanceAmountInYuan || 0) * Number(res.factoringRate || 0) * dateDiff) / 360).toFixed(2) : 0 // 试算利息
					let _extralMount = res.factoringRate
						? Number(res.inputFinanceTransferAmountInYuan || 0) - Number(res.inputFinanceAmountInYuan || 0) - Number(_ratenow || 0)
						: 0 // 余额试算利息
					// 设置基本信息表格中的数据
					setBaseInfoDataSource({
						applicationNumber: res.applicationNumber, // 融资申请编号
						status: res.status, // 融资申请状态
						inputFinanceTransferAmountInYuan: res.inputFinanceTransferAmountInYuan, // 转让融信金额
						discount: res.discount, // 融资比例
						inputFinanceAmountInYuan: res.inputFinanceAmountInYuan, // 融资金额
						fiCompanyName: res.fiCompanyName, // 金融机构
						financeValueDate: res.financeValueDate, // 起息日
						financeDueDate: res.financeDueDate, // 融资到期日
						applicantCompanyName: res.applicantCompanyName, // 融资企业
						zdwRegNo: res.zdwRegNo, // 中登网登记编号
						zdwAmendNo: res.zdwAmendNo, // 变更中登网登记编号
						createTime: res.createTime, // 创建日期
						interestPayWay: res.interestPayWay, // 付息方式
						auditReason: res.auditReason, // 审核意见
						factoringRate: res.factoringRate, // 融资年华利率
						dateDiff: dateDiff, //融资天数
						ratenow: _ratenow, // 试算利息
						extralMount: _extralMount, // 试算余额
					})
					let diffDay: number = 0
					// 当融资申请被确认前，计算费用的时间是从创建融资的时间(createTime) ---> 兑付到期日
					// 当融资申请被确认后，计算费用的时间是从起息日(financeValueDate) ---> 兑付到期日
					if (res.status === 'CONFIRM') {
						diffDay = timeCalculate(res.financeValueDate, res.financeDueDate, 'day') || 0
					} else {
						diffDay = timeCalculate(res.createTime, res.financeDueDate, 'day') || 0
					}
					console.log('diffDay', diffDay)
					// 设置融资费用表格中的数据
					setCostDataSource({
						factoringRate: res.factoringRate, // 保理手续利率
						factoringCost: (res.factoringRate * res.inputFinanceAmountInYuan) / 100,
						interestRate: res.interestRate, // 融资利率
						interestCost: (res.inputFinanceAmountInYuan * res.interestRate * diffDay) / 100 / 360,
						serviceFeeRate: res.serviceFeeRate, // 平台服务费率
						serviceFeeCost: (res.inputFinanceAmountInYuan * res.serviceFeeRate * diffDay) / 100 / 360,
						platformFeeModel: res.platformFeeModel ? platformFeeModelEnum.filter(item => res.platformFeeModel === item.value)[0]['label'] : null,
					})
					// 设置融资融信表格中的数据
					console.log('融资融信', res.couList)
					setFinanceCouDataSource(res.couList)
					// 设置发票表格中的数据
					setFinancingInvoiceDataSource(res.invoices)
					setOpeningInvoiceDataSource(res.invoicesForCouCreate)
					setZdCheckList(res.zdwDuplicateCheckList)
					//设置平台协议
					setProtocolList(res?.protocolList)
					// 设置融资合同
					setFinanceContractInfo(JSON.parse(res.fileInfo))
					// 设置附件信息
					setAttachmentInfoList(JSON.parse(res.attachment || '[]'))
					// 设置还款信息
					setRefundInfo(res?.refundInfo)
				},
				reason => {
					setTimeout(hide, 0)
					console.log('reason: ', reason)
				}
			)
	}
	// 从协议数组中获取对应协议的url
	const getProtocolUrlByType = (arr, type) => {
		const files = arr.filter(element => {
			return element.type === type
		})
		return files[0]?.url || null
	}
	//展示协议
	const showProtocol = fileUrl => {
		setPath(fileUrl)
		setPdfVisible(true)
	}
	// 点击查看文件
	const clickSeeFile = (record, type) => {
		let fileUrl = ''
		if (type === 'paymentUndertaking') {
			fileUrl = getProtocolUrlByType(JSON.parse(record.commitFile).protocolList, 'commitment')
		}
		setPath(fileUrl)
		setPdfVisible(true)
	}
	// 单据一键下载
	const clickDownloadBill = async () => {
		setBillLoading(true)
		let couUuids: any = []
		financeCouDataSource.forEach((cou, index) => {
			console.log(cou)
			couUuids.push({
				originalUuid: cou.originalUuid,
				couUuid: cou.uuid,
				couNo: cou.couNo,
			})
		})
		const fileUrl =
			getProtocolUrlByType(financeContractInfo.protocolList, 'financeContract') || getProtocolUrlByType(financeContractInfo.protocolList, 'financeContractToC')
		const params: any = {
			fileUrl: fileUrl,
			couUuids,
			financeNumber: baseInfoDataSource?.applicationNumber,
		}
		const res: any = await financeSearchApi.billDownload(params).catch(err => {
			console.log('err', err)
		})
		if (res) {
			const fileblob = b64toBlob(res)
			FileSaver.saveAs(fileblob, `${baseInfoDataSource?.applicationNumber}.zip`)
		}
		setBillLoading(false)
	}
	// 点击下载文件
	const clickDownloadFile = (record: any, type: string) => {
		setFinanceCouAllTableLoading(true)
		try {
			if (type === 'flowContract') {
				financeSearchApi.allContractDownload({ couUuid: record.uuid }).then(value => {
					const fileblob = b64toBlob(value)
					FileSaver.saveAs(fileblob, `${record.couNo}_合同.zip`)
					setFinanceCouAllTableLoading(false)
				})
			} else if (type === 'couFlowSheetAll') {
				financeSearchApi.allTransferDownload({ couUuid: record.uuid }).then(value => {
					const fileblob = b64toBlob(value)
					FileSaver.saveAs(fileblob, `${record.couNo}_流转单（全路径）.zip`)
					setFinanceCouAllTableLoading(false)
				})
			} else if (type === 'couFlowTable') {
				const fileUrl = JSON.parse(record.transferFile).transferPath
				// 兼容老数据，老数据的融信浏转路径表是svg、png
				const extName = fileUrl.split('.').pop().toLowerCase()
				commonApi.downloadFile({ fileUrl }).then(url => {
					const fileblob = b64toBlob(url)
					FileSaver.saveAs(fileblob, `${record.couNo}_流转路径表.${extName}`)
					setFinanceCouAllTableLoading(false)
				})
			}
		} catch (e) {
			message.error('请求失败')
			setFinanceCouAllTableLoading(false)
		}
	}

	const getColumns = type => {
		const columnsDic = {
			checkState: getInvoicCheckStatusCols().checkState,
			discount: {
				render: (text: any, record: any) => {
					return text * 100 + '%'
				},
			},
			invoiceNumber: {
				render: (text: any, record: any) => {
					return (
						<div
							onClick={() => {
								clickInvoiceNumber(record)
							}}
						>
							<Tooltip className="link" title={text}>
								{text}
							</Tooltip>
						</div>
					)
				},
			},
			transferPracticalAmountInYuan: {
				render: (text: any, record: any) => {
					return renderAmount(record.transferPracticalAmountInYuan)
				},
			},
			// 付款承诺函
			paymentUndertaking: {
				dataIndex: 'paymentUndertaking',
				render: (text: any, record: any) => {
					return (
						<span
							className="link"
							onClick={() => {
								clickSeeFile(record, 'paymentUndertaking')
							}}
						>
							查看
						</span>
					)
				},
			},
			// 流转合同
			circulationContract: {
				dataIndex: 'circulationContract',
				render: (text: any, record: any) => {
					return (
						<span
							className="link"
							onClick={() => {
								clickDownloadFile(record, 'flowContract')
							}}
						>
							下载
						</span>
					)
				},
			},
			// 融信浏转路径表
			couFlowTable: {
				dataIndex: 'couFlowTable',
				render: (text: any, record: any) => {
					return (
						<span
							className="link"
							onClick={() => {
								clickDownloadFile(record, 'couFlowTable')
							}}
						>
							下载
						</span>
					)
				},
			},
			// 融信流转单（全路径）
			couFlowSheetAll: {
				dataIndex: 'couFlowSheetAll',
				render: (text: any, record: any) => {
					return (
						<span
							className="link"
							onClick={() => {
								clickDownloadFile(record, 'couFlowSheetAll')
							}}
						>
							下载
						</span>
					)
				},
			},
			cost: {
				dataIndex: 'cost',
				title: (
					<div>
						费用(￥)&nbsp;&nbsp;
						<Tooltip placement="top" title="以创建日期作为起息日估算，实际费用以金融机构实际放款日计算12。">
							<ExclamationCircleOutlined className="costConfirm" style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer' }} />
						</Tooltip>
					</div>
				),
			},
			onChainCertificate: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => showCertificate(record)}>
							查看证书
						</span>
					)
				},
			},
		}
		return evenlyTableColunms(getColumnsByPageName(type, columnsDic))
	}

	const getOperatingColumns = () => {
		return evenlyTableColunms(getColumnsByPageName('operatingRecord'))
	}
	const showCertificate = record => {
		commonApi
			.queryCouProof({ couNo: record.couNo })
			.then(data => {
				if (data && data.status === 'DONE') {
					setOnChainData(data)
					setChainInfoModalVisible(true)
				} else {
					message.warning('证书未生成，完成审批且上链后生成证书')
				}
			})
			.catch(err => {
				console.log(err)
			})
	}
	const viewResultModalProps: WxModalProps = {
		modalProps: {
			title: '查重失败原因',
			width: 420,
			bodyStyle: {
				padding: '32px 20px',
			},
			footer: null,
		},
	}
	return (
		<Box>
			<div className="card-wrapper">
				{/* 融资信息 */}
				<div className="financeInfo">
					<div className="boxContentWrap">
						<SmallTitle text="融资信息" />
						<div className="boxContent">
							<div className="baseInfo">
								<div className="baseInfoHeader" style={{ marginBottom: '10px' }}>
									<div className="title">
										<span>基本信息</span>
									</div>
									{/* <div className="message" style={{ marginLeft: '50px' }}>
								<Button
									onClick={() => {
										if (!apiMessage) {
											message.error('无报文信息')
										} else {
											setMessageVisible(true)
										}
									}}
									type="primary"
								>
									查看接口报文
								</Button>
							</div>
							<div className="bill" style={{ marginLeft: '50px' }}>
								<Button
									onClick={() => {
										clickDownloadBill()
									}}
									type="primary"
									loading={billLoading}
								>
									单据一键下载
								</Button>
							</div> */}
								</div>
								<BaseInfo data={baseInfoDataSource}></BaseInfo>
							</div>
							<div>
								<div className="financExpensesTitle" style={{ marginBottom: '10px' }}>
									融资费用
								</div>
								<CostInfo data={baseInfoDataSource}></CostInfo>
							</div>
							{baseInfoDataSource?.status === 'REPAID' && refundInfo ? (
								<div className="boxContentWrap">
									<SmallTitle text="还款信息" />
									<div className="boxContent">
										<RepaymentInfo data={refundInfo}></RepaymentInfo>
									</div>
								</div>
							) : (
								''
							)}

							<div className="financExpenses" style={{ marginBottom: '40px' }}>
								<div className="financExpensesTitle" style={{ marginBottom: '10px' }}>
									中登查重信息
								</div>
								<Table dataSource={zdCheckList} columns={modalColumns} pagination={false} />
							</div>
						</div>
					</div>
					{/* <div className="financExpenses" style={{ marginBottom: '40px' }}>
						<div className="financExpensesTitle" style={{ marginBottom: '10px' }}>
							融资费用
						</div>
						<Table dataSource={financeCostData} columns={getColumns('financeCost')} pagination={false} />
					</div> */}
				</div>
				{/* 融资协议 */}
				<div className="financeAgree">
					<div className="boxContentWrap">
						<SmallTitle text="融资协议" />
						<div className="boxContent">
							<div className="financePdf" style={{ marginBottom: '40px', backgroundColor: 'rgb(240, 240, 240)', color: 'rgb(3, 114, 171)', padding: '20px' }}>
								{protocolList.length > 0 &&
									protocolList.map(item => {
										const suffix = item?.protocolMinioUrl?.split('.')?.[1]
										return (
											<div
												style={{ cursor: 'pointer', marginTop: '8px' }}
												onClick={() => {
													showProtocol(item?.protocolMinioUrl)
												}}
											>
												{item?.protocolName + '.' + suffix}
											</div>
										)
									})}
							</div>
						</div>
					</div>
				</div>
				{/* 融资融信 */}
				<div className="financeCou">
					<div className="boxContentWrap">
						<SmallTitle text="运营机构审核" />
						<div className="boxContent">
							<Table
								loading={financeCouAllTableLoading}
								rowKey="id"
								dataSource={financeCouDataSource}
								columns={getColumns('financeCouAll')}
								pagination={false}
								style={{ marginBottom: '40px' }}
							/>
						</div>
					</div>
				</div>
				{/* 融资发票 */}
				<div className="financeInvoice">
					<div className="boxContentWrap">
						<SmallTitle text="融资发票" />
						<div className="boxContent">
							<Table
								rowKey="id"
								dataSource={financingInvoiceDataSource}
								columns={getColumns('financeInvoice')}
								pagination={false}
								style={{ marginBottom: '40px' }}
							/>
						</div>
					</div>
				</div>
				{/* 开立发票 */}
				<div className="financeInvoice">
					<div className="boxContentWrap">
						<SmallTitle text="开立发票" />
						<div className="boxContent">
							<Table
								rowKey="id"
								dataSource={openingInvoiceDataSource}
								columns={getColumns('financeInvoice')}
								pagination={false}
								style={{ marginBottom: '40px' }}
							/>
						</div>
					</div>
				</div>
				{/* 其他附件 */}
				<div className="boxContentWrap">
					<AttachmentTable ref={attachmentTable} isEdit={false} initialValues={attachmentInfoList} />
				</div>
				<div className="boxContentWrap">
					<SmallTitle text="操作记录" />
					<div className="boxContent">
						<BaseTable columns={getOperatingColumns()} dataSource={operatingList} havePagination={false}></BaseTable>
					</div>
				</div>
			</div>
			<PDFViewer title="" visible={pdfVisible} pdfUrl={path} onCancel={() => setPdfVisible(false)} />
			<MessageModal visible={messageVisible} data={apiMessage} onCancel={() => setMessageVisible(false)} />
			<ChainInfoModal visible={chainInfoModalVisible} onCancel={() => setChainInfoModalVisible(false)} chainData={onChainData} />
			<WxModal {...viewResultModalProps} ref={resultModalRef}>
				<span>{failText}</span>
			</WxModal>
			<DetailModal title="查看发票" pageName="invoice" onCancel={() => setInDetailVisible(false)} visible={inDetailVisible} dataSource={invoiceData} />
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.financExpenses {
		position: relative;
	}
	.baseInfoHeader {
		display: flex;
		align-items: center;
	}
`

export default financeDetail
