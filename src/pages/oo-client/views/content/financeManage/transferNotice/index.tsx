import React, { useEffect, useState } from 'react'
import { Toolt<PERSON> } from 'antd'
import PDFViewer from '@globalComponents/PDFViewer'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import styled from 'styled-components'
import BaseTable from '@src/globalComponents/BaseTable'
import OperatingArea from '@src/globalComponents/OperatingArea'
import SearchBar from '@src/pages/oo-client/components/SearchBar'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { financeSearchApi } from '@src/pages/oo-client/api'
import moment from 'moment'

const TransferNotice = () => {
	const [dataSource, setNoticeDataSrc] = useState<any[]>([])
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [searchParams, setSearchParams] = useState<{
		relationUuid?: string
	}>({})

	const [loading, setLoading] = useState(false)
	//pdf预览弹框控制
	const [pdfVisible, setPdfVisible] = React.useState<boolean>(false)
	const [pdfUrl, setPdfUrl] = React.useState<string>('')

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	function getList() {
		setLoading(true)
		console.log(pagination)

		financeSearchApi
			.getTransferNotice({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...searchParams,
			})
			.then(
				res => {
					if (res) {
						setPagination({
							...pagination,
							total: res.total,
						})
						if (res.list && res.list.length > 0) {
							setNoticeDataSrc([...res.list])
						} else {
							setNoticeDataSrc([])
						}
						setLoading(false)
					}
				},
				err => {
					setLoading(false)
					pagination.total = 0
					setNoticeDataSrc([])
				}
			)
	}

	//获取table的column
	const getColumns = () => {
		const pageName = 'transferNotice'
		const columnsDic = {
			fromName: {
				title: '发送方',
				render: (text: any, record: any) => {
					return (
						<Tooltip placement="topLeft" title={record.fromName}>
							{record.fromName}
						</Tooltip>
					)
				},
			},
			toName: {
				title: '接收方',
			},
			createDate: {
				title: '发送日期',
			},
			fiCompanyName: {
				dataIndex: 'fiName',
			},
			financeCompany: {
				dataIndex: 'financeCompanyName',
			},
			applicationNumber: {
				dataIndex: 'financeNumber',
			},
			operation: {
				render: (text: any, record: any) => {
					return (
						<div>
							{
								<span
									className="link"
									onClick={() => {
										setPdfUrl(record.fileInfo)
										setPdfVisible(true)
									}}
								>
									查看
								</span>
							}
						</div>
					)
				},
			},
		}
		return getColumnsByPageName(pageName, columnsDic)
	}

	const handlePageChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	const onSubmit = (params: any) => {
		pagination.current = 1
		setPagination({ ...pagination })
		setSearchParams(params)
	}

	const onClear = () => {
		setSearchParams({})
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar pageName="tansferNotice" onSubmit={onSubmit} onClear={onClear} />
					</OperatingArea>
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						rowKey="id"
					/>
				</div>
			</LayoutSlot>
			<PDFViewer title={''} visible={pdfVisible} pdfUrl={pdfUrl} onCancel={() => setPdfVisible(false)} />
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
`

export default TransferNotice
