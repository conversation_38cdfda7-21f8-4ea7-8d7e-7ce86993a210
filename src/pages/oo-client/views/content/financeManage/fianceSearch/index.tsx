import React, { useEffect, useRef, useState } from 'react'
import { But<PERSON>, message, Tooltip } from 'antd'
import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import { financeSearchApi } from '@src/pages/oo-client/api'
import { stampToTime } from '@src/utils/timeFilter'
import OperatingArea from '@src/globalComponents/OperatingArea'
import FlowChartModal from '@src/globalComponents/FlowChartModal'
import SearchBar from '@ooClientComponents/SearchBar'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { financeStatusList } from '../financeConfig'
import { timeToStamp } from '@utils/timeFilter'
import { getFlowElementStatus, addFIStatus } from '@utils/dataClean'
import styled from 'styled-components'
import { history } from '@src/utils/router'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import { financeStatusMap } from '@src/pages/oo-client/config/financeManage'
import { renderDate_ } from '@src/pages/oo-client/config/TableColumnsRender'
import { invalidVal } from '@src/pages/oo-client/biz/bizIndex'
import { hasAuth } from '@src/pages/oo-client/biz/bizIndex'

function searchPage() {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const urlParams = new URLSearchParams(window.location.search || window.location.href.split('?')[1] || '')
	const orgId = urlParams.get('orgId')
	const orgName = urlParams.get('orgName')
	const status = urlParams.get('status') ? (urlParams.get('status') == 'totalFinanceAmount' ? ['CONFIRM', 'REPAID'] : [urlParams.get('status')]) : null
	const startDate = urlParams.get('startDate') ? Number(urlParams.get('startDate')) : null
	const endDate = urlParams.get('endDate') ? Number(urlParams.get('endDate')) : null

	const [searchParams, setSearchParams] = useState<any>({ orgId, startDate, endDate, statusList: status })
	const [loading, setLoading] = useState(false)
	const [exportLoading, setExportLoading] = React.useState(false)
	const [flowChartVisible, setFlowChartVisible] = useState(false) // 审核流传图的弹窗
	const [flowChartData, setFlowChartData] = useState<any[]>([]) // 流转图数据

	const initValues = { orgId: { id: orgId, name: orgName }, createDate: [startDate, endDate], statusList: status }

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	// 得到融资申请列表
	const getList = () => {
		setLoading(true)
		financeSearchApi
			.getFinanceList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...searchParams,
			})
			.then(
				(res: any) => {
					pagination.total = res['total']
					if (res.list) {
						setDataSource([...res.list])
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => {
					setLoading(false)
					pagination.total = 0
					setDataSource([])
				}
			)
	}
	// 导出融资申请列表
	const handleExportClick = async () => {
		setExportLoading(true)
		const fileName = `Download_${stampToTime(new Date().getTime(), 9)}.xlsx`
		financeSearchApi
			.exportOoFinanceList({
				...searchParams,
				pageNum: 1,
				pageSize: 2147483646,
			})
			.then((response: any) => {
				let type = 'application/vnd.ms-excel'
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}
	//手动推送影像
	const manualSendImages = record => {
		financeSearchApi
			.manualPushImages({ uuidOrNumber: record?.applicationNumber })
			.then(res => {
				message.success('已重新推送,若接收异常请再手动推送')
				getList()
			})
			.catch(err => {
				console.log(err)
			})
	}
	// 获取融信流转的流程图数据
	const getFlowChartData = record => {
		let businessKey = record.applicationNumber
		financeSearchApi.getTransferChartData({ businessKey }).then(
			res => {
				if (res.returnCode == 17000) {
					setFlowChartVisible(true)
					let result = getFlowElementStatus(res.data)
					// 江阴质押融资
					if (res.data.processDefKey === 'pledgeFinanceFinal') {
						setFlowChartData(result)
					}
					// 直连模式（江西）和非直连模式（润楼）
					else if (res.data.processDefKey === 'finance') {
						let resultAddFI = addFIStatus(result, record)
						setFlowChartData(resultAddFI)
					} else if (res.data.processDefKey === 'rlFinance') {
						setFlowChartData(result)
					}
				} else {
					message.error('此项数据暂不支持查看流程图')
				}
			},
			reason => {
				console.log('reason', reason)
			}
		)
	}
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	const onOperCallback = (str: string, data: any) => {
		if (str === 'applicationNumber') {
			// 存储融资申请详情，防止刷新数据丢失
			history.push('/content/financeManage/financeDetail', {
				uuidOrNumber: data.uuid,
				applicationNumber: data.applicationNumber,
			})
		}
	}
	const getColumns = () => {
		const columnsDic = {
			applicationNumber: {
				width: 250,
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									onOperCallback('applicationNumber', record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			financeValueDate: {
				render: (text: any, record: any) => {
					let { status } = record
					if (status === financeStatusMap.confirm || status === financeStatusMap.repaid) {
						return renderDate_(text)
					}
					return invalidVal
				},
			},
			operation: {
				width: 80,
				render: (text: any, record: any) => {
					// 2024/3/30增加手动推送影像操作 status CONFIRM(已放款)
					return (
						<div>
							<span
								className="link"
								onClick={() => {
									getFlowChartData(record)
								}}
							>
								审核流程
							</span>
							<span
								className="link"
								onClick={() => {
									manualSendImages(record)
								}}
							>
								{record.status === 'WAIT_CONFIRM' && hasAuth('oo_financialManage:fianceSearch:push') ? '手动推送影像' : ''}
							</span>
						</div>
					)
				},
			},
		}
		return getColumnsByPageName('searchCouFinance', columnsDic)
	}
	const onSubmit = (params: any) => {
		const statusList = params['statusList']
			? Object.prototype.toString.call(params['statusList']) === '[object Array]'
				? params['statusList']
				: params['statusList'].includes(',')
				? params['statusList'].split(',')
				: [params['statusList']]
			: null
		const applicationNumber = params['applicationNumber'] ? params['applicationNumber'] : ''
		const fiUuid = params['fiUuid'] ? params['fiUuid'] : ''
		const orgId = params['orgId'] ? params['orgId'] : ''
		let fiUuids: any = []
		if (fiUuid) fiUuids = [fiUuid]
		let startDate, endDate
		if (params && params.createDate && params.createDate.length === 2) {
			endDate = initValues?.createDate[1] === params.createDate[1] ? params.createDate[1] : timeToStamp(params.createDate[1], 'end')
			startDate = params.createDate[0]
		}
		//起息日
		let startValueDate, endValueDate
		if (params && params.valueDate && params.valueDate.length === 2) {
			endValueDate = params.valueDate[1] && stampToTime(params.valueDate[1], 6)
			startValueDate = params.valueDate[0] && stampToTime(params.valueDate[0], 6)
		}
		//融资到期日
		let startDueDate, endDueDate
		if (params && params.dueDate && params.dueDate.length === 2) {
			endDueDate = params.dueDate[1] && stampToTime(params.dueDate[1], 6)
			startDueDate = params.dueDate[0] && stampToTime(params.dueDate[0], 6)
		}
		const paramsObj = {
			statusList,
			applicationNumber,
			startDate,
			endDate,
			fiUuids,
			startValueDate,
			endValueDate,
			startDueDate,
			endDueDate,
			orgId,
		}
		pagination.current = 1
		setSearchParams(paramsObj)
	}
	const onClear = () => {
		setSearchParams({})
	}
	function handlePageChange(current: number) {
		pagination.current = current
		setPagination({ ...pagination })
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<div style={{ alignItems: 'flex-start', display: 'flex' }}>
							<SearchBar
								optionData={{ statusList: financeStatusList }}
								onSubmit={onSubmit}
								onClear={onClear}
								pageName="searchFinance"
								showLabel={true}
								initValues={initValues}
								// needFold={true}
							/>
							<Button style={{ marginLeft: '5px' }} onClick={handleExportClick} type="primary" loading={exportLoading}>
								导出
							</Button>
						</div>
					</OperatingArea>
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						rowKey="id"
					/>
				</div>
				<FlowChartModal
					type="finance"
					data={flowChartData}
					visible={flowChartVisible}
					onCancel={() => {
						setFlowChartVisible(false)
					}}
				></FlowChartModal>
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
`

export default searchPage
