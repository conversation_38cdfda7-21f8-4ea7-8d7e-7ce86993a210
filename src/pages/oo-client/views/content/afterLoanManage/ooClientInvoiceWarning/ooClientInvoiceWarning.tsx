import React from 'react'
import { Button } from 'antd'
import styled from 'styled-components'
import { invoicingManageApi } from '@src/pages/oo-client/api'
import InvoiceWarning from '@src/globalComponents/cross-platform-page/invoiceWarning/invoiceWarning'
import { history } from '@src/utils/router'
import { getStorage, hasAuth } from '@src/pages/oo-client/biz/bizIndex'
const OoClientInvoiceWarning = () => {
	let requestList = {
		getListData: {
			requestApi: invoicingManageApi.getInvoiceAlarmList,
		},
		exportCouCsv: {
			requestApi: invoicingManageApi.exportInvoiceAlarm,
		},
	}
	return (
		<PageTag>
			<div className="page-top-container">
				{
					// 客户经理不展示此按钮
					getStorage('roleCode') !== 'OPOperator' && (
						<Button
							className="top-task-btn"
							onClick={() => {
								history.push('/content/afterLoanManage/invoiceWarningTaskMange')
							}}
							type="primary"
						>
							发票查验任务管理
						</Button>
					)
				}
			</div>
			<InvoiceWarning
				excelBizType="ooInvoiceAlarm"
				hasFiInputAuth={hasAuth('oo_afterLoanManage:ooClientInvoiceWarning_with_fi_search')}
				requestList={requestList}
			/>
		</PageTag>
	)
}

export default OoClientInvoiceWarning

const PageTag = styled.div`
	.page-top-container {
		position: relative;
		height: 10px;
		.top-task-btn {
			position: absolute;
			top: 30px;
			right: 17px;
			z-index: 10;
		}
	}
`
