import React, { useState, useRef, forwardRef, useEffect } from 'react'
import { invoicingManageApi } from '@src/pages/oo-client/api'
import SearchTable from '@globalComponents/new-widget/wx-table/searchTable'
import { WxBaseForm, WxModal, useModalParams } from '@globalComponents/@cnpm/wx-rc/components/index'
import type { WxBaseFormProps, WxModalProps } from '@globalComponents/@cnpm/wx-rc/components/index'
import { getTabTit, invalidVal } from '@globalBiz/gBiz'
import { Button, Modal, DatePicker, Form, message, Select } from 'antd'

import { ExclamationCircleFilled } from '@ant-design/icons'
import styled from 'styled-components'
import { taskStatusMap, createNextTaskMap } from '@src/globalBiz/invoiceBizModule'
import dayjs from 'dayjs'
import moment from 'moment'

const taskStatusSelectMap = [
	{
		label: '不创建',
		value: 0,
	},
	{
		label: '下一天',
		value: 1,
	},
	{
		label: '下一周',
		value: 2,
	},
	{
		label: '下一月',
		value: 3,
	},
]

const AddNewTask = forwardRef((props: any, modalRef: any) => {
	const [form] = Form.useForm()
	const [loading, setLoading] = useState(false)
	const { open, tableRef } = useModalParams(modalRef)
	const { recode, title } = props

	const taskModalProps = {
		modalProps: {
			title: title,
			onOk: () => {
				form.submit()
			},
			confirmLoading: loading,
			bodyStyle: {
				width: '650px',
			},
		},
	}

	const taskFormProps: WxBaseFormProps = {
		formProps: {
			form,
			onFinish: values => {
				setLoading(true)
				const id = recode ? { id: recode?.id } : {}
				const params = {
					startTime: dayjs(values.startTime).format('YYYY-MM-DD HH:mm:ss'),
					nextStep: values.nextStep,
					...id,
				}
				invoicingManageApi
					.taskCreate(params)
					.then(res => {
						tableRef?.current?.updateTable()
						modalRef?.current?.hide()
						message.success({
							content: '提交成功',
							key: 'taskCreateSuccess',
						})
					})
					.finally(() => {
						setLoading(false)
					})
			},
		},
		formItemList: [
			{
				control: <DatePicker showTime />,
				formItemProps: {
					label: '启动时间',
					name: 'startTime',
					rules: [
						{
							required: true,
							message: '请设置',
						},
						{
							validator: (r, value) => {
								if (dayjs(value).valueOf() < Date.now()) {
									return Promise.reject('所选时间应该大于当前时间')
								} else {
									return Promise.resolve()
								}
							},
						},
					],
				},
			},
			{
				control: <Select placeholder="请选择" options={taskStatusSelectMap} />,
				formItemProps: {
					label: '自动创建下次任务',
					name: 'nextStep',
					rules: [
						{
							required: true,
							message: '请设置',
						},
					],
				},
			},
		],
		needFooter: false,
	}

	useEffect(() => {
		if (open) {
			form.resetFields()
			if (recode) {
				form.setFieldsValue({
					startTime: moment(recode.startTime),
					nextStep: Number(recode.nextStep),
				})
			}
		}
	}, [open, recode])

	return (
		<WxModal {...taskModalProps} ref={modalRef}>
			<div style={{ margin: '40px 0 30px 0' }}>
				<WxBaseForm {...taskFormProps}></WxBaseForm>
				<div style={{ wordBreak: 'break-all', width: '450px', color: '#F19922' }}>当前任务执行时，会自动创建下一次查验任务。下一次查验任务继承当前任务配置</div>
			</div>
		</WxModal>
	)
})

const InvoiceWarningTaskManage = () => {
	const [loading, setLoading] = useState(false)
	const [modal, contextHolder] = Modal.useModal()
	const [currentRecord, setCurrentRecord] = useState(null)
	const [taskTitle, setTaskTitle] = useState('')
	const tableRef = useRef(null)
	const addTaskRef = useRef(null)

	const columns = [
		getTabTit(['任务编号', 'taskNo']),
		getTabTit(['启动时间', 'startTime']),
		getTabTit(['停止时间', 'endTime']),
		getTabTit(['自动创建下次任务', 'nextStep'], text => {
			return createNextTaskMap[text] || invalidVal
		}),
		getTabTit(['状态', 'taskStatus'], text => {
			return taskStatusMap[text] || invalidVal
		}),
		getTabTit(['操作', 'operation'], (text, record) => {
			/*
            0: "未执行", // 可以删除
            1: "执行中", // 可以停止
            */
			const statusMap = {
				0: (
					<div style={{ display: 'flex' }}>
						<div
							onClick={() => {
								setCurrentRecord(record)
								showAddModal()
								setTaskTitle('修改任务')
							}}
							style={{ marginRight: '10px', color: '#2BBBE9' }}
							className="link"
						>
							修改
						</div>
						<div onClick={showDelModal.bind(null, record)} className="red">
							删除
						</div>
					</div>
				),
				1: (
					<div onClick={showStopModal.bind(null, record)} className="red">
						停止
					</div>
				),
			}

			return statusMap[record.taskStatus] || invalidVal
		}),
	]
	const showDelModal = record => {
		modal.confirm({
			title: '确认删除',
			icon: <ExclamationCircleFilled />,
			content: <div style={{ height: '50px' }}></div>,

			onOk: () => {
				setLoading(true)
				return invoicingManageApi
					.taskDelete({
						id: record.id,
					})
					.then(() => {
						tableRef?.current?.updateTable()
					})
					.finally(() => {
						setLoading(false)
					})
			},
		})
	}
	const showStopModal = record => {
		modal.confirm({
			title: '确认停止',
			icon: <ExclamationCircleFilled />,
			content: <div style={{ height: '50px' }}></div>,

			onOk: () => {
				setLoading(true)
				return invoicingManageApi
					.taskStop({
						id: record.id,
					})
					.then(() => {
						tableRef?.current?.updateTable()
					})
					.finally(() => {
						setLoading(false)
					})
			},
			cancelText: '取消',
		})
	}

	const tableConfig = {
		table: {
			columns,
			requestUrl: invoicingManageApi.taskList,
		},
	}
	const showAddModal = () => {
		addTaskRef?.current?.show({
			tableRef,
		})
	}
	return (
		<PageTag>
			{contextHolder}
			<AddNewTask ref={addTaskRef} recode={currentRecord} title={taskTitle} />
			<div className="operation-box">
				<Button
					onClick={() => {
						setCurrentRecord(null)
						setTaskTitle('新增任务')
						showAddModal()
					}}
					type="primary"
				>
					新增任务
				</Button>
			</div>
			<SearchTable ref={tableRef} {...tableConfig} />
		</PageTag>
	)
}

export default InvoiceWarningTaskManage

const PageTag = styled.div`
	background: #fff;
	padding: 0 15px;
	margin-top: 10px;
	.operation-box {
		padding: 15px 0;
	}
`
