import React, { useState } from 'react'
import { Descriptions, Tooltip } from 'antd'
import BloackInfo from './BlockInfo'
import { renderTransferStatus, renderAmountInCent, renderDate_, renderTooltip } from '@src/pages/oo-client/config/TableColumnsRender'
import { transferManageApi } from '@src/pages/oo-client/api'

const Baseinfo = (props: { data: any }) => {
	const { data } = props
	const [BlockVisible, setBlockVisible] = useState<boolean>(false)
	const [blockData, setBlockData] = useState<any>()

	const getBlockChainInfo = () => {
		if (data.transferUuid) {
			transferManageApi.getChainInfo({ transferUuid: data.transferUuid }).then(res => {
				setBlockData(res)
				setBlockVisible(true)
			})
		}
	}

	return (
		<div style={{ marginBottom: '20px' }}>
			<Descriptions bordered column={2}>
				<Descriptions.Item label="支付编号">
					<Tooltip title={data.transferNo}>{data.transferNo}</Tooltip>
				</Descriptions.Item>
				<Descriptions.Item label="状态">{renderTransferStatus(data.status)}</Descriptions.Item>
				<Descriptions.Item label="支付金额(¥)">{renderAmountInCent(data.sumTransferAmountInCent)}</Descriptions.Item>
				<Descriptions.Item label="付款方" contentStyle={{ maxWidth: '220px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
					{renderTooltip(data.fromName)}
				</Descriptions.Item>
				<Descriptions.Item label="收款方" contentStyle={{ maxWidth: '220px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
					{renderTooltip(data.toName)}
				</Descriptions.Item>
				<Descriptions.Item label="创建日期">{renderDate_(data.createTime)}</Descriptions.Item>
				{/* <Descriptions.Item label="链上明细"> // 2024/4/17隐藏  
					<span className="link" onClick={getBlockChainInfo}>
						查看链上明细
					</span>
				</Descriptions.Item> */}
				<Descriptions.Item label="处理意见">{data.rejectReason ? data.rejectReason : '- -'}</Descriptions.Item>
				<Descriptions.Item label="支付说明">{data.transferRemark ? data.transferRemark : '- -'} </Descriptions.Item>
			</Descriptions>
			<BloackInfo
				visible={BlockVisible}
				data={blockData}
				onOk={() => {
					return
				}}
				onCancel={() => setBlockVisible(false)}
			/>
		</div>
	)
}

export default Baseinfo
