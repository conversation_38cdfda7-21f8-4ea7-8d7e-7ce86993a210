import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import styled from 'styled-components'
import AuditModal from '@src/globalComponents/AuditModal'
import { Tooltip, message, Modal, Button, Row, Col } from 'antd'
import { ExclamationCircleFilled } from '@ant-design/icons'
import commonModuleApi from '@src/pages/oo-client/api/common'
import { deepExtend } from '@utils/util'
import { history } from '@src/utils/router'
import { couDataOnChain, getStorage } from '@src/pages/oo-client/biz/bizIndex'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'

const WaitAuditList = () => {
	const [auditTransferPagination, setAuditTransferPagination] = useState({ current: 1, pageSize: 10, total: 0 }) //表格 配置
	const [loading, setLoading] = useState(true) //表格加载
	const [dataSource, setDataSource] = useState<any[]>([]) //表格数据
	const [transferData, setTransferData] = useState<any>({}) //表格数据
	const [auditAlertVisible, setAuditAlertVisible] = useState(false)
	const [transferAuditVisible, setTransferAuditVisible] = useState(false)
	const [transferAuditType, setTransferAuditType] = useState('agree')
	const [transferAuditTitle, setTransferAuditTitle] = useState('确定通过？')
	const [auditConfirmLoading, setAuditConfirmLoading] = React.useState(false)
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')
	//判断是否是最后一个审核者
	const [isLastAuditor, setIsLastAuditor] = React.useState(false)

	useEffect(() => {
		getList()
	}, [auditTransferPagination.current, auditTransferPagination.pageSize])

	const getList = (current?: any) => {
		setLoading(true)
		const roleCode = getStorage('roleCode') || ''
		const roleNameList = roleCode === 'OPAdmin' ? ['OPOperator'] : [roleCode]
		commonModuleApi
			.loadCurrentUserTask({
				pageNum: current ? current : auditTransferPagination.current,
				pageSize: auditTransferPagination.pageSize,
				processDefinitionKeyList: ['create', 'pay'],
				roleNameList: roleNameList,
				accountId: ext?.user?.accountId,
			})
			.then(
				(res: any) => {
					let copyTransferData = deepExtend({}, res)
					let _auditTransferPagination = { ...auditTransferPagination }
					_auditTransferPagination.total = copyTransferData['count']
					setAuditTransferPagination({ ..._auditTransferPagination })
					// auditTransferPagination.total = copyTransferData['count']
					if (!copyTransferData.data.length && auditTransferPagination.current !== 1) {
						_auditTransferPagination.current -= 1
						// getList()
						setAuditTransferPagination({ ..._auditTransferPagination })
					}
					if (copyTransferData.data.length > 0) {
						let filterTransferData: Array<any> = []
						filterTransferData = copyTransferData['data'].map(item => {
							//数据结构调整
							let auditTransferData = {
								assignee: item.assignee,
								businessKey: item.businessKey,
								createTime: item.createTime,
								id: item.id,
								name: item.name,
							}
							return { ...item.bizMap, ...{ auditTransferData } }
						})
						setDataSource(filterTransferData)
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => setLoading(false)
			)
	}

	//审核操作

	const getIsLastAuditor = async (taskId: string): Promise<boolean> => {
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let companyId: string = ext.user && ext.user.company ? ext.user.company.id : ''
		let isLastAuditor: any = await commonModuleApi
			.isLastFlowPerson({
				companyUuid: companyId,
				role: getStorage('roleCode') || '',
				taskId,
			})
			.catch(err => {
				console.log('err', err)
				return false
			})
		return isLastAuditor
	}
	const auditTransfer = async (value, title, type) => {
		let isLastAuditor = await getIsLastAuditor(value.auditTransferData.id)
		if (isLastAuditor) {
			setIsLastAuditor(isLastAuditor)
		}
		setTransferAuditType(type)
		setTransferAuditTitle(title)
		setTransferData(value)
		setTransferAuditVisible(true)
	}

	const submitAuditData = async submitData => {
		setAuditConfirmLoading(true)
		let isAgree = true
		if (transferAuditType !== 'agree') {
			isAgree = false
		}
		let queryData = {
			isAgree: isAgree, //是否同意
			transferUuid: transferData.transferUuid,
			agreeReason: submitData.comment,
			lastNodeFlag: isLastAuditor,
		}
		let param = {}
		param = {
			businessKey: transferData.auditTransferData.businessKey, // auditTransferData
			taskId: transferData.auditTransferData.id,
			outcome: transferAuditType,
			jsonParamStr: JSON.stringify(queryData),
		}
		commonModuleApi
			.doTask(param)
			.then(item => {
				setTransferAuditVisible(false)
				setAuditConfirmLoading(false)
				if (item.returnCode === 17000) {
					message.success('处理成功')
					//判断是否是开立过来的cou
					let isPublishCou = false
					if (transferData.transferCous.length === 1 && transferData.transferCous[0].uuid === transferData.transferCous[0].originalUuid) {
						isPublishCou = true
					}
					// if (isLastAuditor) {
					//融信数据存证上链
					let couNoList = []
					couNoList = transferData.transferCous.map(cou => {
						return cou['couNo']
					})
					if (transferAuditType === 'agree') {
						//如果是同意，开立或者支付都要存证上链
						if (isLastAuditor) {
							couDataOnChain(couNoList)
						}
					} else {
						//如果是拒绝，只有支付存证上链，开立不存证上链
						//待确认
						if (!isPublishCou) {
							couDataOnChain(couNoList)
						}
					}
					// }
				} else if (item.returnCode === 30015) {
					message.error('重复操作，该项支付已完成处理')
				} else {
					// 删除关键字（银行接口异常）
					let returnDesc = item.returnDesc.replace('银行接口异常', '')
					message.error(returnDesc)
				}
				// let _auditTransferPagination = { ...auditTransferPagination }
				// _auditTransferPagination.current = 1
				// setAuditTransferPagination({ ..._auditTransferPagination })
				getList()
			})
			.catch(err => {
				setAuditConfirmLoading(false)
			})
	}

	//查看数据详情
	const checkdetail = values => {
		const { transferUuid } = values
		history.push('/content/couTransfer/payDetails', {
			DetailForTransfer: transferUuid,
			isFromAudit: true,
			auditData: values,
		})
	}

	//表格改变 当前页面
	const handleChange = (current: number) => {
		auditTransferPagination.current = current
		setAuditTransferPagination({ ...auditTransferPagination })
	}
	//表格改变 改变 size
	const handleSizeChange = (size: number) => {
		auditTransferPagination.current = 1
		auditTransferPagination.pageSize = size
		setAuditTransferPagination({ ...auditTransferPagination })
	}

	//获取合同的表格列
	const getColumns = () => {
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let companyPubKey = ext.user && ext.user.company ? ext.user.company.pubKey : ''
		const columnsDic = {
			transferNo: {
				width: 240,
				render: (text, record) => {
					return (
						<Tooltip title={text}>
							<div style={{ display: 'flex', alignItems: 'center', height: '20px' }}>
								<div style={{ verticalAlign: 'middle', height: '20px' }}>
									{record.fromCouPubKey === companyPubKey ? (
										<div className="auditTableInfoTest" style={{ backgroundColor: 'rgb(64, 197, 133)' }}>
											付
										</div>
									) : (
										<div className="auditTableInfoTest" style={{ backgroundColor: 'rgb(86, 166, 254)' }}>
											收
										</div>
									)}
								</div>
								&nbsp;
								<div onClick={() => checkdetail(record)} style={{ color: '#02A7F0', cursor: 'pointer', height: '20px', lineHeight: '20px' }}>
									{text}
								</div>
							</div>
						</Tooltip>
					)
				},
			},
			transferFor: {
				render: (text, record) => {
					let typeText = ''
					if (record.fromCouPubKey === companyPubKey) {
						typeText = '付款'
					} else {
						typeText = '收款'
					}
					return typeText
				},
			},
			operator: {
				render: (_, record) => {
					if (record.fromCouPubKey === companyPubKey) {
						return <Tooltip title={record.operator}>{record.operator ? record.operator : '- -'}</Tooltip>
					} else {
						return '- -'
					}
				},
			},
			operation: {
				render: (text, record) => {
					return (
						<div className="options_btn">
							<span className="link" onClick={() => auditTransfer(record, '确定通过？', 'agree')}>
								通过
							</span>
							<span className="red" onClick={() => auditTransfer(record, '确定拒绝？', 'reject')}>
								拒绝
							</span>
						</div>
					)
				},
			},
		}
		return getColumnsByPageName('AuditList', columnsDic)
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<BaseTable
						{...auditTransferPagination}
						columns={getColumns()}
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
						loading={loading}
						dataSource={dataSource}
						rowKey="transferUuid"
					/>
				</div>
			</LayoutSlot>
			<AuditModal
				title={transferAuditTitle}
				onCancel={() => setTransferAuditVisible(false)}
				onSubmit={submitAuditData}
				visible={transferAuditVisible}
				type={transferAuditType}
				confirmLoading={auditConfirmLoading}
			/>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.options_btn {
		span {
			display: inline-block;
			margin-right: 10px;
		}
	}
	.warnalert {
		position: fixed;
		top: 50px;
	}
	.auditTableInfoTest {
		width: 20px;
		height: 20px;
		background-color: rgb(64, 197, 133);
		color: white;
		line-height: 20px;
		text-align: center;
		border-radius: 4px;
	}
`

export default WaitAuditList
