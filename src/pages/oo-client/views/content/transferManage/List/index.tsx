import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import styled from 'styled-components'
import SearchBar from '@src/pages/oo-client/components/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import { Tooltip, message, Button } from 'antd'
import { transferManageApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { deepExtend } from '@utils/util'
import { searchCouPageList, searchTransferTypeList } from '../../couManage/couConfig'
import { getFlowElementStatus } from '@utils/dataClean'
import FlowChartModal from '@src/globalComponents/FlowChartModal'
import commonModuleApi from '@src/pages/oo-client/api/common'
import { stampToTime, timeToStamp } from '@utils/timeFilter'
import { history } from '@src/utils/router'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'

const optionsDate = {
	accrualPrinciple: [
		{ key: 'RECEIVEABLE', name: '收款' },
		{ key: 'PAY', name: '付款' },
	],
	status: searchCouPageList,
	transferTypeList: searchTransferTypeList,
}

const initValues = {
	accrualPrinciple: 'RECEIVEABLE',
}

const TransferList = () => {
	const { pubKey } = JSON.parse(localStorage.getItem('companyInfo') || '{}') //该用户的公司名称和类型
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 }) //表格 配置
	const [loading, setLoading] = useState(true) //表格加载
	const [dataSource, setDataSource] = useState<any[]>([]) //表格数据
	const [searchParams, setSearchParams] = useState<any>() //searchbar 字段
	const [exportLoading, setExportLoading] = React.useState(false)

	const [flowChartVisible, setFlowChartVisible] = useState(false) // 审核流传图的弹窗
	const [flowChartData, setFlowChartData] = useState<any[]>([]) // 流转图数据
	const [flowChartType, setFlowChartType] = useState<string>('pay') // 流转图类型

	//这个页面 是不是 限额操作员用户
	const isBuild = false
	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	const getList = () => {
		setLoading(true)
		transferManageApi
			.getTransferList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...searchParams,
				limitOperator: isBuild,
				isWX: true,
			})
			.then(
				(res: any) => {
					let data = deepExtend({}, res)
					pagination.total = data['total']
					if (data.list) {
						data.list.forEach((i: { key: any; transferUuid: any; fromCouPubKey: any; peeOrPay: any }) => {
							i.key = i.transferUuid
							if (i.fromCouPubKey === pubKey) {
								i.peeOrPay = '付款'
							} else {
								i.peeOrPay = '收款'
							}
						})
						setDataSource([...data['list']])
					}
					setLoading(false)
				},
				err => setLoading(false)
			)
	}

	//查看数据详情
	const checkdetail = values => {
		const { transferUuid } = values
		history.push('/content/couTransfer/payDetails', {
			DetailForTransfer: transferUuid,
		})
	}
	// 获取融信流转的流程图数据
	const getFlowChartData = record => {
		let businessKey = record.transferUuid
		transferManageApi.getTransferChartData({ businessKey }).then(
			res => {
				if (res.returnCode == 17000) {
					setFlowChartVisible(true)
					// 设置审核流程图的类型
					setFlowChartType(res.data.processDefKey)
					let result = getFlowElementStatus(res.data)
					setFlowChartData(result)
				} else {
					message.error('此项数据暂不支持查看流程图')
				}
			},
			reason => {
				console.log('reason', reason)
			}
		)
	}

	//search 提交 事件
	const onSubmit = (params: { createDate: any; status: string; transferNo: string }) => {
		let endDate, startDate
		if (params && params.createDate && params.createDate.length === 2) {
			endDate = timeToStamp(params.createDate[1], 'end')
			startDate = timeToStamp(params.createDate[0], 'begin')
			delete params.createDate
		}
		const transferTypeList = params['transferTypeList']
			? Object.prototype.toString.call(params['transferTypeList']) === '[object Array]'
				? params['transferTypeList']
				: params['transferTypeList'].includes(',')
				? params['transferTypeList'].split(',')
				: [params['transferTypeList']]
			: null
		pagination.current = 1
		// setPagination({ ...pagination })
		setSearchParams({ ...params, startDate, endDate, transferTypeList })
	}
	//Search 重置
	const onClear = () => {
		pagination.current = 1
		setSearchParams({})
	}

	//表格改变 当前页面
	const handleChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	//表格改变 改变 size
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	//获取合同的表格列
	const getColumns = () => {
		const columnsDic = {
			transferNo: {
				width: 240,
				render: (text, record) => {
					return (
						<Tooltip title={text}>
							<span onClick={() => checkdetail(record)} style={{ color: '#02A7F0', cursor: 'pointer' }}>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			operator: {
				render: (_, record) => {
					if (record.peeOrPay === '付款') {
						return <Tooltip title={record.operator}>{record.operator ? record.operator : '- -'}</Tooltip>
					} else {
						return '- -'
					}
				},
			},
			operation: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => getFlowChartData(record)}>
							审核流程
						</span>
					)
				},
			},
		}
		return getColumnsByPageName('Transferlist', columnsDic)
	}
	//导出流转管理信息
	const exportCouCsv = async () => {
		setExportLoading(true)
		let serverTime = await commonModuleApi.syncTime().catch(err => {
			console.log('err', err)
		})
		let params = {
			...searchParams,
			pageNum: 1,
			pageSize: 2147483646,
			limitOperator: isBuild,
		}

		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`
		transferManageApi
			.transferExport(params)
			.then((response: any) => {
				let type = 'application/vnd.ms-excel'
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar showLabel={false} pageName={'transferManage'} optionData={optionsDate} onClear={onClear} onSubmit={onSubmit} />
						<Button style={{ marginLeft: '20px' }} type="primary" onClick={exportCouCsv} loading={exportLoading}>
							导出
						</Button>
					</OperatingArea>
					<BaseTable
						{...pagination}
						columns={getColumns()}
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
						loading={loading}
						dataSource={dataSource}
					/>
				</div>
			</LayoutSlot>
			<FlowChartModal
				type={flowChartType}
				data={flowChartData}
				visible={flowChartVisible}
				onCancel={() => {
					setFlowChartVisible(false)
				}}
			></FlowChartModal>
		</Box>
	)
}

const Box = styled.div`
	.options_btn {
		span {
			display: inline-block;
			margin-right: 15px;
		}
	}
`

export default TransferList
