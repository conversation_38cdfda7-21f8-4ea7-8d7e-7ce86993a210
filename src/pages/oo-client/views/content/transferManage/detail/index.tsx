import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import styled from 'styled-components'
import SmallTitle from '@src/globalComponents/SmallTitle'
import Baseinfo from '../components/Baseinfo'
import BaseTable from '@src/globalComponents/BaseTable'
import { Table, Tooltip, message, Descriptions, Button } from 'antd'
import { transferManageApi, commonApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import AttachmentTable from '@src/globalComponents/EditTable/AttachmentTable'
import DetailModal from '@src/pages/oo-client/components/detailModal'
import PDFViewer from '@globalComponents/PDFViewer'
import { history } from '@src/utils/router'
import ChainInfoModal from '@src/globalComponents/chainInfoComponents/ChainInfoModal'
import { renderDate_, renderTime } from '@src/pages/oo-client/config/TableColumnsRender'
import AuditModal from '@src/globalComponents/AuditModal'
import { useLocation, useNavigate } from 'react-router-dom'
import commonModuleApi from '@src/pages/oo-client/api/common'
import { couDataOnChain, getStorage } from '@src/pages/oo-client/biz/bizIndex'

function detail() {
	// const transferUuid = history.location.state?.DetailForTransfer
	const { state } = useLocation() as any
	const transferUuid = state?.DetailForTransfer
	const isFromAudit = state?.isFromAudit || false
	const auditData = state?.auditData || {}
	const attachmentTable = React.useRef(null as any)
	const [detailInfo, setDetailInfo] = useState<any>()
	const [visible, setVisible] = useState<boolean>(false)
	const [pdfPath, setPdfPath] = useState<string>('')
	const [invoiceData, setInvoiceData] = useState<any>({})

	const [detailVisible, setDetailVisible] = useState(false)
	const [inDetailVisible, setInDetailVisible] = useState(false)
	const [auditorInfo, setAuditorInfo] = useState<any>()
	const [contractData, setContractData] = useState<any>({
		contractCode: '', // 合同编号
		name: '', // 合同名称
		companySellerName: '',
		companyBuyerName: '',
		productName: '', // 产品名称
		amount: '', // 合同金额
		startDate: [], // 开始时间
		endDate: [], // 结束时间
		signDate: '', // 签订日期
	})
	const [chainInfoModalVisible, setChainInfoModalVisible] = useState<boolean>(false)
	const [onChainData, setOnChainData] = useState({})
	const [invoiceList, setInvoiceList] = useState([])
	const [transferAuditVisible, setTransferAuditVisible] = useState(false)
	const [transferAuditType, setTransferAuditType] = useState('agree')
	const [transferAuditTitle, setTransferAuditTitle] = useState('确定通过？')
	const [operatingList, setOperatingList] = useState([])
	const [auditConfirmLoading, setAuditConfirmLoading] = React.useState(false)
	//判断是否是最后一个审核者
	const [isLastAuditor, setIsLastAuditor] = React.useState(false)
	const navigate = useNavigate()

	useEffect(() => {
		if (transferUuid) {
			getDetail(transferUuid)
			getInvoiceList(transferUuid)
		}
		return () => {
			localStorage.removeItem('DetailForTransfer')
		}
	}, [])

	const getInvoiceList = (paramsTransferUuid: string) => {
		transferManageApi
			.getTransferInvoices({
				id: paramsTransferUuid,
			})
			.then(
				res => {
					setInvoiceList(res.list)
				},
				error => console.log(error)
			)
	}

	const getDetail = (paramsTransferUuid: string) => {
		transferManageApi
			.getTransferDetail({
				transferUuid: paramsTransferUuid,
			})
			.then(
				res => {
					setDetailInfo(res)
				},
				error => console.log(error)
			)
		getFlowChartData(paramsTransferUuid)
	}

	//查看pdf  承诺函
	const viewPdf = (url: string) => {
		if (url) {
			setPdfPath(url)
			setVisible(true)
		}
	}

	// 点击合同编号
	const clickContractCode = record => {
		setDetailVisible(true)
		setContractData(record)
	}
	const clickInvoiceNumber = record => {
		setInDetailVisible(true)
		setInvoiceData(record)
	}

	const getFlowChartData = businessKey => {
		transferManageApi.getTransferChartData({ businessKey }).then(
			res => {
				if (res?.data?.activityList) {
					res?.data?.activityList.map(item => {
						if (item?.activityName === 'OPAuditor') {
							setAuditorInfo({ username: item?.assigneeInfo?.username, endTime: item?.endTime })
						}
						if (item?.activityType === 'userTask' && item?.assignee) {
							let { taskComments, endTime, assigneeInfo } = item
							setOperatingList(preOperatingList => [...preOperatingList, { taskComments, endTime, ...assigneeInfo }])
						}
					})
				}
			},
			reason => {
				console.log('reason', reason)
			}
		)
	}

	const getTransferColumns = type => {
		const columnsDic = {
			contractCode: {
				render: (text: any, record: any) => {
					return (
						<div
							onClick={() => {
								clickContractCode(record)
							}}
						>
							<Tooltip className="link" title={text}>
								{text}
							</Tooltip>
						</div>
					)
				},
			},
			invoiceNumber: {
				render: (text: any, record: any) => {
					return (
						<div
							onClick={() => {
								clickInvoiceNumber(record)
							}}
						>
							<Tooltip className="link" title={text}>
								{text}
							</Tooltip>
						</div>
					)
				},
			},
			fileInfo: {
				dataIndex: 'fileInfo',
				title: '查看',
				render: (text: any, record: any) => {
					let getProtocol: any = {}
					const protocolMap = {
						transfer: '融信流转单',
						commitment: '付款承诺函',
					}
					if (text) {
						getProtocol = JSON.parse(text).protocolList[0]
					}
					console.log(getProtocol.name)

					return (
						<span className="link" onClick={() => viewPdf(getProtocol.url ? getProtocol.url : null)}>
							{protocolMap[getProtocol.type]}
						</span>
					)
				},
			},
			onChainCertificate: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => showCertificate(record)}>
							查看证书
						</span>
					)
				},
			},
		}
		return getColumnsByPageName(type, columnsDic)
	}
	const showCertificate = record => {
		commonApi
			.queryCouProof({ couNo: record.couNo })
			.then(data => {
				if (data && data.status === 'DONE') {
					setOnChainData(data)
					setChainInfoModalVisible(true)
				} else {
					message.warning('证书未生成，完成审批且上链后生成证书')
				}
			})
			.catch(err => {
				console.log(err)
			})
	}

	const getOperatingColumns = () => {
		return evenlyTableColunms(getColumnsByPageName('operatingRecord'))
	}

	const auditTransfer = async (title: string, type: string) => {
		let isLastAuditor = await getIsLastAuditor(auditData.auditTransferData.id)
		if (isLastAuditor) {
			setIsLastAuditor(isLastAuditor)
		}
		setTransferAuditType(type)
		setTransferAuditTitle(title)
		setTransferAuditVisible(true)
	}

	const submitAuditData = async submitData => {
		setAuditConfirmLoading(true)
		let isAgree = true
		if (transferAuditType !== 'agree') {
			isAgree = false
		}
		let queryData = {
			isAgree: isAgree, //是否同意
			transferUuid: auditData.transferUuid,
			agreeReason: submitData.comment,
		}
		let param = {}
		param = {
			businessKey: auditData.auditTransferData.businessKey, // auditTransferData
			taskId: auditData.auditTransferData.id,
			outcome: transferAuditType,
			jsonParamStr: JSON.stringify(queryData),
		}
		commonModuleApi
			.doTask(param)
			.then(item => {
				setTransferAuditVisible(false)
				setAuditConfirmLoading(false)
				if (item.returnCode === 17000) {
					message.success('处理成功')
					//判断是否是开立过来的cou
					let isPublishCou = false
					if (auditData.transferCous.length === 1 && auditData.transferCous[0].uuid === auditData.transferCous[0].originalUuid) {
						isPublishCou = true
					}
					// if (isLastAuditor) {
					//融信数据存证上链
					let couNoList = []
					couNoList = auditData.transferCous.map(cou => {
						return cou['couNo']
					})
					if (transferAuditType === 'agree') {
						//如果是同意，开立或者支付都要存证上链
						if (isLastAuditor) {
							couDataOnChain(couNoList)
						}
					} else {
						//如果是拒绝，只有支付存证上链，开立不存证上链
						if (!isPublishCou) {
							couDataOnChain(couNoList)
						}
					}
					// }
					navigate('/content/waitAudit/list')
				} else if (item.returnCode === 30015) {
					message.error('重复操作，该项支付已完成处理')
				} else {
					// 删除关键字（银行接口异常）
					let returnDesc = item.returnDesc.replace('银行接口异常', '')
					message.error(returnDesc)
				}
			})
			.catch(err => {
				setAuditConfirmLoading(false)
			})
	}

	const getIsLastAuditor = async (taskId: string): Promise<boolean> => {
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let companyId: string = ext.user && ext.user.company ? ext.user.company.id : ''
		let isLastAuditor: any = await commonModuleApi
			.isLastFlowPerson({
				companyUuid: companyId,
				role: getStorage('roleCode') || '',
				taskId,
			})
			.catch(err => {
				console.log('err', err)
				return false
			})
		return isLastAuditor
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<div className="boxContentWrap">
						<SmallTitle text="基本信息" />
						<div className="boxContent">{detailInfo && <Baseinfo data={detailInfo} />}</div>
					</div>
					<div className="boxContentWrap">
						<SmallTitle text="合同" />
						<div className="boxContent">
							{detailInfo && (
								<Table
									rowKey="uuid"
									style={{ marginBottom: '20px' }}
									columns={getTransferColumns('contractInDetail')}
									dataSource={detailInfo?.contracts}
									pagination={false}
								/>
							)}
						</div>
					</div>

					{detailInfo?.transferType == 'CREATE' ? (
						<>
							<div className="boxContentWrap">
								<SmallTitle text="发票" />
								<div className="boxContent">
									<Table
										rowKey="uuid"
										style={{ marginBottom: '20px' }}
										columns={getTransferColumns('invoiceDetail')}
										dataSource={invoiceList}
										pagination={false}
									/>
								</div>
							</div>
						</>
					) : null}
					<div className="boxContentWrap">
						<SmallTitle text="融信明细" />
						<div className="boxContent">
							{detailInfo && (
								<Table
									rowKey="couNo"
									style={{ marginBottom: '20px' }}
									columns={getTransferColumns('couTransferInDetail')}
									dataSource={detailInfo.transferCous}
									pagination={false}
								/>
							)}
						</div>
					</div>

					{detailInfo && (
						/* 其他附件 */
						<div className="boxContentWrap">
							<AttachmentTable ref={attachmentTable} isEdit={false} initialValues={JSON.parse(detailInfo?.attachment || '[]')} />
						</div>
					)}
					<div className="boxContentWrap">
						<SmallTitle text="操作记录" />
						<div className="boxContent">
							<BaseTable columns={getOperatingColumns()} dataSource={operatingList} havePagination={false}></BaseTable>
						</div>
					</div>
					{isFromAudit && (
						<div className="options_btn">
							<Button type="primary" danger onClick={() => auditTransfer('确定拒绝？', 'reject')} style={{ marginRight: 10 }}>
								拒绝
							</Button>
							<Button type="primary" onClick={() => auditTransfer('确定通过？', 'agree')}>
								通过
							</Button>
						</div>
					)}
					<PDFViewer title="" visible={visible} onCancel={() => setVisible(false)} pdfUrl={pdfPath} />
					<DetailModal title="查看合同" pageName="contract" onCancel={() => setDetailVisible(false)} visible={detailVisible} dataSource={contractData} />
				</div>
			</LayoutSlot>
			<ChainInfoModal visible={chainInfoModalVisible} onCancel={() => setChainInfoModalVisible(false)} chainData={onChainData} />
			<DetailModal title="查看发票" pageName="invoice" onCancel={() => setInDetailVisible(false)} visible={inDetailVisible} dataSource={invoiceData} />
			<AuditModal
				title={transferAuditTitle}
				onCancel={() => setTransferAuditVisible(false)}
				onSubmit={submitAuditData}
				visible={transferAuditVisible}
				type={transferAuditType}
				confirmLoading={auditConfirmLoading}
			/>
		</Box>
	)
}

const Box = styled.div`
	.options_btn {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 40px;
		span {
			padding: 0 10px;
		}
	}
`
export default detail
