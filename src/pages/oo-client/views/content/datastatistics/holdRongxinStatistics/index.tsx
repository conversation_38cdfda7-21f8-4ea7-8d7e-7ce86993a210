import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, message } from 'antd'
import { statisticsApi } from '@oo/api'
import { formatTableData } from '@utils/format'
import { getTabTit, invalidVal } from '@globalBiz/gBiz'
import { stampToTime, timeToStamp, numberToThousands } from '@src/utils/timeFilter'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import BaseTable from '@globalComponents/BaseTable'
import LayoutSlot from '@globalComponents/LayoutSlot'
import SearchBar from '@ooClientComponents/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import styled from 'styled-components'

let orgidInfo = { isSearch: false, pubKey: '', orgName: '' }
function SearchCompany() {
	//table相关
	const [searchParams, setSearchParams] = useState<any>({})
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [dataSource, setDataSource] = useState([])
	const [tableLoading, setTableLoading] = useState(false)
	const [exportLoading, setExportLoading] = useState(false)

	useEffect(() => {
		getCompanyInfoList()
	}, [pagination.current, pagination.pageSize, searchParams])
	//
	const getColumns = () => {
		let availableCouAmountInYuan = getTabTit(
			[
				<>
					<span>
						可用融信（￥）
						<Tooltip title={'未兑付、未质押、未流转的融信，可用来流转或融资'}>
							<ExclamationCircleOutlined style={{ color: 'rgb(245,154,35)', marginLeft: '4px' }} />
						</Tooltip>
					</span>
				</>,
				'availableCouAmountInYuan',
				120,
			],
			(text, record) => linkRender(text, record, 'availableCouAmountInYuan')
		)
		const columnsDic = {
			totalCouAmountInYuan: {
				render: (text, recode) => linkRender(text, recode),
			},
			cashOkCouAmountInYuan: {
				render: (text, recode) => linkRender(text, recode, 'CASH_OK'),
			},
			noCashCouAmountInYuan: {
				render: (text, recode) => linkRender(text, recode, 'NO_CASH'),
			},
			waitCashCouAmountInYuan: {
				render: (text, recode) => linkRender(text, recode, 'WAIT_CASH'),
			},
			pledgeCouAmountInYuan: {
				render: (text, recode) => linkRender(text, recode, '1'),
			},
			availableCouAmountInYuan,
			toTalamountOpened: {
				render: (text, recode) => linkRender(text, recode),
			},
		}
		return evenlyTableColunms(getColumnsByPageName('holdingStatistics', columnsDic))
	}
	// createDate
	const linkRender = (text: any, recode: any, key?: string) => {
		const linkClick = () => {
			let params = {}
			params['holderPubKey'] = recode?.holderPubKey || ''
			params['orgName'] = recode?.holderName || ''
			params['cashStatus'] = key || ''

			searchParams?.endCreateDate ? (params['endDate'] = searchParams?.endCreateDate) : ''
			searchParams?.startCreateDate ? (params['startDate'] = searchParams?.startCreateDate) : ''
			window.open(`${window.location.origin}/#/content/couManage/holderDetail?${new URLSearchParams(params).toString()}`, '_blank')
		}
		return !Number.isNaN(Number.parseFloat(text)) ? (
			<span className="link" onClick={linkClick}>
				{numberToThousands(text)}
			</span>
		) : (
			'--'
		)
	}

	const getCompanyInfoList = async (values?: any) => {
		let params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...searchParams,
		}
		if (values) {
			params = Object.assign(params, values)
		}
		setTableLoading(true)
		const res = await statisticsApi.getHolderStatistics(params).catch(() => {
			setDataSource([])
			setTableLoading(false)
			pagination.total = 0
			setPagination({ ...pagination })
		})
		if (res) {
			if (res.list && res.list.length > 0) {
				setTableLoading(false)
				setDataSource(formatTableData.addKey(res.list))
				pagination.total = res.total
				setPagination({ ...pagination })
			} else {
				setDataSource([])
				setTableLoading(false)
				pagination.total = 0

				setPagination({ ...pagination })
			}
		}
	}
	//处理搜索
	const handleSearch = (values: any) => {
		if (values && values.createExpireDate && values.createExpireDate.length === 2) {
			if (values.createExpireDate[1]) values.endCreateDate = timeToStamp(values.createExpireDate[1], 'end')
			if (values.createExpireDate[0]) values.startCreateDate = values.createExpireDate[0]
			delete values.createExpireDate
		}

		//点击查询后，带入参数页码置为1, 记录当前查询参数
		orgidInfo.isSearch = true
		pagination.current = 1
		setSearchParams(values)
		setPagination({ ...pagination })
	}

	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}

	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}

	const handleReset = () => {
		pagination.current = 1
		orgidInfo.isSearch = false
		setSearchParams({})
		setPagination({ ...pagination })
	}

	//导出公司列表信息
	const exportCouCsv = async () => {
		setExportLoading(true)
		const fileName = `Download_${stampToTime(new Date().getTime(), 9)}.xlsx`
		let type = 'application/vnd.ms-excel'
		// let type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
		statisticsApi
			.getHolderStatisticsExport({
				...searchParams,
				pageNum: 1,
				pageSize: 2147483646,
			})
			.then((response: any) => {
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}

	return (
		<LayoutSlot>
			<SearchCompanyStyle className="card-wrapper">
				<div style={{ marginBottom: '20px' }}></div>
				<OperatingArea>
					<SearchBar
						pageName="holdingStatistics"
						onSubmit={handleSearch}
						onClear={handleReset}
						byNameExtendMethod={value => (orgidInfo = { ...value, isSearch: false })}
					/>
					<Button style={{ marginLeft: '5px' }} type="primary" onClick={exportCouCsv} loading={exportLoading}>
						导出
					</Button>
				</OperatingArea>
				<BaseTable
					className="card-table"
					loading={tableLoading}
					dataSource={dataSource}
					columns={getColumns()}
					total={pagination.total}
					current={pagination.current}
					onPageChange={handlePageChange}
					onSizeChange={handleSizeChange}
				/>
			</SearchCompanyStyle>
		</LayoutSlot>
	)
}

const SearchCompanyStyle = styled.div`
	.left-btn {
		margin-right: 5px;
	}
`

export default SearchCompany
