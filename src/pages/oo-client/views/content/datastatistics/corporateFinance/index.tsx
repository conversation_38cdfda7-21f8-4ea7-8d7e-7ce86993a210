import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, message } from 'antd'
import { statisticsApi } from '@oo/api'
import { formatTableData } from '@utils/format'
import { deepExtend, getUserData } from '@utils/util'
import { companyTypeList } from '@ooContent/companyManage/enterpriseManage/searchCompanyConfig'
import { stampToTime, timeToStamp, numberToThousands } from '@src/utils/timeFilter'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import BaseTable from '@globalComponents/BaseTable'
import LayoutSlot from '@globalComponents/LayoutSlot'
import SearchBar from '@ooClientComponents/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import styled from 'styled-components'
import moment from 'moment'

let orgidInfo = { isSearch: false, id: '', orgName: '' }
function SearchCompany() {
	//table相关
	const [searchParams, setSearchParams] = useState<any>({})
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [dataSource, setDataSource] = useState([])
	const [tableLoading, setTableLoading] = useState(false)
	const [exportLoading, setExportLoading] = useState(false)

	useEffect(() => {
		getCompanyInfoList()
	}, [pagination.current, pagination.pageSize, searchParams])

	const getColumns = () => {
		const columnsDic = {
			auditAmount: {
				render: (text, recode) => linkRender(text, recode, 'AUDIT'),
			},
			pendingAmount: {
				render: (text, recode) => linkRender(text, recode, 'PENDING'),
			},
			waitConfirmAmount: {
				render: (text, recode) => linkRender(text, recode, 'WAIT_CONFIRM'),
			},
			confirmAmount: {
				render: (text, recode) => linkRender(text, recode, 'CONFIRM'),
			},
			repaidAmount: {
				render: (text, recode) => linkRender(text, recode, 'REPAID'),
			},
			totalFinanceAmount: {
				render: (text, recode) => linkRender(text, recode, 'totalFinanceAmount'),
			},
		}
		return evenlyTableColunms(getColumnsByPageName('enterpriseStatistics', columnsDic))
	}

	const linkRender = (text: any, recode: any, key: string) => {
		const linkClick = () => {
			let params = {}
			params['orgId'] = recode?.applicantUuid
			params['orgName'] = recode?.orgName
			params['status'] = key
			searchParams?.endDueDate ? (params['endDate'] = timeToStamp(new Date(searchParams?.endDueDate), 'end')) : ''
			searchParams?.startDueDate ? (params['startDate'] = timeToStamp(new Date(searchParams?.startDueDate), 'begin')) : ''
			window.open(`${window.location.origin}/#/content/financeManage/fianceSearch?${new URLSearchParams(params).toString()}`, '_blank')
		}
		return !Number.isNaN(Number.parseFloat(text)) ? (
			<span className="link" onClick={linkClick}>
				{numberToThousands(text)}
			</span>
		) : (
			'--'
		)
	}

	const getCompanyInfoList = async (values?: any) => {
		let params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...searchParams,
		}
		if (values) {
			params = Object.assign(params, values)
		}
		setTableLoading(true)
		const res = await statisticsApi.getStatisticsList(params).catch(() => {
			setDataSource([])
			setTableLoading(false)
			pagination.total = 0
			setPagination({ ...pagination })
		})
		if (res) {
			if (res.list && res.list.length > 0) {
				setTableLoading(false)
				setDataSource(formatTableData.addKey(res.list))
				pagination.total = res.total
				setPagination({ ...pagination })
			} else {
				setDataSource([])
				setTableLoading(false)
				pagination.total = 0

				setPagination({ ...pagination })
			}
		}
	}
	//处理搜索
	const handleSearch = (values: any) => {
		if (values && values.createExpireDate && values.createExpireDate.length === 2) {
			if (values.createExpireDate[1]) values.endDueDate = moment(values.createExpireDate[1]).format('YYYY-MM-DD')
			if (values.createExpireDate[0]) values.startDueDate = moment(values.createExpireDate[0]).format('YYYY-MM-DD')
			delete values.createExpireDate
		}
		//点击查询后，带入参数页码置为1, 记录当前查询参数
		pagination.current = 1
		setSearchParams(values)
		setPagination({ ...pagination })
	}

	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}

	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}

	const handleReset = () => {
		pagination.current = 1
		setSearchParams({})
		setPagination({ ...pagination })
	}

	//导出公司列表信息
	const exportCouCsv = async () => {
		setExportLoading(true)
		const fileName = `Download_${stampToTime(new Date().getTime(), 9)}.xlsx`
		let type = 'application/vnd.ms-excel'
		// let type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
		statisticsApi
			.getStatisticsExport({
				...searchParams,
				pageNum: 1,
				pageSize: 2147483646,
			})
			.then((response: any) => {
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}

	return (
		<LayoutSlot>
			<SearchCompanyStyle className="card-wrapper">
				<div style={{ marginBottom: '20px' }}></div>
				<OperatingArea>
					<SearchBar
						pageName="enterpriseStatistics"
						onSubmit={handleSearch}
						onClear={handleReset}
						byNameExtendMethod={value => (orgidInfo = { ...value, isSearch: false })}
					/>
					<Button style={{ marginLeft: '5px' }} type="primary" onClick={exportCouCsv} loading={exportLoading}>
						导出
					</Button>
				</OperatingArea>
				<BaseTable
					className="card-table"
					loading={tableLoading}
					dataSource={dataSource}
					columns={getColumns()}
					total={pagination.total}
					current={pagination.current}
					onPageChange={handlePageChange}
					onSizeChange={handleSizeChange}
				/>
			</SearchCompanyStyle>
		</LayoutSlot>
	)
}

const SearchCompanyStyle = styled.div`
	.left-btn {
		margin-right: 5px;
	}
`

export default SearchCompany
