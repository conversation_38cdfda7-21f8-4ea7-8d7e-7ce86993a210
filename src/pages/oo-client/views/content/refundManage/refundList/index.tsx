import React, { useEffect, useState } from 'react'
import { But<PERSON>, message, Tooltip } from 'antd'
import { getRefoundStatus } from '@src/pages/oo-client/config/TableColumnsRender'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import { refundManageApi, commonApi } from '@src/pages/oo-client/api/index'
import { getFlowElementStatus } from '@utils/dataClean'
import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import SearchBar from '@ooClientComponents/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import FlowChartModal from '@src/globalComponents/FlowChartModal'
import styled from 'styled-components'
import { history } from '@src/utils/router'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import commonModuleApi from '@src/pages/oo-client/api/common'
import { stampToTime } from '@src/utils/timeFilter'

function searchPage() {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [searchParams, setSearchParams] = useState({})
	const [loading, setLoading] = useState(false)

	const [flowChartVisible, setFlowChartVisible] = useState(false) // 审核流传图的弹窗
	const [flowChartData, setFlowChartData] = useState<any[]>([]) // 流转图数据

	const [exportLoading, setExportLoading] = useState<boolean>(false)

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	// 得到退款申请列表
	const getList = () => {
		setLoading(true)
		refundManageApi
			.getRefundList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...searchParams,
			})
			.then(
				(res: any) => {
					pagination.total = res['total']
					if (res.list) {
						setDataSource([...res.list])
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => {
					setLoading(false)
					pagination.total = 0
					setDataSource([])
					message.error('获取退款申请列表错误')
				}
			)
	}
	// 获取审核流程图的数据
	const getFlowChartData = record => {
		let businessKey = record.refundUuid
		commonApi.getTransferChartData({ businessKey }).then(
			res => {
				if (res.returnCode == 17000) {
					setFlowChartVisible(true)
					let result = getFlowElementStatus(res.data)
					setFlowChartData(result)
				} else {
					message.error('此项数据暂不支持查看流程图')
				}
			},
			reason => {
				console.log('reason', reason)
				message.error(reason)
			}
		)
	}
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	const handlePageChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const onOperCallback = (str: string, data: any) => {
		// 存储退款申请详情，防止刷新数据丢失
		history.push('/content/refundManage/refundDetail', {
			refundUuid: data.refundUuid,
		})
	}
	const onSubmit = (params: any) => {
		const oldTransferNo = params['originPayNumber'] ? params['originPayNumber'] : ''
		const refundNo = params['refundApplicationNumber'] ? params['refundApplicationNumber'] : ''
		const paramsObj = {
			oldTransferNo,
			refundNo,
		}
		pagination.current = 1
		setSearchParams(paramsObj)
	}
	const onClear = () => {
		setSearchParams({})
	}
	const getColumns = () => {
		const columnsDic = {
			oldPayNo: {
				width: 200,
			},
			refundStatus: {
				render: (text: any, record: any) => {
					return <span title={text}>{getRefoundStatus(text)}</span>
				},
			},
			refundNo: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									onOperCallback('refundNo', record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			operation: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => getFlowChartData(record)}>
							审核流程
						</span>
					)
				},
			},
		}
		return getColumnsByPageName('searchRefundList', columnsDic)
	}

	// 导出退款申请列表
	const handleExportClick = async () => {
		setExportLoading(true)
		let serverTime = await commonModuleApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`

		refundManageApi
			.exportWxRefundList({
				...searchParams,
				pageNum: 1,
				pageSize: 2147483646,
			})
			.then((response: any) => {
				let type = 'application/vnd.ms-excel'
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar onSubmit={onSubmit} onClear={onClear} pageName="refundList" />
						{/* <Button style={{ marginLeft: '20px' }} type="primary" onClick={handleExportClick} loading={exportLoading}>
							导出
						</Button> */}
					</OperatingArea>
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						rowKey="id"
					/>
				</div>
				<FlowChartModal
					type="refund"
					data={flowChartData}
					visible={flowChartVisible}
					onCancel={() => {
						setFlowChartVisible(false)
					}}
				></FlowChartModal>
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
`

export default searchPage
