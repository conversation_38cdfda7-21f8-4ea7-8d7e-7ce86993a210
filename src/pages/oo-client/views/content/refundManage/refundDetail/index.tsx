import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import styled from 'styled-components'
import SmallTitle from '@src/globalComponents/SmallTitle'
import Baseinfo from './components/Baseinfo'
import { Table, message } from 'antd'
import { refundManageApi, commonApi, transferManageApi } from '@src/pages/oo-client/api'
import { getColumnsByPageName } from '@src/pages/oo-client/config/table-columns-config'
import PDFViewer from '@globalComponents/PDFViewer'
import { history } from '@src/utils/router'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import ChainInfoModal from '@src/globalComponents/chainInfoComponents/ChainInfoModal'
import BaseTable from '@src/globalComponents/BaseTable'

function detail() {
	const refundUuid = history.location.state?.refundUuid
	const [detailInfo, setDetailInfo] = useState<any>()
	const [visible, setVisible] = useState<boolean>(false)
	const [pdfPath, setPdfPath] = useState<string>('')

	const [oldTransfer, setOldTransfer] = useState<[{ [key: string]: any }]>([{}])
	const [transferCous, setTransferCous] = useState<[{ [key: string]: any }]>([{}])
	//判断是否是 开立退款  or 支付退款
	const [iSCreateRefound, setiSCreateRefound] = useState<boolean>(false)

	const [chainInfoModalVisible, setChainInfoModalVisible] = useState<boolean>(false)
	const [onChainData, setOnChainData] = useState({})
	const [operatingList, setOperatingList] = useState([])

	useEffect(() => {
		if (refundUuid) {
			getDetail(refundUuid)
			getFlowChartData(refundUuid)
		}
		return () => {
			localStorage.removeItem('refundUuid')
		}
	}, [])

	const getDetail = (paramsRefundUuid: string) => {
		refundManageApi
			.getRefundDetail({
				refundUuid: paramsRefundUuid,
			})
			.then(
				(res: any) => {
					console.log(res.refundType)

					if (res.refundType === 'CREATE_REFUND') setiSCreateRefound(true)
					else setiSCreateRefound(false)
					//将详情返回数据 拆分到 页面 对应的三个部分对应的数据
					//原支付记录
					const { oldTransferAmountInCent, oldTransferNo, oldTransferPayName, oldTransferReceiveName, oldTransferUuid } = res
					let initTransferCous: any = []
					//融信信息
					res.refundCouList.forEach(item => {
						let data: any = {}
						data.couNo = item.refundCouNo
						data.amount = item.refundCouAmountInCent
						data.publishName = item.refundCouPublishName
						data.creditCompanyName = item.refundCouCreditName
						data.dueDate = item.refundCouDueDate
						data.fileInfo = item.refundCouTransferUrl
						initTransferCous.push(data)
					})
					setTransferCous(initTransferCous)
					setOldTransfer([
						{
							oldTransferAmountInCent,
							oldTransferNo,
							oldTransferPayName,
							oldTransferReceiveName,
							oldTransferUuid,
						},
					])
					setDetailInfo(res)
				},
				error => console.log(error)
			)
	}

	const getFlowChartData = businessKey => {
		transferManageApi.getTransferChartData({ businessKey }).then(
			res => {
				if (res?.data?.activityList) {
					res?.data?.activityList.map(item => {
						if (item?.activityType === 'userTask' && item?.assignee) {
							let { taskComments, endTime, assigneeInfo } = item
							setOperatingList(preOperatingList => [...preOperatingList, { taskComments, endTime, ...assigneeInfo }])
						}
					})
				}
			},
			reason => {
				console.log('reason', reason)
			}
		)
	}

	//查看pdf  承诺函
	const viewPdf = (url: string) => {
		if (url) {
			setPdfPath(url)
			setVisible(true)
		}
	}

	const getTransferColumns = type => {
		console.log(oldTransfer)

		const columnsDic: any = {
			fromName: {
				dataIndex: 'oldTransferPayName',
			},
			toCompanyName: {
				dataIndex: 'oldTransferReceiveName',
			},
		}
		return getColumnsByPageName(type, columnsDic)
	}
	const getOperatingColumns = () => {
		return evenlyTableColunms(getColumnsByPageName('operatingRecord'))
	}
	const showCertificate = record => {
		commonApi
			.queryCouProof({ couNo: record.couNo })
			.then(data => {
				if (data && data.status === 'DONE') {
					setOnChainData(data)
					setChainInfoModalVisible(true)
				} else {
					message.warning('证书未生成，完成审批且上链后生成证书')
				}
			})
			.catch(err => {
				console.log(err)
			})
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<SmallTitle text="退款详情" />
					{detailInfo && <Baseinfo data={detailInfo} iSCreateRefound={iSCreateRefound} isDetail={false} />}
					<SmallTitle text="原支付记录" />
					{oldTransfer.length > 0 && (
						<Table rowKey="uuid" style={{ marginBottom: '20px' }} columns={getTransferColumns('oldTransfer')} dataSource={oldTransfer} pagination={false} />
					)}
					<SmallTitle text="融信信息" />
					{transferCous.length > 0 && (
						<Table
							rowKey="couNo"
							style={{ marginBottom: '20px' }}
							columns={getTransferColumns('couTransferInDetailForRefund')}
							dataSource={transferCous}
							pagination={false}
						/>
					)}
					<SmallTitle text="操作记录" />
					<BaseTable columns={getOperatingColumns()} dataSource={operatingList} havePagination={false}></BaseTable>
					<PDFViewer title="" visible={visible} onCancel={() => setVisible(false)} pdfUrl={pdfPath} />
				</div>
			</LayoutSlot>
			<ChainInfoModal visible={chainInfoModalVisible} onCancel={() => setChainInfoModalVisible(false)} chainData={onChainData} />
		</Box>
	)
}

const Box = styled.div`
	.options_btn {
		span {
			display: inline-block;
			margin: 0 20px;
		}
	}
`
export default detail
