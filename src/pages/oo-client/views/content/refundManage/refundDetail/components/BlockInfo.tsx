import React from 'react'
import { Descriptions, Modal } from 'antd'
import styled from 'styled-components'
import { renderTime, renderAmountInCent } from '@src/pages/oo-client/config/TableColumnsRender'

interface Props {
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 提交确定操作
	 */
	onOk: () => void
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
	/**
	 * @description 弹窗展示的数据
	 */
	data: any
	/**
	 * @description 是否是开立退款
	 */
	iSCreateRefound: boolean
}

const status = {
	WAIT: '等待上链',
	GOING: '上链中',
	DONE: '上链成功',
	FAIL: '上链失败',
}
const BlockInfo = (props: Props) => {
	const { visible, onOk, onCancel, data, iSCreateRefound } = props

	return (
		<div>
			<Modal open={visible} title={'融信链上信息'} onCancel={onCancel} onOk={onOk} footer={null} className="wx_Modal">
				<Box>
					{data && (
						<div>
							{/* <div className="wx_Modal_top_center">
								<div className="wx_Modal_top_center_item">
									<p className="text">¥ {renderAmountInCent(data.para)}</p>
									<p className="label">融信金额</p>
								</div>
							</div> */}
							<div className="wx_Modal_Main">
								<Descriptions column={1}>
									<Descriptions.Item label="交易标识">{data.transactionHash || '--'}</Descriptions.Item>
									<Descriptions.Item label="上链状态">{data.transactionHash ? '上链成功' : '未上链'}</Descriptions.Item>
									<Descriptions.Item label="区块高度">{data.transactionHash ? data.blockNumber : '--'}</Descriptions.Item>
									<Descriptions.Item label="上链时间">{data.transactionHash ? renderTime(data.createTime) : '- -'}</Descriptions.Item>
									{iSCreateRefound ? (
										<>
											<Descriptions.Item label="发起账户">{data.fromPubKey}</Descriptions.Item>
										</>
									) : (
										<>
											<Descriptions.Item label="付款账户">{data.fromPubKey}</Descriptions.Item>
											<Descriptions.Item label="收款账户">{data.toPubKey}</Descriptions.Item>
										</>
									)}
									<Descriptions.Item label="融信金额">¥ {renderAmountInCent(data.para)}</Descriptions.Item>
								</Descriptions>
							</div>
						</div>
					)}
				</Box>
			</Modal>
		</div>
	)
}

const Box = styled.div`
	.wx_Modal_Main {
		padding: 20px;
	}
`
export default BlockInfo
