import { Form, Input, Modal } from 'antd'
import React, { useEffect, useState } from 'react'
import styled from 'styled-components'

interface IProp {
	visible: boolean
	onOk: ({ linkBiz }) => void
	onCancel: () => void
}
const AddLinkBizModal = ({ visible, onOk, onCancel }: IProp) => {
	const [form] = Form.useForm()
	const [loading, setLoading] = useState(false)
	useEffect(() => {
		if (visible) {
			form.resetFields()
		}
	}, [visible])
	const onFinish = () => {
		setLoading(true)
		form
			.validateFields()
			.then(values => {
				setLoading(false)
				onOk && onOk(values)
			})
			.catch(err => {
				console.log(err)
				setLoading(false)
			})
	}
	return (
		<Modal open={visible} onOk={onFinish} title="提交发票" onCancel={onCancel} confirmLoading={loading}>
			<Box>
				<Form form={form}>
					<Form.Item label="开票业务" name="linkBiz" rules={[{ required: true, message: '请输入开票业务' }]}>
						<Input type="text" placeholder="请输入，例如6月平台服务费" maxLength={50}></Input>
					</Form.Item>
				</Form>
			</Box>
		</Modal>
	)
}

const Box = styled.div`
	padding: 60px 75px;
`

export default AddLinkBizModal
