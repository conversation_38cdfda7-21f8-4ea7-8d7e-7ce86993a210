import { stampToTime } from '@src/utils/timeFilter'
import { Form, Modal, DatePicker, message } from 'antd'
import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
const RangePicker = DatePicker.RangePicker
import { invoicingManageApi } from '@src/pages/oo-client/api'

interface IProp {
	visible: boolean
	onOk: () => void
	onCancel: () => void
}
const NotifyInvoiceEmail = ({ visible, onOk, onCancel }: IProp) => {
	const [form] = Form.useForm()
	const [loading, setLoading] = useState(false)
	useEffect(() => {
		if (visible) {
			form.resetFields()
		}
	}, [visible])

	/**
	 * 推送发票
	 */

	const onNotifyInvoiceEmail = async params => {
		let res = await invoicingManageApi.notifyInvoiceEmail(params).catch(err => {
			console.log(err)
			setLoading(false)
		})
		if (res) {
			setLoading(false)
			onOk && onOk()
			message.success('推送成功')
		}
	}

	const onFinish = () => {
		setLoading(true)
		form
			.validateFields()
			.then(async values => {
				if (values.invoiceDate && values.invoiceDate.length > 1) {
					values.invoiceDateFrom = stampToTime(values.invoiceDate[0], 6)
					values.invoiceDateTo = stampToTime(values.invoiceDate[1], 6)
					delete values.invoiceDate
				}
				await onNotifyInvoiceEmail(values)
			})
			.catch(err => {
				console.log(err)
				setLoading(false)
			})
	}
	return (
		<Modal width={560} open={visible} onOk={onFinish} title="批量邮件推送" onCancel={onCancel} confirmLoading={loading}>
			<Box>
				<Form form={form}>
					<Form.Item
						label="开票日期"
						name="invoiceDate"
						rules={[
							({ getFieldValue }) => ({
								validateTrigger: 'onChange',
								required: true,
								validator(_, value) {
									if (value && value.length > 1) {
										let days = value[1].diff(value[0], 'days')
										if (days > 90) {
											return Promise.reject('开票日期跨度最大为90个自然日')
										} else {
											return Promise.resolve()
										}
									} else {
										return Promise.reject('请选择开票日期')
									}
								},
							}),
						]}
					>
						<RangePicker
							getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
							placeholder={['开票日期（开始)', '开票日期（结束)']}
							style={{ width: 300 }}
							format="YYYY-MM-DD"
						></RangePicker>
					</Form.Item>
				</Form>
			</Box>
		</Modal>
	)
}

const Box = styled.div`
	padding: 60px 75px;
	.form .ant-form-item.invoiceDate {
		width: 300px;
	}
`

export default NotifyInvoiceEmail
