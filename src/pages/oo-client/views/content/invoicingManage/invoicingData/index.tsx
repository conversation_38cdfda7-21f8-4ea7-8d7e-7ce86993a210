import React, { useState, useRef } from 'react'
import { invoicingManageApi } from '@src/pages/oo-client/api'
import SearchTable from '@globalComponents/new-widget/wx-table/searchTable'
import { findMatchItemByPageHash } from '@src/pages/oo-client/biz/bizIndex'
import { getTabTit, invalidVal } from '@globalBiz/gBiz'
import { Button, DatePicker, Input, Modal, Space, Tooltip, message } from 'antd'
import FormItem from 'antd/es/form/FormItem'
import styled from 'styled-components'
const RangePicker = DatePicker.RangePicker
import DetailModal from '@clientComponents/detailModal'
import { renderAmount } from '@src/pages/oo-client/config/TableColumnsRender'
import { useNavigate } from 'react-router-dom'
import { stampToTime } from '@src/utils/timeFilter'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import NotifyInvoiceModal from './components/notifyInvoiceModal'

const InvoiceHeader = () => {
	const [detailVisible, setDetailVisible] = useState<boolean>(false)
	const [invoiceData, setInvoiceData] = useState<any>({})
	const navigator = useNavigate()
	const searchBarTableRef = useRef(null)
	const [notifyInvoiceVisible, setNotifyInvoiceVisible] = useState(false)

	/**
	 * 删除发票
	 * @returns
	 */
	const onDeleteInvoice = async params => {
		let res = await invoicingManageApi.deleteInvoice(params).catch(err => {
			console.log(err)
		})
		if (res) {
			message.success('删除成功')
			searchBarTableRef?.current?.updateTable()
		}
	}
	/**
	 * 推送发票
	 */

	const onNotifyInvoiceEmail = async params => {
		let res = await invoicingManageApi.notifyInvoiceEmail(params).catch(err => {
			console.log(err)
		})
		if (res) {
			message.success('推送成功')
			searchBarTableRef?.current?.updateTable()
		}
	}

	const columns = [
		getTabTit(['发票号码', 'invoiceNumber'], (text, record) => {
			if (text === undefined || text === null) {
				return invalidVal
			}
			return (
				<Tooltip title={text}>
					<span
						className="link"
						title={text}
						onClick={() => {
							setInvoiceData(record)
							setDetailVisible(true)
						}}
					>
						{text}
					</span>
				</Tooltip>
			)
		}),
		getTabTit(['买方名称', 'buyerName', null, true]),
		getTabTit(['卖方名称', 'supplierName', null, true]),
		getTabTit(['含税金额（￥）', 'pretaxAmount'], renderAmount),
		getTabTit(['开票日期', 'invoiceDate']),
		getTabTit(['开票业务', 'linkBiz']),
		getTabTit(['操作', 'operate'], (text, record) => {
			return (
				<Space>
					<span
						className="link"
						onClick={() => {
							let { id } = record
							if (id === null || id === undefined) return
							onNotifyInvoiceEmail({ idList: [id] })
						}}
					>
						推送发票
					</span>
					<span
						className="red"
						onClick={() => {
							Modal.confirm({
								title: '确认删除发票',
								icon: <ExclamationCircleOutlined />,
								okText: '确认',
								cancelText: '取消',
								onOk: () => {
									let { id } = record
									if (id === null || id === undefined) return
									onDeleteInvoice({ id })
								},
							})
						}}
					>
						删除
					</span>
				</Space>
			)
		}),
	]
	const tableConfig = {
		table: {
			columns,
			requestUrl: invoicingManageApi.queryInvoiceList,
		},
		searchBar: {
			transform: param => {
				if (param.invoiceDate && param.invoiceDate.length > 1) {
					param.invoiceDateFrom = stampToTime(param.invoiceDate[0], 6)
					param.invoiceDateTo = stampToTime(param.invoiceDate[1], 6)
					delete param.invoiceDate
				}
				return param
			},
			formItems: (
				<>
					<FormItem name="buyerName" key="buyerName">
						<Input placeholder="买方名称" />
					</FormItem>
					<FormItem name="invoiceDate" label="" className="invoiceDate">
						<RangePicker
							getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
							placeholder={['开票日期（开始)', '开票日期（结束)']}
							style={{ width: 300 }}
							format="YYYY-MM-DD"
						/>
					</FormItem>
				</>
			),
		},
	}

	const goPage = () => {
		let menuItem = findMatchItemByPageHash()
		navigator('/content/invoicingManage/uploadInvoice', { state: { menuItem } })
	}
	return (
		<Box>
			<div className="card-wrapper">
				<SearchTable {...tableConfig} ref={searchBarTableRef}>
					<div className="btnDom">
						<Space>
							<Button
								type="primary"
								onClick={() => {
									setNotifyInvoiceVisible(true)
								}}
							>
								批量邮件推送
							</Button>
							<Button type="primary" onClick={goPage}>
								上传发票
							</Button>
						</Space>
					</div>
				</SearchTable>
			</div>
			<DetailModal title="查看发票" pageName="invoicing" onCancel={() => setDetailVisible(false)} visible={detailVisible} dataSource={invoiceData} />
			<NotifyInvoiceModal
				visible={notifyInvoiceVisible}
				onOk={() => {
					setNotifyInvoiceVisible(false)
					searchBarTableRef?.current?.updateTable()
				}}
				onCancel={() => {
					setNotifyInvoiceVisible(false)
				}}
			></NotifyInvoiceModal>
		</Box>
	)
}
const Box = styled.div`
	.form .ant-form-item.invoiceDate {
		width: 300px;
	}
	.btnDom {
		margin-bottom: 20px;
	}
`

export default InvoiceHeader
