import React, { useState, useRef } from 'react'
import { invoicingManageApi } from '@src/pages/oo-client/api'
import SearchTable from '@globalComponents/new-widget/wx-table/searchTable'
import { getTabTit, invalidVal } from '@globalBiz/gBiz'
import { Button, DatePicker, Input, Tooltip } from 'antd'
import FormItem from 'antd/es/form/FormItem'
import styled from 'styled-components'
const RangePicker = DatePicker.RangePicker
import InvoiceDetailModal from './components/invoiceDetailModal'
import { InvoiceHeaderInfoIP } from '@src/@types/invoice'
import { stampToTime } from '@src/utils/timeFilter'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'

const InvoiceHeader = () => {
	const [detailVisible, setDetailVisible] = useState<boolean>(false)
	const [invoiceData, setInvoiceData] = useState<InvoiceHeaderInfoIP>()
	const searchBarRef = useRef(null)
	const [exportLoading, setExportLoading] = useState(false)

	const columns = [
		getTabTit(['纳税人识别码', 'socialCreditCode'], (text, record) => {
			if (text === undefined || text === null) {
				return invalidVal
			}
			return (
				<Tooltip title={text}>
					<span
						className="link"
						title={text}
						onClick={() => {
							setInvoiceData(record)
							setDetailVisible(true)
						}}
					>
						{text}
					</span>
				</Tooltip>
			)
		}),
		getTabTit(['发票抬头', 'companyName', null, true]),
		getTabTit(['修改时间', 'updateTime'], text => {
			if (text === undefined || text === null) {
				return invalidVal
			}
			return stampToTime(text, 4)
		}),
	]
	//导出
	const onExport = () => {
		setExportLoading(true)
		const params = searchBarRef?.current?.getValues()
		invoicingManageApi
			.exportInvoice(params)
			.then(res => {
				if (res) {
					let type = 'application/vnd.ms-excel'
					downloadFileByFileFlow(res, type, '开票信息.xlsx', () => {
						setExportLoading(false)
					})
				}
			})
			.catch(err => {
				console.log(err)
				setExportLoading(false)
			})
	}

	const tableConfig = {
		table: {
			columns,
			requestUrl: invoicingManageApi.queryInvoicingTitleInfo,
		},
		searchBar: {
			transform: param => {
				if (param.updateTime && param.updateTime.length > 1) {
					param.updateTimeFrom = stampToTime(param.updateTime[0], 1)
					param.updateTimeTo = stampToTime(param.updateTime[1], 1)
					delete param.updateTime
				}
				return param
			},
			btnSlot: (
				<Button type="primary" className="ml10" onClick={onExport} loading={exportLoading}>
					导出
				</Button>
			),
			formItems: (
				<>
					<FormItem name="socialCreditCode" key="socialCreditCode">
						<Input placeholder="纳税人识别码" />
					</FormItem>
					<FormItem name="companyName" key="companyName">
						<Input placeholder="发票抬头" />
					</FormItem>
					<FormItem name="updateTime" label="" className="updateTime">
						<RangePicker
							getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
							placeholder={['修改时间（开始)', '修改时间（结束)']}
							style={{ width: 390 }}
							showTime
							format="YYYY-MM-DD HH:mm:ss"
						/>
					</FormItem>
				</>
			),
		},
	}

	return (
		<Box>
			<div className="card-wrapper">
				<SearchTable {...tableConfig} ref={searchBarRef}></SearchTable>
			</div>
			<InvoiceDetailModal onCancel={() => setDetailVisible(false)} visible={detailVisible} dataSource={invoiceData} />
		</Box>
	)
}
const Box = styled.div`
	.form .ant-form-item.updateTime {
		width: 390px;
	}
`

export default InvoiceHeader
