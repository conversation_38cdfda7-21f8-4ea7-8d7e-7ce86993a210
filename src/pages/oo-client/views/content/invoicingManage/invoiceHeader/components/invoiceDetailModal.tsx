import SmallTitle from '@src/globalComponents/SmallTitle'
import React, { useEffect, useState } from 'react'
import WxDetailDisplay from '@globalComponents/wxDetailDisplay/wxDetailDisplay'
import { Modal } from 'antd'
import styled from 'styled-components'

interface PropsStruct {
	/**
	 * @description 授信信息
	 */
	dataSource: any
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
}

const InvoiceDetailModal = ({ visible, dataSource, onCancel }: PropsStruct) => {
	const invoiceContentFields: DisplayField[] = [
		{
			key: 'companyName',
			note: '发票抬头:',
			value: '',
			span: 12,
		},
		{
			key: 'socialCreditCode',
			note: '纳税人识别码:',
			value: '',
			span: 12,
		},
		{
			key: 'address',
			note: '公司地址:',
			value: '',
			span: 12,
		},
		{
			key: 'phone',
			note: '公司电话:',
			value: '',
			span: 12,
		},
		{
			key: 'bankName',
			note: '开户银行:',
			value: '',
			span: 12,
		},
		{
			key: 'bankNo',
			note: '银行账号:',
			value: '',
			span: 12,
		},
	]
	const recipientFields: DisplayField[] = [
		{
			key: 'recipientName',
			note: '收件人姓名:',
			value: '',
			span: 12,
		},
		{
			key: 'recipientPhone',
			note: '收件人电话:',
			value: '',
			span: 12,
		},
		{
			key: 'recipientAddress',
			note: '收件人地址:',
			value: '',
			span: 12,
		},
	]
	const othersFields: DisplayField[] = [
		{
			key: 'recipientEmail',
			note: '接收邮箱:',
			value: '',
			span: 12,
		},
	]

	const [invoiceContentField, setInvoiceContentField] = useState(invoiceContentFields)
	const [recipientField, setRecipientField] = useState(recipientFields)
	const [othersField, setOthersField] = useState(othersFields)
	useEffect(() => {
		if (visible) {
			//组装开票内容信息数据
			for (let item of invoiceContentFields) {
				item.value = dataSource[item.key]
				if (item.format) {
					item.value = item.format(dataSource[item.key])
				}
			}
			setInvoiceContentField(invoiceContentFields)
			for (let item of recipientFields) {
				item.value = dataSource[item.key]
				if (item.format) {
					item.value = item.format(dataSource[item.key])
				}
			}
			setRecipientField(recipientFields)
			for (let item of othersFields) {
				item.value = dataSource[item.key]
				if (item.format) {
					item.value = item.format(dataSource[item.key])
				}
			}
			setOthersField(othersFields)
		}
	}, [visible])
	return (
		<Modal title="查看开票信息" open={visible} onCancel={onCancel} footer={null} width="860px">
			<Box>
				<div className="main-wrapper">
					<SmallTitle text="开票内容"></SmallTitle>
					<WxDetailDisplay colNum={2} fields={invoiceContentField}></WxDetailDisplay>
					<SmallTitle text="邮寄信息"></SmallTitle>
					<WxDetailDisplay colNum={2} fields={recipientField}></WxDetailDisplay>
					<SmallTitle text="其他"></SmallTitle>
					<WxDetailDisplay colNum={2} fields={othersField}></WxDetailDisplay>
				</div>
			</Box>
		</Modal>
	)
}
const Box = styled.div`
	.main-wrapper {
		padding: 20px;
		.wxDetailDisplay {
			padding: 10px 20px;
			margin-bottom: 15px;
			background-color: #fafafa;
			color: #575757;
			border-radius: 4px;
		}
	}
`

export default InvoiceDetailModal
