import { makeAutoObservable, observable, action } from 'mobx'
import { getStorage, setStorage } from '@factor/biz/bizIndex'
import { constant } from '@factor/config'

type themeType = 'dark' | 'light'
type menuBgType = 'blue' | 'green'
class UiData {
	public constructor() {
		makeAutoObservable(this, {
			theme: observable,
			language: observable,
			breadcrumbItems: observable,
			frontCodeList: observable,
			jumpPageMap: observable,
			setLanguage: action,
			addLoadingNum: action,
			addDetailNameForBreadcrumb: action,
			updateBreadcrumb: action,
			updateFrontCodeList: action,
			updateJumpPageMap: action,
		})
	}
	//dark light
	public theme: themeType = 'dark'
	public menuBg: menuBgType = 'green'

	//左侧菜单的是否收缩； 模认不收缩
	public collapsed = false
	//左侧菜单长度配置
	public menuWidth = 208

	//是否展示copyright
	public showCopyRight = true
	// 内容页面页标题
	public pageTitle = '我是第一个页面'
	//全局loading控制
	public spinning = false
	public paddingNum = 0
	public breadcrumbItems = getStorage('breadcrumbItems') || []
	public frontCodeList = getStorage('frontCodeList') || []
	public jumpPageMap: any = getStorage('jumpPageMap') || {}
	public menuList = getStorage('menuList') || []
	// 默认中文
	public language: languageType = getStorage('language', 'local') || constant.language.zhCN

	public triggerCollapsed(collapsed?: boolean) {
		if (Boolean(collapsed)) {
			this.collapsed = true
			this.menuWidth = 48
		} else {
			this.collapsed = false
			this.menuWidth = 208
		}
	}

	public updateFrontCodeList(frontCodeList: string[]) {
		this.frontCodeList = frontCodeList
	}

	public setLanguage(language: languageType): void {
		this.language = language
		setStorage({ language }, 'local')
	}

	// 请求增加一个添加一个
	// 成功或失败一个 减一个，至到0 ，取消Loading
	public addLoadingNum(num: number) {
		this.paddingNum += num
		if (this.paddingNum === 0) {
			if (this.spinning !== false) {
				this.spinning = false
			}
		} else {
			if (this.spinning !== true) {
				this.spinning = true
			}
		}
	}

	public addDetailNameForBreadcrumb(breadcrumbName: string) {
		let item: MenuType = getStorage('lastSelectedMenuItem')
		if (item && item.path && item.name) {
			let { breadcrumb, path, name } = item
			if (Array.isArray(breadcrumb)) {
				breadcrumb[breadcrumb.length - 1] = { path, name }
				breadcrumb.push(breadcrumbName)
				this.breadcrumbItems = breadcrumb
			}
		}
	}

	public updateBreadcrumb(breadcrumbItems: breadcrumbType) {
		this.breadcrumbItems = breadcrumbItems
		setStorage({ breadcrumbItems })
	}

	public setPageTitle(pageTitle: string) {
		this.pageTitle = pageTitle
	}

	public setPageStyle(theme: themeType, menuBg: menuBgType) {
		if (theme) {
			this.theme = theme
		}
		if (menuBg) {
			this.menuBg = menuBg
		}
	}

	public updateMenuList(menuList) {
		this.menuList = getStorage('menuList')
		setStorage({ menuList })
	}
	public updateJumpPageMap(jumpPageMap) {
		this.jumpPageMap = jumpPageMap
	}
}

export default new UiData()
