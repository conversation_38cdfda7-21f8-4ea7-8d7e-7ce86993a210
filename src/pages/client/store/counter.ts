import { getStorage, setStorage } from '@src/pages/sp-client/biz/bizIndex'
import { makeAutoObservable, observable, action } from 'mobx'

interface CountInf {
	count: number
	testCountMemory: number
	addCount: (num: number) => void
	addCountStorage: (num: number) => void
}

class Counter implements CountInf {
	constructor() {
		makeAutoObservable(this, {
			count: observable,
			addCount: action,
		})
	}

	public count = 1
	public testCountMemory = getStorage('testCountMemory') || 0

	public addCount(num: number): void {
		this.count = this.count + num
	}

	public addCountStorage(num: number): void {
		this.testCountMemory = this.testCountMemory + num
		setStorage({
			testCountMemory: this.testCountMemory,
		})
	}
}
export default new Counter()
