import stores from '@src/pages/client/sotre/store'
import '@styles/loading.css'
import '@styles/reset.less'
import { ConfigProvider } from 'antd'
import 'antd/dist/antd.less'
import zhCN from 'antd/lib/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { Provider } from 'mobx-react'
import React from 'react'
import { render } from 'react-dom'
import styled from 'styled-components'
import './assets/styles/common.less'
import RouteMain from '@factor/routes'
import { observer } from 'mobx-react-lite'
import { getStorage, getFirstPagePath, getFrontCodeListForAdmin, getRealMenuList, setStorage } from '@src/pages/client/biz/bizIndex'
dayjs.locale('zh-cn')

const App = observer(() => {
	return (
		<AppStyle>
			<ConfigProvider componentSize="middle" locale={zhCN}>
				<RouteMain />
			</ConfigProvider>
		</AppStyle>
	)
})
const AppStyle = styled.div`
	position: relative;
	width: 100vw;
	/* 最小宽度为1280是为了当计算机分辨率是 1920x1080，浏览器缩放为150%时，水平不会出现滚动条。并且1280px可以展示home页的五个 */
	/*min-width: 1280px;*/
	/* overflow-y: scroll; */
	height: 100vh;
	min-height: 500px;
	/* user-select: none; */
	background-color: #f8f8f8;
`

render(
	<Provider store={stores}>
		<App />
	</Provider>,
	document.getElementById('root')
)
