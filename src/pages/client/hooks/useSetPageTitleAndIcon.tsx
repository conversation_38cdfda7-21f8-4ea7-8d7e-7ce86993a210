//设置页面的title 及 icon
import { useEffect } from 'react'
import { logoApi, commonModuleApi } from '@src/pages/client/api'
import { setFavicon } from '@factor/biz/bizIndex'

const getImgUrl = (data: string) => {
	return commonModuleApi.downloadFileUrl({ fileUrl: data })
}

const getHomePageInfo = async () => {
	logoApi
		.getHomePageInfo()
		.then(async res => {
			if (!res) return
			const { tabTitle, iconUrl } = res

			if (tabTitle) {
				//设置页面 title
				document.title = tabTitle
			}
			let newIconUrl = JSON.parse(iconUrl)['url']
			let favicon = await getImgUrl(newIconUrl)
			setFavicon(favicon)
		})
		.catch(e => {
			console.log(e)
		})
}

const useSetPageTitleAndIcon = () => {
	useEffect(() => {
		// getHomePageInfo()
	}, [])
	return {}
}

export default useSetPageTitleAndIcon
