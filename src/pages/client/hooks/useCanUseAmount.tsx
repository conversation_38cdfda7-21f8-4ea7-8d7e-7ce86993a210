import React, { useEffect, useState } from 'react'
import { transferModuleApi } from '@src/pages/client/api'

const getSCanUseAmount = () => {
	let sCanUseAmount = 0
	let sBuildCanUseAmount = 0
	let sTeamCanUseAmount = 0
	return transferModuleApi.sCouAmount().then(res => {
		if (res) {
			const { availableCouAmountInCent, userAvailableQuotaInCent, teamAvailableQuotaInCent } = res
			// 此处需要判断
			sCanUseAmount = availableCouAmountInCent
			sBuildCanUseAmount = availableCouAmountInCent > userAvailableQuotaInCent ? userAvailableQuotaInCent : availableCouAmountInCent
			sTeamCanUseAmount = availableCouAmountInCent > teamAvailableQuotaInCent ? Number(teamAvailableQuotaInCent) : Number(availableCouAmountInCent)
			return {
				sCanUseAmount,
				sBuildCanUseAmount,
				sTeamCanUseAmount,
			}
		}
	})
}

type sCanUseAmountType = {
	sCanUseAmount: number
	sBuildCanUseAmount: number
	sTeamCanUseAmount: number
}

export default () => {
	let [canUseObj, setCanUseObj] = useState<sCanUseAmountType>({
		sCanUseAmount: 0,
		sBuildCanUseAmount: 0,
		sTeamCanUseAmount: 0,
	})

	useEffect(() => {
		getSCanUseAmount().then((res: sCanUseAmountType) => {
			setCanUseObj(res)
		})
	}, [])

	return canUseObj
}
