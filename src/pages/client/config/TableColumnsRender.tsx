import React from 'react'
import { numberToThousands, stampToTime } from '@utils/timeFilter'
import { formatDate, formatListData, formatNumber } from '@utils/format'
import { TransferStatus, cashStatus, payStatus, couStatusList, RefoundStatus, destroyStatus } from '@factor/config/cou/couAssetsConfig'
import { businessType } from '@globalConfig/registerManageConfig'
import { creditStatus, creditStatusMap } from '@factor/config/companyCreditManage'
import { Tooltip, Tag } from 'antd'

import { activeFinaceServiceStatus, businessSchema } from '@src/globalConfig/codeMaps'

const invalidVal = '--'
function _parseFloatText(text: string) {
	return Number.parseFloat(text)
}
//格式化金额截断2位
export const renderAmount = (text: string) => {
	//元
	const param = _parseFloatText(text)
	if (!Number.isNaN(param)) {
		return numberToThousands(param)
	} else {
		return invalidVal
	}
}
export function renderRelationName(text: any) {
	return text ? (
		<Tooltip placement="topLeft" title={text}>
			{text}
		</Tooltip>
	) : (
		'--'
	)
}
//格式化金额四舍五入2位
export const renderAmountRound = (text: string) => {
	const param = _parseFloatText(text)
	if (!Number.isNaN(param)) {
		return formatNumber.round2Thousands(param)
	} else {
		return invalidVal
	}
}
export const render2Fixed = (text: string) => {
	//元
	const param = _parseFloatText(text).toFixed(2)
	if (!Number.isNaN(param)) {
		return numberToThousands(param)
	} else {
		return invalidVal
	}
}

export function renderCompanyType(text: string) {
	return formatListData.companyType(text)
}
//日期 eg:2018/05/05
export function renderDate(text: string) {
	if (formatDate.keeMonthAndDay(text) == '- -') {
		return formatDate.keeMonthAndDay(text)
	} else {
		return (
			<div className="ellipsis-table-clon" style={{ display: 'flex', alignItems: 'center' }}>
				<Tooltip title={formatDate.keeMonthAndDay(text)}>{formatDate.keeMonthAndDay(text)}</Tooltip>
			</div>
		)
	}

	// return formatDate.keeMonthAndDay(text)
}
//日期 eg:2018-05-05
export function renderDate_(text: string) {
	if (stampToTime(text, 6) == '- -') {
		return stampToTime(text, 6)
	} else {
		return (
			<div className="ellipsis-table-clon">
				<Tooltip title={stampToTime(text, 6)}>{stampToTime(text, 6)}</Tooltip>
			</div>
		)
	}
}

//时间 eg: 2018/05/05 14:45:47
export function renderTime(text: string) {
	if (formatDate.keepMinutesAndSeconds(text) == '- -') {
		return formatDate.keepMinutesAndSeconds(text)
	} else {
		return (
			<div className="ellipsis-table-clon" style={{ display: 'flex', alignItems: 'center' }}>
				<Tooltip title={formatDate.keepMinutesAndSeconds(text)}>{formatDate.keepMinutesAndSeconds(text)}</Tooltip>
			</div>
		)
	}
}

//验真状态
export function renderCheckStatus(text: any, record) {
	if (text + '' === '1') {
		return <span style={{ color: '#03b003' }}>验真成功</span>
	}
	if (text + '' === '0') {
		return (
			<div>
				<span style={{ color: 'red' }}>验真失败</span>
			</div>
		)
	}
	if (text + '' === '9') {
		return <span style={{ color: '#a16a04' }}>验真中</span>
	}
}

//流转状态
export function renderTransferStatus(text: any) {
	const obj = TransferStatus.find(item => item.code === text)
	return obj ? obj['name'] : text
}

//获取 退款状态
export const getRefoundStatus = (text: string) => {
	const obj = RefoundStatus.find(item => item.code === text)
	return obj ? obj['name'] : text
}

//支付状态
export function renderPayStatus(text: any) {
	return payStatus[text] || '--'
}
//质押兑付状态
export function renderPledgedStatus(text: any) {
	console.log('dkdkdsjjsjsj', text)
	return cashStatus[text] || '--'
}

//分转换为元进行显示
export function renderAmountInCent(text: any) {
	const param = _parseFloatText(text) / 100
	if (!Number.isNaN(param)) {
		return numberToThousands(param)
	} else {
		return invalidVal
	}
}

export function renderTooltip(text: any) {
	return (
		<Tooltip placement="topLeft" title={text}>
			{text}
		</Tooltip>
	)
}
export function renderTooltipStrikethrough(text: any) {
	return text ? (
		<Tooltip placement="topLeft" title={text}>
			{text}
		</Tooltip>
	) : (
		'--'
	)
}
//这里不要用于 select 的 option
export function renderStatus(text: any) {
	return cashStatus[text]
}
//付息方式
export function renderPayWay(text) {
	return text === 'center' ? '核心企业付息' : '供应商付息'
}

export function renderMapFinanceStatus(text, record, index) {
	const key = record['status']
	const statusList = {
		CREATE: '提交失败',
		AUDIT: '审核中',
		REJECT: '已拒绝',
		CONFIRM: '已放款',
		REPAID: '已还款',
	}
	return statusList[key] || invalidVal
}
export function renderCouFinanceStatus(text, record, index) {
	const key = record['status']
	const statusList = {
		PENDING: '待签署协议',
		AUDIT: '审核中',
		REJECT: '已拒绝',
		CONFIRM: '已放款',
		// CANCELED: '已取消',
		REPAID: '已还款',
		WAIT_CONFIRM: '待放款',
	}
	return statusList[key] || invalidVal
}
//table 项 添加 % 号
export function renderAddPrent(text) {
	if (!isNaN(text) && typeof text === 'number') {
		return text + '%'
	}
	if (typeof text === 'string') {
		return Number(text) + '%'
	}
	return null
}
//table 项 添加 % 号
export function renderAddPrent2(text) {
	if (!isNaN(text) && typeof text === 'number') {
		return text.toFixed(5) + '%'
	}
	if (typeof text === 'string') {
		return Number(text).toFixed(5) + '%'
	}
	return null
}
export function renderOriginalCouStatus(text) {
	let statusText = ''
	couStatusList.forEach(item => {
		let keyStatus = item.key.split(',')
		if (keyStatus.includes(text)) {
			statusText = item.name
		}
	})
	return statusText || invalidVal
}

//render 不需要处理的数据
export function renderData(text) {
	return text
}

// 发票类型转换
export function renderInvoiceType(text) {
	const invoiceTypeInfo = {
		VATSpecial: '增值税专用发票',
		VehicleSales: '机动车销售统一发票',
		VATCommon: '增值税普通发票',
		VATOfTransport: '货物运输业增值税专用发票',
		ELEI_SVATI: '电子发票（增值税专用发票）',
		ELEI_OI: '电子发票（普通发票）',
	}
	return invoiceTypeInfo[text]
}

//金融服务商 业务模式
export function renderBusinessSchema(text: any) {
	return text || text === 0 ? businessSchema.filter(item => text === item.code)[0]['name'] : '- -'
}

// 配置金融服务状态（企业圈）
export function renderActiveFinaceServiceStatus(name: string) {
	return activeFinaceServiceStatus.filter(item => item.code === name)[0].name || '- -'
}

//授信审核状态
export function renderAuditStatus(text: string) {
	if (text === 'checking') {
		return (
			<Tag color="#F59A23" style={{ width: '64px', textAlign: 'center' }}>
				审核中
			</Tag>
		)
	}
	if (text === 'confirmed') {
		return <Tag color="#87d068">审核通过</Tag>
	}
	if (text === 'rejected') {
		return <Tag color="#f50">审核拒绝</Tag>
	}
	return '- -'
}

//授信状态
export function renderCreditStatus(text: number) {
	if (text === creditStatus.enable) {
		return <Tag color="#87d068">{creditStatusMap[creditStatus.enable]}</Tag>
	}
	if (text === creditStatus.disable) {
		return <Tag color="#f50">{creditStatusMap[creditStatus.disable]}</Tag>
	}
	return invalidVal
}

//核销状态
export function renderDestroyStatus(text: string) {
	const obj = destroyStatus.find(item => item.key === text)
	return obj ? obj['name'] : text
}

//业务交易类型
export function renderBusinessType(text: string) {
	if (text === null || text === undefined) {
		return '- -'
	}
	const obj = businessType.find(item => item.key === text)
	return obj ? obj['name'] : text
}

export function renderPledgeStatus(val: string) {
	if (val == '1') {
		return '质押中'
	} else {
		return '未质押'
	}
}

export function renderCashTypeOption(val: string) {
	return { SETTLEMENT: '到期清分', REFUND: '退款核销', UN_CASH: '--' }[val] || '--'
}
