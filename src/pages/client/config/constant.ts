import zhCN from 'antd/lib/locale/zh_CN'
import enUS from 'antd/lib/locale/en_US'

interface languageProps {
	zhCN: languageType
	enUS: languageType
}

//国际化配置
const language: languageProps = {
	zhCN: 'zhCN',
	enUS: 'enUS',
}

const languageList: Array<{ name: string; code: languageType }> = [
	{ name: '中文', code: language.zhCN },
	{ name: 'English', code: language.enUS },
]

//antd的国际化
const antdLanguage = {
	zhCN: zhCN,
	enUS: enUS,
}

// 相关企业类型
export enum ESupportOrgType {
	FI = 'FI', // 金融机构
	FP = 'FP', // 金融机构母公司
	C = 'C', // 核心企业
	S = 'S', // 供应商
	B = 'B', // 票据平台普通企业
}

const constant = {
	language,
	languageList,
	antdLanguage,
	leftMenuWidth: 208,
	collapsedWidth: 48,
	rightForCollapsedWidth: 992,
}

export { constant }
