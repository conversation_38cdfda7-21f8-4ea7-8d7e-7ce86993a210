import React from 'react'
import { numberToThousands, stampToTime } from '@utils/timeFilter'
import { invalidVal } from '@src/globalBiz/gBiz'
import { invoiceCheckStatusMap } from '@src/globalBiz/invoiceBizModule'

const columns: any = {
	contract: ['contractCode', 'name', 'companyBuyerName', 'companySellerName', 'startDate', 'endDate', 'productName', 'amount', 'signDate', 'contractFileList'],
	invoice: [
		'invoiceType',
		'checkStatus',
		// 'businessName',
		'buyerName',
		'supplierName',
		'invoiceCode',
		'invoiceNumber',
		'pretaxAmount',
		'invoiceDate',
		'invoiceAmount',

		'checkCode',
		//1.8.7.1 新增字段，发票状态
		'checkState',
		//江阴 排序放到最后
		'invoiceFileList',
	],
	invoicing: [
		'invoiceType',
		// 'businessName',
		'buyerName',
		'supplierName',
		'invoiceCode',
		'invoiceNumber',
		'pretaxAmount',
		'invoiceDate',
		'invoiceAmount',
		'invoicingFileList',
		'checkCode',
	],
}

const columnsRender: any = {
	contractCode: { key: 'contractCode', title: '合同编号:' },
	name: { key: 'name', title: '合同名称:' },
	companyBuyerName: { key: 'companyBuyerName', title: '买方名称:' },
	companySellerName: { key: 'companySellerName', title: '卖方名称:' },
	productName: { key: 'productName', title: '产品名称:' },
	amount: { key: 'amount', title: '合同金额(￥):', render: amount },
	startDate: { key: 'startDate', title: '开始日期:', render: (val: number) => stampToTime(val, 6) },
	endDate: { key: 'endDate', title: '结束日期:', render: (val: number) => stampToTime(val, 6) },
	signDate: { key: 'signDate', title: '签订日期:', render: (val: number) => stampToTime(val, 6) },
	invoiceFileList: { key: 'fileUrl', title: '发票附件:', render: fileList },
	invoicingFileList: { key: 'fileUrl', title: '发票附件:', render: fileList },
	contractFileList: { key: 'fileUrl', title: '合同扫描件:', render: fileList },
	invoiceType: { key: 'invoiceType', title: '发票类型:', render: invoiceType },
	// businessName: { key: 'businessName', title: '购买方名称' },
	buyerName: { key: 'buyerName', title: '买方名称:' },
	invoiceCode: { key: 'invoiceCode', title: '发票代码:' },
	invoiceNumber: { key: 'invoiceNumber', title: '发票号码:' },
	pretaxAmount: { key: 'pretaxAmount', title: '含税金额(￥):', render: amount },
	invoiceAmount: { key: 'amount', title: '不含税金额(￥):', render: amount },
	checkCode: { key: 'checkCode', title: '校验码后6位:' },
	invoiceDate: { key: 'invoiceDate', title: '开票日期:', render: (val: number) => stampToTime(val, 6) },
	checkStatus: { key: 'checkFlag', title: '验真状态:', render: (val: number) => (val ? '验真成功' : '验真失败') },
	supplierName: { key: 'supplierName', title: '卖方名称:' },
	checkState: {
		key: 'checkState',
		title: '发票状态:',
		render: (text, record) => {
			return invoiceCheckStatusMap[text] || invalidVal
		},
	},
}

export const getColumns = (pageName: string, columnsObj: any) => {
	const arr = columns[pageName]
	const columnsArr: any[] = []
	if (arr) {
		arr.forEach((i: string) => {
			if (!columnsObj[i]) columnsArr.push(columnsRender[i])
			else columnsArr.push({ ...columnsRender[i], ...columnsObj[i] })
		})
	}
	return columnsArr
}

function amount(val: any) {
	// 不含税金额不是必传且未传则是null，需要展示--
	if (val === null) {
		return '--'
	} else {
		return numberToThousands(parseFloat(val).toFixed(2))
	}
}
function fileList(val: any, callback: any) {
	const arr: any = []
	if (val) {
		const list = JSON.parse(val) || []
		if (Array.isArray(list)) {
			list.forEach(item => {
				for (const key in item) {
					arr.push({ fileName: key, fileUrl: item[key] })
				}
			})
		} else {
			for (const key in list) {
				arr.push({ fileName: key, fileUrl: list[key] })
			}
		}
	}
	return arr.map((i: any) => {
		return (
			<div key={i.fileUrl} className="fileBox">
				<a className="fileName" onClick={() => callback(i.fileUrl)} title={i.fileName}>
					{i.fileName}
				</a>
			</div>
		)
	})
}
function invoiceType(val: string) {
	const invoiceInfo = [
		{ code: 'VATSpecial', name: '增值税专用发票', ocrCode: ['01'] },
		{ code: 'VehicleSales', name: '机动车销售统一发票', ocrCode: ['03'] },
		{ code: 'VATCommon', name: '增值税普通发票', ocrCode: ['04', '10', '11', '14'] },
		{ code: 'ELEI_SVATI', name: '电子发票（增值税专用发票）', ocrCode: ['03'] },
		{ code: 'ELEI_OI', name: '电子发票（普通发票）', ocrCode: ['03'] },
	]
	const obj = invoiceInfo.find(i => i.code === val)
	return obj && obj.name
}
