import { commonModuleApi } from '@src/pages/client/api'
interface Config {
	[name: string]: SearchConfig
}

interface ArrConfig {
	[index: number]: {
		key: string //下拉项key
		name: string //下拉项name
	}
}

interface SearchConfig {
	[x: string]: any
	[index: number]: {
		component: 'input' | 'select' | 'rangeDate' | 'date' | 'searchSelect' | 'queryByName' | 'inputGroup' //对应展示的组件
		label: string //组件的label
		componentKey: string //组件的搜索内容字段
		arrData?: string | ArrConfig //组件初始设定的下拉数据, typeof string 取props.optionData[arrData]
		noAddAll?: boolean //默认添加all 设为true不添加
		placeholder?: string | [string, string] // rangeDate 为[]形式
		width?: number //宽度 默认100
		selectTitle?: string //下拉框的 title
		Api?: any //获取公司名称的api
		companyType?: string // 公司的类型
		valuekey?: string //通过api接口要取什么字段名字（服务于 searchBycompanyName）
		maxLength?: number
		dropdownMatchSelectWidth?: number | boolean // 选择框中下拉框的宽度
	}
}
const couMeaning = '融信'

const invoiceWarnCommonControl: SearchConfig = [
	{
		component: 'select',
		label: '',
		placeholder: '查验状态',
		componentKey: 'checkFlag',
		arrData: 'checkFlagList',
		noAddAll: true,
		width: 150,
	},
	{
		component: 'select',
		label: '',
		placeholder: '发票状态',
		componentKey: 'invoiceStatus',
		arrData: 'invoiceStatusList',
		noAddAll: true,
		width: 150,
	},
	{
		component: 'rangeDate',
		label: '',
		placeholder: ['查验时间(开始)', '查验时间(结束)'],
		componentKey: 'due',
		width: 300,
	},
]
const SearchBarObject: Config = {
	tobeAgreement: [
		{
			component: 'input',
			label: '',
			placeholder: '融资申请编号',
			componentKey: 'applicationNumber',
			width: 220,
		},
	],
	invoiceList: [
		{
			//发票列表
			component: 'input',
			label: '',
			componentKey: 'invoiceNumber',
			placeholder: '发票号码',
			width: 140,
		},
		{
			component: 'select',
			label: '',
			componentKey: 'buyerName',
			arrData: 'buyerName',
			placeholder: '买方名称',
			noAddAll: true,
			width: 150,
		},
		{
			component: 'select',
			label: '',
			componentKey: 'checkFlag',
			arrData: 'checkFlag',
			placeholder: '验真状态',
			noAddAll: true,
			width: 150,
		},
		{
			component: 'select',
			label: '',
			componentKey: 'checkState',
			arrData: 'checkState',
			placeholder: '发票状态',
			noAddAll: true,
			width: 150,
		},
	],
	contractList: [
		{
			//合同编号
			component: 'input',
			label: '',
			componentKey: 'contractCode',
			placeholder: '合同编号',
			width: 220,
		},
	],
	chooseContractPage: [
		{
			//选择合同页面
			component: 'input',
			label: '合同编号',
			componentKey: 'contractCode',
		},
		{
			component: 'input',
			label: '合同名称',
			componentKey: 'name',
		},
	],
	searchCouC: [
		{
			//COU支付查询页面
			component: 'select',
			label: '收款方',
			componentKey: 'toUuid',
			arrData: 'toList',
		},
		{
			component: 'select',
			label: '状态',
			componentKey: 'status',
			arrData: 'searchCouPageList',
		},
		{
			component: 'input',
			label: `${couMeaning}支付编号`,
			componentKey: 'transferNo',
		},
	],
	searchCouS: [
		{
			//COU支付查询页面
			component: 'select',
			label: '付款方',
			componentKey: 'fromUuid',
			arrData: 'fromList',
		},
		{
			component: 'select',
			label: '状态',
			componentKey: 'status',
			arrData: 'searchCouPageList',
		},
		{
			component: 'input',
			label: `${couMeaning}支付编号`,
			componentKey: 'transferNo',
		},
	],
	verifyCou: [
		{
			//COU确认接收
			component: 'select',
			label: '付款方',
			componentKey: 'fromUuid',
			arrData: 'fromList',
		},
		{
			component: 'input',
			label: `${couMeaning}支付编号`,
			componentKey: 'transferNo',
		},
	],

	chooseCou: [
		{
			//融信支付-选择cou
			component: 'select',
			label: '开立方',
			componentKey: 'publishUuid',
			arrData: 'publishList',
			noAddAll: true,
		},
		{
			component: 'rangeDate',
			label: '兑付到期日',
			componentKey: 'dueDate',
		},
	],
	searchFinance: [
		{
			//创建融资
			component: 'input',
			label: '',
			placeholder: '融资申请编号',
			componentKey: 'applicationNumber',
			width: 220,
		},
		// {
		// 	//金融机构
		// 	component: 'select',
		// 	label: '',
		// 	placeholder: '金融机构',
		// 	componentKey: 'financeList',
		// 	arrData: 'financeList',
		// 	noAddAll: true,
		// 	width: 220,
		// },
		{
			//融资状态
			component: 'select',
			label: '',
			placeholder: '状态',
			componentKey: 'statusList',
			arrData: 'statusList',
			noAddAll: true,
			width: 150,
		},
		// {
		// 	//创建融资
		// 	component: 'rangeDate',
		// 	label: '',
		// 	componentKey: 'createDate',
		// 	placeholder: ['创建日期(开始)', '创建日期(结束)'],
		// },
	],
	createFinance: [
		{
			//创建融资
			component: 'date',
			label: '兑付到期日',
			componentKey: 'dueDate',
		},
	],
	//cou兑付页面
	cashSearch: [
		{
			component: 'rangeDate',
			label: '',
			componentKey: 'dueDate',
			placeholder: ['兑付到期日(开始)', '兑付到期日(结束)'],
			width: 290,
		},
		{
			component: 'rangeDate',
			label: '',
			componentKey: 'createDate',
			placeholder: ['创建日期(开始)', '创建日期(结束)'],
			width: 270,
		},
		{
			component: 'select',
			label: '',
			placeholder: '兑付状态',
			componentKey: 'cashStatusList',
			arrData: 'cashStatusList',
			noAddAll: true,
		},
		// {
		// 	component: 'select',
		// 	label: '',
		// 	componentKey: 'pledgeStatusList',
		// 	arrData: 'pledgeStatusList',
		// 	placeholder: '质押状态',
		// 	noAddAll: true,
		// },
		{
			component: 'input',
			label: `支付编号`,
			placeholder: '融信编号',
			componentKey: 'couNo',
		},
	],
	//持有人明细
	holderDetailSearch: [
		{
			component: 'rangeDate',
			label: '',
			componentKey: 'catchDate',
			placeholder: ['兑付到期日(开始)', '兑付到期日(结束)'],
			width: 330,
		},
		{
			component: 'select',
			label: '',
			componentKey: 'cashStatusList',
			arrData: 'cashStatusList',
			placeholder: '兑付状态',
			noAddAll: true,
		},
		{
			component: 'select',
			label: '',
			componentKey: 'pledgeStatus',
			arrData: 'pledgeStatusList',
			placeholder: '质押状态',
			noAddAll: true,
		},
		{
			component: 'input',
			label: `支付编号`,
			placeholder: '融信编号',
			componentKey: 'couNo',
		},
	],
	credit: [
		{
			//cou兑付页面
			component: 'input',
			label: '',
			placeholder: '授信企业',
			componentKey: 'companyName',
			width: 200,
		},
		{
			component: 'select',
			label: '',
			componentKey: 'interestPayWay',
			arrData: 'interestPayWay',
			placeholder: '付息方式',
			width: 150,
			noAddAll: true,
		},
		{
			component: 'select',
			label: '',
			componentKey: 'status',
			arrData: 'status',
			placeholder: '审核状态',
			noAddAll: true,
			width: 150,
		},
	],
	createCou: [
		//开立融信页面
		{
			component: 'select',
			label: '',
			placeholder: '收款方',
			componentKey: 'toPubKey',
			arrData: 'toPubKey',
			width: 150,
			noAddAll: true,
		},
		{
			component: 'input',
			label: '融信编号',
			placeholder: '融信编号',
			componentKey: 'couNo',
			width: 220,
		},
		{
			component: 'select',
			label: '',
			placeholder: '支付状态',
			componentKey: 'transferStatus',
			arrData: 'status',
			noAddAll: true,
			width: 150,
		},
	],
	//融信开立 =》 modal 中 选择融信
	selectCouModalT: [
		{
			component: 'select',
			label: '开立方',
			componentKey: 'seter',
			arrData: 'seter',
			selectTitle: '授信方',
			width: 150,
		},
		{
			component: 'rangeDate',
			label: '兑付到期日',
			componentKey: 'dueDate',
		},
	],
	//创建融资申请 =》 modal 中 选择融信
	selectCouModalF: [
		{
			component: 'select',
			label: '',
			componentKey: 'publishPubKey',
			arrData: 'publishPubKey',
			// width: 120,
			width: 220,
			placeholder: '开立方',
			noAddAll: true,
		},
		// {
		// 	component: 'select',
		// 	label: '',
		// 	componentKey: 'creditPubKey',
		// 	arrData: 'creditPubKey',
		// 	// width: 120,
		// 	width: 220,
		// 	placeholder: '授信方',
		// 	noAddAll: true,
		// },
	],
	//创建核销申请 =》 drawer 中 选择融信
	selectCouDrawerD: [
		{
			component: 'input',
			label: '融信编号',
			placeholder: '融信编号',
			componentKey: 'couNo',
			width: 200,
		},
	],
	//创建融资申请 =》 modal 中 选择发票
	selectInvoiceModalF: [
		{
			label: '',
			component: 'rangeDate',
			placeholder: ['开票日期(开始)', '开票日期(结束)'],
			componentKey: 'invoiceDate',
			width: 300,
		},
		{
			component: 'input',
			label: '',
			componentKey: 'invoiceNumber',
			placeholder: '发票号码',
			width: 120,
		},
		{
			component: 'select',
			label: '',
			componentKey: 'checkFlag',
			arrData: 'checkFlag',
			placeholder: '验真状态',
			noAddAll: true,
			width: 100,
		},
	],
	//我的融信
	myCou: [
		{
			component: 'input',
			label: '',
			componentKey: 'couNo',
			placeholder: '融信编号',
			width: 220,
		},
		{
			component: 'select',
			label: '',
			placeholder: '开立方',
			componentKey: 'publishPubKey',
			arrData: 'publishPubKey',
			noAddAll: true,
			width: 220,
		},
		{
			component: 'rangeDate',
			label: '',
			placeholder: ['兑付到期日(开始)', '兑付到期日(结束)'],
			componentKey: 'due',
			width: 300,
		},
	],
	myCouCash: [
		{
			//质押状态
			component: 'select',
			label: '',
			placeholder: '质押状态',
			componentKey: 'pledgeStatus',
			arrData: 'pledgeStatus',
			noAddAll: true,
			width: 150,
		},
		{
			//兑付状态
			component: 'select',
			label: '',
			placeholder: '兑付状态',
			componentKey: 'cashStatusList',
			arrData: 'cashStatusList',
			noAddAll: true,
			width: 150,
		},
		{
			component: 'input',
			label: '',
			componentKey: 'couNo',
			placeholder: '融信编号',
			width: 220,
		},
		{
			component: 'select',
			label: '',
			placeholder: '开立方',
			componentKey: 'publishPubKey',
			arrData: 'publishPubKey',
			noAddAll: true,
			width: 220,
		},
		{
			component: 'rangeDate',
			label: '',
			placeholder: ['兑付到期日(开始)', '兑付到期日(结束)'],
			componentKey: 'due',
			width: 300,
		},
	],
	paySelectAvibleCou: [
		{
			component: 'select',
			label: '',
			placeholder: '开立方',
			componentKey: 'publishPubKey',
			arrData: 'publishPubKey',
			noAddAll: true,
			width: 220,
		},
		{
			component: 'select',
			label: '',
			placeholder: '授信方',
			componentKey: 'creditPubKey',
			arrData: 'creditPubKey',
			noAddAll: true,
			width: 220,
		},
		{
			component: 'rangeDate',
			label: '',
			placeholder: ['兑付到期日(开始)', '兑付到期日(结束)'],
			componentKey: 'due',
			width: 300,
		},
	],

	//流转管理(供应商)
	transferManageToS: [
		{
			component: 'select',
			label: '',
			placeholder: '支付类型',
			componentKey: 'accrualPrinciple',
			arrData: 'accrualPrinciple',
			noAddAll: true,
		},
		{
			component: 'input',
			label: '',
			placeholder: '收款方',
			componentKey: 'toCompanyName',
		},
		{
			component: 'select',
			label: '',
			placeholder: '状态',
			componentKey: 'status',
			arrData: 'status',
			noAddAll: true,
		},
		{
			component: 'input',
			label: '支付编号',
			placeholder: '支付编号',
			componentKey: 'transferNo',
			maxLength: 32,
		},
	],
	//流转管理(核心企业)
	transferManageToC: [
		{
			component: 'input',
			label: '',
			placeholder: '收款方',
			componentKey: 'toCompanyName',
		},
		{
			component: 'select',
			label: '',
			placeholder: '状态',
			componentKey: 'status',
			arrData: 'status',
			noAddAll: true,
		},
		{
			component: 'input',
			label: '支付编号',
			placeholder: '支付编号',
			componentKey: 'transferNo',
			maxLength: 32,
		},
	],

	//授信信息 pfi
	creditList: [
		{
			component: 'inputGroup',
			label: '授信企业',
			componentKey: 'companyType',
			arrData: 'companyTypes',
			noAddAll: true,
			width: 200,
		},
		{
			component: 'select',
			label: '',
			componentKey: 'interestPayWay',
			arrData: 'interestPayWay',
			placeholder: '付息方式',
			noAddAll: true,
			width: 150,
		},
		{
			component: 'select',
			label: '',
			componentKey: 'status',
			arrData: 'status',
			placeholder: '审核状态',
			noAddAll: true,
			width: 150,
		},
	],
	// 退款管理
	refundList: [
		{
			//原支付编号
			component: 'input',
			label: '',
			placeholder: '原支付编号',
			componentKey: 'originPayNumber',
			width: 120,
		},
		{
			//退款申请编号
			component: 'input',
			label: '',
			placeholder: '退款申请编号',
			componentKey: 'refundApplicationNumber',
			width: 120,
		},
	],
	auditRefund: [
		{
			//原支付编号
			component: 'input',
			label: '',
			placeholder: '原支付编号',
			componentKey: 'originPayNumber',
			width: 120,
		},
		{
			//退款申请编号
			component: 'input',
			label: '',
			placeholder: '退款申请编号',
			componentKey: 'refundApplicationNumber',
			width: 120,
		},
	],
	selectPayModal: [
		{
			component: 'input',
			label: '',
			componentKey: 'transferNo',
			placeholder: '支付编号',
			width: 120,
		},
	],
	selectCouModalR: [
		{
			component: 'select',
			label: '',
			componentKey: 'publishPubKey',
			arrData: 'publishPubKey',
			width: 220,
			placeholder: '开立方',
			noAddAll: true,
		},
		// {
		// 	component: 'select',
		// 	label: '',
		// 	componentKey: 'creditPubKey',
		// 	arrData: 'creditPubKey',
		// 	width: 220,
		// 	placeholder: '授信方',
		// 	noAddAll: true,
		// },
	],
	// 应收账款转让通知
	tansferNotice: [
		{
			component: 'queryByName',
			label: ``,
			placeholder: '融资企业',
			componentKey: 'uuid',
			width: 220,
			dropdownMatchSelectWidth: 320,
			companyType: 'S',
			valuekey: 'uuid',
			Api: commonModuleApi.searchCompanyByName,
		},
		{
			component: 'input',
			label: '',
			placeholder: '融资申请编号',
			componentKey: 'financeNumber',
			width: 220,
		},
	],
	ratesSetting: [
		{
			component: 'queryByName',
			label: ``,
			placeholder: '供应商',
			componentKey: 'companyUuid',
			width: 150,
			dropdownMatchSelectWidth: 320,
			companyType: 'S',
			valuekey: 'uuid',
			Api: commonModuleApi.searchCompanyByName,
		},
	],
	// 融信核销
	destroyList: [
		{
			component: 'input',
			label: '',
			placeholder: '核销申请编号',
			componentKey: 'destroyNo',
			width: 120,
		},
		{
			component: 'input',
			label: '',
			placeholder: '融信编号',
			componentKey: 'couNo',
			width: 120,
		},
		{
			component: 'select',
			label: '',
			placeholder: '状态',
			componentKey: 'destroyStatus',
			arrData: 'destroyStatus',
			noAddAll: true,
			width: 120,
		},
	],
	waitRegisterList: [
		{
			component: 'input',
			label: '',
			placeholder: '融资申请编号',
			componentKey: 'applicationNumber',
			width: 220,
		},
	],
	/**
	 * 中登网登记管理
	 * 登记列表
	 */
	registerList: [
		{
			component: 'input',
			label: '',
			placeholder: '登记证明编号',
			componentKey: 'registerNumber',
			width: 120,
		},
		{
			component: 'select',
			label: '',
			placeholder: '交易业务类型',
			componentKey: 'businessType',
			arrData: 'businessType',
			noAddAll: true,
			width: 160,
		},
		{
			component: 'rangeDate',
			label: '',
			placeholder: ['登记时间(开始)', '登记时间(结束)'],
			componentKey: 'createTime',
			width: 300,
		},
		{
			component: 'select',
			label: '',
			placeholder: '登记状态',
			componentKey: 'registerStatus',
			arrData: 'registerStatus',
			noAddAll: true,
			width: 120,
		},
		{
			component: 'input',
			label: '',
			placeholder: '关联业务编号',
			componentKey: 'requestNo',
			width: 120,
		},
	],

	//发票监控
	invoiceWarn: invoiceWarnCommonControl,
	invoiceWarnWithFiSearch: [
		// @ts-ignore
		...invoiceWarnCommonControl,
		{
			//金融机构（模糊搜索出现下拉框）
			component: 'queryByName',
			label: '',
			placeholder: '金融机构',
			componentKey: 'fiUuid',
			noAddAll: true,
			width: 150,
			dropdownMatchSelectWidth: 320,
			companyType: 'FI',
			valuekey: 'uuid',
			Api: commonModuleApi.searchCompanyByName,
		},
	],
}

export default SearchBarObject
