/*
 * @Descripttion:
 * @Version: 1.0
 * @Author: chenyan
 * @Date: 2023-03-31 18:28:17
 * @LastEditors: chenyan
 * @LastEditTime: 2023-04-03 16:24:52
 */
import { couAssetsPermsMap, couAssetsRouteConfig } from './perms-cou-assets'
import { couDestroyPermsMap, couDestroyRouteConfig } from './perms-cou-destroy'
import { couFinancePermsMap, couFinanceBtnPermsConfig, couFinanceRouteConfig } from './perms-cou-finance'
import { couManagePermsMap, couManageBtnPermsConfig, couManageRouteConfig } from './perms-cou-manage'
import { couTransferPermsMap, couTransferRouteConfig } from './perms-cou-transfer'
import { refundManagePermsMap, refundManageRouteConfig } from './perms-refund-manage'
import { companyCreditManagePermsMap, companyCreditManageRouteConfig } from './perms-company-credit-manage'
import { companyInfoManagePermsMap, companyInfoManageRouteConfig } from './perms-company-info-manage'
import { contractInvoiceManagePermsMap, contractInvoiceManageRouteConfig } from './perms-contract-invoice-manage'
import { myCouCreatePermsMap, myCouCreateRouteConfig } from './perms-my-cou-create'
import { userAuthPermsMap, userAuthRouteConfig } from './perms-user-auth'
import { registerManagePermsMap, registerManageRouteConfig } from './perms-register-manage'
import { invoicingManagePermsMap, invoicingManageRouteConfig } from './perms-invoicing-manage'
import { afterLoanManagePermsMap, afterLoanManageRouteConfig } from './perms-after-loan-manage'
import { financeCheckManagePermsMap, financeCheckManageRouteConfig } from './perms-finance-check-manage'
import { dashboardPermsConfigMap, dashboardRouteConfig } from './perms-dashboard'

export const permsMap = {
	...dashboardPermsConfigMap, //概览
	// ...couAssetsPermsMap, // 江阴银行隐藏
	// ...couDestroyPermsMap, // 江阴银行隐藏
	...couFinancePermsMap,
	...couManagePermsMap,
	...couTransferPermsMap,
	...refundManagePermsMap, // 江阴银行隐藏
	// ...companyCreditManagePermsMap, // 江阴银行隐藏
	...companyInfoManagePermsMap,
	...contractInvoiceManagePermsMap,
	...myCouCreatePermsMap,
	...userAuthPermsMap,
	// ...registerManagePermsMap, // 江阴银行隐藏
	...couFinanceBtnPermsConfig,
	...couManageBtnPermsConfig,
	// ...invoicingManagePermsMap, // 江阴银行隐藏
	// ...afterLoanManagePermsMap, // 江阴银行隐藏
	// ...financeCheckManagePermsMap, // 江阴银行隐藏
}

export const allModulesRouteConfig = [
	dashboardRouteConfig,
	// couAssetsRouteConfig,
	// couDestroyRouteConfig,
	couFinanceRouteConfig,
	couManageRouteConfig,
	couTransferRouteConfig,
	refundManageRouteConfig,
	// companyCreditManageRouteConfig,
	companyInfoManageRouteConfig,
	contractInvoiceManageRouteConfig,
	myCouCreateRouteConfig,
	userAuthRouteConfig,
	// registerManageRouteConfig,
	// invoicingManageRouteConfig,
	// afterLoanManageRouteConfig,
	// financeCheckManageRouteConfig,
]

permsInit()

function permsInit() {}
