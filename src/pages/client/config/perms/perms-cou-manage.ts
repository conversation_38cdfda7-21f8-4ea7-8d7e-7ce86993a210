import React from 'react'
import { AuditOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

const MyCou = React.lazy(() => import(/* webpackChunkName: "couManage" */ '@factorContent/couManage/myCou'))
const SLimitOperatorPay = React.lazy(() => import(/* webpackChunkName: "couManage" */ '@factorContent/couManage/couPay/sBuildOperatorCouPay'))
const SOperatorPay = React.lazy(() => import(/* webpackChunkName: "couManage" */ '@factorContent/couManage/couPay/sOperatorCouPay'))
const STeamOperatorPay = React.lazy(() => import(/* webpackChunkName: "couManage" */ '@factorContent/couManage/couPay/sTeamOperatorCouPay'))

const Create = React.lazy(() => import(/* webpackChunkName: "couManage" */ '@factorContent/couManage/couCreate/create'))
const CBuildOperatorRongXinCreate = React.lazy(
	() => import(/* webpackChunkName: "couManage" */ '@factorContent/couManage/couCreate/cBuildOperatorRongXinCreate')
)
const COperatorRongXinCreate = React.lazy(() => import(/* webpackChunkName: "couManage" */ '@factorContent/couManage/couCreate/cOperatorRongXinCreate'))
const CTeamOperatorRongXinCreate = React.lazy(() => import(/* webpackChunkName: "couManage" */ '@factorContent/couManage/couCreate/cTeamOperatorRongxinCreate'))
const CooperateProtocols = React.lazy(() => import(/* webpackChunkName: "CooperateProtocols" */ '@factorContent/couManage/protocols'))
const SignCooperateProtocols = React.lazy(() => import(/* webpackChunkName: "SignCooperateProtocols" */ '@factorContent/couManage/protocols/sign'))

// 运营机构管理
// 资源码映射表
// 命名规范 appname_modulename:menu:function
let couManagePermsConfig: Record<string, PermItem> = {
	'bl_couManage:dire': {
		name: '融信管理',
		//组件名称
		needValid: true,
		icon: 'AuditOutlined',
	},
	'bl_couManage:myCou:list': {
		path: '/content/couManage/myCou',
		name: '我的融信',
		//组件名称
		needValid: true,
		component: MyCou,
	},
	//操作员支付
	'bl_couManage:sOperatorPay:list': {
		path: '/content/couManage/sOperatorPay',
		name: '融信支付',
		//组件名称
		needValid: true,
		component: SOperatorPay,
	},
	//S项目操作员支付
	// 'bl_couManage:sTeamOperatorPay:list': {
	// 	path: '/content/couManage/sTeamOperatorPay',
	// 	name: '融信支付',
	// 	//组件名称
	// 	needValid: true,
	// 	component: STeamOperatorPay,
	// },
	// //限额操作员
	// 'bl_couManage:sLimitOperatorPay:list': {
	// 	path: '/content/couManage/sLimitOperatorPay',
	// 	name: '融信支付',
	// 	//组件名称
	// 	needValid: true,
	// 	component: SLimitOperatorPay,
	// },
	'bl_couManage:couCreate:create': {
		path: '/content/couManage/create',
		name: '开立',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: Create,
	},
	// 'bl_couManage:cBuildOperatorRongXinCreate:list': {
	// 	path: '/content/couManage/cBuildOperatorRongXinCreate',
	// 	name: '融信开立',
	// 	//组件名称
	// 	needValid: true,
	// 	component: CBuildOperatorRongXinCreate,
	// },
	'bl_couManage:COperatorRongXinCreate:list': {
		path: '/content/couManage/COperatorRongXinCreate',
		name: '融信开立',
		//组件名称
		needValid: true,
		component: COperatorRongXinCreate,
	},
	// 'bl_couManage:CTeamOperatorRongXinCreate:list': {
	// 	path: '/content/couManage/CTeamOperatorRongXinCreate',
	// 	name: '融信开立',
	// 	//组件名称
	// 	needValid: true,
	// 	component: CTeamOperatorRongXinCreate,
	// },
	'bl_couManage:protocolList': {
		path: '/content/couManage/protocol',
		name: '合作协议',
		//组件名称
		needValid: true,
		component: CooperateProtocols,
	},
	'bl_couManage:protocolSign': {
		path: '/content/couManage/protocolSign',
		name: '签署合作协议',
		showOnMenu: false,
		needValid: true,
		component: SignCooperateProtocols,
	},
}

//添加 permsKey
for (let k in couManagePermsConfig) {
	couManagePermsConfig[k]['permsKey'] = k
}

export let couManageBtnPermsConfig: Record<string, PermItem> = {
	'bl_couManage:myCou:SBuildOperatorStatusCard': {
		desc: '供应商限额操作员卡片权限',
	},
	'bl_couManage:myCou:SOperatorStatusCard': {
		desc: '供应商操作员卡片权限',
	},
	'bl_couManage:myCou:STeamOperatorStatusCard': {
		desc: '供应商项目操作员卡片权限',
	},
}

// 路由配置，包含菜单
export const couManageRouteConfig: PermItem = {
	...couManagePermsConfig['bl_couManage:dire'],
	children: getChildrenRouteConfig(couManagePermsConfig),
}

export const couManagePermsMap = couManagePermsConfig
