import React from 'react'
import { RotateLeftOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

const AuditRefund = React.lazy(() => import(/* webpackChunkName: "refundManage" */ '@src/pages/client/views/content/refundManage/auditRefund/components'))
const CreateRefund = React.lazy(() => import(/* webpackChunkName: "refundManage" */ '@factorContent/refundManage/createRefund'))
const RefundDetail = React.lazy(() => import(/* webpackChunkName: "refundManage" */ '@factorContent/refundManage/refundDetail'))
const SOperatorRefundList = React.lazy(
	() => import(/* webpackChunkName: "refundManage" */ '@src/pages/client/views/content/refundManage/refundList/sOperatorList')
)

// 运营机构管理
// 资源码映射表
// 命名规范 appname_modulename:menu:function
let refundManagePermsConfig: Record<string, PermItem> = {
	'bl_refundManage:dire': {
		name: '退款管理',
		//组件名称
		needValid: true,
		icon: 'RotateLeftOutlined',
	},
	'bl_refundManage:auditRefund:list': {
		path: '/content/refundManage/auditRefund',
		name: '待审核',
		//组件名称
		needValid: true,
		component: AuditRefund,
	},
	//退款列表
	'bl_refundManage:sOperatorRefundList:list': {
		path: '/content/refundManage/sOperatorRefundList',
		name: '退款列表',
		//组件名称
		needValid: true,
		component: SOperatorRefundList,
	},
	'bl_refundManage:createRefund:form': {
		path: '/content/refundManage/createRefund',
		name: '创建退款申请',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: CreateRefund,
	},
	'bl_refundManage:refundDetail:detail': {
		path: '/content/refundManage/refundDetail',
		name: '退款详情',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: RefundDetail,
	},
}

//添加 permsKey
for (let k in refundManagePermsConfig) {
	refundManagePermsConfig[k]['permsKey'] = k
}

// 路由配置，包含菜单
export const refundManageRouteConfig: PermItem = {
	...refundManagePermsConfig['bl_refundManage:dire'],
	children: getChildrenRouteConfig(refundManagePermsConfig),
}

export const refundManagePermsMap = refundManagePermsConfig
