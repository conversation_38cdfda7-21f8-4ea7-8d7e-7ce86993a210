import React from 'react'
import { getChildrenRouteConfig } from '@src/utils/util'

const DashboardPage = React.lazy(() => import(/* webpackChunkName: "DashboardPage" */ '@factorContent/dashboard'))

// 资源码映射表
// 命名规范 appname_modulename:menu:function
let dashboardPermsConfig: Record<string, PermItem> = {
	'bl_dashboard:dire': {
		name: '概览',
		path: '/content/dashboard',
		needValid: true,
		icon: 'MoneyCollectOutlined',
		component: DashboardPage,
	},
}

//添加 permsKey
for (let k in dashboardPermsConfig) {
	dashboardPermsConfig[k]['permsKey'] = k
}

// 路由配置，包含菜单
export const dashboardRouteConfig: PermItem = {
	...dashboardPermsConfig['bl_dashboard:dire'],
	//children: getChildrenRouteConfig(dashboardPermsConfig),
}

export const dashboardPermsConfigMap = dashboardPermsConfig
