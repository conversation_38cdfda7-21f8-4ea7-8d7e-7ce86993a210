import React from 'react'
import { UserOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

/**
 * 中登查重管理
 */
const FiOperatorCheckList = React.lazy(() => import('@src/pages/client/views/content/financeCheckManage/fiOperatorCheckList'))
const PFiOperatorCheckList = React.lazy(() => import('@src/pages/client/views/content/financeCheckManage/pfiOperatorCheckList'))
const FinanceCheckDetail = React.lazy(() => import(/* webpackChunkName: "couFinance" */ '@factorContent/financeCheckManage/financeCheckDetail'))

// 资源码映射表
// 命名规范 appname_modulename:menu:function
let financeCheckManagePermsConfig: Record<string, PermItem> = {
	'bl_financeCheckManage:dire': {
		name: '中登查重管理',
		//组件名称
		needValid: true,
		icon: 'UserOutlined',
	},
	'bl_financeCheckManage:fiOperatorCheckList:list': {
		path: '/content/financeCheckManage/fiOperatorCheckList',
		name: '融资查重记录',
		//组件名称
		needValid: true,
		component: FiOperatorCheckList,
	},
	'bl_financeCheckManage:pfiOperatorCheckList:list': {
		path: '/content/financeCheckManage/pfiOperatorCheckList',
		name: '融资查重记录',
		//组件名称
		needValid: true,
		component: PFiOperatorCheckList,
	},
	'bl_financeCheckManage:fiFinanceCheckDetail:detail': {
		path: '/content/financeCheckManage/fiFinanceCheckDetail',
		name: '查看任务详情',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: FinanceCheckDetail,
	},
	'bl_financeCheckManage:pfiFinanceCheckDetail:detail': {
		path: '/content/financeCheckManage/pfiFinanceCheckDetail',
		name: '查看任务详情',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: FinanceCheckDetail,
	},
}
//添加 permsKey
for (let k in financeCheckManagePermsConfig) {
	financeCheckManagePermsConfig[k]['permsKey'] = k
}

// 路由配置，包含菜单
export const financeCheckManageRouteConfig: PermItem = {
	...financeCheckManagePermsConfig['bl_financeCheckManage:dire'],
	children: getChildrenRouteConfig(financeCheckManagePermsConfig),
}

export const financeCheckManagePermsMap = financeCheckManagePermsConfig
