import React from 'react'
import { UserOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

const InvoiceHeader = React.lazy(() => import(/* webpackChunkName: "userAuth" */ '@factorContent/invoicingManage/invoiceHeader'))
const MyInvoiceData = React.lazy(() => import(/* webpackChunkName: "userAuth" */ '@factorContent/invoicingManage/myInvoiceData'))

// 运营机构管理
// 资源码映射表
// 命名规范 appname_modulename:menu:function
let invoicingManagePermsConfig: Record<string, PermItem> = {
	'bl_invoicingManage:dire': {
		name: '开票管理',
		//组件名称
		needValid: true,
		icon: 'UserOutlined',
	},
	'bl_invoicingManage:searchInvoiceHeader:list': {
		path: '/content/invoicingManage/invoiceHeader',
		name: '发票抬头管理',
		//组件名称
		needValid: true,
		component: InvoiceHeader,
	},
	'bl_invoicingManage:myInvoiceData:list': {
		path: '/content/invoicingManage/invoicingData',
		name: '我的发票数据',
		//组件名称
		needValid: true,
		component: MyInvoiceData,
	},
}
//添加 permsKey
for (let k in invoicingManagePermsConfig) {
	invoicingManagePermsConfig[k]['permsKey'] = k
}

// 路由配置，包含菜单
export const invoicingManageRouteConfig: PermItem = {
	...invoicingManagePermsConfig['bl_invoicingManage:dire'],
	children: getChildrenRouteConfig(invoicingManagePermsConfig),
}

export const invoicingManagePermsMap = invoicingManagePermsConfig
