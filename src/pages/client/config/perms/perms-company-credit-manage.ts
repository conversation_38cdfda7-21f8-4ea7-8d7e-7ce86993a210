import React from 'react'
import { CreditCardOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

const Credit = React.lazy(() => import(/* webpackChunkName: "companyCreditManage" */ '@factorContent/companyCreditManage/financeCreditList'))
const CreditList = React.lazy(() => import(/* webpackChunkName: "companyCreditManage" */ '@factorContent/companyCreditManage/creditList'))
const RatesSetting = React.lazy(() => import(/* webpackChunkName: "companyCreditManage" */ '@factorContent/companyCreditManage/ratesSetting'))

// 命名规范 appname_modulename:menu:function
let companyCreditManagePermsConfig: Record<string, PermItem> = {
	'bl_companyCreditManage:dire': {
		name: '授信管理',
		//组件名称
		needValid: true,
		icon: 'CreditCardOutlined',
	},
	'bl_companyCreditManage:creditList:list': {
		path: '/content/companyCreditManage/creditList',
		name: '授信列表',
		//组件名称
		needValid: true,
		component: CreditList,
	},
	'bl_companyCreditManage:financeCreditList:list': {
		path: '/content/companyAuth/financeCreditList',
		name: '授信设置',
		//组件名称
		needValid: true,
		component: Credit,
	},
	'bl_companyCreditManage:ratesSetting:list': {
		path: '/content/companyCreditManage/ratesSetting',
		name: '特殊利率配置',
		//组件名称
		needValid: true,
		component: RatesSetting,
	},
	//特殊利率配置
	///content/companyAuth/financeCreditList
}
//添加 permsKey
for (let k in companyCreditManagePermsConfig) {
	companyCreditManagePermsConfig[k]['permsKey'] = k
}

// 路由配置，包含菜单
export const companyCreditManageRouteConfig: PermItem = {
	...companyCreditManagePermsConfig['bl_companyCreditManage:dire'],
	children: getChildrenRouteConfig(companyCreditManagePermsConfig),
}

export const companyCreditManagePermsMap = companyCreditManagePermsConfig

//companyCreditManage
