import React from 'react'
import { FileDoneOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

const COperatorCash = React.lazy(() => import(/* webpackChunkName: "couTransfer" */ '@factorContent/mycreated/cOperatorCash'))
const CLimitOperatorCash = React.lazy(() => import(/* webpackChunkName: "couTransfer" */ '@factorContent/mycreated/cLimitOperatorCash'))
const CTeamOperatorCash = React.lazy(() => import(/* webpackChunkName: "couTransfer" */ '@factorContent/mycreated/cTeamOperatorCash'))
const COperatorHolderDetail = React.lazy(() => import(/* webpackChunkName: "myCouCreate" */ '@factorContent/mycreated/holderDetail/cOperatorHolderDetail'))
const CLimitOperatorHolderDetail = React.lazy(
	() => import(/* webpackChunkName: "myCouCreate" */ '@factorContent/mycreated/holderDetail/cLimitOperatorHolderDetail')
)
const CTeamOperatorHolderDetail = React.lazy(
	() => import(/* webpackChunkName: "myCouCreate" */ '@factorContent/mycreated/holderDetail/cTeamOperatorHolderDetail')
)

// 命名规范 appname_modulename:menu:function
let myCouCreatePermsConfig: Record<string, PermItem> = {
	'bl_myCouCreate:dire': {
		name: '我的开立',
		//组件名称
		needValid: true,
		icon: 'FileDoneOutlined',
	},
	'bl_myCouCreate:cOperatorholderDetail:list': {
		path: '/content/couManage/holderDetail',
		name: '持有人明细',
		//组件名称
		needValid: true,
		component: COperatorHolderDetail,
	},
	// 'bl_myCouCreate:cLimitOperatorHolderDetail:list': {
	// 	path: '/content/couManage/holderDetail',
	// 	name: '持有人明细',
	// 	//组件名称
	// 	needValid: true,
	// 	component: CLimitOperatorHolderDetail,
	// },
	// 'bl_myCouCreate:cTeamOperatorholderDetail:list': {
	// 	path: '/content/couManage/holderDetail',
	// 	name: '持有人明细',
	// 	//组件名称
	// 	needValid: true,
	// 	component: CTeamOperatorHolderDetail,
	// },
	'bl_myCouCreate:cOperatorCouCash:list': {
		path: '/content/couTransfer/transferCash',
		name: '融信兑付',
		//组件名称
		needValid: true,
		component: COperatorCash,
	},
	// 'bl_myCouCreate:cLimitOperatorCouCash:list': {
	// 	path: '/content/couTransfer/transferCash',
	// 	name: '融信兑付',
	// 	//组件名称
	// 	needValid: true,
	// 	component: CLimitOperatorCash,
	// },
	// 'bl_myCouCreate:cTeamOperatorCouCash:list': {
	// 	path: '/content/couTransfer/transferCash',
	// 	name: '融信兑付',
	// 	//组件名称
	// 	needValid: true,
	// 	component: CTeamOperatorCash,
	// },
}
//添加 permsKey
for (let k in myCouCreatePermsConfig) {
	myCouCreatePermsConfig[k]['permsKey'] = k
}

// 路由配置，包含菜单
export const myCouCreateRouteConfig: PermItem = {
	...myCouCreatePermsConfig['bl_myCouCreate:dire'],
	children: getChildrenRouteConfig(myCouCreatePermsConfig),
}

export const myCouCreatePermsMap = myCouCreatePermsConfig
