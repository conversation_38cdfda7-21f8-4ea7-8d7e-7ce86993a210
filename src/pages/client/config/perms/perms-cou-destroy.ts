import React from 'react'
import { FileExcelOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

const AuditDestroy = React.lazy(() => import(/* webpackChunkName: "couDestroy" */ '@factorContent/couDestroy/auditDestroy'))
const DestroyList = React.lazy(() => import(/* webpackChunkName: "couDestroy" */ '@factorContent/couDestroy/destroyList'))
const CreateDestroy = React.lazy(() => import(/* webpackChunkName: "couDestroy" */ '@factorContent/couDestroy/createDestroy'))
const DestroyDetail = React.lazy(() => import(/* webpackChunkName: "couDestroy" */ '@factorContent/couDestroy/destroyDetail'))

// 运营机构管理
// 资源码映射表
// 命名规范 appname_modulename:menu:function
let couDestroyPermsConfig: Record<string, PermItem> = {
	'bl_couDestroy:dire': {
		name: '融信核销',
		//组件名称
		needValid: true,
		icon: 'FileExcelOutlined',
	},
	//待审核
	'bl_couDestroy:auditDestroy:list': {
		path: '/content/couDestroy/soAudit',
		name: '待审核',
		//组件名称
		needValid: true,
		component: AuditDestroy,
	},
	'bl_couDestroy:destroyList:list': {
		path: '/content/couDestroy/destroyList',
		name: '核销列表',
		//组件名称
		needValid: true,
		component: DestroyList,
	},
	'bl_couDestroy:createDestroy:create': {
		path: '/content/couDestroy/createDestroy',
		name: '创建核销',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: CreateDestroy,
	},
	'bl_couDestroy:detail:detail': {
		path: '/content/couDestroy/detail',
		name: '核销详情',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: DestroyDetail,
	},
}

//添加 permsKey
for (let k in couDestroyPermsConfig) {
	couDestroyPermsConfig[k]['permsKey'] = k
}

// 路由配置，包含菜单
export const couDestroyRouteConfig: PermItem = {
	...couDestroyPermsConfig['bl_couDestroy:dire'],
	children: getChildrenRouteConfig(couDestroyPermsConfig),
}

export const couDestroyPermsMap = couDestroyPermsConfig
