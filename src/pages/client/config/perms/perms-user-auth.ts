import React from 'react'
import { UserOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

const SearchUser = React.lazy(() => import(/* webpackChunkName: "userAuth" */ '@factorContent/userAuth/searchUser'))
const OpenQuota = React.lazy(() => import(/* webpackChunkName: "userAuth" */ '@factorContent/userAuth/createQuota'))
const PayQuota = React.lazy(() => import(/* webpackChunkName: "userAuth" */ '@factorContent/userAuth/payQuota'))
const TeamManage = React.lazy(() => import(/* webpackChunkName: "userAuth" */ '@factorContent/userAuth/teamManage'))
const TeamManageDetails = React.lazy(() => import(/* webpackChunkName: "userAuth" */ '@factorContent/userAuth/teamDetails'))

// 运营机构管理
// 资源码映射表
// 命名规范 appname_modulename:menu:function
let userAuthPermsConfig: Record<string, PermItem> = {
	'bl_userAuth:dire': {
		name: '用户和权限管理',
		//组件名称
		needValid: true,
		icon: 'UserOutlined',
	},
	'bl_userAuth:searchUser:list': {
		path: '/content/userAuth/searchUser',
		name: '用户管理',
		//组件名称
		needValid: true,
		component: SearchUser,
	},
	// 江阴银行隐藏
	// 'bl_userAuth:openQuota:list': {
	// 	path: '/content/userAuth/openQuota',
	// 	name: '开立额度',
	// 	//组件名称
	// 	needValid: true,
	// 	component: OpenQuota,
	// },
	// 'bl_userAuth:payQuota:list': {
	// 	path: '/content/userAuth/payQuota',
	// 	name: '支付/融资额度',
	// 	//组件名称
	// 	needValid: true,
	// 	component: PayQuota,
	// },
	// 江阴银行隐藏
	// 'bl_userAuth:teamManage:list': {
	// 	path: '/content/userAuth/teamManage',
	// 	name: '项目组管理',
	// 	//组件名称
	// 	needValid: true,
	// 	component: TeamManage,
	// },
	'bl_userAuth:teamManage:details': {
		path: '/content/userAuth/teamManageDetails',
		name: '项目组详情',
		//组件名称
		needValid: true,
		component: TeamManageDetails,
		showOnMenu: false,
	},
}
//添加 permsKey
for (let k in userAuthPermsConfig) {
	userAuthPermsConfig[k]['permsKey'] = k
}

// 路由配置，包含菜单
export const userAuthRouteConfig: PermItem = {
	...userAuthPermsConfig['bl_userAuth:dire'],
	children: getChildrenRouteConfig(userAuthPermsConfig),
}

export const userAuthPermsMap = userAuthPermsConfig
