import React from 'react'

const Home = React.lazy(() => import(/* webpackChunkName: "home" */ '@factor/views/home'))
const Base = React.lazy(() => import(/* webpackChunkName: "home" */ '@factor/views/home/<USER>/base'))
const Guidepage = React.lazy(() => import(/* webpackChunkName: "home" */ '@src/pages/client/views/home/<USER>'))
const Forget = React.lazy(() => import(/* webpackChunkName: "home" */ '@factor/views/home/<USER>/forget'))
const ActivateAccount = React.lazy(() => import(/* webpackChunkName: "home" */ '@factor/views/home/<USER>/activateAccount'))
const Register = React.lazy(() => import(/* webpackChunkName: "home" */ '@factor/views/home/<USER>/register'))
const Authexception = React.lazy(() => import(/* webpackChunkName: "home" */ '@factor/views/home/<USER>/index'))

// 不需要权限的路由配置
export const homeListRouteConfig: PermItem[] = [
	{
		isIndex: true,
		path: '/home',
		component: Home,
	},
	{ path: '/home/<USER>', component: Base },
	{ path: '/home/<USER>', component: Forget },
	{ path: '/home/<USER>', component: Register },
	{ path: '/home/<USER>', component: ActivateAccount },
	{ path: '/home/<USER>', component: Authexception },
	{ path: '/home/<USER>', component: Guidepage },
]
