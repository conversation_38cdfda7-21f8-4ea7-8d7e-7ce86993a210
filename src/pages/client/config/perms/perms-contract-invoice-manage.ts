import React from 'react'
import { FolderOpenOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

const Contract = React.lazy(() => import(/* webpackChunkName: "contractInvoiceManage" */ '@factorContent/couAssets/contract'))
const TeamOperatorContract = React.lazy(() => import(/* webpackChunkName: "contractInvoiceManage" */ '@factorContent/couAssets/teamOperatorContract'))
const LimitOperatorContract = React.lazy(() => import(/* webpackChunkName: "contractInvoiceManage" */ '@factorContent/couAssets/limitOperatorContract'))
const Invoice = React.lazy(() => import(/* webpackChunkName: "contractInvoiceManage" */ '@factorContent/couAssets/invoice'))
const TeamOperatorInvoice = React.lazy(() => import(/* webpackChunkName: "contractInvoiceManage" */ '@factorContent/couAssets/teamOperatorInvoice'))
const LimitOperatorInvoice = React.lazy(() => import(/* webpackChunkName: "contractInvoiceManage" */ '@factorContent/couAssets/limitOperatorInvoice'))
const UploadInvoice = React.lazy(() => import(/* webpackChunkName: "contractInvoiceManage" */ '@factorContent/couAssets/uploadInvoice/uploadInvoice'))

// 运营机构管理
// 资源码映射表
// 命名规范 appname_modulename:menu:function
let contractInvoiceManagePermsConfig: Record<string, PermItem> = {
	'bl_contractInvoiceManage:dire': {
		name: '合同和发票管理',
		//组件名称
		needValid: true,
		icon: 'FolderOpenOutlined',
	},
	/*
	'bl_contractInvoiceManage:teamOperatorInvoice:list': {
		path: '/content/couAssets/teamOperatorInvoice',
		name: '发票管理',
		//组件名称
		needValid: true,
		component: TeamOperatorInvoice,
	},
	*/
	'bl_contractInvoiceManage:invoice:list': {
		path: '/content/couAssets/invoice',
		name: '发票管理',
		//组件名称
		needValid: true,
		component: Invoice,
	},
	/*
	'bl_contractInvoiceManage:limitOperatorInvoice:list': {
		path: '/content/couAssets/limitOperatorInvoice',
		name: '发票管理',
		//组件名称
		needValid: true,
		component: LimitOperatorInvoice,
	},
	*/
	//上传发票
	'bl_contractInvoiceManage:uploadInvoice:upload': {
		path: '/content/couAssets/uploadInvoice',
		name: '上传发票',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: UploadInvoice,
	},
	'bl_contractInvoiceManage:contract:list': {
		path: '/content/couAssets/contract',
		name: '合同管理',
		//组件名称
		needValid: true,
		component: Contract,
	},
	/*
	'bl_contractInvoiceManage:teamOperatorContract:list': {
		path: '/content/couAssets/teamOperatorContract',
		name: '合同管理',
		//组件名称
		needValid: true,
		component: TeamOperatorContract,
	},
	//限额操作员 页面 合同管理
	'bl_contractInvoiceManage:limitOperatorContract:list': {
		path: '/content/couAssets/limitOperatorContract',
		name: '合同管理',
		//组件名称
		needValid: true,
		component: LimitOperatorContract,
	},
	*/
}
//添加 permsKey
for (let k in contractInvoiceManagePermsConfig) {
	contractInvoiceManagePermsConfig[k]['permsKey'] = k
}

// 路由配置，包含菜单
export const contractInvoiceManageRouteConfig: PermItem = {
	...contractInvoiceManagePermsConfig['bl_contractInvoiceManage:dire'],
	children: getChildrenRouteConfig(contractInvoiceManagePermsConfig),
}

export const contractInvoiceManagePermsMap = contractInvoiceManagePermsConfig
