import React from 'react'
import { InteractionOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

const PayDetails = React.lazy(() => import(/* webpackChunkName: "couTransfer" */ '@factorContent/transferManage/detail'))
const SList = React.lazy(() => import(/* webpackChunkName: "couTransfer" */ '@factorContent/transferManage/list/sList'))
const CLimitOperatorList = React.lazy(() => import(/* webpackChunkName: "couTransfer" */ '@factorContent/transferManage/list/cLimitOperatorList'))
const SLimitOperatorList = React.lazy(() => import(/* webpackChunkName: "couTransfer" */ '@factorContent/transferManage/list/sLimitOperatorList'))
const COtherList = React.lazy(() => import(/* webpackChunkName: "couTransfer" */ '@factorContent/transferManage/list/cOtherList'))
const CouAcceptList = React.lazy(() => import(/* webpackChunkName: "couTransfer" */ '@src/pages/client/views/content/transferManage/audit'))
const WaitAuditorList = React.lazy(() => import(/* webpackChunkName: "couTransfer" */ '@src/pages/client/views/content/transferManage/audit'))

// 运营机构管理
// 资源码映射表
// 命名规范 appname_modulename:menu:function
let couTransferPermsConfig: Record<string, PermItem> = {
	'bl_couTransfer:dire': {
		name: '流转管理',
		//组件名称
		needValid: true,
		icon: 'InteractionOutlined',
	},
	// 江阴银行隐藏
	// s 操作员角色
	// 'bl_couTransfer:transferAudit:couAcceptList': {
	// 	path: '/content/couTransfer/couAcceptList',
	// 	name: '融信接收',
	// 	//组件名称
	// 	needValid: true,
	// 	component: CouAcceptList,
	// },
	//c s 审核员角色
	'bl_couTransfer:transferAudit:waitAuditorList': {
		path: '/content/couTransfer/waitAuditorList',
		name: '待审核',
		//组件名称
		needValid: true,
		component: WaitAuditorList,
	},
	'bl_couTransfer:transferManage:sList': {
		path: '/content/couTransfer/sList',
		name: '流转列表',
		//组件名称
		needValid: true,
		component: SList,
	},
	// 'bl_couTransfer:transferManage:cLimitOperatorList': {
	// 	path: '/content/couTransfer/cLimitOperatorList',
	// 	name: '流转列表',
	// 	//组件名称
	// 	needValid: true,
	// 	component: CLimitOperatorList,
	// },
	// 'bl_couTransfer:transferManage:sLimitOperatorList': {
	// 	path: '/content/couTransfer/sLimitOperatorList',
	// 	name: '流转列表',
	// 	//组件名称
	// 	needValid: true,
	// 	component: SLimitOperatorList,
	// },
	// 'bl_couTransfer:transferManage:cOtherList': {
	// 	path: '/content/couTransfer/cOtherList',
	// 	name: '流转列表',
	// 	//组件名称
	// 	needValid: true,
	// 	component: COtherList,
	// },
	'bl_couTransfer:payDetails:info': {
		path: '/content/couTransfer/payDetails',
		name: '支付详情',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: PayDetails,
	},
}

//添加 permsKey
for (let k in couTransferPermsConfig) {
	couTransferPermsConfig[k]['permsKey'] = k
}

// 路由配置，包含菜单
export const couTransferRouteConfig: PermItem = {
	...couTransferPermsConfig['bl_couTransfer:dire'],
	children: getChildrenRouteConfig(couTransferPermsConfig),
}

export const couTransferPermsMap = couTransferPermsConfig
