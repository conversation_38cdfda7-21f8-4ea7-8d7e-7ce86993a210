import React from 'react'
import { AccountBookOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

const FinanceSearch = React.lazy(() => import(/* webpackChunkName: "couFinance" */ '@factorContent/couFinance/financeSearch'))
const FinanceReSubmit = React.lazy(() => import(/* webpackChunkName: "couFinance" */ '@factorContent/couFinance/financeReSubmit'))
const FinanceAgreement = React.lazy(() => import(/* webpackChunkName: "couFinance" */ '@factorContent/couFinance/financeAgreement'))
const SBuildOperatorCreateFinance = React.lazy(
	() => import(/* webpackChunkName: "couFinance" */ '@factorContent/couFinance/createFinance/sBuildOperatorCreateFinance')
)
const SOperatorCreateFinance = React.lazy(() => import(/* webpackChunkName: "couFinance" */ '@factorContent/couFinance/createFinance/sOperatorCreateFinance'))
const STeamOperatorCreateFinance = React.lazy(
	() => import(/* webpackChunkName: "couFinance" */ '@factorContent/couFinance/createFinance/sTeamOperatorCreateFinance')
)
const FiAuditFinance = React.lazy(() => import(/* webpackChunkName: "couFinance" */ '@factorContent/couFinance/auditFinance/fiAuditFinance'))
const SAuditFinance = React.lazy(() => import(/* webpackChunkName: "couFinance" */ '@factorContent/couFinance/auditFinance/sAuditFinance'))
const CFinanceDetail = React.lazy(() => import(/* webpackChunkName: "couFinance" */ '@factorContent/couFinance/financeDetail/cFinanceDetail'))
const SFinanceDetail = React.lazy(() => import(/* webpackChunkName: "couFinance" */ '@factorContent/couFinance/financeDetail/sFinanceDetail'))
const FiFinanceDetail = React.lazy(() => import(/* webpackChunkName: "couFinance" */ '@factorContent/couFinance/financeDetail/fiFinanceDetail'))
const FiOperatorNotice = React.lazy(
	() => import(/* webpackChunkName: "couFinance" */ '@src/pages/client/views/content/couFinance/transferNotice/fiOperatorNotice')
)
const OtherNotice = React.lazy(() => import(/* webpackChunkName: "couFinance" */ '@src/pages/client/views/content/couFinance/transferNotice/otherNotice'))

// 运营机构管理
// 资源码映射表
// 命名规范 appname_modulename:menu:function
let couFinancePermsConfig: Record<string, PermItem> = {
	'bl_couFinance:dire': {
		name: '融信质押融资',
		//组件名称
		needValid: true,
		icon: 'AccountBookOutlined',
	},
	'bl_couFinance:financeSearch:list': {
		path: '/content/couFinance/financeSearch',
		name: '融资列表',
		//组件名称
		needValid: true,
		component: FinanceSearch,
	},
	// 'bl_couFinance:financeAgreement:list': { //20243/30 需求评审暂时隐藏
	// 	path: '/content/couFinance/financeAgreement',
	// 	name: ' 待签署协议',
	// 	//组件名称
	// 	needValid: true,
	// 	component: FinanceAgreement,
	// },
	// 'bl_couFinance:financeReSubmit:list': {
	// 	path: '/content/couFinance/financeReSubmit',
	// 	name: '待重新提交',
	// 	//组件名称
	// 	needValid: true,
	// 	component: FinanceReSubmit,
	// },
	// 'bl_couFinance:fiOperatorNotice:list': {
	// 	path: '/content/financeManage/fiOperatorNotice',
	// 	name: '应收账款转质押通知',
	// 	//组件名称
	// 	needValid: true,
	// 	component: FiOperatorNotice,
	// },
	'bl_couFinance:otherNotice:list': {
		path: '/content/financeManage/otherNotice',
		name: '应收账款质押通知',
		//组件名称
		needValid: true,
		component: OtherNotice,
	},
	//普通操作员 创建融资申请
	'bl_couFinance:sOperatorCreateFinance:create': {
		path: '/content/couFinance/sOperatorCreateFinance',
		name: '创建融资申请',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: SOperatorCreateFinance,
	},
	//限制操作员 创建融资申请
	// 'bl_couFinance:sBuildOperatorCreateFinance:create': {
	// 	path: '/content/couFinance/sBuildOperatorCreateFinance',
	// 	name: '创建融资申请',
	// 	//组件名称
	// 	needValid: true,
	// 	showOnMenu: false,
	// 	component: SBuildOperatorCreateFinance,
	// },
	//项目操作员操作员 创建融资申请
	// 'bl_couFinance:sTeamOperatorCreateFinance:create': {
	// 	path: '/content/couFinance/sTeamOperatorCreateFinance',
	// 	name: '创建融资申请',
	// 	//组件名称
	// 	needValid: true,
	// 	showOnMenu: false,
	// 	component: STeamOperatorCreateFinance,
	// },
	// 'bl_couFinance:financeDetail:cDetails': {
	// 	path: '/content/couFinance/cFinanceDetail',
	// 	name: '融资申请详情',
	// 	//组件名称
	// 	needValid: true,
	// 	showOnMenu: false,
	// 	component: CFinanceDetail,
	// },
	'bl_couFinance:financeDetail:sDetails': {
		path: '/content/couFinance/sFinanceDetail',
		name: '融资申请详情',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: SFinanceDetail,
	},
	// 'bl_couFinance:financeDetail:fiDetails': {
	// 	path: '/content/couFinance/fiFinanceDetail',
	// 	name: '融资申请详情',
	// 	//组件名称
	// 	needValid: true,
	// 	showOnMenu: false,
	// 	component: FiFinanceDetail,
	// },
	// 'bl_couFinance:auditFinance:fiAuditFinance': {
	// 	path: '/content/couFinance/fiAuditFinance',
	// 	name: '待审核',
	// 	//组件名称
	// 	needValid: true,
	// 	component: FiAuditFinance,
	// },
	'bl_couFinance:auditFinance:sAuditFinance': {
		path: '/content/couFinance/sAuditFinance',
		name: '待审核',
		//组件名称
		needValid: true,
		component: SAuditFinance,
	},
}

//添加 permsKey
for (let k in couFinancePermsConfig) {
	couFinancePermsConfig[k]['permsKey'] = k
}

export const couFinanceBtnPermsConfig: Record<string, PermItem> = {
	'bl_couFinance:financeDetail:detailsViewResponse': {
		name: '查看接口报文',
		permsKey: 'bl_couFinance:financeDetail:detailsViewResponse',
	},
	'bl_couFinance:auditFinance:detailsBillDownload': {
		name: '单据一键下载',
		permsKey: 'bl_couFinance:auditFinance:detailsBillDownload',
	},
	'bl_couFinance:sBuildOperatorCreateFinance:create': {
		name: '去融资',
		permsKey: 'bl_couFinance:sBuildOperatorCreateFinance:create',
	},
}

// 路由配置，包含菜单
export const couFinanceRouteConfig: PermItem = {
	...couFinancePermsConfig['bl_couFinance:dire'],
	children: getChildrenRouteConfig(couFinancePermsConfig),
}

export const couFinancePermsMap = couFinancePermsConfig
