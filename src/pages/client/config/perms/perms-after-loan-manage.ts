import React from 'react'
import { HomeOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

const InvoiceWarning = React.lazy(
	() => import(/* webpackChunkName: "afterLoanManage" */ '@src/pages/client/views/content/afterLoanManage/clientInvoiceWarning/clientInvoiceWarning')
)

// 运营机构管理
// 资源码映射表
// 命名规范 appname_modulename:menu:function
let afterLoanManagePermsConfig: Record<string, PermItem> = {
	'bl_afterLoanManage:dire': {
		name: '贷后管理',
		//组件名称
		needValid: true,
		icon: 'HomeOutlined',
	},
	'bl_afterLoanManage:invoiceWarning': {
		path: '/content/afterLoanManage/invoiceWarning',
		name: '发票监控',
		//组件名称
		needValid: true,
		component: InvoiceWarning,
	},
}

//添加 permsKey
for (let k in afterLoanManagePermsConfig) {
	afterLoanManagePermsConfig[k]['permsKey'] = k
}

export const afterLoanManageBtnPermsConfig: Record<string, PermItem> = {
	'bl_afterLoanManage:invoiceWarning_with_fi_search': {
		desc: '金融机母公司的搜索控件',
		permsKey: 'bl_afterLoanManage:invoiceWarning_with_fi_search',
	},
}

// 路由配置，包含菜单
export const afterLoanManageRouteConfig: PermItem = {
	...afterLoanManagePermsConfig['bl_afterLoanManage:dire'],
	children: getChildrenRouteConfig(afterLoanManagePermsConfig),
}

export const afterLoanManagePermsMap = afterLoanManagePermsConfig
