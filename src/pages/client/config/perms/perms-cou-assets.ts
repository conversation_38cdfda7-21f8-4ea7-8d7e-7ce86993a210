import React from 'react'
import { FolderOpenOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

const Invoice = React.lazy(() => import(/* webpackChunkName: "couAssets" */ '@factorContent/couAssets/invoice'))
const Contract = React.lazy(() => import(/* webpackChunkName: "couAssets" */ '@factorContent/couAssets/contract'))

// 运营机构管理
// 资源码映射表
// 命名规范 appname_modulename:menu:function
let couAssetsPermsConfig: Record<string, PermItem> = {
	'bl_couAssets:dire': {
		name: '权限配置',
		//组件名称
		needValid: true,
		icon: 'FolderOpenOutlined',
	},
	'bl_couAssets:invoice:list': {
		path: '/content/couAssets/invoice',
		name: '发票管理',
		//组件名称
		needValid: true,
		component: Invoice,
	},
	'bl_couAssets:contract:list': {
		path: '/content/couAssets/contract',
		name: '合同管理',
		//组件名称
		needValid: true,
		component: Contract,
	},
}
//添加 permsKey
for (let k in couAssetsPermsConfig) {
	couAssetsPermsConfig[k]['permsKey'] = k
}

// 路由配置，包含菜单
export const couAssetsRouteConfig: PermItem = {
	...couAssetsPermsConfig['bl_couAssets:dire'],
	children: getChildrenRouteConfig(couAssetsPermsConfig),
}

export const couAssetsPermsMap = couAssetsPermsConfig
