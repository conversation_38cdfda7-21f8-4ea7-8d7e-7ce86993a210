/*
 * @Descripttion:
 * @Version: 1.0
 * @Author: chenyan
 * @Date: 2023-04-03 16:16:49
 * @LastEditors: chenyan
 * @LastEditTime: 2023-04-03 16:39:23
 */

import React from 'react'
import { UserOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'
import ChangeDetail from '../../views/content/registerManage/changeDetail'

/**
 * 中登登记管理
 */
const RegisterList = React.lazy(() => import('@src/pages/client/views/content/registerManage/registerList'))
const RegisterDetail = React.lazy(() => import('@src/pages/client/views/content/registerManage/registerDetail'))
const WaitRegisterList = React.lazy(() => import('@src/pages/client/views/content/registerManage/waitRegisterList'))

// 资源码映射表
// 命名规范 appname_modulename:menu:function
let registerManagePermsConfig: Record<string, PermItem> = {
	'bl_registerManage:dire': {
		name: '中登登记管理',
		//组件名称
		needValid: true,
		icon: 'UserOutlined',
	},
	'bl_registerManage1:dire': {
		name: '中登登记管理',
		//组件名称
		needValid: true,
		icon: 'UserOutlined',
		desc: '',
	},
	'bl_registerManage:waitRegisterList:list': {
		path: '/content/registerManage/waitRegisterList',
		name: '待补登记',
		//组件名称
		needValid: true,
		component: WaitRegisterList,
	},
	'bl_registerManage:registerList:list': {
		path: '/content/registerManage/registerList',
		name: '登记列表',
		//组件名称
		needValid: true,
		component: RegisterList,
	},
	'bl_registerManage:registerDetail:detailInfo': {
		path: '/content/registerManage/registerDetail',
		name: '初始登记详情',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: RegisterDetail,
	},
	'bl_register_manage:change_detail:detail_info': {
		path: '/content/registerManage/changeDetail',
		name: '变更详情',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: ChangeDetail,
	},
}
//添加 permsKey
for (let k in registerManagePermsConfig) {
	registerManagePermsConfig[k]['permsKey'] = k
}

// 路由配置，包含菜单
export const registerManageRouteConfig: PermItem = {
	...registerManagePermsConfig['bl_registerManage:dire'],
	children: getChildrenRouteConfig(registerManagePermsConfig),
}

export const registerManagePermsMap = registerManagePermsConfig
