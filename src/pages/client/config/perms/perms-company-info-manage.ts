import React from 'react'
import { HomeOutlined } from '@ant-design/icons'
import { getChildrenRouteConfig } from '@src/utils/util'

const BankAccountManage = React.lazy(() => import(/* webpackChunkName: "companyInfoManage" */ '@factorContent/companyInfoManage/bankAccountManage'))
const ActiveFinaceService = React.lazy(() => import(/* webpackChunkName: "companyInfoManage" */ '@factorContent/companyInfoManage/activefinaceService'))
const CertifyCompanyInfo = React.lazy(() => import(/* webpackChunkName: "companyInfoManage" */ '@factorContent/companyInfoManage/certifyCompanyInfo'))
const CertifyCompanyInfoDetail = React.lazy(
	() => import(/* webpackChunkName: "companyInfoManage" */ '@factorContent/companyInfoManage/certifyCompanyInfoDetail')
)
const EditCertifyCompanyInfoDetail = React.lazy(
	() => import(/* webpackChunkName: "companyInfoManage" */ '@factorContent/companyInfoManage/editCertifyCompanyInfoDetail')
)

// 运营机构管理
// 资源码映射表
// 命名规范 appname_modulename:menu:function
let companyInfoManagePermsConfig: Record<string, PermItem> = {
	'bl_companyInfoManage:dire': {
		name: '企业管理',
		//组件名称
		needValid: true,
		icon: 'HomeOutlined',
	},
	// 江阴银行隐藏
	// 'bl_companyInfoManage:certifyCompanyInfo:baseInfo': {
	// 	path: '/content/companyInfoManage/certifyCompanyInfo',
	// 	name: '基本信息',
	// 	//组件名称
	// 	needValid: true,
	// 	component: CertifyCompanyInfo,
	// },
	// 江阴银行隐藏
	// 'bl_companyInfoManage:activeFinaceService:list': {
	// 	path: '/content/companyInfoManage/activeFinaceService',
	// 	name: '激活金融服务',
	// 	//组件名称
	// 	needValid: true,
	// 	component: ActiveFinaceService,
	// },

	'bl_companyInfoManage:bankAccountManage:info': {
		path: '/content/companyInfoManage/bankAccountManage',
		name: '银行账户管理',
		//组件名称
		needValid: true,
		component: BankAccountManage,
	},

	'bl_companyInfoManage:certifyCommpanyInfoDetail:details': {
		path: '/content/companyInfoManage/certifyCommpanyInfoDetail',
		name: '查看认证信息',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: CertifyCompanyInfoDetail,
	},

	'bl_companyInfoManage:editCertifyCompanyInfoDetail:edit': {
		path: '/content/companyInfoManage/editCertifyCompanyInfoDetail',
		name: '企业认证',
		//组件名称
		needValid: true,
		showOnMenu: false,
		component: EditCertifyCompanyInfoDetail,
	},
}
//添加 permsKey
for (let k in companyInfoManagePermsConfig) {
	companyInfoManagePermsConfig[k]['permsKey'] = k
}

// 路由配置，包含菜单
export const companyInfoManageRouteConfig: PermItem = {
	...companyInfoManagePermsConfig['bl_companyInfoManage:dire'],
	children: getChildrenRouteConfig(companyInfoManagePermsConfig),
}

export const companyInfoManagePermsMap = companyInfoManagePermsConfig

//companyCreditManage
