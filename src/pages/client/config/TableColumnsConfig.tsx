import React from 'react'
import { invalidVal } from '@src/globalBiz/gBiz'
import {
	renderAmount,
	renderAmountRound,
	renderCompanyType,
	renderDate,
	renderCheckStatus,
	renderTransferStatus,
	renderAmountInCent,
	renderTooltip,
	renderTooltipStrikethrough,
	renderStatus,
	renderPayWay,
	renderMapFinanceStatus,
	renderOriginalCouStatus,
	renderAddPrent,
	renderAddPrent2,
	renderInvoiceType,
	renderPayStatus,
	renderDate_,
	renderBusinessSchema,
	renderActiveFinaceServiceStatus,
	renderAuditStatus,
	renderDestroyStatus,
	renderBusinessType,
	renderTime,
	renderCreditStatus,
	renderPledgeStatus,
	renderCouFinanceStatus,
	renderPledgedStatus,
	renderRelationName,
	renderCashTypeOption,
} from './TableColumnsRender'
const couMeaning = '融信'

const pageColumns: { [str: string]: { [str: string]: any; [index: number]: string } } = {
	//发票管理
	invoice: [
		'invoiceNumber',
		'invoiceCode',
		'buyer',
		'seller',
		'pretaxAmount',
		'availableAmount',
		'usedAmount',
		'ivOpenDate',
		'verificationStatus',
		'checkFlagWithReason',
		'checkState',
		'operation',
	],
	creatInvoice: [
		//融信开立中的发票字段
		'applyAmount',
		'availableAmount',
		'invoiceNumber',
		'invoiceCode',
		'pretaxAmount',
		'seller',
		'buyer',
		'ivOpenDate',
		'checkFlag',
	],
	//合同管理择合同
	contractList: ['contractCode', 'name', 'companyBuyerName', 'companySellerName', 'signDate', 'createTime', 'operation'],
	contract: ['contractCode', 'name', 'companySellerName', 'startDate', 'endDate', 'signDate'],
	contractInDetail: ['contractCode', 'name', 'companySellerName', 'contractPrice', 'signDate'],
	invoiceDetail: ['invoiceNumber', 'invoiceCode', 'pretaxAmount', 'seller', 'buyer', 'ivOpenDate', 'verificationStatus', 'checkState'],
	//融信开立
	couContract: ['contractCode', 'name', 'companySellerName', 'productName', 'contractPrice', 'contractCanUseAmount', 'startDate', 'endDate', 'signDate'],
	couContractInform: ['contractCode', 'name', 'companySellerName', 'contractPrice', 'signDate'],
	couTransferInDetail: ['couNo', 'TransferAmount', 'publishName', 'creditCompanyName', 'dueDate'],
	payDetailsCouTransferInDetail: ['couNo', 'TransferAmount', 'publishName', 'creditCompanyName', 'dueDate', 'fileInfo', 'onChainCertificate'],
	couCreateHistory: ['couNo', 'couAmountInCent', 'creditName', 'toCompanyName', 'dueDate', 'createTime', 'transferStatus1', 'operation'], //开立融信 开立明细
	//流转管理
	Transferlist: ['transferNo', 'transferListAmount', 'transferListStatus', 'toName', 'fromName', 'transferDate', 'receiveDate', 'operation'],
	//待审核
	AuditList: ['transferNo', 'transferListAmount', 'toName', 'fromName', 'transferDate', 'operation'],
	//融信支付发起：融信支付明细
	couPay: ['money', 'createName', 'dueDate', 'protocolFile'],
	couPaySO: ['shouldpriceS', 'publishName', 'holderName', 'dueDate', 'protocolFile'],
	//融信支付流转: 选择融信
	chooseCou: ['shouldprice', 'couNo', 'couAmountInCent', 'dueDate'],
	//查询cou应收
	searchPage: ['transferNo', 'status', 'sumTransferAmountInCent', 'fromName', 'toName', 'createTime'],
	//融信查询应付列表
	searchPageForPay: ['transferNo', 'status', 'sumTransferAmountInCent', 'fromName', 'toName', 'createTime', 'operation'],
	//融信接收确认
	verifyCou: ['transferNo', 'status', 'sumTransferAmountInCent', 'fromName', 'createTime', 'operation'],
	//我的cou
	myCouPage: ['couNo', 'couAmountInCent', 'creditName', 'publishName', 'dueDate', 'couAcceptDate', 'onChainCertificate'],
	myCouPageForPledgeing: ['couNo', 'couAmountInCent', 'creditName', 'publishName', 'dueDate', 'subFiOrgName', 'onChainCertificate'],
	myCouPageForPledged: [
		'couNo',
		'couAmountInCent',
		'creditName',
		'publishName',
		'couCashStatus',
		'cashType',
		'pledgeStatus',
		'dueDate',
		'couAcceptDate',
		'onChainCertificate',
	],
	//cou详情
	couDetail: ['couNo', 'amount', 'publishName', 'holderName', 'dueDate', 'protocolFile'],
	//业务组详情
	bizGroupDetail: ['companyName', 'type'],
	//用户管理
	searchUser: ['accountId', 'mobile', 'accountName', 'roleList', 'operation'],
	//开立额度
	cBuildOperatorList: ['cRole', 'email', 'openQuotaAmount', 'operation'],
	//支付/融资额度
	sBuildOperatorList: ['sRole', 'email', 'payQuotaAmount', 'operation'],

	//待补登记
	waitRegisterList: ['applicationNumber', 'registerFinanceCompanyName', 'financeAmountInYuan', 'fiCompanyName', 'status', 'createDate', 'operation'],

	//融资申请查询
	// searchCouFinanceS: ['applicationNumber', 'inputFinanceAmountInYuan', 'financeStatus', 'financeDueDate', 'createTime'],
	searchCouFinance: [
		'applicationNumber',
		'applicantCompanyName',
		'inputFinanceAmountInYuan',
		'relationName',
		'inputFinanceTransferAmountInYuan',
		'fiCompanyName',
		'cousListtatus',
		'financeDueDate',
		'createTime',
		'operation',
	],
	//重新提交融资查询
	searchReSubmitFinance: [
		'applicationNumber',
		'applicantCompanyName',
		'inputFinanceAmountInYuan',
		'fiCompanyName',
		'financeDueDate',
		'createTime',
		'operation',
	],
	auditFinanceList: ['applicationNumber', 'applicantCompanyName', 'inputFinanceAmountInYuan', 'fiCompanyName', 'financeDueDate', 'createTime', 'operation'],
	fiauditFinanceList: [
		'applicationNumber',
		'applicantCompanyName',
		'inputFinanceAmountInYuan',
		'fiCompanyName',
		'financeDueDate',
		'createTime',
		'receiveAccount',
		'operation',
	],

	// 应收账款转让通知
	transferNoticeFI: ['fromName', 'toName', 'createTime', 'financeCompanyName', 'financeNumber', 'operation'],
	transferNoticeCS: ['fromName', 'createTime', 'financeCompanyName', 'fiName', 'financeNumber', 'operation'],

	//融资申请创建
	selectCou: ['inputAmountInCent', 'publishName', 'dueDate', 'couFlowSheet'],
	selectCouF: [
		'inputAmountInCent',
		// 'discount',
		//  'actualAmountInYuan',
		'publishName',
		'dueDate',
		//'couFlowSheet'
	],
	// 融资申请选择一手发票
	selectInvoice: ['invoiceNumber', 'invoiceCode', 'checkState', 'pretaxAmount', 'buyer', 'seller', 'ivOpenDate', 'checkFlag', 'operation'],
	// 融资申请选择多手发票
	selectInvoiceMultiple: [
		'applyAmount',
		'availableAmount',
		'invoiceNumber',
		'invoiceCode',
		'checkState',
		'pretaxAmount',
		'buyer',
		'seller',
		'ivOpenDate',
		'checkFlag',
		'operation',
	],
	//选择发票 侧面的抽屉弹框
	selectInvoiceModel: [
		'availableAmount',
		'invoiceNumber',
		'invoiceCode',
		'checkState',
		'invoiceType',
		'pretaxAmount',
		'noTaxMoney',
		'buyer',
		'seller',
		'ivOpenDate',
		'checkFlag',
	],
	selectCouModel: ['inputAmountInCent', 'couNo', 'couAmountInCent', 'publishName', 'creditName', 'originalNo', 'dueDate'],
	// 创建融资申请中选择融信
	selectCouModelF: [
		'inputAmountInCent',
		// 'discount',
		// 'actualAmountInYuan',
		'originalNo',
		'couNo',
		'couAmountInCent',
		'publishName',
		// 'creditName',
		'dueDate',
	],
	selectCouDrawerD: ['couNo', 'couAmountInYuan', 'holderCompanyName', 'publishCompanyName', 'creditCompanyName', 'dueDate'],
	selectCouCheckBoxModel: ['couNo', 'couAmountInCent', 'publishName', 'creditName', 'dueDate'],

	//融资申请创建：确认融资信息页面-融资融信列表
	couFinanceInfo: ['financeAmount', 'publishName', 'dueDate', 'protocolFile'],
	//兑付查询
	cashSearch: [
		'originalCouNo',
		'originalCouStatus',
		'cashType',
		// 'pledgeStatus',
		'couAmountInCent',
		'creditName',
		'holderName',
		'dueDate',
		'createTime',
		'couAcceptDate',
		'payInfo',
		'onChainCertificate',
		'operation',
	],
	//持有人明细
	holderDetail: [
		'couNo',
		'couAmountInCent',
		'holderName',
		'creditName',
		'dueDate',
		'originalCouStatus',
		'cashType',
		'pledgeStatus',
		'originalCouNo',
		'originalCouAmountInCent',
		'onChainCertificate',
	],
	//融资授信设置
	credit: ['creditCompany', 'quota', 'quotaCanUse', 'creditDueDate', 'interestPayWay', 'creditStatus', 'verifyStatus', 'operation'],

	openingBank: ['lname', 'addr', 'operation'],
	// 融资申请详情中的合同和融信明细
	financeContract: ['contractCode', 'contractName', 'seller', 'productName', 'contractPrice', 'startDate', 'endDate'],
	transferDetaill: ['couTransferNo', 'shouldpriceS', 'createName'],
	financeCost: [
		'project',
		'rateType',
		'rate',
		// 'cost',
		//   'platformFeeModel'
	], // 融资费用
	financeCouS: ['couAmountInCent', 'discount', 'transferPracticalAmountInYuan', 'publishCompanyName', 'dueDate', 'couFlowSheet', 'onChainCertificate'], // 融资融信-仅有融信流转单
	financeCouC: [
		'couAmountInCent',
		'discount',
		'transferPracticalAmountInYuan',
		'publishCompanyName',
		'dueDate',
		'couFlowSheet',
		'paymentUndertaking',
		'onChainCertificate',
	], // 融资融信-缺少流转合同-应收账款转让通知
	financeCouFi: [
		'couAmountInCent',
		// 'discount', //240508隐藏
		// 'transferPracticalAmountInYuan',//5/6隐藏
		'publishCompanyName',
		'dueDate',
		'paymentUndertaking',
		'circulationContract',
		'couFlowTable',
		'couFlowSheetAll',
		'onChainCertificate',
	], // 融资融信
	// 融资详情新增 字段 'checkFlag', 'checkState',
	financeInvoice: ['invoiceNumber', 'invoiceCode', 'checkFlag', 'checkState', 'pretaxAmount', 'buyer', 'seller', 'ivOpenDate'], // 融资发票

	// PFI授信管理
	creditList: ['creditCompany', 'relationFiName', 'quota', 'availableCredit', 'creditDueDate', 'interestPayWay', 'creditStatus', 'verifyStatus'],
	//原支付记录
	oldTransfer: ['oldTransferNo', 'oldTransferAmountInCent', 'fromName', 'toCompanyName'],

	// 退款管理
	selectPayModel: ['transferNo', 'transferFor', 'transferListAmount', 'fromName', 'receiveDate'],
	selectPay: ['transferNo', 'transferFor', 'transferListAmount', 'fromName', 'receiveDate'],
	selectCouModelR: ['financeUseAmount', 'couNo', 'couAmountInCent', 'publishName', 'creditName', 'dueDate'],
	selectCouRC: ['couNo', 'amountOfCredit', 'publishName', 'dueDate', 'originalUuid'],
	searchRefundList: ['refundNo', 'createTime', 'status', 'refundAmount', 'refundCompany', 'receiveCompany', 'oldPayNo', 'operation'],
	auditRefundList: ['refundNo', 'createTime', 'refundAmount', 'refundCompany', 'receiveCompany', 'oldPayNo', 'operation'],
	auditRefundContractList: ['contractCode', 'name', 'companyBuyerName', 'productName', 'startDate', 'endDate'],
	/**
	 * 企业中心模块
	 */
	ActiveFinaceService: ['financialInstitutions', 'businessSchema', 'activeFinaceServiceState'],
	/* 利率设置 */
	ratesSetting: ['sName', 'interestRate', 'factorRate', 'createTime', 'operation'],

	// 融信核销
	searchDestroyList: ['destroyNo', 'createTime', 'destroyStatus', 'couNo', 'couPublishCompanyName', 'couHolderCompanyName', 'couAmountInYuan', 'operation'],
	destroyCou: ['originalCouNo', 'couAmountInYuan', 'holderCompanyName', 'publishCompanyName', 'creditCompanyName', 'dueDate'],
	auditDestroy: ['destroyNo', 'createTime', 'originalCouNo', 'publishCompanyName', 'holderCompanyName', 'couAmountInYuan', 'operation'],

	/**
	 * 中登网登记管理
	 */
	registerList: [
		'registerNumber',
		'businessType',
		'registerType',
		'registerDate',
		'limitDate',
		'changeRecord',
		'registerStatus',
		'requestNo',
		'requestData',
		'operation',
	],

	// 融信兑付提醒
	couCashRemindData: ['couNo', 'remindCouAmount', 'holderCompanyName', 'creditCompanyName', 'dueDate'],

	// 应收账款质押通知
	pledgeRemindData: ['sendName', 'createTime', 'financeCompanyName', 'financeNumber', 'operation'],

	// 发票监控列表
	invoiceWarnList: [
		'createTime',
		'invoiceNumber',
		'invoiceCode',
		'supplierName',
		'buyerName',
		'pretaxAmount',
		'invoiceDate',
		//******* 新增字段
		'checkFlagWithReason',
		'checkState',
		'checkFlag',
		'applicationNumber',
		'applicantName',
		'relationName',
		'financeAmountInYuan',
		'fiName',
		'financeCreateDate',
		// ******* 新增字段 起息日
		'financeValueDate',
		'financeDueDate',
	],
	tobeAgreement: [
		//江阴项目新增待签署协议
		'applicationNumber',
		'applicantCompanyName',
		'inputFinanceAmountInYuan',
		'financeDueDate',
		'createTime',
		'operation',
	],
	//融信管理-合作协议
	cooperateProtocol: ['order', 'protocolNum', 'createTime', 'operation'],
}

const columnsDetail: { [str: string]: { [index: string]: any } } = {
	sName: {
		dataIndex: 'companyName',
		title: '供应商名称',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	attachmentName: {
		dataIndex: 'attachmentName',
		title: '附件名称',
	},
	attachmentLink: {
		dataIndex: 'attachmentLink',
		title: '附件',
	},
	//编号相关
	invoiceCode: {
		dataIndex: 'invoiceCode',
		title: '发票代码',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltipStrikethrough,
	},
	invoiceNumber: {
		dataIndex: 'invoiceNumber',
		title: '发票号码',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	contractCode: {
		dataIndex: 'contractCode',
		title: '合同编号',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	transferNo: { dataIndex: 'transferNo', title: `支付编号` },
	couNo: {
		dataIndex: 'couNo',
		title: `${couMeaning}编号`,
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
		width: 200,
	},
	originalUuid: { dataIndex: 'originalNo', title: '原始融信编号', render: renderTooltipStrikethrough },
	originalNo: { dataIndex: 'originalNo', title: '原始融信编号', render: renderTooltip },
	originalCouNo: { dataIndex: 'couNo', title: '融信编号', render: renderTooltip },
	oldTransferNo: { dataIndex: 'oldTransferNo', title: '支付编号', render: renderTooltip }, //原支付记录支付编号
	oldPayNo: { dataIndex: 'oldTransferNo', title: '原支付编号', render: renderTooltip }, //退款申请列表原支付编号
	userSign: { dataIndex: 'tag', title: '用户标识' },
	userRoles: { dataIndex: 'roleList', title: '角色名称' },
	applicationNumber: { dataIndex: 'applicationNumber', title: '融资申请编号', width: 200, render: renderTooltip },
	financeNumber: { dataIndex: 'financeNumber', title: '融资申请编号', width: 200, render: renderTooltip },
	refundNo: { dataIndex: 'refundNo', title: '退款申请编号', width: 200, render: renderTooltip },
	destroyNo: { dataIndex: 'destroyNo', title: '核销申请编号', width: 200, render: renderTooltip },

	//企业名称相关字段
	companyName: {
		dataIndex: 'companyName',
		title: '企业名称',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	relationFI: {
		dataIndex: 'relationFi',
		title: '授信企业',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	username: { dataIndex: 'username', title: '用户名' },
	cRole: { dataIndex: 'username', title: '核心企业限额操作员' },
	sRole: { dataIndex: 'username', title: '供应商限额操作员' },
	email: { dataIndex: 'email', title: '用户邮箱' },
	phoneNum: { dataIndex: 'mobile', title: '手机号' },
	bizGroupName: { dataIndex: 'name', title: '业务组名称' },
	buyer: {
		dataIndex: 'buyerName',
		title: '买方名称',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	seller: {
		dataIndex: 'supplierName',
		title: '卖方名称',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},

	name: {
		dataIndex: 'name',
		title: '合同名称',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	companyBuyerName: {
		dataIndex: 'companyBuyerName',
		title: '买方名称',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	createName: { dataIndex: 'companyBuyerName', title: '开立方' },
	publishName: {
		dataIndex: 'publishName',
		title: '开立方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	publishCompanyName: {
		dataIndex: 'publishCompanyName',
		title: '开立方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	couPublishCompanyName: {
		dataIndex: 'publishCompanyName',
		title: '融信开立方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	holderName: {
		dataIndex: 'holderName',
		title: '持有方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	holderCompanyName: {
		dataIndex: 'holderCompanyName',
		title: '持有方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	couHolderCompanyName: {
		dataIndex: 'holderCompanyName',
		title: '融信持有方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},

	toCompanyName: {
		dataIndex: 'toCompanyName',
		title: '收款方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	companySellerName: {
		dataIndex: 'companySellerName',
		title: '卖方名称',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	fromName: {
		dataIndex: 'fromName',
		title: '付款方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	sendName: {
		dataIndex: 'fromName',
		title: '发送方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	toName: {
		dataIndex: 'toName',
		title: '收款方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	receiveCompany: {
		dataIndex: 'receiveCompanyFullName',
		title: '收款方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	refundCompany: {
		dataIndex: 'refundCompanyFullName',
		title: '退款方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	applicantCompanyName: {
		title: '融资企业',
		dataIndex: 'applicantCompanyName',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	interestPayWay: { dataIndex: 'interestPayWay', title: '付息方式', render: renderPayWay },
	relationName: {
		dataIndex: 'relationName',
		title: '开立企业',
		ellipsis: {
			showTitle: false,
		},
		render: renderRelationName,
	},
	fiCompanyName: {
		dataIndex: 'fiCompanyName',
		title: '金融机构',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	contractName: { dataIndex: 'contractName', title: '合同名称' },
	productName: {
		dataIndex: 'productName',
		title: '产品名称',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	creditName: {
		dataIndex: 'creditName',
		title: '授信方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	creditCompanyName: {
		dataIndex: 'creditCompanyName',
		title: '授信方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	settleName: { dataIndex: 'settleName', title: '授信方' },
	creditCompany: {
		dataIndex: 'companyName',
		title: '授信企业',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	relationFiName: {
		dataIndex: 'relationFiName',
		title: '授信方',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	financeCompany: {
		title: '融资企业',
		dataIndex: 'applicantCompanyName',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	financeCompanyName: {
		title: '融资企业',
		dataIndex: 'financeCompanyName',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	registerFinanceCompanyName: {
		title: '融资企业',
		dataIndex: 'companyName',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	fiName: {
		dataIndex: 'fiName',
		title: '金融机构',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},

	//金额相关
	TransferAmount: {
		dataIndex: 'amount',
		ellipsis: true,
		title: '融信金额(￥)',
		render: text => {
			// 保证标题不换行
			return <div style={{ minWidth: '120px' }}>{renderAmountInCent(text)}</div>
		},
	},
	availableCredit: { title: '可用开立(￥)', dataIndex: 'availableCredit', render: renderAmountInCent },
	// ellipsis: true, 防止标题换行
	pretaxAmount: {
		dataIndex: 'pretaxAmount',
		ellipsis: true,
		title: '含税金额(￥)',
		render: text => {
			// 保证标题不换行
			return <div style={{ minWidth: '120px' }}>{renderAmount(text)}</div>
		},
	},
	availableAmount: {
		dataIndex: 'availableAmount',
		ellipsis: true,
		title: '可用金额(￥)',
		render: text => {
			return <div style={{ minWidth: '120px' }}>{renderAmount(text)}</div>
		},
	},
	usedAmount: {
		dataIndex: 'usedAmount',
		ellipsis: true,
		title: '占用金额(￥)',
		render: text => {
			return <div style={{ minWidth: '120px' }}>{renderAmount(text)}</div>
		},
	},
	applyAmount: {
		dataIndex: 'applyAmount',
		title: `使用金额(￥)`,
		render: text => {
			return <div style={{ minWidth: '120px' }}>{renderAmount(text)}</div>
		},
	},
	money: { dataIndex: 'money', title: `${couMeaning}金额(￥)`, render: renderAmount },
	sumTransferAmountInCent: { dataIndex: 'sumTransferAmountInCent', title: `${couMeaning}金额(￥)`, render: renderAmountInCent },
	couAmountInCent: {
		dataIndex: 'couAmountInCent',
		// ellipsis: true, 防止标题换行
		ellipsis: true,
		title: `融信金额(￥)`,
		render: renderAmountInCent,
	},
	couAmount: { dataIndex: 'financeUseAmount', title: `转让融信金额(￥)`, render: renderAmountInCent }, // 创建融资申请页面的融信金额
	amount: { dataIndex: 'amount', title: `${couMeaning}金额(￥)`, render: renderAmountInCent },
	noTaxMoney: { dataIndex: 'amount', title: '不含税金额(￥)', render: renderAmount },
	shouldprice: { dataIndex: 'shouldprice', title: '本次支付金额(￥)' },
	oldTransferAmountInCent: { dataIndex: 'oldTransferAmountInCent', ellipsis: true, title: '金额(￥)', render: renderAmountInCent },
	shouldpriceS: { dataIndex: 'shouldprice', title: '融信金额(￥)', render: renderAmount },
	inputFinanceAmountInYuan: { dataIndex: 'inputFinanceAmountInYuan', title: '融资申请金额(￥)', render: renderAmount },
	financeAmountInYuan: { dataIndex: 'financeAmountInYuan', title: '融资金额(￥)', render: renderAmount },
	financeAmount: { dataIndex: 'financeAmount', title: '融资金额(￥)' },
	refundAmount: { dataIndex: 'amountInCent', title: '退款金额(￥)', render: renderAmountInCent },
	quota: { dataIndex: 'quotaInCent', title: '开立额度(￥)', render: renderAmountInCent },
	financeUseAmount: { dataIndex: 'financeUseAmount', title: '融资使用金额(￥)' },
	inputAmountInCent: { dataIndex: 'inputAmountInCent', title: '质押融信金额(￥)' },
	actualAmountInYuan: { dataIndex: 'actualAmountInYuan', title: '实际融资申请金额(￥)' },
	transferPracticalAmountInYuan: { dataIndex: 'transferPracticalAmountInYuan', title: '实际申请金额(￥)' },
	refundUseAmount: { dataIndex: 'refundUseAmount', title: '退款使用金额(￥)' },
	// 防止标题换行
	transferListAmount: { dataIndex: 'sumTransferAmountInCent', ellipsis: true, title: '金额(￥)', render: renderAmountInCent },
	amountOfCredit: { dataIndex: 'amount', ellipsis: true, title: '金额(￥)', render: renderAmountInCent },
	couAcceptDate: { dataIndex: 'couCreateTime', title: '接收日期', render: renderDate_, width: 125 },
	originalCouAmountInCent: { dataIndex: 'originalCouAmountInCent', title: '原始融信金额(￥)', render: renderAmountInCent, width: 130 },
	cost: {
		dataIndex: 'cost',
		title: '费用(￥)',
		render: renderAmountRound,
	},
	contractPrice: { dataIndex: 'amount', title: '合同金额', render: renderAmount },
	openQuotaAmount: { dataIndex: 'quotaInCent', title: `开立额度(￥)`, render: renderAmountInCent },
	payQuotaAmount: { dataIndex: 'quotaInCent', title: `支付/融资额度(￥)`, render: renderAmountInCent },
	couAmountInYuan: { dataIndex: 'couAmountInYuan', title: `融信金额(￥)`, render: renderAmount },
	remindCouAmount: { dataIndex: 'couAmount', title: `开立金额(￥)` },

	//日期相关
	ivOpenDate: { dataIndex: 'invoiceDate', title: '开票日期', render: renderDate_, width: 125 },
	startDate: { dataIndex: 'startDate', title: '开始日期', render: renderDate_, width: 125 },
	endDate: { dataIndex: 'endDate', title: '结束日期', render: renderDate_, width: 125 },
	signDate: { dataIndex: 'signDate', title: '签订日期', render: renderDate_, width: 125 },
	dueDate: { title: '兑付到期日', dataIndex: 'dueDate', render: renderDate_, width: 125 },
	couCreateTime: { dataIndex: 'couCreateTime', title: '创建日期', render: renderDate },
	transferDate: { dataIndex: 'createTime', title: '创建日期', render: renderDate_, width: 125 },
	receiveDate: { dataIndex: 'receiveTime', title: '接收日期', render: renderDate_, width: 125 },
	createTime: { dataIndex: 'createTime', title: '创建日期', render: renderDate_, width: 125 },
	createDate: { dataIndex: 'createDate', title: '创建日期', render: renderDate_, width: 125 },
	financeDueDate: { dataIndex: 'financeDueDate', title: '融资到期日', render: renderDate_, width: 125 },
	creditDueDate: { dataIndex: 'creditDueDate', title: '授信到期日', render: renderDate_, width: 125 },

	//比例 率
	interestRate: { dataIndex: 'interestRate', title: '年化融资利率', render: renderAddPrent2 },
	factorRate: { dataIndex: 'factoringRate', title: '保理手续费率', render: renderAddPrent2 },
	rate: { dataIndex: 'rate', title: '费率', render: renderAddPrent },
	discount: { dataIndex: 'discount', title: '质押率' },

	//状态相关
	verifyStatus: { dataIndex: 'status', title: '审核状态', ellipsis: true, render: renderAuditStatus },
	cousListtatus: { dataIndex: 'status', title: '状态', ellipsis: true, render: renderCouFinanceStatus },
	cashStatus: { dataIndex: 'cashStatus', title: '支付状态' },
	couCashStatus: {
		dataIndex: 'couCashStatus',
		ellipsis: true,
		title: '兑付状态',
		render: renderPledgedStatus,
	},
	transferStatus: { dataIndex: 'transferStatus', title: '状态', render: renderPayStatus },
	transferStatus1: { dataIndex: 'transferStatus', title: '支付状态', render: renderPayStatus },
	transferListStatus: { dataIndex: 'status', title: '状态', render: renderPayStatus },
	// 这里的字段应该不删除，产品没说就保持原来的展示不变，*******
	checkFlag: { dataIndex: 'checkFlag', title: '验真状态', render: renderCheckStatus },
	status: { dataIndex: 'status', title: '状态', render: renderTransferStatus },
	couStatus: { dataIndex: 'couStatus', title: '流转状态', render: renderStatus },
	financeStatus: { dataIndex: 'status', title: '状态', render: renderMapFinanceStatus },
	originalCouStatus: { dataIndex: 'cashStatus', ellipsis: true, title: '兑付状态', render: renderOriginalCouStatus },
	destroyStatus: { dataIndex: 'status', title: '状态', render: renderDestroyStatus },
	creditStatus: { dataIndex: 'enable', title: '授信状态', render: renderCreditStatus },

	// 类型
	rateType: { dataIndex: 'rateType', title: '费率类型' },
	invoiceType: {
		dataIndex: 'invoiceType',
		title: '发票类型',
		ellipsis: {
			showTitle: false,
		},
		render: renderInvoiceType,
	},
	transferFor: { dataIndex: 'peeOrPay', title: '支付类型' },
	type: { dataIndex: 'type', title: '企业类型', render: renderCompanyType },
	cashType: { dataIndex: 'cashType', title: '兑付类型', render: renderCashTypeOption },

	//操作相关字段
	operation: { dataIndex: 'operation', title: '操作', width: '100px' },
	receiveAccount: { dataIndex: 'receiveAccount', title: '收款账号', width: '100px' },

	operator: {
		dataIndex: 'operator',
		title: '操作员',
		ellipsis: {
			showTitle: false,
		},
	},
	//文件
	project: { dataIndex: 'project', title: '项目' },
	couCash_payDetail: { dataIndex: 'couCash_payDetail', title: '付款明细表' },
	protocolFile: { dataIndex: 'protocolFile', title: '支付协议文件' },
	couFlowSheet: { dataIndex: 'couFlowSheet', title: '融信流转单' },
	couFlowTable: { dataIndex: 'couFlowTable', title: '融信流转路径表' },
	couFlowSheetAll: { dataIndex: 'couFlowSheetAll', title: '融信流转单（全路径）' },
	paymentUndertaking: { dataIndex: 'paymentUndertaking', title: '付款承诺函' },
	circulationContract: { dataIndex: 'circulationContract', title: '流转合同' },
	// credit: [  'factorRate', 'operation']
	businessSchema: { dataIndex: 'businessSchema', title: '业务模式', render: renderBusinessSchema },
	activeFinaceServiceState: {
		dataIndex: 'status',
		title: '激活状态',
		render: renderActiveFinaceServiceStatus,
	},
	//金融机构
	financialInstitutions: {
		dataIndex: 'fiName',
		title: '金融机构',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	//中登网登记列表表格字段
	businessType: {
		dataIndex: 'businessType',
		title: '交易业务类型',
		ellipsis: {
			showTitle: false,
		},
		render: renderBusinessType,
	},
	registerDate: {
		dataIndex: 'createTime',
		title: '登记时间',
		ellipsis: {
			showTitle: false,
		},
		render: text => {
			if ([undefined, null].includes(text)) {
				return invalidVal
			}
			return renderTooltip(renderTime(text))
		},
	},
	limitDate: {
		dataIndex: 'limitDate',
		title: '登记到期日',
		ellipsis: {
			showTitle: false,
		},
		render: text => {
			if ([undefined, null].includes(text)) {
				return invalidVal
			}
			return renderTooltip(renderDate_(text))
		},
	},
	requestNo: {
		dataIndex: 'requestNo',
		title: '关联业务编号',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	requestData: {
		dataIndex: 'requestData',
		title: '关联业务',
		ellipsis: {
			showTitle: false,
		},
		render: renderTooltip,
	},
	onChainCertificate: {
		dataIndex: 'onChainCertificate',
		// 防止标题换行
		ellipsis: true,
		title: '链上证书',
	},
	platformFeeModel: { dataIndex: 'platformFeeModel', title: '扣费方式' },
	accountId: { dataIndex: 'accountId', title: '用户ID' },
	accountName: { dataIndex: 'accountName', title: '姓名' },
	mobile: { dataIndex: 'mobile', title: '手机号' },
	roleList: { dataIndex: 'roleList', title: '角色权限' },
	pledgeStatus: { dataIndex: 'pledgeStatus', title: '质押状态', width: 75, render: renderPledgeStatus },
	subFiOrgName: { dataIndex: 'subFiOrgName', title: '质押权人' },
	sendDate: { dataIndex: 'sendDate', title: `发送日期` },
	inputFinanceTransferAmountInYuan: { dataIndex: 'inputFinanceTransferAmountInYuan', title: '质押融信金额(￥)', render: renderAmount },

	protocolNum: { dataIndex: 'protocolNo', title: '协议编号', render: renderTooltipStrikethrough },
	order: { dataIndex: 'protocolSerial', title: '序号' },
	lname: { dataIndex: 'lname', title: '银行名称', render: renderTooltipStrikethrough },
	addr: { dataIndex: 'addr', title: '银行地址', render: renderTooltipStrikethrough },
}

export const getColumnsByPageName = (pageName: string, columns?: { [name: string]: any }) => {
	const arr = pageColumns[pageName]
	if (!arr) return []
	const result: any = []
	arr.forEach((item: string) => {
		if (columns && columns[item]) result.push(Object.assign({}, columnsDetail[item], columns[item]))
		else if (columnsDetail[item]) result.push(columnsDetail[item])
	})

	return result
}
