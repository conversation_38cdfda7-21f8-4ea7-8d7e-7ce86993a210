import React from 'react'
import { commonModule<PERSON>pi, loginApis, transferModule<PERSON><PERSON> } from '@src/pages/client/api'
import { permsMap } from '@factor/config/perms/perms-config'
import { routeConfig } from '@factor/routes/route-config'
import { Tooltip, message } from 'antd'
import { history } from '@src/utils/router'
import { filterCouOnChainData } from '@src/globalComponents/chainInfoComponents/chain-data-handle-json'
import { getCurAppConfig } from '@utils/format'

//storage的命名空间
const storageKeyName = 'pledge:bl'
type storeType = 'session' | 'local'

interface JumpPageType {
	isUserVerified: boolean //用户状态
	isCompanyApproved: boolean // 企业状态
	isPerfectAccount: boolean // 没有银行卡信息
	signProtocol: boolean | any[] // 未签署平台协议
	identitySelection: boolean // 多角色
}
//sessionStorage存储类型定义
interface SessionStorageType {
	originMenuList: Record<string, unknown>[]
	menuList: Record<string, unknown>[]
	urlList: string[]
	permList: string[]
	frontCodeList: string[]
	roleList: string[]
	userInfo: Record<string, unknown>
	token: string
	menuState: {
		openKeys: string[]
		selectedKeys: string[]
	}
	lastSelectedMenuItem: Record<string, any>
	lastSelectedMenuKeys: string[]
	innerPagesRoutePermsMap: Record<string, any>
	lastOpenKeys: string[]
	pageParams: Record<string, unknown>
	testFieldAge: number
	testCountMemory: number
	breadcrumbItems: (string | { name: string; path: string })[]
	tabTitle: string
	favicon: string
	accountNum?: string
	accountBank?: string
	roleId?: string
	roleCode?: string
	jumpPageMap?: JumpPageType
}

//LocalStorageType存储类型定义
interface LocalStorageType {
	testFieldAge: number
	language: string
	OoInfo: { [k: string]: any }
	lastSelectedMenuItem: Record<string, any>
	createCouInfo: { [k: string]: any }
	paramsFromRoleManage: { [k: string]: any }
}

type storgeType = keyof SessionStorageType | keyof LocalStorageType

//Record<string, unknown>
type storageParams = Partial<SessionStorageType> | Partial<LocalStorageType>

type storageKeyType = keyof storageParams

const getObjFromStorage = (type: storeType) => {
	if (type === 'session') {
		return JSON.parse(sessionStorage.getItem(storageKeyName) || '{}')
	} else if (type === 'local') {
		return JSON.parse(localStorage.getItem(storageKeyName) || '{}')
	}
}

export const clearStorage = (type?: storeType): boolean => {
	if (type === 'session') {
		sessionStorage.clear()
	} else if (type === 'local') {
		localStorage.clear()
	} else {
		sessionStorage.clear()
		localStorage.clear()
	}
	return true
}

//默认是local
export const setStorage = (param: storageParams, type: storeType = 'local'): boolean => {
	try {
		const objFromStorage: SessionStorageType | LocalStorageType = getObjFromStorage(type)
		let key: any
		for (key in param) {
			if (param.hasOwnProperty(key)) {
				objFromStorage[key] = param[key]
			}
		}
		const objStr = JSON.stringify(objFromStorage)
		if (type === 'session') {
			sessionStorage.setItem(storageKeyName, objStr)
		} else {
			localStorage.setItem(storageKeyName, objStr)
		}
	} catch (e) {
		return false
	}
	return true
}

//默认是local
export const getStorage = (key: storgeType, type: storeType = 'local'): any => {
	const objFromStorage = getObjFromStorage(type)
	return objFromStorage[key]
}

// 得到图片地址

export const getImageUrl = async (data: string) => {
	return await commonModuleApi.downloadFileUrl({ fileUrl: data })
}

export const inputTrim = (form, keyName, e) => {
	let value = (e.target.value + '').trim()
	form.setFieldsValue({
		[keyName]: value,
	})
}

export const hasAuth = curPerms => {
	let frontCodeList = getStorage('frontCodeList') || []
	return frontCodeList.includes(curPerms)
}

export const inputForbiddenWhiteSpace = (form, keyName, e) => {
	let value = (e.target.value + '').replace(/\s/g, '')
	form.setFieldsValue({
		[keyName]: value,
	})
}

export const copyText = (text: string) => {
	let oInput = document.createElement('input')
	oInput.value = text
	document.body.appendChild(oInput)
	oInput.select() // 选择对象;
	document.execCommand('Copy') // 执行浏览器复制命令
	oInput.remove()
}

export const handleRequestPath = (path: string, useMock = false) => {
	const env = (process_env + '').toLocaleLowerCase()

	//注意keyName可能写错
	if (env === 'production' && window.config) {
		const { serverAddress } = getCurAppConfig()
		if (path.indexOf('http') === -1) {
			if (serverAddress) {
				path = serverAddress + path
			}
		}
	}
	// development下进入 mock模式
	if (env === 'development' && useMock) {
		if (path.indexOf('/mock') === -1) {
			path = `/mock${path}`
		}
	}
	return path
}

export const getPageHash = (): string => {
	return window.location.hash.slice(1)
}

export const findMatchItemByPageHash = (): MenuType => {
	let pageHash = getPageHash()
	let outMenuList = getStorage('menuList') || []
	const findItemsByHash = (initMenuList: MenuType[], initFindedItems: MenuType[] = []) => {
		if (Array.isArray(initMenuList)) {
			for (let i = 0; i < initMenuList.length; i++) {
				let item = initMenuList[i]
				if (item.path) {
					if (pageHash.includes(item.path)) {
						initFindedItems.push(item)
					}
				}
				if (item.children) {
					findItemsByHash(item.children, initFindedItems)
				}
			}
		}
		return initFindedItems
	}
	let findedItems = findItemsByHash(outMenuList)
	findedItems.sort((a, b) => {
		return b.path.length - a.path.length
	})
	return findedItems[0]
}

//获取当前项目，真实menuList
export const getRealMenuList = (frontCodeList, originMenuList) => {
	const getMenuOrderNumMap = () => {
		let menuOrderNumMap = {}
		const setPermsOrderNumMap = list => {
			list.forEach(item => {
				//有排序用排序的顺序，没有排序时用最大值
				if (item.orderNum || item.orderNum === 0) {
					menuOrderNumMap[item.perms] = item.orderNum
				} else {
					menuOrderNumMap[item.perms] = 99999
				}
				if (Array.isArray(item.children) && item.children.length > 0) {
					setPermsOrderNumMap(item.children)
				}
			})
		}
		setPermsOrderNumMap(originMenuList)
		return menuOrderNumMap
	}

	const addMenuItem = (frontCodeList, routeList, menuList, breadcrumb, pId = '') => {
		const menuOrderNumMap = getMenuOrderNumMap()
		routeList.forEach((item, index) => {
			// console.log(`index = ${index}---item = ${JSON.stringify(item)}`)
			if (!item) {
				return
			}
			let { permsKey } = item
			if (!permsKey) {
				return
			}
			if (permsKey && !permsMap[permsKey]) {
				console.error(`${permsKey} - 该资源码不存在，请查证后再试`)
				return
			}
			//needValid 默认是true
			let { name, path, showOnMenu = true, icon, needValid = true } = item
			// 是否展示在菜单上
			// 1、有权限
			// 2、不需要校验
			if (showOnMenu && (frontCodeList.includes(permsKey) || !needValid)) {
				let menuItem = {
					name,
					path,
					icon,
					pId,
					perms: permsKey,
					breadcrumb: [...breadcrumb, name],
					id: pId ? `${pId}_${index}` : index + '',
					children: null,
					orderNum: menuOrderNumMap[permsKey],
				}
				menuList.push(menuItem)
				if (Array.isArray(item.children) && item.children.length > 0) {
					menuItem.children = addMenuItem(frontCodeList, item.children, [], [...menuItem.breadcrumb], menuItem.id) || []
					//children需排序
					menuItem.children.sort((a, b) => {
						return a.orderNum - b.orderNum
					})
				}
			}
		})
		//增加排序功能,需同步到脚手架里
		menuList.sort((a, b) => {
			return a.orderNum - b.orderNum
		})
		return menuList
	}

	const getMenuList = () => {
		let menuList = []
		try {
			const curAppRoutes = routeConfig.routes[0].children
			const authRoute = curAppRoutes.find(item => {
				return item.path === '/content'
			})
			if (authRoute) {
				let menuRouteList = authRoute.children
				let breadcrumb = []
				let pId = null
				addMenuItem(frontCodeList, menuRouteList, menuList, breadcrumb, pId)
			}
			setStorage({ menuList: menuList })
			return menuList
		} catch (e) {
			return menuList
		}
	}

	return getMenuList()
}

// 获取超级管理员的所有资源码
export const getFrontCodeListForAdmin = () => {
	let allPermsList = Object.keys(permsMap)
	let adminFrontCodeList = []
	allPermsList.forEach(item => {
		adminFrontCodeList.push(item)
	})
	return adminFrontCodeList
}

export const getFirstPagePath = menuList => {
	let getPathFromArr = list => {
		let targetPath = ''
		if (Array.isArray(list)) {
			for (let i = 0, len = list.length; i < len; i++) {
				let item = list[i]
				if (item.path) {
					targetPath = item.path
					return targetPath
				}
				if (item.children) {
					return getPathFromArr(item.children)
				}
			}
		}
		return targetPath
	}
	return getPathFromArr(menuList)
}

export const getOOInfo = async () => {
	try {
		let ext = JSON.parse(localStorage.getItem('ext')) || {
			user: {
				company: {},
			},
		}
		let company = ext?.user?.company
		let names = await loginApis.getOoNamesByUuids({
			uuids: [company.operatingOrganizationUuid],
		})
		return {
			operatingOrganizationName: names.ooNameMap[company.operatingOrganizationUuid],
			operatingOrganizationUuid: company.operatingOrganizationUuid,
		}
	} catch (err) {
		message.error('获取运营机构信息错误')
		return {}
	}
}

export const getChildrenRouteConfig = (moduleConfig: Record<string, PermItem>) => {
	let routeConfigs = []
	for (let key of Object.keys(moduleConfig)) {
		if (!moduleConfig[key].path) {
			continue
		}
		routeConfigs.push(moduleConfig[key])
	}
	return routeConfigs
}

export const invalidVal = '--'

export const handleLogout = async (e?, cb?: () => void) => {
	await loginApis.logout().catch(error => {
		console.log(error)
	})
	message.success('退出成功')
	history.push('/home')
	clearStorage()
	if (cb) {
		cb()
	}
}

export const addLogoutForDev = () => {
	if (process.env.NODE_ENV === 'development') {
		return <a onClick={handleLogout}>退出登录</a>
	} else {
		return null
	}
}

//为内页定义面包屑名称
export const initInnerPagesName = () => {
	let innerPagesRoutePermsMap = {}
	for (let k in permsMap) {
		let item = permsMap[k]
		//内页的判断依据
		if (item.name && item.path && item.showOnMenu === false) {
			innerPagesRoutePermsMap[item.path] = item
		}
	}
	setStorage({ innerPagesRoutePermsMap })
}

//处理cou数据存证上链
export const couDataOnChain = couNoList => {
	let onChainData = ''
	let proofBeans = []
	// 支付提交成功之后马上查询出来的cou就是那个时候生效的cou也就是找零的cou，审核中cou这个时候还为创建完成
	transferModuleApi
		.queryCouListByNoList({ couNoList })
		.then(res => {
			if (res.data && res.data.length > 0) {
				res.data.forEach(element => {
					onChainData = JSON.stringify(filterCouOnChainData(element))
					proofBeans.push({
						couNo: element.couNo,
						originData: onChainData,
					})
				})
				commonModuleApi.setCouProof({ proofBeans }).catch(err => console.log(err))
			}
		})
		.catch(err => console.log(err))
}

export const setFavicon = data => {
	const link: HTMLLinkElement = document.querySelector("link[rel*='icon']") || document.createElement('link')
	link.type = 'image/x-icon'
	link.rel = 'shortcut icon'
	link.href = data
	let headDom = document.getElementsByTagName('head')
	if (headDom && headDom[0]) {
		headDom[0].appendChild(link)
	}
}

//获取当前用户roleItem
export const getCurrentRole = () => {
	const currentRoleId = getStorage('roleId')
	// const currentRoleId = '12';
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')
	return ext?.loginPerm?.roleList?.find(i => i?.roleId === currentRoleId)
}
