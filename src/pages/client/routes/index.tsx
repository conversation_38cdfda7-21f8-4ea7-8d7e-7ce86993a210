import React from 'react'
import { Route, Routes } from 'react-router-dom'
import { cloneDeep } from 'lodash'
import { observer } from 'mobx-react-lite'
import { routeConfig } from './route-config'
import { uiData } from '@factor/store/'
import { HistoryRouter, history } from '@src/utils/router'

/*
重新生成路由树函数;
基于route-config, 实现以下2点功能
1)将没有权限的路由剔除
2)将菜单格式的配置打平 (如果是子路保持子路由格式不变)

返回新的routesTree
@param routes 原始的路由配置数据（源数据）,基于这个数据做修改然后返回
@param frontCodeList 后端返回的资源码列表(权限数据)
@param targetRoutes 如果是子菜单配置的话，需要将路由push到targetRoutes数组里
*/
const getRealRoutesConfig = (routes: RouteItemProps[], frontCodeList, targetRoutes?) => {
	for (let i = 0; i < routes.length; i++) {
		let item = routes[i]
		if (!item) {
			break
		}
		let path = item.path
		/*
		有path一定是路由; 没有path一定不是路由，是目录
		*/
		if (path) {
			if (!frontCodeList.includes(item.permsKey) && item.needValid) {
				routes.splice(i, 1)
				i--
			} else {
				if (targetRoutes) {
					targetRoutes.push(item)
				}
				//检测是否有  子路由
				if (item.children && item.children.length > 0) {
					getRealRoutesConfig(item.children, frontCodeList)
				}
			}
		} else {
			//这是菜单目录，不是路由不能要
			routes.splice(i, 1)
			i--
			// 菜单页面的路由要添加到路由的上一次层,因为他的，父级点没有用到
			if (item.children && item.children.length > 0) {
				getRealRoutesConfig(item.children, frontCodeList, routes)
			}
		}
	}
	return routes
}

// observer为了响应
export default observer(() => {
	//目前通过映射建立假数据
	//后面要改!!
	let frontCodeList = uiData.frontCodeList
	const getComponent = Comp => {
		const loadingStyle = { height: '400px', margin: '0 0 0 45%', lineHeight: '400px' }
		return (
			<React.Suspense
				fallback={
					<div className="spinner-load">
						<div className="loader"></div>
						<p style={{ textAlign: 'center', color: 'rgb(146, 146, 144)' }}>加载中...</p>
					</div>
				}
			>
				<Comp />
			</React.Suspense>
		)
	}

	const getRoutes = (childrenRoutes: RouteItemProps[]) => {
		let routeItemArr: any = []
		let routePathMap = {}
		childrenRoutes.forEach(({ component, path, children, isIndex }, index) => {
			// 路径去重
			if (routePathMap[path]) {
				return
			} else {
				routePathMap[path] = true
			}
			// 没有子路由
			if (!children || children.length === 0) {
				//多存一份route,默认index=0是isIndex是true
				if (isIndex || index === 0) {
					routeItemArr.push(<Route key={path + '_index'} index element={getComponent(component)} />)
				}
				routeItemArr.push(<Route key={path} path={path} element={getComponent(component)} />)
			} else if (children?.length > 0) {
				// 有子路由, route嵌套route
				routeItemArr.push(
					<Route key={path} path={path} element={getComponent(component)}>
						{getRoutes(children)}
					</Route>
				)
			}
		})

		//routeItemArr
		routeItemArr = Array.from(new Set(routeItemArr))
		return routeItemArr
	}

	const AppRouter = HistoryRouter
	let realRoutesConfig = getRealRoutesConfig(cloneDeep(routeConfig.routes), frontCodeList)
	const authedRoutes = getRoutes(realRoutesConfig)

	return (
		<AppRouter history={history}>
			<Routes>{authedRoutes}</Routes>
		</AppRouter>
	)
})
