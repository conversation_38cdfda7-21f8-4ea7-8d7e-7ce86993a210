import React from 'react'
import { allModulesRouteConfig } from '@factor/config/perms/perms-config'
import { homeListRouteConfig } from '@factor/config/perms/perms-home-list'
const OutLayout = React.lazy(() => import(/* webpackChunkName: "outLayout" */ '../layout/out-layout'))
//这个可以挪到layout那边
const Content = React.lazy(() => import(/* webpackChunkName: "content" */ '@factor/views/content/index'))
const Home = React.lazy(() => import(/* webpackChunkName: "home" */ '@factor/views/home'))
const NewsList = React.lazy(() => import(/* webpackChunkName: "news" */ '@factor/views/news/newsList/newsList'))
const NotFound = React.lazy(() => import(/* webpackChunkName: "notFound" */ '@factor/views/not-found'))

interface RouteConfigProps {
	routes: PermItem[]
}

export const routeConfig: RouteConfigProps = {
	routes: [
		{
			path: '/',
			component: OutLayout,
			children: [
				...homeListRouteConfig,
				// {
				// 	path: '/news',
				// 	component: NewsList,
				// },
				{
					path: '/content',
					component: Content,
					children: [
						...allModulesRouteConfig,
						{
							path: '*',
							needValid: false,
							component: NotFound,
						},
					],
				},
				{
					path: '*',
					component: Home,
				},
			],
		},
		{
			path: '*',
			component: Home,
		},
	],
}
