import { Input, message } from 'antd'
import React, { useState } from 'react'
const NumericInput = props => {
	const { value, onChange } = props

	const handleChange = e => {
		const { value: inputValue } = e.target
		const reg = /^-?\d*(\.\d*)?$/
		if (reg.test(inputValue) || inputValue === '' || inputValue === '-') {
			onChange(inputValue)
		}
	}
	// '.' at the end or only '-' in the input box.
	const handleBlur = () => {
		let valueTemp = value
		if (value.charAt(value.length - 1) === '.' || value === '-') {
			valueTemp = value.slice(0, -1)
		}
		if (!(valueTemp > 0)) {
			message.warning('融资申请金额必须大于0！！!')
			// onChange(valueTemp.replace(/0*(\d+)/, '$1'))
			return
		}
		onChange(valueTemp.replace(/0*(\d+)/, '$1'))
	}
	return <Input {...props} onChange={handleChange} onBlur={handleBlur} maxLength={16} />
}
export default NumericInput
