import LayoutSlot from '@src/globalComponents/LayoutSlot'
import { Button, Result, Modal } from 'antd'
import React from 'react'
import { ExclamationCircleOutlined } from '@ant-design/icons'

interface Props {
	/**
	 * @description 点击确定按钮 回调函数
	 */
	onOk: () => void
	/**
	 * @description 点击查看详情的 回调函数
	 */
	onDetail: () => void
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 成功结果的标题描述
	 */
	title: string
	/**
	 * @description 成功结果的小描述文字
	 */
	subTitle: string
	/**
	 * @description 按钮文案  完成  查看
	 */
	btnTxts: [string, string]
	/**
	 * @description Result的状态
	 */
	status?: 'success' | 'error' | 'info' | 'warning' | '404' | '403' | '500'
}

const Index = (props: Props) => {
	const { onOk, onDetail, visible, title, subTitle, btnTxts, status = 'success' } = props

	return (
		<Modal open={visible} footer={null} closable={false}>
			<LayoutSlot>
				<Result
					icon={status === 'warning' ? <ExclamationCircleOutlined /> : ''} // 让警告的图标为圆形
					status={status}
					title={title}
					subTitle={subTitle}
					extra={[
						btnTxts[0] ? (
							<Button onClick={onOk} type="primary" key="over">
								{btnTxts[0]}
							</Button>
						) : null,
						btnTxts[1] ? (
							<Button key="detail" onClick={onDetail}>
								{btnTxts[1]}
							</Button>
						) : null,
					]}
				/>
			</LayoutSlot>
		</Modal>
	)
}

export default Index
