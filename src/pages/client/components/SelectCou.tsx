import React, { ReactElement, useState, useEffect } from 'react'
import { Alert, Table, message, Tooltip } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { transferModuleApi, financeModuleApi, commonModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { renderAmount } from '@src/pages/client/config/TableColumnsRender'
import { timeToStamp } from '@utils/timeFilter'
import { formatNumber } from '@utils/format'
import SearchBar from '@src/pages/client/components/SearchBar'
import BaseDrawer from '@src/globalComponents/BaseDrawer'
import OperatingArea from '@src/globalComponents/OperatingArea'
import styled from 'styled-components'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'

interface Props {
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 用于 回显
	 */
	dataInit: any[]
	/**
	 * @description 页面的类型
	 */
	pageName: pagename
	/**
	 * @description 	退款（开立）中支付记录中的授信方PubKey
	 */
	creditPubKey?: string
	/**
	 * @description 搜索框的类型
	 */
	searchPageName: searchPageName
	/**
	 * @description 表格的类型
	 */
	tablePageName: tablePageName
	/**
	 * @description Alert组件中的提示信息
	 */
	alertMessage?: string
	/**
	 * 仅仅用于创建融信的时候
	 */
	queryType?: number
	/**
	 * @description 退款时的可退金额
	 */
	refundAmountInCent?: number
	/**
	 * @description 提交确定操作
	 */
	onOk: (value: any, selectAmount: any) => void
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
	/**
	 * @description 金额选项卡回调函数
	 */
	amountTabCallback: (selectCouAmount: number, selectCouActualAmount: any) => ReactElement
}

type pagename = 'TRANSFER' | 'FINANCE' | 'PAYREFUND'
type searchPageName = 'selectCouModalF' | 'paySelectAvibleCou' | 'selectCouModalR'
type tablePageName = 'selectCouModel' | 'selectCouModelF'

const SelectCou = (props: Props) => {
	const { visible, dataInit, pageName, searchPageName, tablePageName, creditPubKey, alertMessage, refundAmountInCent, onOk, onCancel, amountTabCallback } =
		props
	const [loading, setLoading] = useState(false)
	const [searchBarObj, setSearchBarObj] = useState<any>({}) // 选择融信弹窗中搜索框中的数据
	const [searchParams, setSearchParams] = useState<any>({}) //searchbar 字段

	const [selectCouAmount, setSelectCouAmount] = useState<number>(0)
	const [selectCouActualAmount, setSelectCouActualAmount] = useState<number>(0)
	const [couDataSource, setCouDataSource] = useState<any[]>([]) // 融信表格中的数据

	const [modifyMap, setModifyMap] = useState<any>({}) // 修改数据的map结构，用于回显（查询、重置、页面大小改变、页码改变）

	useEffect(() => {
		if (visible) {
			let map = {}
			if (dataInit.length) {
				dataInit.forEach(item => {
					map[item.uuid] = item.inputAmountInCent
				})

				setModifyMap(map)
			} else {
				setModifyMap({})
			}

			getCouList(map)
			getPublishAndCreditList()
		}
	}, [visible])

	useEffect(() => {
		if (visible) getCouList(modifyMap)
	}, [searchParams])

	// 得到开立方和授信方数组
	const getPublishAndCreditList = () => {
		financeModuleApi.getPublishAndCreditList().then(
			res => {
				setSearchBarObj(res)
			},
			reason => {
				console.log('reason: ', reason)
			}
		)
	}

	// 得到融信列表
	const getCouList = map => {
		let params = {
			pageSize: 99999999,
			pageNum: 0,
			creditPubKey,
			...searchParams,
		}
		if (props.queryType) {
			//3/30xx要求加的
			params.queryType = props.queryType
		}
		setLoading(true)
		// modified by john 7-11 退款使用新的接口:dev_133
		const reslfunc = res => {
			handleResponse(res, map)
		}
		const rejFunc = reason => {
			console.log('reason: ', reason)
			setLoading(false)
		}
		if (pageName === 'PAYREFUND') {
			transferModuleApi.getNewCouList(params).then(reslfunc, rejFunc)
		} else {
			transferModuleApi.getCouList(params).then(reslfunc, rejFunc)
		}
	}
	//  处理结果函数
	const handleResponse = (res, map) => {
		if (res.list && res.list.length > 0) {
			res.list.forEach(
				(i: { key: any; couNo: any; uuid: string; inputAmountInCent: number; isSelect: boolean; discount: number; actualAmountInYuan: number }) => {
					// 用random做key是为了防止点击重置时react的diff算法对input进行复用，导致input的value不能被清空
					i.key = Math.random()
					// i.key = i.uuid
					//主表单 已经选择的融信 进行回显
					if (map[i.uuid]) {
						// 输入框回显
						i.isSelect = true
						i.inputAmountInCent = map[i.uuid]
						i.actualAmountInYuan = parseFloat(formatNumber.truncation2((map[i.uuid] / 100) * i.discount))
					}
				}
			)
			//计算出已经选的 融信 的金额
			getAmount(res['list'])
			setCouDataSource(res['list'])
		} else {
			getAmount([])
			setCouDataSource([])
		}
		setLoading(false)
	}
	//实时计算出 已经选择的金额 和 还需要选择的金额
	const getAmount = couList => {
		// 过滤数据（保留填入融资使用金额的数据)
		let inputAmountInCent = 0
		let actualAmountInYuan = 0
		const filterDataSource = couList.filter(couItem => {
			return couItem.isSelect
		})
		filterDataSource.forEach(item => {
			inputAmountInCent += item.inputAmountInCent
			actualAmountInYuan += item.actualAmountInYuan
		})
		setSelectCouAmount(inputAmountInCent / 100)
		setSelectCouActualAmount(actualAmountInYuan)
	}
	//设置cent，注意形参cent单位为分
	const setAmount = (cent, record) => {
		if (cent > 0 && cent < record.couAmountInCent) {
			record.inputAmountInCent = cent
			// 先把分转换成元，保留两位小数，截取两位
			record.actualAmountInYuan = parseFloat(formatNumber.truncation2((cent / 100) * record.discount))
			record.isSelect = true
		} else if (cent >= record.couAmountInCent) {
			record.inputAmountInCent = Number(record.couAmountInCent)
			record.actualAmountInYuan = parseFloat(formatNumber.truncation2((record.couAmountInCent / 100) * record.discount))
			record.isSelect = true
		} else {
			record.inputAmountInCent = 0
			record.actualAmountInYuan = undefined
			record.isSelect = false
		}
	}
	const getColumns = () => {
		let title: any = ''
		if (pageName === 'TRANSFER') {
			title = '支付使用金额(￥)'
		} else if (pageName === 'FINANCE') {
			title = (
				<div>
					质押融信金额(￥)&nbsp;&nbsp;
					{/* <ExclamationCircleOutlined style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer' }} /> */}
					{/* <Tooltip placement="top" title="转让融信金额表示您转让给金融机构的应收账款金额">
						<ExclamationCircleOutlined style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer' }} />
					</Tooltip> */}
				</div>
			)
		} else if (pageName === 'PAYREFUND') {
			title = '退款使用金额(￥)'
		}

		// 如果是退款（开立）则表格中是复选框
		const columnsDic = {
			couAmountInCent: {
				title: '融信金额(￥)',
				unit: 13,
			},
			discount: {
				width: 80,
				render: (text: any, record: any) => {
					return text * 100 + '%'
				},
			},
			actualAmountInYuan: {
				width: 150,
				title: (
					<div>
						实际融资金额(￥)&nbsp;&nbsp;
						<Tooltip placement="top" title="实际融资金额，等于您转让给金融机构的应收账款金额*融资比例">
							<ExclamationCircleOutlined style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer' }} />
						</Tooltip>
					</div>
				),
				render: (text: any, record: any) => {
					return renderAmount(record.actualAmountInYuan)
				},
			},
			inputAmountInCent: {
				width: 150,
				unit: 15,
				dataIndex: 'inputAmountInCent',
				title: title,
				render: (text: any, record: any) => {
					return (
						<input
							type="text"
							defaultValue={text ? text / 100 : undefined}
							onChange={event => {
								const value = event.target.value
								let reg = /^(([0-9]*)|([0-9]+\.[0-9]{0,2}))$/
								// 先判断是否满足正则
								if (!reg.test(value)) {
									if (isNaN(Number(value[0]))) {
										// 如果第一个字符非数值，则清空
										event.target.value = ''
										setAmount(0, record)
									} else {
										// 如果刚输入的字符是非数值或者是第三位小数则走到这里
										const nStr: string = value.slice(0, -1)
										event.target.value = nStr
										setAmount(Number.parseFloat(nStr) * 100, record)
									}
								} else {
									// 转换成 分
									const cent = Number.parseFloat(value) * 100
									if (cent) {
										if (cent >= record.couAmountInCent) {
											// 改变输入框的内容为融信金额，输入框的内容为字符串
											event.target.value = record.couAmountInCent / 100 + ''
										}
										setAmount(cent, record)
									} else {
										// 此时输入框的内容为0，此时不确定用户是不是想输入0.x，则这一步不对event.target.value处理
										setAmount(cent, record)
									}
								}
								getAmount(couDataSource)
							}}
							onBlur={event => {
								// 如果融资时选择融信，计算出的实际融资金额为0，那么转让融信金额字段展示0.00，实际融资金额字段展示- -
								if (pageName === 'FINANCE' && !record.actualAmountInYuan) {
									event.target.value = ''
									setAmount(0, record)
									setModifyMap(map => {
										return { ...map, [record.uuid]: 0 }
									})
								}
								if (!Number(event.target.value)) {
									event.target.value = ''
									setAmount(0, record)
									setModifyMap(map => {
										return { ...map, [record.uuid]: 0 }
									})
								} else {
									setModifyMap(map => {
										return { ...map, [record.uuid]: Number(event.target.value) * 100 }
									})
								}
								getAmount(couDataSource)
							}}
							style={{ width: '100px' }}
						/>
					)
				},
			},
		}
		return evenlyTableColunms(getColumnsByPageName(tablePageName, columnsDic))
	}

	// 点击弹窗的确定按钮
	const onClickOK = () => {
		// 过滤数据（保留填入融资使用金额的数据)
		console.log('couDataSource', couDataSource)
		let filterDataSource = couDataSource.filter(couItem => {
			return couItem.actualAmountInYuan > 0
		})
		let hasSameOriginCou = filterDataSource.every(item => item.originalUuid === filterDataSource[0].originalUuid)
		// 过滤完判断选择的是否是同一开立方、同一授信方、同一兑付日期
		if (!hasSameOriginCou) {
			message.warning('只能使用同一个原始融信')
			return
		}
		if (filterDataSource.length !== 0) {
			// let notEqualFlag = true

			// 取第一个融信作为标准
			// const creditName = filterDataSource[0].creditName
			// const publishName = filterDataSource[0].publishName
			// const dueDate = filterDataSource[0].dueDate
			// if (pageName === 'FINANCE') {
			// 	notEqualFlag = filterDataSource.some(couItem => {
			// 		// 如果有一个元素满足条件，则表达式返回true , 剩余的元素不会再执行检测。
			// 		// 如果没有满足条件的元素，则返回false。
			// 		return couItem.creditName !== creditName || couItem.publishName !== publishName || couItem.dueDate !== dueDate
			// 	})
			// } else if (pageName === 'TRANSFER' || pageName === 'PAYREFUND') {
			// 	// 退款（退给供应商）、流转只需要选择的融信为同一开立方即可 新增逻辑：并且 同一 授信方
			// 	notEqualFlag = filterDataSource.some(couItem => {
			// 		return couItem.publishName !== publishName || couItem.creditName !== creditName
			// 	})
			// } else {
			// 	notEqualFlag = false
			// }
			// if (!notEqualFlag) {
			// 	if (pageName === 'PAYREFUND') {
			// 		// 校验金额是否满足关系（针对退款）
			// 		console.log(selectCouAmount * 100, Number(refundAmountInCent))
			// 		if (selectCouAmount * 100 > Number(refundAmountInCent)) {
			// 			message.warning('已选融信金额不可大于支付记录可退金额')
			// 			return
			// 		}
			// 	}

			// 	// 判断授信到期日是否过期
			// 	commonModuleApi.syncTime().then(
			// 		res => {
			// 			const syncTime = new Date(res).getTime()

			// 			let isExpired = false
			// 			for (const couData of filterDataSource) {
			// 				const creditDueDate = new Date(couData.creditDueDate).getTime()
			// 				if (syncTime >= creditDueDate) {
			// 					isExpired = true
			// 					break
			// 				}
			// 			}
			// 			if (isExpired) {
			// 				message.warning('所关联企业授信已到期，提交失败')
			// 			} else {
			// 				// 注意不能把数组的引用传递过去
			// 				onOk([...filterDataSource], selectCouAmount)
			// 			}
			// 		},
			// 		reason => {
			// 			console.log('reason: ', reason)
			// 		}
			// 	)

			// 判断授信到期日是否过期
			commonModuleApi.syncTime().then(
				res => {
					const syncTime = new Date(res).getTime()

					let isExpired = false
					for (const couData of filterDataSource) {
						const creditDueDate = new Date(couData.creditDueDate).getTime()
						if (syncTime >= creditDueDate) {
							isExpired = true
							break
						}
					}
					if (isExpired) {
						message.warning('本企业授信已到期，提交失败')
					} else {
						// 注意不能把数组的引用传递过去
						onOk([...filterDataSource], selectCouAmount)
					}
				},
				reason => {
					console.log('reason: ', reason)
				}
			)
		} else {
			message.warning('请选择融信')
		}
	}
	//search 提交 事件
	const onSubmit = (params?: { due?: any; maxDueDate?: any; minDueDate?: any }) => {
		if (params && params.due && params.due.length === 2) {
			params.maxDueDate = timeToStamp(params.due[1], 'end')
			params.minDueDate = params.due[0]
			delete params.due
		}
		setSearchParams({ ...params })
	}
	//Search 重置
	const onClear = () => {
		setSearchParams({})
	}

	return (
		<BaseDrawer
			visible={visible}
			title={'选择融信'}
			width={pageName === 'FINANCE' ? '1120px' : '1000px'}
			onClose={() => {
				setSelectCouAmount(0)
				setSelectCouActualAmount(0)
				setCouDataSource([])
				onCancel()
			}}
			onOk={onClickOK}
		>
			<Box>
				{alertMessage && <Alert message={alertMessage} type="warning" showIcon style={{ marginBottom: '10px' }} />}
				<OperatingArea>
					<SearchBar
						pageName={searchPageName}
						optionData={{ publishPubKey: searchBarObj.publishPubKeys, creditPubKey: searchBarObj.creditPubKeys }}
						onClear={() => {
							onClear()
						}}
						onSubmit={(params: any) => {
							onSubmit(params)
						}}
					/>
				</OperatingArea>
				{amountTabCallback(selectCouAmount, selectCouActualAmount)}
				<Table columns={getColumns()} dataSource={couDataSource} pagination={false} loading={loading} />
			</Box>
		</BaseDrawer>
	)
}

const Box = styled.div`
	/* padding: 10px 24px; */
	.title {
		padding: 5px 10px;
		display: flex;
		background: #ececec;
		border-radius: 3px;
		margin-bottom: 10px;
		div {
			flex: 1;
		}
	}
`

export default SelectCou
