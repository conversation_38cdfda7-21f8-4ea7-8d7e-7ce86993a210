import PDFViewer from '@globalComponents/PDFViewer'
import { Col, Modal, Row } from 'antd'
import React, { useState } from 'react'
import styled from 'styled-components'
import { getColumns } from '../config/detailColumn'

interface PropsStruck {
	className?: string
	pageName: string
	dataSource: { [str: string]: any }
	title: string
	visible: boolean
	onCancel: () => void
}

function detailModalBox(props: PropsStruck) {
	const [visible, setVisible] = useState(false)
	const [pdfPath, setPdfPath] = useState('')

	const showPDfView = (path: string) => {
		setPdfPath(path)
		setVisible(true)
	}

	const getDetailCfg = () => {
		const fileList = { callback: showPDfView }
		// 设置这个属性props.pageName + 'FileList'是因为，预览合同和发票时的字段不一样
		const columns = getColumns(props.pageName, { [props.pageName + 'FileList']: fileList })
		if (columns && columns.length) {
			return (
				<Row>
					{columns.map((i: any) => {
						return (
							<Col className="item" key={i.key} span={12}>
								<span className="title">{i.title}</span>
								<span className="value">{i.render ? i.render(props.dataSource[i.key], i.callback) : props.dataSource[i.key] || '- -'}</span>
							</Col>
						)
					})}
				</Row>
			)
		}
	}
	return (
		<>
			<Modal
				centered
				width={680}
				open={props.visible}
				title={props.title}
				maskClosable={false}
				onCancel={props.onCancel}
				className={props.className}
				footer={null}
			>
				<div className="content">{getDetailCfg()}</div>
			</Modal>
			<PDFViewer title="" visible={visible} onCancel={() => setVisible(false)} pdfUrl={pdfPath} />
		</>
	)
}

const detailModal = styled(detailModalBox)`
	font-size: 14px;
	.content {
		padding: 0px;
	}
	.title {
		font-family: PingFangSC, PingFangSC-Regular;
		font-weight: 400;
	}
	.value {
		margin-left: 20px;
		flex: 1;
		width: 190px;
		padding-right: 8px;
	}
	.item {
		display: flex;
		margin-bottom: 15px;
	}
`
export default detailModal
