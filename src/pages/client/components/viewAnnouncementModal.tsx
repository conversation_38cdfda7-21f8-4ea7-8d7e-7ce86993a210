import React from 'react'
import { Modal } from 'antd'
import styled from 'styled-components'
import Scrollbars from 'react-custom-scrollbars'
import xss from 'xss'
interface Props {
	/**
	 * 公告的内容
	 */
	data: any
	/**
	 * 关闭公告后调取下一个公告
	 */
	afterClose: (index: number) => void
}

const ViewModal = (props: Props) => {
	const { data, afterClose } = props

	const customTite = () => {
		return (
			<Titlewarp>
				<img src={require('@images/announcement.png')} alt="" />
				<span>公告</span>
			</Titlewarp>
		)
	}

	const defaultWhiteList = (xss as any).whiteList || {}
	const updatedWhiteList = Object.keys(defaultWhiteList).reduce((acc, tag) => {
		acc[tag] = [...(defaultWhiteList[tag] || []), 'style']
		return acc
	}, {})

	let options = {
		whiteList: {
			...defaultWhiteList,
			...updatedWhiteList,
		},
	}

	return (
		<Wrap>
			<Modal
				className="announcement"
				open={data.visible}
				width="700px"
				centered
				title={customTite()}
				bodyStyle={{ padding: '20px' }}
				footer={null}
				onCancel={() => afterClose(data.id)}
			>
				<Scrollbars style={{ height: '322px' }}>
					<div className="resetBox">
						<div
							dangerouslySetInnerHTML={{
								__html: typeof data.content === 'string' ? xss(data.content, options) : '',
							}}
						></div>
					</div>
				</Scrollbars>
			</Modal>
		</Wrap>
	)
}

const Wrap = styled.div``

const Titlewarp = styled.div`
	display: flex;
	align-items: center;
	span {
		font-size: 16px;
		color: rgba(255, 255, 255);
		margin-left: 10px;
	}
	img {
		width: 25px;
		height: 25px;
	}
`

export default ViewModal
