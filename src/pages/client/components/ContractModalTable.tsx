import { getColumnsByPageName } from '../config/TableColumnsConfig'
import { Table } from 'antd'
import { TableProps } from 'antd/lib/table'
import React, { ReactElement, useState } from 'react'
import styled from 'styled-components'
import DetailModal from './detailModal'

//将contract||invoice列表与详情弹窗结合，精简代码
interface Props extends TableProps<any> {
	type: 'contract' | 'invoice'
}
export default function ContractModalTable(props: Props): ReactElement {
	const [detailVisible, setDetailVisible] = useState(false)
	const [selectData, setSelectData] = useState<any>({})

	// refactor by john 两个render函数一致:dev_133
	const renderFunc = (text: any, record: any) => {
		return (
			<span
				title={text}
				className="link"
				onClick={() => {
					setSelectData(record)
					setDetailVisible(true)
				}}
			>
				{text}
			</span>
		)
	}
	function getColumns() {
		if (props.type === 'contract') {
			const columnsDic = {
				contractCode: {
					dataIndex: 'contractCode',
					render: renderFunc,
				},
			}
			return getColumnsByPageName('contract', columnsDic)
		} else {
			const columnsDic = {
				invoiceCode: {
					dataIndex: 'invoiceCode',
					render: renderFunc,
				},
			}
			return getColumnsByPageName('selectInvoice', columnsDic)
		}
	}

	return (
		<Box>
			<Table pagination={false} columns={getColumns()} {...props} />
			<DetailModal
				title={props.type === 'contract' ? '合同详情' : '发票详情'}
				pageName={props.type}
				onCancel={() => setDetailVisible(false)}
				visible={detailVisible}
				dataSource={selectData}
			/>
		</Box>
	)
}

const Box = styled.div``
