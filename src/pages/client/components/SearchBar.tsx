import React, { forwardRef, memo, useEffect, useImperativeHandle, useRef, useState } from 'react'
import { Input, Form, Button, Select, DatePicker, Tooltip } from 'antd'
import SearchConfig from '../config/SearchBarConfig'
import GetCompanyName from '@src/globalComponents/SearchComByName'
import { timeToStamp } from '@utils/timeFilter'
import styled from 'styled-components'
import moment from 'moment'

const FormItem = Form.Item
const Option = Select.Option
const RangePicker = DatePicker.RangePicker

type Config = {
	pageName: string //页面标识
	className?: string
	onClear?: () => void //清空回调处理
	onSubmit?: (params: any) => void //搜索回调处理
	initValues?: { [name: string]: any } //传入回显搜索项
	optionData?: { [name: string]: any } //传入的初始数据
	showLabel?: boolean //是否显示label
}

const SearchBar = memo(
	forwardRef((props: Config, ref: any) => {
		const [form] = Form.useForm()
		const [config, setConfig] = useState<any>([])
		const [getInitValues, setInitValues] = useState<any>({})
		const buttonRef: any = useRef(null)
		const { showLabel } = props

		useImperativeHandle(ref, () => ({
			click: () => {
				buttonRef.current.click()
			},
		}))
		// 得到配置对象
		useEffect(() => {
			setConfig(SearchConfig[props.pageName])
			form.resetFields()
		}, [props.pageName])

		useEffect(() => {
			setInitValues(props.initValues)
		}, [])
		function getComponent() {
			const result: any[] = []
			// refactor by john 2022-7-12:dev_133
			let strategies = {
				input: item => {
					return InputComponent(item)
				},
				select: item => {
					return SelectComponent(item)
				},
				searchSelect: item => {
					return renderSearchSelectComponent(item)
				},
				date: item => {
					return DateComponent(item)
				},
				rangeDate: item => {
					return RangeDateComponent(item)
				},
				inputGroup: item => {
					return InputGroupComponent(item)
				},
				queryByName: item => {
					return queryByName(item)
				},
			}
			if (config && config.length) {
				config.forEach((item: any) => {
					if (item.isHidden) return
					if (strategies[item.component]) {
						result.push(strategies[item.component](item))
					}
				})
			}
			return result
		}
		function InputComponent({
			componentKey,
			label,
			placeholder,
			width,
			maxLength,
		}: {
			componentKey: string
			label: string
			placeholder: string
			width: number
			maxLength: number
		}) {
			const { initValues } = props
			const defaultValue = initValues ? initValues[componentKey] : ''
			return (
				<FormItem initialValue={defaultValue} name={componentKey} key={componentKey} className="input-area" label={showLabel ? label : null}>
					<Input style={{ width: width || 100 }} placeholder={placeholder || '请输入'} maxLength={maxLength ? maxLength : undefined} />
				</FormItem>
			)
		}
		function SelectComponent({
			componentKey,
			label,
			placeholder,
			noAddAll,
			arrData,
			width,
			selectTitle,
		}: {
			componentKey: string
			label: string
			placeholder: string
			arrData: string | Array<any>
			noAddAll: boolean
			width: number
			selectTitle: string
		}) {
			const { initValues, optionData } = props
			const option = arrData || []
			const arr = option && typeof option === 'string' ? (optionData && optionData[option]) || [] : option
			const list = noAddAll ? arr : [{ name: selectTitle, key: 'all' }].concat(arr)
			const defaultValue = initValues && initValues[componentKey] ? initValues[componentKey] : noAddAll ? null : 'all'
			return (
				<FormItem initialValue={defaultValue} name={componentKey} key={componentKey} label={showLabel ? label : null}>
					<Select
						placeholder={placeholder || '请选择'}
						style={{ width: width || 100 }}
						dropdownStyle={{ textAlign: 'left' }}
						getPopupContainer={triggerNode => triggerNode.parentElement}
					>
						{list &&
							list.map((item: { name: string; key: string }) => {
								return (
									<Option title={item.name} value={item.key} key={item.key}>
										{item.name}
									</Option>
								)
							})}
					</Select>
				</FormItem>
			)
		}
		const filterOption = (input, option) => {
			return option['no-title'].toLowerCase().indexOf(input.toLowerCase()) >= 0
		}
		//模糊查询专属
		function renderSearchSelectComponent({
			componentKey,
			label,
			placeholder,
			arrData,
		}: {
			componentKey: string
			label: string
			placeholder: string
			arrData: string | Array<any>
		}) {
			const { initValues, optionData } = props
			const option = arrData || []
			const arr = option && typeof option === 'string' ? (optionData && optionData[option]) || [] : option
			const defaultValue = initValues ? initValues[componentKey] : undefined
			return (
				<FormItem initialValue={defaultValue} name={componentKey} key={componentKey} label={label}>
					<Select
						optionLabelProp="no-title"
						filterOption={filterOption}
						style={{ width: '150px', textAlign: 'left' }}
						showSearch
						placeholder={placeholder || '请输入'}
						dropdownStyle={{ textAlign: 'center' }}
						getPopupContainer={triggerNode => triggerNode.parentElement}
					>
						{arr &&
							arr.map((item: { name: string; key: string }) => {
								return (
									<Option no-title={item.name} value={item.key} key={item.key}>
										<Tooltip placement="left" title={item.name}>
											{item.name}
										</Tooltip>
									</Option>
								)
							})}
					</Select>
				</FormItem>
			)
		}
		// 模糊搜索出现下拉框 VagueSelectComponent
		function queryByName({
			Api,
			valuekey,
			companyType,
			placeholder,
			label,
			componentKey,
			width,
			dropdownMatchSelectWidth,
		}: {
			Api: any
			valuekey: string
			companyType: string
			placeholder: string
			label: string
			componentKey: string
			width: number
			dropdownMatchSelectWidth: number | boolean
		}) {
			return (
				<GetCompanyName
					width={width}
					dropdownMatchSelectWidth={dropdownMatchSelectWidth}
					name={componentKey}
					label={label}
					placeholder={placeholder}
					getCompany={Api}
					companyType={companyType}
					valuekey={valuekey}
					required={false}
				/>
			)
		}
		function DateComponent({ componentKey, label, placeholder, width }: { componentKey: string; label: string; placeholder: string; width: number }) {
			const { initValues } = props
			const defaultValue = initValues && initValues[componentKey] ? moment(initValues[componentKey]) : ''
			return (
				<FormItem name={componentKey} key={componentKey} label={label} initialValue={defaultValue}>
					{/* <DatePicker style={{ width: '200px' }} format="YYYY-MM-DD" placeholder={placeholder} /> */}
					<DatePicker style={{ width: width || 200 }} format="YYYY-MM-DD" placeholder={placeholder} />
				</FormItem>
			)
		}
		function RangeDateComponent({
			componentKey,
			label,
			placeholder,
			width,
		}: {
			componentKey: string
			label: string
			placeholder: [string, string]
			width: number
		}) {
			const { initValues } = props
			const defaultValue =
				initValues && initValues[componentKey]
					? [
							initValues[componentKey][0] ? moment(initValues[componentKey][0]) : undefined,
							initValues[componentKey][1] ? moment(initValues[componentKey][1]) : undefined,
					  ]
					: []
			return (
				<FormItem name={componentKey} initialValue={defaultValue} key={componentKey} label={label}>
					<RangePicker style={{ width: width || 270 }} placeholder={placeholder} format="YYYY-MM-DD" />
				</FormItem>
			)
		}
		function onButtonSubmit() {
			const { onSubmit } = props
			form
				.validateFields()
				.then(values => {
					console.log('values', values)
					const params = { ...values }
					// refactor by john 2022-7-14:dev_133
					let strategies = {
						select: compKey => {
							if (values[compKey] === 'all') delete params[compKey]
						},
						input: compKey => {
							if (values[compKey] === '') delete params[compKey]
						},
						date: compKey => {
							if (values[compKey]) params[compKey] = timeToStamp(params[compKey], 'begin')
						},
						rangeDate: compKey => {
							if (values[compKey]) params[compKey] = [timeToStamp(params[compKey][0], 'begin'), timeToStamp(params[compKey][1], 'begin')]
						},
					}
					config.forEach((item: { component: string; componentKey: string }) => {
						let compStr = item.component,
							compKey = item.componentKey
						if (strategies[compStr]) {
							strategies[compStr](compKey) //对应处理
						}
						if (values[compKey] === null) delete params[compKey]
					})
					console.log('params', params)
					if (onSubmit) {
						onSubmit(params)
					}
				})
				.catch(errorInfo => {
					console.log(errorInfo)
				})
		}
		function clear() {
			const result: { [name: string]: any } = {}
			let emptyCheckMap = {
				select: undefined,
				input: '',
				date: null,
				rangeDate: [],
				searchSelect: undefined,
				queryByName: undefined,
			}

			config.forEach((item: { [name: string]: any }) => {
				// refactor by john 2022-7-14:dev_133
				let compStr = item.component,
					compKey = item.componentKey
				if (!props.initValues) {
					result[compKey] = emptyCheckMap[compStr]
				} else {
					if (props.initValues.needClear) {
						result[compKey] = emptyCheckMap[compStr]
					} else {
						if (['select', 'input', 'date', 'searchSelect', 'rangeDate', 'queryByName'].includes(compStr)) {
							result[compKey] = props.initValues[compKey]
						}
						if (compStr == 'select' && !item['noAddAll']) {
							result[compKey] = 'all'
						}
						if (compStr == 'inputGroup') {
							console.log(getInitValues)
							form.resetFields([getInitValues[compKey].key])
							setInitValues(props.initValues)
						}
					}
				}
			})
			console.log(result)
			form.setFieldsValue(result)
			if (props.onClear) {
				props.onClear()
			}
		}
		return (
			<Box>
				<Form form={form} layout="inline">
					<div className="paramsContainer">{getComponent()}</div>
					<div className="operation">
						<Button type="primary" className="search" onClick={onButtonSubmit}>
							查询
						</Button>
						<Button ref={buttonRef} className="clear" onClick={clear}>
							重置
						</Button>
					</div>
				</Form>
			</Box>
		)
		function InputGroupComponent({
			componentKey,
			label,
			placeholder,
			arrData,
			noAddAll,
			selectTitle,
			width,
		}: {
			width: string
			noAddAll: boolean
			selectTitle: string
			componentKey: string
			label: string
			placeholder: string
			arrData: string | Array<any>
		}) {
			const { optionData } = props
			const option = arrData || []
			const arr = option && typeof option === 'string' ? (optionData && optionData[option]) || [] : option
			const list = noAddAll ? arr : [{ name: selectTitle, key: 'all' }].concat(arr)
			const defaultValue = (getInitValues && getInitValues[componentKey]) || ''
			return (
				<FormItem name={defaultValue.key}>
					<Input.Group compact style={{ display: 'flex' }}>
						<Select
							style={{ width: width || 100 }}
							dropdownStyle={{ textAlign: 'left' }}
							defaultValue={[defaultValue.key]}
							getPopupContainer={triggerNode => triggerNode.parentElement}
							onChange={(value, onChangeOption: any) => {
								form.resetFields([defaultValue.key])
								let data: any = {}
								data.name = onChangeOption.title
								data.key = value
								setInitValues({
									companyType: data,
								})
							}}
						>
							{list &&
								list.map((item: { name: string; key: string }) => {
									return (
										<Option title={item.name} value={item.key} key={item.key}>
											{item.name}
										</Option>
									)
								})}
						</Select>
						<Input
							placeholder={'请输入' + defaultValue.name}
							onChange={value => {
								const name = defaultValue.key
								let data = {}
								data[name] = value.target.value
								form.setFieldsValue(data)
							}}
						/>
					</Input.Group>
				</FormItem>
			)
		}
	})
)

const Box = styled.div`
	margin-left: auto;
	.paramsContainer {
		display: flex;
		.ant-select.status {
			width: 100px;
		}
		.ant-select {
			width: 100px;
			text-align: center;
			.ant-select-selection-selected-value {
				float: none;
			}
		}
	}
	.operation {
		margin-left: auto;
	}
	.search {
		margin-right: 10px;
	}

	.input-area {
		.ant-input {
			// 输入框背景颜色
			background-color: white;
			border: 1px solid #d9d9d9;
		}
	}
`
export default SearchBar
