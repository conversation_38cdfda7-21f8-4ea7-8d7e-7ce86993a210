import { Alert, message } from 'antd'
import React, { useEffect, useState } from 'react'
import { invoicingManageModuleApi } from '@src/pages/client/api'

interface IProps {
	message: string
	description: string
	style?: any
}
const IsFillInvoicingInfoTip = ({ message, description, style }: IProps) => {
	const [show, setShow] = useState(false)
	useEffect(() => {
		invoicingManageModuleApi
			.isFillInvoicingInfo()
			.then(res => {
				if (res) {
					console.log(res)
					let isShow = !res?.fillCompanyInvoicingInfo
					setShow(isShow)
				}
			})
			.catch(err => {
				console.log(err)
			})
	}, [])
	return (
		<React.Fragment>
			{show ? <Alert message={message} description={description} type="warning" showIcon style={style || { marginBottom: '15px' }}></Alert> : null}
		</React.Fragment>
	)
}

export default IsFillInvoicingInfoTip
