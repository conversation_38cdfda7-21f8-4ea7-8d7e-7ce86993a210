import { Alert } from 'antd'
import React, { useEffect, useState } from 'react'
// import { commonModuleApi } from '@src/pages/client/api'
// import { CompanyNameStatusMap } from '@src/globalConfig/codeMaps'

const CompanyNameChangTip = (props: any) => {
	// const [show, setShow] = useState(false)
	// useEffect(() => {
	// 	commonModuleApi
	// 		.getCompanyInfo()
	// 		.then(res => {
	// 			if (res) {
	// 				let isShow = res?.companyNameStatus === CompanyNameStatusMap.anomaly
	// 				setShow(isShow)
	// 			}
	// 		})
	// 		.catch(err => {
	// 			console.log(err)
	// 		})
	// }, [])
	return (
		<React.Fragment>
			{!props?.ishidden ? (
				<Alert message="查询到您的企业名称有变更，请联系运营人员处理" type="warning" description=" " showIcon style={{ marginBottom: '15px' }}></Alert>
			) : null}
		</React.Fragment>
	)
}

export default CompanyNameChangTip
