import React, { useState, useEffect } from 'react'
import { Alert, Table, message } from 'antd'
import BaseDrawer from '@src/globalComponents/BaseDrawer'
import { transferModuleApi, commonModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { renderAmountInCent, renderAmount } from '@src/pages/client/config/TableColumnsRender'
import styled from 'styled-components'

interface Props {
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 提交确定操作
	 */
	onOk: (value: any, selectAmount: any) => void
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
	/**
	 * @description 	退款时的可退金额
	 */
	refundAmountInCent?: number
	/**
	 * @description 	退款（开立）中支付记录中的开立方PubKey
	 */
	publishPubKey?: string
	/**
	 * @description 	退款（开立）中支付记录中的授信方PubKey
	 */
	creditPubKey?: string
	/**
	 * @description 用于 回显
	 */
	dataInit: any[]
	/**
	 * @description 调用的页面
	 */
	pageName: 'CREATEREFUND'
}

const SelectCouCheckbox = (props: Props) => {
	const { visible, onOk, onCancel, refundAmountInCent, publishPubKey, creditPubKey, dataInit, pageName } = props
	const [loading, setLoading] = useState(false)

	const [selectCouAmount, setSelectCouAmount] = useState<number>(0)
	const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]) // 表格中选中的row的key

	const [couDataSource, setCouDataSource] = useState<any[]>([]) // 融信表格中的数据
	const [selectedDataSource, setSelectedDataSource] = useState<any[]>([]) // 表格中选中的数据源

	useEffect(() => {
		if (visible) {
			let map: any = {}
			if (dataInit.length) {
				let arr: any = []
				dataInit.forEach(item => {
					arr.push(item.key)
					map[item.key] = item.inputAmountInCent
				})
				setSelectedRowKeys(arr)
				setSelectedDataSource(dataInit)
			} else {
				setSelectedRowKeys([])
				setSelectedDataSource([])
			}
			getCouList(map)
		}
	}, [visible])

	// 得到融信列表
	const getCouList = map => {
		let params = {
			pageSize: 99999999,
			pageNum: 0,
			publishPubKey,
			creditPubKey,
		}

		setLoading(true)
		transferModuleApi.getNewCouList(params).then(
			res => {
				if (res.list && res.list.length > 0) {
					res.list.forEach((i: { key: any; couNo: any; uuid: string; inputAmountInCent: number; isSelect: boolean }) => {
						// 用random做key是为了防止点击重置时react的diff算法对input进行复用，导致input的value不能被清空
						// i.key = Math.random()
						i.key = i.uuid
						//主表单 已经选择的融信 进行回显
						// if (dataInit && dataInit.length > 0) {
						// 	dataInit.forEach((item: { uuid: string; inputAmountInCent: number }) => {
						// 		if (item.uuid === i.uuid) {
						// 			// 复选框回显
						// 			selectedRowKeys.push(i.key)
						// 		}
						// 	})
						// }
						if (map[i.key]) {
							i.isSelect = true
							i.inputAmountInCent = map[i.key]
						}
					})

					//计算出已经选的 融信 的金额
					const filterDataSource = res['list'].filter(couItem => {
						return couItem.isSelect
					})
					let amountInCent = 0
					filterDataSource.forEach(item => {
						amountInCent += item.inputAmountInCent
					})
					console.log('amountInCent', amountInCent)
					setSelectCouAmount(amountInCent / 100)
					setCouDataSource(res['list'])
				} else {
					setSelectCouAmount(0)
					setCouDataSource([])
				}
				setLoading(false)
			},
			reason => {
				console.log('reason: ', reason)
				setLoading(false)
			}
		)
	}

	const getColumns = () => {
		const columnsDic = {
			couAmountInCent: {
				title: '融信金额(￥)',
			},
		}
		return getColumnsByPageName('selectCouCheckBoxModel', columnsDic)
	}

	// 点击弹窗的确定按钮
	const onClickOK = () => {
		// 整理数据格式，方便在创建退款申请页面的表格中展示（回显）
		selectedDataSource.forEach(cou => {
			cou.isSelecct = true
			cou.inputAmountInCent = cou.couAmountInCent
		})

		let filterDataSource = selectedDataSource

		// 过滤完判断选择的是否是同一开立方、同一授信方、同一兑付日期
		if (filterDataSource.length) {
			if (pageName === 'CREATEREFUND' && selectCouAmount * 100 > Number(refundAmountInCent)) {
				// 校验金额是否满足关系（针对退款）
				message.warning('已选融信金额不可大于支付记录可退金额')
				return
			}

			// 判断授信到期日是否过期
			commonModuleApi.syncTime().then(
				res => {
					let isExpired = false
					const syncTime = new Date(res).getTime()
					for (const couData of filterDataSource) {
						const creditDueDate = new Date(couData.creditDueDate).getTime()
						// 可以不需要遍历，因为cou结果是同一开立方同一授信方，只需比较第一个即可
						// 此处便利比较是为了防止此组件以后被复用，开发者忘记比较所有的授信到期日是否过期
						if (syncTime >= creditDueDate) {
							isExpired = true
							break
						}
					}
					if (isExpired) {
						message.warning('所关联企业授信已到期，提交失败')
					} else {
						// 注意不能把数组的引用传递过去
						onOk([...filterDataSource], selectCouAmount)
					}
				},
				reason => {
					console.log('reason: ', reason)
				}
			)
		} else {
			message.warning('请选择融信')
		}
	}

	// 复选框选中的行发生改变
	const handleSelectChange = (record, selected, selectedRows) => {
		setSelectedRowKeys([record.key])
		setSelectedDataSource(selectedRows)
		setSelectCouAmount(Number(record.couAmountInCent) / 100)
	}

	return (
		<BaseDrawer
			visible={visible}
			title={'选择融信'}
			width="880px"
			onClose={() => {
				setSelectCouAmount(0)
				setCouDataSource([])
				onCancel()
			}}
			onOk={onClickOK}
		>
			<Box>
				<Alert message="每次退款仅可选择一个融信，若需选择多个融信，请发起多次退款申请" type="warning" showIcon style={{ marginBottom: '10px' }} />
				<div className="title">
					<div className="selected">已选融信（退款金额）：￥{renderAmount(selectCouAmount + '')}</div>
					<div className="selected">支付记录可退金额：¥{renderAmountInCent(Number(refundAmountInCent))} </div>
				</div>
				<Table
					rowSelection={{
						selectedRowKeys: selectedRowKeys,
						onSelect: handleSelectChange,
						hideSelectAll: true, // 去掉全选
						type: 'radio',
					}}
					columns={getColumns()}
					dataSource={couDataSource}
					pagination={false}
					loading={loading}
				/>
			</Box>
		</BaseDrawer>
	)
}

const Box = styled.div`
	/* padding: 10px 24px; */
	.title {
		padding: 5px 10px;
		display: flex;
		background: #ececec;
		border-radius: 3px;
		margin-bottom: 10px;
		div {
			flex: 1;
		}
	}
`

export default SelectCouCheckbox
