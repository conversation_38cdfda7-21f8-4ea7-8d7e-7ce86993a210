import React, { useEffect, useState, useRef } from 'react'
import { But<PERSON>, message, Popconfirm, Toolt<PERSON>, Alert } from 'antd'
import { invoiceModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import SearchBar from '@clientComponents/SearchBar'
import DetailModal from '@clientComponents/detailModal'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import OperatingArea from '@src/globalComponents/OperatingArea'
import styled from 'styled-components'
import { findMatchItemByPageHash } from '@src/pages/sp-client/biz/bizIndex'
import { useNavigate } from 'react-router-dom'
import { ViewReasonModal, getInvoicCheckStatusCols, invoiceCheckStatusOptions, invoiceCheckFlagOptions } from '@src/globalBiz/invoiceBizModule'
import { evenlyTableColunms, invalidVal } from '@src/globalBiz/gBiz'
interface Pagination {
	current: number
	pageSize: number
	total: number
}

interface PropsInterface {
	getInvoiceList: Function
}

function Index(props: PropsInterface) {
	const { getInvoiceList } = props
	const [dataSource, setDataSource] = useState<any[]>([])
	const [detailVisible, setDetailVisible] = useState<boolean>(false)
	const [loading, setLoading] = useState<boolean>(false)
	const [invoiceData, setInvoiceData] = useState<any>({})
	const [pagination, setPagination] = useState<Pagination>({ current: 1, pageSize: 10, total: 0 })
	const [buyerOptionData, setBuyerOptionData] = useState<any[]>([])
	const [searchParams, setSearchParams] = useState<any>({})
	const [hasBrokenInvioce, setHasBrokenInvioce] = useState(false)
	const navigator = useNavigate()
	const viewReasonModalRef = useRef(null)

	useEffect(() => {
		getBuyerNames()
	}, [])

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	function getList() {
		setLoading(true)
		console.log('searchParams', searchParams)
		setLoading(true)
		getInvoiceList({
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...searchParams,
		}).then(
			(res: any) => {
				pagination.total = res['total']
				if (res.list && res.list.length) {
					let hasBroken = false
					res.list.forEach((i: { [x: string]: string; key: string }) => {
						i.key = i['invoiceCode'] + '_' + i['invoiceNumber']
						if (i.checkState + '' !== '0') {
							hasBroken = true
						}
					})
					setHasBrokenInvioce(hasBroken)
					setDataSource([...res['list']])
				} else {
					setDataSource([])
				}
				setPagination({ ...pagination })
				setLoading(false)
			},
			err => setLoading(false)
		)
	}

	// 根据当前用户对应公司的uuid，查询买方名
	const getBuyerNames = () => {
		const ext = JSON.parse(localStorage.getItem('ext') || '{}')
		const companyUuid: string = ext.user && ext.user.company ? ext.user.company.uuid : ''
		invoiceModuleApi
			.getBuyerNames({
				companyUuid,
				limit: 0,
			})
			.then(
				(res: any) => {
					console.log(res)
					var arr: any[] = []
					res.forEach((item, index) => {
						// key用于查询，name用于显示
						arr.push({ key: item, name: item })
					})
					setBuyerOptionData(arr)
				},
				err => {
					console.log('err', err)
				}
			)
	}

	const onSubmit = (params: { invoiceCode: string; createTime: string }) => {
		pagination.current = 1
		setSearchParams(params)
	}

	const onClear = () => {
		pagination.current = 1
		setPagination({ ...pagination })
		setSearchParams({})
	}

	const goPage = () => {
		let menuItem = findMatchItemByPageHash()

		navigator('/content/couAssets/uploadInvoice', { state: { menuItem } })
	}

	function handleChange(current: number) {
		pagination.current = current
		setPagination({ ...pagination })
	}

	function handleSizeChange(size: number) {
		console.log(size)
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	const onOperCallback = (operation: string, data: any) => {
		if (operation == 'detail') {
			setInvoiceData({ ...data })
			setDetailVisible(true)
		} else if (operation == 'delete') {
			invoiceModuleApi.deleteInvoice({ uuid: data.uuid }).then(
				res => {
					message.success('删除成功')
					getList()
				},
				err => {
					console.log('err', err)
				}
			)
		}
	}

	const handleClickView = record => {
		viewReasonModalRef?.current?.show({ reason: record?.checkMsg })
	}

	const getColumns = () => {
		const tablePageName = 'invoice'
		const columnsDic = {
			//字段 验证状态  *******
			...getInvoicCheckStatusCols(handleClickView),
			invoiceNumber: {
				dataIndex: 'invoiceNumber',
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text} className="link">
							<span
								onClick={() => {
									onOperCallback('detail', record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			operation: {
				dataIndex: 'operation',
				render: (text: any, record: any) => {
					return (
						<Popconfirm
							title="确认删除发票"
							className="link deleteLink"
							onConfirm={() => {
								onOperCallback('delete', record)
							}}
						>
							删除
						</Popconfirm>
					)
				},
			},
		}

		let colns = getColumnsByPageName(tablePageName, columnsDic)
		// 表格列长度的简写形式
		let columnIndexLengthMap = {
			0: 9,
			1: 10,
			2: 10,
			3: 10,
			4: 9,
			5: 9,
			6: 10,
			7: 11,
		}
		colns = evenlyTableColunms(colns, columnIndexLengthMap)
		return colns
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<Button onClick={goPage} type="primary" className="big-btn">
							上传发票
						</Button>
						<SearchBar
							onClear={onClear}
							optionData={{
								buyerName: buyerOptionData,
								checkFlag: invoiceCheckFlagOptions,
								checkState: invoiceCheckStatusOptions,
							}}
							onSubmit={onSubmit}
							pageName="invoiceList"
						/>
					</OperatingArea>
					<div>
						{hasBrokenInvioce && (
							<Alert message={'您上传的部分发票为异常发票(已作废、冲红、失控等)，异常发票不能用于融资申请，请注意检查'} type="warning" showIcon />
						)}
					</div>
					<BaseTable
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						columns={getColumns()}
					/>
				</div>
			</LayoutSlot>
			<ViewReasonModal ref={viewReasonModalRef} />
			<DetailModal title="查看发票" pageName="invoice" onCancel={() => setDetailVisible(false)} visible={detailVisible} dataSource={invoiceData} />
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.deleteLink {
		color: #f5222d;
	}
`

export default Index
