import React, { useState, useEffect } from 'react'
import { message } from 'antd'
import BaseTable from '@src/globalComponents/BaseTable'
import BaseDrawer from '@src/globalComponents/BaseDrawer'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'

import { renderAmount } from '@src/pages/client/config/TableColumnsRender'
import styled from 'styled-components'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
interface Props {
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 提交确定操作
	 */
	onOk: (any) => void
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
	/**
	 * @description 上级传过来的 信息 用于判断金额
	 */
	payAmount: any
	/**
	 * @description 买方uuid  本企业
	 */
	companyBuyerUuid: string
	/**
	 * @description 卖方uuid   收款方
	 */
	companySellerUuid: string
	/**
	 * @description 用于 回显
	 */
	dataInit: any[]
	//获取合同列表的方法
	getContractList: Function
	/**
	 * 仅仅在开立和支付的时候添加
	 */
	availableAmountLimit
}

const SelectContract = (props: Props) => {
	//this.getBuildList : this.getList
	const { getContractList, visible, onOk, onCancel, payAmount, companyBuyerUuid, companySellerUuid, dataInit } = props
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [dataSource, setDataSource] = useState<any[]>([])
	const [loading, setLoading] = useState(true)
	const [selectedData, setSelectedData] = useState<any>()
	const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]) // 表格中选中的row的key
	useEffect(() => {
		if (visible) {
			// 设置回显
			console.log('dataInit', dataInit)
			if (dataInit.length) {
				setSelectedRowKeys([dataInit[0].key])
				setSelectedData(dataInit[0])
			} else {
				setSelectedRowKeys([])
				setSelectedData({})
			}
			getContractListPageFun()
		}
	}, [visible])

	useEffect(() => {
		if (visible) getContractListPageFun()
	}, [pagination.current, pagination.pageSize, companyBuyerUuid, companySellerUuid])

	const getContractListPageFun = async () => {
		getContractList({
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			companyBuyerUuid,
			companySellerUuid,
			availableAmountLimit: props?.availableAmountLimit || 0,
		}).then(
			(res: any) => {
				pagination.total = res['total']
				if (res.list) {
					res.list.forEach((i: { key: any; id: any }) => {
						i.key = i.id
					})
					setDataSource([...res['list']])
				}
				setLoading(false)
			},
			err => setLoading(false)
		)
	}

	//表格改变 当前页面
	const handleChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	//表格改变 改变 size
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	const handleOnChange = (onSelectedRowKeys, selectedRows) => {
		setSelectedRowKeys(onSelectedRowKeys)
		setSelectedData(selectedRows[0])
	}

	//获取表格列
	const getColumns = () => {
		const columnsDic = {
			/*
			contractCode: {
				fixed: 'left',
			},
			*/
			contractCanUseAmount: {
				dataIndex: 'contractCanUseAmount',
				title: '可用金额',
				render: (text, record) => {
					return renderAmount((Number(record.amount) * 100 - Number(record.usedAmount) * 100) / 100 + '')
				},
			},
		}

		const colns = getColumnsByPageName('couContract', columnsDic)
		const evenlyColns = evenlyTableColunms(colns)
		return evenlyColns
	}

	return (
		<BaseDrawer
			visible={visible}
			title={'选择合同'}
			width="1000px"
			onClose={() => {
				onCancel()
			}}
			onOk={() => {
				if (selectedData.id) {
					//已经选择的合同和表单中支付的金额 进行比较 => 合同不可用，合同可用金额应不小于支付金额。
					const canUseAmount = (Number(selectedData.amount) * 100 - Number(selectedData.usedAmount) * 100) / 100
					if (canUseAmount >= Number(payAmount)) {
						onOk(selectedData)
					} else {
						message.warning('合同不可用，合同可用金额应不小于支付金额')
					}
				} else {
					message.warning('请选择合同')
				}
			}}
		>
			<Box>
				<div style={{ overflowX: 'scroll' }}>
					<BaseTable
						{...pagination}
						columns={getColumns()}
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
						loading={loading}
						dataSource={dataSource}
						rowSelection={{
							type: 'radio',
							onChange: handleOnChange,
							selectedRowKeys: selectedRowKeys,
						}}
						style={{ minWidth: '1500px' }}
					/>
				</div>
			</Box>
		</BaseDrawer>
	)
}

const Box = styled.div`
	/* padding: 10px 24px; */
	.ant-alert {
		margin-bottom: 10px;
	}
`

export default SelectContract
