import React, { useEffect, useRef, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import styled from 'styled-components'
import SearchBar from '@src/pages/client/components/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import { Button, Modal, message } from 'antd'
import { cashModuleApi, transferModuleApi, commonModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import { formatTableData } from '@utils/format'
import TransferPath from './components/transferTree'
import { cashCouStatusList, pledgeStatusList } from '@factor/config/cou/couAssetsConfig'
import PDFViewer from '@globalComponents/PDFViewer'
import { stampToTime, timeToStamp } from '@utils/timeFilter'
import { history } from '@src/utils/router'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import ChainInfoModal from '@src/globalComponents/chainInfoComponents/ChainInfoModal'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import { useLocation } from 'react-router-dom'
import moment from 'moment'

const options = {
	cashStatusList: cashCouStatusList,
	pledgeStatusList,
}

interface CashInterface {
	limitOperator: boolean
}

const Cash = (props: CashInterface) => {
	const { limitOperator } = props
	const [cashPagination, setCashPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [cashTableDataSource, setCashTableDataSource] = useState<any[]>([])
	const [cashLoading, setCashLoading] = useState(false)
	const [transferTreeVisible, setTransferTreeVisible] = useState(false)
	const [pathData, setPathData] = useState<any>({})
	const [exportLoading, setExportLoading] = React.useState(false)
	const [pdfVisible, setPdfVisible] = useState(false)
	const [path, setPath] = useState('')
	const { pubKey } = JSON.parse(localStorage.getItem('ext') || '{}').user?.company || {} //该用户的公司名称和类型
	const [chainInfoModalVisible, setChainInfoModalVisible] = useState<boolean>(false)
	const [onChainData, setOnChainData] = useState({})
	const location = useLocation()
	const state = location?.state as any
	const dueDate = state?.dueDate
	const cashStatusList = state?.cashStatusList
	const [searchParams, setSearchParams] = useState<any>({ dueDateStart: dueDate?.[0], dueDateEnd: dueDate?.[1], cashStatusList })

	useEffect(() => {
		getOriginanlList()
	}, [cashPagination.current, cashPagination.pageSize, searchParams])

	const getOriginanlList = () => {
		setCashLoading(true)
		transferModuleApi
			.getCashList({
				pageNum: cashPagination.current,
				pageSize: cashPagination.pageSize,
				publishPubKey: pubKey,
				cashStatusList: ['NO_CASH', 'WAIT_CASH', 'CASH_OK'],
				couStatusList: ['AVAILABLE', 'SPLIT', 'TRANSFERRING'],
				...searchParams,
				limitOperator: limitOperator,
			})
			.then(result => {
				if (result) {
					cashPagination.total = result['total']
					if (result.list) {
						setCashTableDataSource(formatTableData.addKey(result['list']))
					} else {
						setCashTableDataSource([])
					}
				}
				setCashLoading(false)
			})
			.catch(() => {
				setCashLoading(false)
			})
	}

	// 得到老数据付款明细的path
	const getOldPayDetailPath = async uuid => {
		const res = await cashModuleApi.getOldPayDetailPath({ uuid }).catch(err => {
			console.log('err', err)
		})
		if (res) {
			setPath(res)
			setPdfVisible(true)
		}
	}

	//search 提交 事件
	const onSubmit = (params: any) => {
		if (params['cashStatusList']) {
			params['cashStatusList'] =
				Object.prototype.toString.call(params['cashStatusList']) === '[object Array]'
					? params['cashStatusList']
					: params['cashStatusList'].includes(',')
					? params['cashStatusList'].split(',')
					: [params['cashStatusList']]
		}
		if (params && params.createDate && params.createDate.length === 2) {
			params.endDate = timeToStamp(params.createDate[1], 'end')
			params.startDate = params.createDate[0]
			delete params.createDate
		}
		if (params && params.dueDate && params.dueDate.length === 2) {
			params.dueDateEnd = params.dueDate[1]
			params.dueDateStart = params.dueDate[0]
			delete params.dueDate
		}

		cashPagination.current = 1
		setSearchParams({ ...params })
	}

	//Search 重置
	const onClear = () => {
		cashPagination.current = 1
		setSearchParams({})
	}

	//查看数据详情
	const checkdetail = values => {
		const { transferUuid } = values
		history.push('/content/couTransfer/payDetails', {
			DetailForTransfer: transferUuid,
		})
	}

	const showTransferTree = record => {
		cashModuleApi.getTransferPath({ couUuidList: [record.uuid] }).then(
			result => {
				let data = {
					nodes: result[0].nodes,
					edges: result[0].edges,
				}
				setPathData(data)
				setTransferTreeVisible(true)
			},
			err => {
				console.log('err', err)
			}
		)
	}
	const getCashTableColumns = () => {
		const columnsDic = {
			couAmountInCent: {
				title: '金额(￥)',
				width: 75,
			},
			payInfo: {
				title: '支付信息',
				render: (text, record) => {
					return (
						<React.Fragment>
							<span className="link" style={{ marginRight: '20px' }} onClick={() => checkdetail(record)}>
								详情
							</span>
						</React.Fragment>
					)
				},
			},
			operation: {
				title: '查看',
				render: (text, record) => {
					return (
						<React.Fragment>
							<span
								className="link"
								onClick={() => {
									showTransferTree(record)
								}}
							>
								流转路径
							</span>
							<br />
							{record.cashStatus !== 'NO_CASH' && (
								<span
									className="link"
									onClick={() => {
										// 兼容老数据
										if (record.payDetailFileUrl) {
											setPath(record.payDetailFileUrl)
											setPdfVisible(true)
										} else {
											getOldPayDetailPath(record.uuid)
										}
									}}
								>
									付款明细
								</span>
							)}
						</React.Fragment>
					)
				},
			},
			onChainCertificate: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => showCertificate(record)}>
							查看证书
						</span>
					)
				},
			},
		}

		let colns = getColumnsByPageName('cashSearch', columnsDic)
		// 表格列长度的简写形式
		let columnIndexLengthMap = {
			0: 13,
			1: 6,
			2: 6,
			3: 9,
			4: 12,
			5: 13,
			9: 5,
		}
		colns = evenlyTableColunms(colns, columnIndexLengthMap)
		return colns
	}

	const showCertificate = record => {
		commonModuleApi
			.queryCouProof({ couNo: record.couNo })
			.then(data => {
				if (data && data.status === 'DONE') {
					setOnChainData(data)
					setChainInfoModalVisible(true)
				} else {
					message.warning('证书未生成，完成审批且上链后生成证书')
				}
			})
			.catch(err => {
				console.log(err)
			})
	}

	function handleCashTableChange(current: number) {
		cashPagination.current = current
		setCashPagination({ ...cashPagination })
	}
	function handleCashTableSizeChange(size: number) {
		cashPagination.current = 1
		cashPagination.pageSize = size
		setCashPagination({ ...cashPagination })
	}
	//导出融信信息
	const cashExportCouCsv = async () => {
		setExportLoading(true)
		let serverTime = await commonModuleApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`
		transferModuleApi
			.exportCashList({
				publishPubKey: pubKey,
				cashStatusList: ['NO_CASH', 'WAIT_CASH', 'CASH_OK'],
				couStatusList: ['AVAILABLE', 'SPLIT', 'TRANSFERRING'],
				...searchParams,
				pageNum: 1,
				pageSize: 2147483646,
				limitOperator: limitOperator,
			})
			.then((response: any) => {
				let type = 'application/vnd.ms-excel'
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar
							showLabel={false}
							pageName={'cashSearch'}
							optionData={options}
							initValues={{ dueDate: [dueDate?.[0], dueDate?.[1]], cashStatusList, needClear: true }}
							onClear={onClear}
							onSubmit={onSubmit}
						/>
						{/* <Button style={{ marginLeft: '20px' }} type="primary" onClick={cashExportCouCsv} loading={exportLoading}>
							导出
						</Button> */}
					</OperatingArea>
					<BaseTable
						columns={getCashTableColumns()}
						loading={cashLoading}
						dataSource={cashTableDataSource}
						{...cashPagination}
						onPageChange={handleCashTableChange}
						onSizeChange={handleCashTableSizeChange}
					/>
				</div>
			</LayoutSlot>
			<Modal
				title="查看流转路径"
				open={transferTreeVisible}
				onCancel={() => {
					setTransferTreeVisible(false)
				}}
				footer={null}
				bodyStyle={{ height: '70vh', overflow: 'scroll', padding: 0 }}
				width="960px"
			>
				<div className="chart">
					<TransferPath data={pathData} />
				</div>
			</Modal>
			<PDFViewer title="付款明细" visible={pdfVisible} pdfUrl={path} onCancel={() => setPdfVisible(false)} />
			<ChainInfoModal visible={chainInfoModalVisible} onCancel={() => setChainInfoModalVisible(false)} chainData={onChainData} />
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.options_btn {
		span {
			display: inline-block;
			margin: 0 20px;
		}
	}
`

export default Cash
