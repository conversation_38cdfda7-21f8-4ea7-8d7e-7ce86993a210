import React, { useEffect, useState } from 'react'
import { Button, DatePicker, Form, Input, message, Modal, Upload, Tooltip } from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import styled from 'styled-components'
import moment from 'moment'
import GetCompanyName from '@src/globalComponents/SearchComByName'
import OperatingArea from '@src/globalComponents/OperatingArea'
import PDFViewer from '@src/globalComponents/PDFViewer'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import DetailModal from '@clientComponents/detailModal'
import SearchBar from '@clientComponents/SearchBar'
import { contractModuleApi } from '@src/pages/client/api'
import { timeToStamp, preDisabledDate } from '@utils/timeFilter'
import { formatUrl } from '@utils/format'
import fileUploading from '@src/globalStore/fileUploading'
import { observer } from 'mobx-react-lite'
import { getUploadStatusValidator, customRequest } from '@src/globalBiz/gBiz'
import { useLocation } from 'react-router-dom'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'

const FormItem = Form.Item

interface Pagination {
	current: number
	pageSize: number
	total: number
}

interface PropsInterface {
	getContractList: Function
}

function Index(props) {
	const { getContractList } = props
	const location = useLocation()
	const querystring = new URLSearchParams(location?.search)

	const [dataSource, setDataSource] = useState<any[]>([]) // 表格中的数据
	const [pagination, setPagination] = useState<Pagination>({ current: 1, pageSize: 10, total: 0 })
	const [searchParams, setSearchParams] = useState<any>({})
	// 处理pdf预览
	const [path, setPath] = useState<string>('')
	const [pdfVisible, setPdfVisible] = useState<boolean>(false)
	const [visible, setVisible] = useState<boolean>(false)
	const [detailVisible, setDetailVisible] = useState<boolean>(false)
	const [confirmLoading, setConfirmLoading] = useState<boolean>(false)
	const [loading, setLoading] = useState<boolean>(false)
	const [action, setAction] = useState<string>('') // 新建合同和修改合同
	const [contractData, setContractData] = useState<any>({
		contractCode: '', // 合同编号
		name: '', // 合同名称
		companySellerName: '',
		companyBuyerName: '',
		productName: '', // 产品名称
		amount: '', // 合同金额
		startDate: [], // 开始时间
		endDate: [], // 结束时间
		signDate: '', // 签订日期
	}) // 用于比较新建的合同买卖方不能为同一家
	const [buyercompanyObj, setBuyerCompanyObj] = useState<any>({}) // 公司买方数组
	const [sellercompanyObj, setSellerCompanyObj] = useState<any>({}) // 公司卖方数组
	const [companyInfo, setCompanyInfo] = useState<any>({})
	const [contractFileList, setFileList] = useState<any[]>([])
	const [form] = Form.useForm()
	const state = location?.state as any

	// 得到买方信息也就是公司信息
	useEffect(() => {
		// 得到卖方信息
		const ext: any = JSON.parse(localStorage.getItem('ext') || '{}')
		const compInfo: any = ext.user && ext.user.company ? ext.user.company : null
		if (compInfo) {
			setCompanyInfo(compInfo)
		}
		// 进入自带add=true的querystring时打开新建合同，可选自带卖方企业
		if (querystring?.get('add') === 'true' || state?.add) {
			const sellerName = querystring?.get('companySellerName')
			const sellerId = querystring?.get('companySellerId')
			if (sellerName && sellerId) {
				setSellerCompanyObj({ id: sellerId, orgName: sellerName })
			}
			handleClick({ companySellerName: sellerId })
		}
	}, [])

	// 每页数量、第几页、查询参数内容变化时，重新调用得到合同列表
	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	// 得到合同列表
	const getList = () => {
		setLoading(true)
		//通过页面参数，区分接口
		getContractList({
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			...searchParams,
		}).then(
			(res: any) => {
				pagination.total = res['total']
				if (res.list && res.list.length > 0) {
					res.list.forEach((i: { key: any; id: any }) => {
						i.key = i.id
					})
					setDataSource([...res['list']])
				} else {
					setDataSource([])
				}
				setLoading(false)
			},
			err => setLoading(false)
		)
	}
	// 点击重置按钮
	const onClear = () => {
		setSearchParams({})
	}

	// 处理页面的页数改变
	function handleChange(current: number) {
		pagination.current = current
		setPagination({ ...pagination })
	}

	// 处理页面的大小改变
	function handleSizeChange(size: number) {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	// 修改和查看详情对应的点击按钮
	const onOperCallback = (operation: string, data: any) => {
		if (operation == 'detail') {
			setDetailVisible(true)
			setContractData({ ...data })
		} else if (operation == 'modify') {
			setAction('modify')
			setContractData(data)

			// 设置回显key
			setBuyerCompanyObj({ id: data['companyBuyerUuid'], orgName: data['companyBuyerName'] })
			setSellerCompanyObj({ id: data['companySellerUuid'], orgName: data['companySellerName'] })
			form.setFieldsValue({
				contractCode: data['contractCode'],
				name: data['name'],
				companySellerName: data['companySellerUuid'],
				companyBuyerName: data['companyBuyerUuid'],
				productName: data['productName'],
				amount: String(data['amount']),
				startEndDate: [moment(data['startDate']), moment(data['endDate'])],
				signDate: moment(data['signDate']),
				// 上传文件的回显
				fileUrl: data['fileUrl'],
			})
			const fileArr = JSON.parse(data['fileUrl'] || '[]')
			setFileList(
				fileArr.map((item: any) => {
					for (const key in item) {
						// 这种情况相同的文件名uid会重复，不唯一
						// return { uid: key, name: key, url: item[key], status: 'done' }
						return { uid: key + Math.random(), name: key, url: item[key], status: 'done' }
					}
				}) || []
			)
			setVisible(true)
		}
	}

	const getColumns = () => {
		const columnsDic = {
			contractCode: {
				dataIndex: 'contractCode',
				render: (text: any, record: any) => {
					return (
						<Tooltip placement="topLeft" title={text} className="link">
							<span
								onClick={() => {
									onOperCallback('detail', record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			operation: {
				dataIndex: 'operation',
				title: '操作',
				render: (text: any, record: any) => {
					return (
						<span
							className="link"
							onClick={() => {
								onOperCallback('modify', record)
							}}
						>
							修改
						</span>
					)
				},
			},
		}

		let colns = getColumnsByPageName('contractList', columnsDic)
		// 表格列长度的简写形式
		let columnIndexLengthMap = {
			0: 15,
			1: 13,
			2: 18,
			3: 18,
			6: 7,
		}
		colns = evenlyTableColunms(colns, columnIndexLengthMap)
		return colns
	}

	// 点击新建合同
	const handleClick = (init = null) => {
		const ext: any = JSON.parse(localStorage.getItem('ext') || '{}')
		const company = ext?.user?.company
		setBuyerCompanyObj({ id: company?.id, orgName: company?.orgName }) // 默认买方为自身企业
		form.setFieldsValue({
			amount: undefined,
			contractCode: undefined,
			companySellerName: init?.companySellerName || undefined,
			companyBuyerName: company?.id,
			name: undefined,
			productName: undefined,
			signDate: undefined,
			startEndDate: undefined,
		})
		setContractData({})
		setFileList([])
		setVisible(true)

		setAction('new')
	}

	//新建/修改合同
	const createAndModifyContract = (values: any) => {
		console.log('新建和修改合同的values:', values)
		if (values['companyBuyerName'] === values['companySellerName']) {
			message.error('买方和卖方不能为同一公司')
			setConfirmLoading(false)
			return
		}
		const params: any = {}
		const curSignDate: number = timeToStamp(values['signDate'], 'begin')
		const curStartDate: number = timeToStamp(values['startEndDate'][0], 'begin')
		const curEndDate: number = timeToStamp(values['startEndDate'][1], 'begin')
		const fileArr: any = []
		contractFileList.forEach((i: any) => {
			if (i.url) {
				fileArr.push({ [i.name]: i.url })
			} else if (i.response?.data) {
				fileArr.push({ [i.name]: i.response?.data })
			}
		})
		if (!fileArr.length) {
			setConfirmLoading(false)
			return message.warning('请上传合同')
		}
		params['companySellerUuid'] = values['companySellerName']
		params['companyBuyerUuid'] = values['companyBuyerName']

		params['contractCode'] = values['contractCode']
		params['name'] = values['name']
		params['productName'] = values['productName']
		params['signDate'] = curSignDate
		params['startDate'] = curStartDate
		params['endDate'] = curEndDate
		params['amount'] = Number(values['amount']).toFixed(2)
		params['fileUrl'] = JSON.stringify(fileArr)
		setConfirmLoading(true)
		if (contractData['uuid']) {
			params['uuid'] = contractData.uuid
			contractModuleApi.modify(params).then(
				res => {
					form.resetFields()
					setFileList([])
					getList()
					setVisible(false)
					setContractData({})
					setConfirmLoading(false)
					message.success('修改成功')
				},
				() => {
					setConfirmLoading(false)
				}
			)
		} else {
			contractModuleApi.create(params).then(
				res => {
					form.resetFields()
					setFileList([])
					getList()
					setVisible(false)
					setContractData({})
					setConfirmLoading(false)
					message.success('创建成功')
				},
				() => {
					setConfirmLoading(false)
				}
			)
		}
	}

	// 新建和修改合同 的 确定和取消按钮
	const handleOk = () => {
		setConfirmLoading(true)
		form
			.validateFields()
			.then(
				values => {
					// if (!companyInfo['pubKey']) {
					// 	message.warning(`操作失败，${companyInfo['name']}尚未进行企业管理员注册`)
					// 	setConfirmLoading(false)
					// 	return
					// } // 2024/3/15 先注释 方便i调试
					if (!contractFileList || !contractFileList.length) {
						message.warning('请上传合同扫描件')
						setConfirmLoading(false)
						return
					}
					createAndModifyContract(values)
				},
				err => {
					setConfirmLoading(false)
				}
			)
			.finally(() => {
				setConfirmLoading(false)
			})
	}
	const handleCancel = () => {
		form.resetFields()
		setVisible(false)
	}

	// 上传pdf的预览函数
	const handlePreview = (file: any) => {
		// console.log('onPreview->file', file)
		setPath(file.url)
		setPdfVisible(true)
	}
	const handleBeforeUpload = file => {
		// 如果文件列表的长度大于等于5则不能上传
		if (contractFileList.length >= 5) {
			message.error('最多只能上传5个文件')
			return Upload.LIST_IGNORE
		}
		const ext = file.name.split('.').pop().toLowerCase()
		const isPdf = ext === 'pdf'
		if (!isPdf) {
			message.error('文件格式错误，只支持pdf格式')
			return Upload.LIST_IGNORE
		}
		const isLt10M = file.size / 1024 / 1024 <= 10
		if (!isLt10M) {
			message.error('文件大小不能超过10M')
			return Upload.LIST_IGNORE
		}
		return Promise.resolve(file)
	}
	const handleUpload = (info: any) => {
		if (info?.file?.status) {
			const { file } = info
			const { status, uid } = file

			if (uid) {
				if (status === 'uploading') {
					fileUploading.addUploadingFile(uid)
				} else {
					fileUploading.removeUploadingFile(uid)
				}
			}
		}
		if (info.fileList && Array.isArray(info.fileList)) {
			let newFileList = []
			info.fileList.forEach(item => {
				if (!item.url && item.response?.data) {
					item.url = item.response?.data
				}
				newFileList.push(item)
			})
			setFileList(newFileList)
		}
	}

	const onSubmit = params => {
		// 待优化
		pagination.current = 1
		// setPagination({ ...pagination })
		setSearchParams(params)
	}

	// 解构出来对应的属性
	const {
		contractCode = '', // 合同编号
		name = '', // 合同名称
		productName = '', // 产品名称
		amount = '', // 合同金额
		startDate = [], // 开始时间
		endDate = [], // 结束时间
		signDate = '', // 签订日期
	} = contractData

	useEffect(() => {
		// 初始化上传loading个数为0
		if (visible) {
			fileUploading.init()
		}
	}, [visible])

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<Button className="big-btn" onClick={handleClick} type="primary">
							新建合同
						</Button>
						<SearchBar onClear={onClear} onSubmit={onSubmit} pageName="contractList" showLabel={false} />
					</OperatingArea>
					<BaseTable
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						columns={getColumns()}
					/>
				</div>
			</LayoutSlot>
			<DetailModal title="查看合同" pageName="contract" onCancel={() => setDetailVisible(false)} visible={detailVisible} dataSource={contractData} />
			<PDFViewer title="" visible={pdfVisible} pdfUrl={path} onCancel={() => setPdfVisible(false)} />
			<Modal
				confirmLoading={confirmLoading || fileUploading.uploadingNum > 0}
				onOk={handleOk}
				bodyStyle={{ paddingBottom: 0 }}
				style={{ top: 180 }}
				width={860}
				open={visible}
				maskClosable={false}
				title={action === 'modify' ? '修改合同' : '新建合同'}
				onCancel={handleCancel}
			>
				<ModalBox>
					<Form form={form} className="form" layout="vertical">
						<ul className="form-list contract-form-list">
							<FormItem
								className="form-item"
								name="contractCode"
								label="合同编号"
								initialValue={contractCode}
								rules={[
									{
										required: true,
										message: '请输入合同编号',
										whitespace: true,
									},
								]}
							>
								<Input placeholder="请输入合同编号" maxLength={50} />
							</FormItem>
							<FormItem
								className="form-item"
								name="name"
								label="合同名称"
								initialValue={name}
								rules={[
									{
										required: true,
										message: '请输入合同名称',
										whitespace: true,
									},
								]}
							>
								<Input placeholder="请输入合同名称" maxLength={100} />
							</FormItem>

							<GetCompanyName
								name="companyBuyerName"
								label="买方名称"
								placeholder="请输入买方名称"
								getCompany={contractModuleApi.modsearchByName}
								requireMsg="请选择买方"
								previewData={buyercompanyObj}
								valuekey="id"
								companyType="C,S"
								key="companyBuyerName"
							/>

							<GetCompanyName
								name="companySellerName"
								label="卖方名称"
								placeholder="请输入卖方名称"
								getCompany={contractModuleApi.modsearchByName}
								requireMsg="请选择卖方"
								previewData={sellercompanyObj}
								valuekey="id"
								companyType="C,S"
								key="companySellerName"
							/>
							<FormItem
								className="form-item"
								name="productName"
								label="产品名称"
								initialValue={productName}
								rules={[
									{
										required: true,
										message: '请输入产品名称',
										whitespace: true,
									},
								]}
							>
								<Input placeholder="请输入产品名称" maxLength={100} />
							</FormItem>
							<FormItem
								name="amount"
								label="合同金额"
								initialValue={amount}
								className="form-item"
								rules={[
									{
										required: true,
										pattern: /^(?!0+(?:\.0+)?$)(?:[1-9]\d{0,11}|0)(?:\.\d{1,2})?$/,
										max: 12,
										message: '请输入合同金额，整数最多12位，小数最多2位',
										whitespace: true,
									},
								]}
							>
								<Input placeholder="请输入合同金额" />
							</FormItem>
							<FormItem
								name="startEndDate"
								label="起止日期"
								className={action === 'new' ? 'new form-item' : 'edit form-item'}
								initialValue={contractData.length ? [moment(startDate), moment(endDate)] : null}
								rules={[{ required: true, message: '请设置起止日期' }]}
							>
								<DatePicker.RangePicker format="YYYY-MM-DD" getPopupContainer={(triggerNode: any) => triggerNode.parentNode} />
							</FormItem>
							<FormItem
								name="signDate"
								className="form-item"
								label="签订日期"
								initialValue={contractData.length ? moment(signDate) : null}
								rules={[{ required: true, message: '请设置签订日期' }]}
							>
								<DatePicker format="YYYY-MM-DD" disabledDate={preDisabledDate} getPopupContainer={(triggerNode: any) => triggerNode.parentNode} />
							</FormItem>
							<div className="line"></div>
							<FormItem
								name="fileUrl"
								label="合同扫描件"
								className="form-item"
								rules={[
									{
										required: true,
										message: '请上传合同文件',
									},
									{
										validator: getUploadStatusValidator('文件上传失败'),
									},
								]}
							>
								<Upload
									accept=".pdf"
									headers={{ 'wx-gw-target-system': 'factor' }}
									beforeUpload={handleBeforeUpload}
									onChange={handleUpload}
									data={{ type: 'transfer' }}
									action={formatUrl('/pledge-config/common/uploadFile')}
									fileList={contractFileList}
									customRequest={customRequest}
									onPreview={handlePreview}
								>
									<div className="fileBox">
										<Button icon={<UploadOutlined />}>上传</Button>
										<p style={{ color: '#7F7F7F' }}>pdf格式，最多5个文件，单个文件不超过10M。</p>
									</div>
								</Upload>
							</FormItem>
						</ul>
					</Form>
				</ModalBox>
			</Modal>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
`

const ModalBox = styled.div`
	.fileBox {
		display: flex;
		line-height: 32px;
		p {
			margin-left: 5px;
		}
	}
	// 起止时间中间的符号居中显示
	.new {
		.ant-picker-range-separator {
			position: relative;
			left: -18px;
		}
	}
	.edit {
		.ant-picker-range-separator {
			position: relative;
			left: -10px;
		}
	}

	.form {
		//间距调整 form
		.form-list {
			padding: 0 8px;
		}
		.ant-upload-list-item-info .ant-upload-text-icon .anticon,
		.ant-upload-list-item-card-actions .anticon {
			color: #1890ff;
		}

		.ant-upload-list-item-name {
			color: #1890ff;
			cursor: pointer;
		}

		.ant-upload-list-item-error .ant-upload-list-item-name,
		.ant-upload-list-item-error .ant-upload-text-icon .anticon,
		.ant-upload-list-item-error .ant-upload-list-item-card-actions .anticon {
			color: #ff4d4f;
		}

		.ant-upload-list-item-card-actions-btn {
			opacity: 1;
		}
	}
	.contract-form-list {
		background: #fff;
	}
	.contract-form-list .form-item {
		width: 44%;
	}
	.contract-form-list .form-item:nth-child(2n + 1) {
		padding-right: 0;
		margin-right: 2%;
	}
	.contract-form-list .form-item:nth-child(2n) {
		margin-left: auto;
	}
	.contract-form-list .line {
		height: 1px;
		width: 100%;
		border-bottom: 1px solid #f0f0f0;
		margin-bottom: 20px;
	}
	.contract-form-list .ant-upload-list {
		width: 50%;
	}
	.contract-form-list .ant-picker-range,
	.ant-picker {
		width: 100%;
	}
`

export default observer(Index)
