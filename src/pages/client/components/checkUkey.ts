import { commonModuleApi, financeModuleApi } from '@src/pages/client/api'
import { getCurrentRole } from '@src/pages/client/biz/bizIndex'
import { GetServiceCertList, initPowerSign, GetServiceSignData, GetServiceSignCert } from '@src/utils/ukey/writeObject'

let timeout

enum checkUkeyErrorStatus {
	UNDETECTED = 'UNDETECTED',
	NOTTHESAME = 'NOTTHESAME',
}

const ext = JSON.parse(localStorage.getItem('ext') || '{}')

let isCheckUkey = false

const clearTimeout = () => {
	if (timeout) {
		clearInterval(timeout)
		timeout = null
	}
}

const getUkeyNo = str => {
	if (str) {
		const pattern = /CN=(\w+),/
		const match = str.match(pattern)
		return match ? match[1] : ''
	}
	return ''
}

const getUkeyNumber = (str, index) => {
	if (str) {
		let numlist = str.split(';')
		if (numlist && numlist[index]) {
			return numlist[index].split(',')[1]
		}
		return ''
	}
	return ''
}

const getPollUkey = (checkItem, checkItems, ukeyChoose, callback) => {
	return new Promise((resolve, reject) => {
		clearTimeout()
		timeout = setInterval(() => {
			let dtd = $.Deferred()

			$.when(GetServiceCertList(dtd)).done(function (info) {
				if (!info || !checkItem || !checkItems) {
					callback({ type: 'choose', validity: 'invalid', errorUkeyModal: true, ukeyArray: [] })
					clearTimeout()
					return
				}

				let errorUkeyArray = []
				let keyList = []
				const map = new Map()
				let tmpList = info.split(';')
				tmpList = tmpList.filter(item => !!item)
				tmpList.forEach((item, index) => {
					const tempArray = item.split(',')
					if (!keyList.includes(tempArray[0])) {
						errorUkeyArray.push({ key: index, mediaNbr: tempArray[0] })
						keyList.push(tempArray[0])
					}
					const tempItem = map.get(tempArray[0])
					map.set(tempArray[0], !!tempItem ? tempItem.concat(';').concat(item) : item)
				})

				keyList = keyList.filter(item => item.startsWith('CN=JRCBHB') || item.startsWith('CN=JRCBFTG'))
				keyList = keyList.filter(item => !item.includes(checkItem?.ukeyNo))
				keyList = keyList.filter(item => checkItems.some(it => item.includes(it?.mediaNbr)))

				if (keyList.length !== 1) {
					if (!ukeyChoose) {
						const tempInfo = info.replaceAll(checkItem?.ukeyNo, '')
						let isFindUkey = !checkItems.some(item => tempInfo.includes(item?.mediaNbr))
						if (isFindUkey) {
							callback({ type: 'choose', validity: 'invalid', errorUkeyModal: true, ukeyArray: errorUkeyArray })
						} else {
							callback({ type: 'choose', validity: 'valid', ukeyChooseModal: true, ukeyArray: errorUkeyArray })
						}
						clearTimeout()
						return
					} else {
						info = map.get(ukeyChoose)
					}
				} else {
					info = map.get(keyList[0])
				}

				// 若没ukey 或者查不到开立时的ukey 或者当前使用的ukey不属于当前企业 或者开立跟审核是同一把ukey 就false
				if (
					!info ||
					!checkItem ||
					!checkItems ||
					!checkItems.some(item => item && info.includes(item?.mediaNbr)) ||
					(!getUkeyNo(info).startsWith('JRCBHB') && !getUkeyNo(info).startsWith('JRCBFTG'))
				) {
					resolve({ ispass: true, info })
					callback({ type: 'result', checkUkeyresult: true, checkUkeyErrorResult: checkUkeyErrorStatus['UNDETECTED'] })
					return
				}
				if (checkItem?.ukeyNo === getUkeyNo(info)) {
					resolve({ ispass: true, info })
					callback({ type: 'result', checkUkeyresult: true, checkUkeyErrorResult: checkUkeyErrorStatus['NOTTHESAME'] })
					return
				}
				resolve({ ispass: false, info })
				callback({ type: 'result', checkUkeyresult: false, checkUkeyErrorResult: '' })
			})
		}, 2000)
	})
}

const checkUkey = async value => {
	let checkResult = await commonModuleApi.getGeneralConfiguration({ configKeys: ['create_paperless_switch'] })
	let isOn = false,
		ischeck = false,
		checkData,
		bankCards,
		ukeylist
	if (checkResult && checkResult.length) {
		isOn = checkResult.find(item => item.configKey === 'create_paperless_switch')?.configValue === 'ON'
	}
	ischeck = getCurrentRole()?.supportOrgType === 'C' && isOn
	isCheckUkey = ischeck
	if (ischeck) {
		checkData = await commonModuleApi.getFindueky({ noOrId: value?.transferUuid || null })
		if (!checkData) {
			return
		}
		bankCards = await financeModuleApi.getBankcardByCompanyId({ orgId: ext?.user?.orgId })
		if (!bankCards || !bankCards.length) {
			return
		}
		// ukeylist = await financeModuleApi.getUkeyList({ accountNo: bankCards[0]?.accountNum })
		let ukeylist = [
			{
				operId: '***************',
				mediaNbr: 'JRCBHBC100001093',
				mediaCompany: 'CFCA',
				certRefnoSm: 'CN=JRCBHBC100001093,OU=Organizational-3,OU=TPC-S3,O=OCA21,C=CN',
				certRefno: '**********',
				certDn: '**********',
			},
			{
				operId: '***************',
				mediaNbr: 'JRCBFTG100000094',
				mediaCompany: 'CFCA',
				certRefnoSm: 'CN=JRCBFTG100000094,OU=Organizational-3,OU=TPC-S3,O=OCA21,C=CN',
				certRefno: '**********',
				certDn: '**********',
			},
			{
				operId: '***************',
				mediaNbr: 'JRCBFTC100000091',
				mediaCompany: 'CFCA',
				certRefnoSm: 'CN=JRCBFTC100000091,OU=Organizational-3,OU=TPC-S3,O=OCA21,C=CN',
				certRefno: '**********',
				certDn: '**********',
			},
		]
		if (!ukeylist || !ukeylist.length) {
			return
		}
		return { checkData, ukeylist }
	}
	return false
}

const handleUkey = async params => {
	const { checkData, ukeylist, value, chooseKeyData, handleUkeyAlert: callback } = params
	let ukey, signedText, certificate
	await $.when(initPowerSign($.Deferred()))
	ukey = await getPollUkey(checkData, ukeylist, chooseKeyData, callback)
	if (ukey) {
		signedText = await $.when(
			GetServiceSignData(
				getUkeyNumber(ukey?.info, 1),
				'sm3',
				`<?xml version=\"1.0\" encoding = \"utf-8\"?><SignData><Timestamp>1727594358082</Timestamp><TradeType name=\"UKEY校验:\">CheckUkey</TradeType><Fields><UkeyNo>${
					value?.transferCous ? value.transferCous[0].couNo : ''
				}-${getUkeyNo(ukey?.info)}</UkeyNo></Fields></SignData>`,
				$.Deferred()
			)
		)
		certificate = await $.when(GetServiceSignCert(getUkeyNumber(ukey?.info, 1), 'sm3', $.Deferred()))
	}
	return {
		signedText: signedText?.responseData,
		certificate: certificate?.responseData,
		info: ukey?.info,
	}
}

const getCheckUkeyMsg = async () => {
	let checkResult = await commonModuleApi.getGeneralConfiguration({ configKeys: ['create_paperless_switch'] }),
		isOn = false
	if (checkResult && checkResult.length) {
		isOn = getCurrentRole()?.supportOrgType === 'C' && checkResult.find(item => item.configKey === 'create_paperless_switch')?.configValue === 'ON'
	}
	return isOn
}

export { checkUkey, handleUkey, isCheckUkey, clearTimeout, getCheckUkeyMsg }
