import { Alert } from 'antd'
import React from 'react'

interface Props {
	checkUkeyErrorResult: string
}
const ukeyAlert = (props: Props) => {
	const { checkUkeyErrorResult } = props
	return (
		<>
			{checkUkeyErrorResult === 'UNDETECTED' ? (
				<Alert
					message="未检测到有效的企业UKEY，请插入UKEY并确认UKEY背部序列号是JRCBHB或JRCBFTG开头，不能与开立是用一把UKEY"
					type="warning"
					description=" "
					showIcon
					style={{ marginBottom: '15px' }}
				></Alert>
			) : (
				''
			)}
			{checkUkeyErrorResult === 'NOTTHESAME' ? (
				<Alert message="不能与开立是同一把UKEY" type="warning" description=" " showIcon style={{ marginBottom: '15px' }}></Alert>
			) : (
				''
			)}
		</>
	)
}

export default ukeyAlert
