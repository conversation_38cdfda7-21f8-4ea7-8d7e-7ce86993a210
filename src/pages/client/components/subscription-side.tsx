import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import { commonModuleApi } from '@src/pages/client/api'

const subIconImg = require('@src/assets/images/subscription-side/sub-icon.png')
const subIconImgHover = require('@src/assets/images/subscription-side/sub-icon-hover.png')
const TestQrcode = require('@src/assets/images/subscription-side/qr-code-test.jpg')

const getImgUrl = (data: string) => {
	return commonModuleApi.downloadFileUrl({ fileUrl: data })
}

const SubscirptionSide = props => {
	const { qrCodeUrlStr } = props
	const [subIcon, setSubIcon] = useState(subIconImg)
	const [qrCodeImgUrl, setQrCodeImgUrl] = useState('')
	//悬浮订阅图标之上
	const onSubIconOver = () => {
		setSubIcon(subIconImgHover)
	}
	const onSubIconOut = () => {
		setSubIcon(subIconImg)
	}

	useEffect(() => {
		if (qrCodeUrlStr) {
			let qrCodeUrlObj = JSON.parse(qrCodeUrlStr) || {}

			if (qrCodeUrlObj.url) {
				getImgUrl(qrCodeUrlObj.url).then(res => {
					setQrCodeImgUrl(res)
				})
			}
		}
	}, [])

	return (
		<SubTag>
			<div className="sub-box" onMouseOver={onSubIconOver} onMouseOut={onSubIconOut}>
				<div className="qrcode-box">
					{qrCodeImgUrl && <img src={qrCodeImgUrl} />}
					<span className="triangle"></span>
				</div>
				<img src={subIcon} className="icon" />
				<p>订阅我们</p>
			</div>
		</SubTag>
	)
}

export default SubscirptionSide

const SubTag = styled.div`
	.sub-box {
		width: 66px;
		height: 66px;
		background: #fff;
		position: fixed;
		bottom: 20px;
		right: 20px;
		text-align: center;
		cursor: pointer;
		.qrcode-box {
			position: absolute;
			top: -98px;
			left: -195px;
			width: 165px;
			height: 165px;
			background: #fff;
			border: 1px solid gray;
			text-align: center;
			display: none;
			opacity: 0;
			transition: opacity 1s ease-in-out;
			img {
				position: absolute;
				top: 15px;
				left: 14px;
				width: 135px;
				height: 135px;
				z-index: 10;
			}
			.triangle {
				display: block;
				position: absolute;
				background: #fff;
				width: 30px;
				height: 30px;
				right: -15px;
				top: 110px;
				transform: rotate(45deg);
				border-top: 1px solid gray;
				border-right: 1px solid gray;
				z-index: 1;
			}
		}
		.icon {
			margin: 10px auto 0 auto;
			svg {
				width: 40px;
				height: 40px;
				color: gray;
			}
		}
		p {
			margin: 5px 0 0 0;
			color: gray;
			font-size: 12px;
		}
	}

	.sub-box:hover {
		background: #4498ff;

		p {
			color: #fff;
		}
		.qrcode-box {
			display: block;
			opacity: 1;
		}
	}
`
