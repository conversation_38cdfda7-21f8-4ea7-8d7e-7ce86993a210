import { get, post } from './apiMethod'

const commonModuleApi = {
	//上传文件
	uploadFile(params: { file: any; type: string; [str: string]: any }, noNeedMessage: boolean) {
		const paramsData = new FormData()
		for (const key in params) {
			paramsData.append(key, params[key])
		}
		const url = '/pledge-config/common/uploadFile'
		// const url = '/pledge-config/common/uploadFile'
		return post(url, paramsData)
	},
	//上传文件base64
	uploadFileStr(params: { fileStr: any; type: string; [str: string]: any; isBase64?: boolean }, noNeedMessage: boolean) {
		const paramsData = new FormData()
		for (const key in params) {
			paramsData.append(key, params[key])
		}
		const url = '/pledge-config/anybody/uploadFileStr'
		return post(url, paramsData)
	},
	// 得到文件的base64
	downloadFile(params: { fileUrl: any }) {
		return post('/pledge-config/anybody/downloadFile', params)
	},
	//链接转换
	downloadFileUrl(params: { fileUrl: any }) {
		return post('/pledge-config/anybody/downloadFileUrl', params)
	},

	//获取服务器时间
	syncTime() {
		return get('/pledge/gw/server-time')
	},
	//得到协议(企业相关协议)
	getProtocol(params: { protocolTypes: string[]; ooUuid: string }) {
		return post('/pledge-config/anybody/user/protocol?handleResponse', params)
	},
	//得到协议(业务相关协议))
	getProtocolTemplate(params: any) {
		return post('/pledge-asset/protocol/get', params)
	},
	//1.3.3 签署补充协议
	signProtocol(params: any) {
		return post('/pledge-config/protocol/sign', params)
	},
	//获取银行账户信息
	queryBankcard(params: any) {
		return post('/pledge-config/bankcard/query/available', params)
	},
	//审核（内审）
	loadCurrentUserTask(params: any) {
		return post('/pledge-asset/process/task/loadCurrentUserTask', params)
	},
	doTask(params: any) {
		return post('/pledge-asset/process/task/doTask?handleResponse', params)
	},
	// 获取开立、支付融信流程图的数据
	getTransferChartData(params: any) {
		return get('/pledge-asset/process/progress?handleResponse', params)
	},
	//获取通用配置（sp上传文件时存的路径）
	getGeneralConfiguration(params: any) {
		return post('/pledge-config/anybody/query/config/keys', params)
	},
	//获取最新版本号
	getVersion() {
		return post('/pledge-config/anybody/version/get')
	},
	//获取编号
	getNo(params: { numberPrefix: string; count?: number }) {
		return post('/pledge-asset/number/generate', params)
	},
	// 根据企业名称搜索企业
	searchCompanyByName(params: any) {
		return post('/pledge-user-service/org/searchByName', params)
	},
	//查询当前登录者是否是本公司的最后一个审核者
	isCompanyLastFlowPerson(params: any) {
		return post('/pledge-asset/flow-audit/companyLastAuditor', params)
	},
	//查询当前登录者是否是本流程的最后一个审核者
	isLastFlowPerson(params: any) {
		return post('/pledge-asset/flow-audit/flowLastAuditor', params)
	},
	//调用接口判读该用户是否有未处理的兑付信息
	getCouCashRemindData() {
		return get('/pledge-asset/settlement/stranded/remind?handleResponse')
	},
	//查询链上融信存证接口
	queryCouProof(params: any) {
		return post('/pledge-asset/cou/proof/query', params)
	},
	//融信存证数据接口
	setCouProof(params: any) {
		return post('/pledge-asset/cou/proof/set?handleResponse', params)
	},
	//查询用户自身公司信息
	getCompanyInfo() {
		return post('/pledge-config/company/getCompanyInfo')
	},
	//查询组织公司信息
	getOrgInfo() {
		return get('/pledge_user/org')
	},
	// 获取企业信息
	getCompanyDetail(params: any) {
		return get(`/pledge-user-service/org/${params?.id}`)
	},
	// 获取当前审核记录的ukey
	getFindueky(params: any) {
		return get(`/pledge-asset/paperless/find/noId`, params)
	},
}

export default commonModuleApi
