/**
 * 开票管理模块接口API
 */
import { post, get } from './apiMethod'

const invoicingManageModuleApi = {
	//企业管理员查看企业开票信息
	queryInvoicingInfo() {
		return get('/pledge-config/company-invoicing-info/query')
	},
	//企业管理员添加企业开票信息
	addInvoiceHeader(params) {
		return post('/pledge-config/company-invoicing-info/add', params)
	},
	//企业管理员维护企业开票信息
	modifyInvoiceHeader(params) {
		return post('/pledge-config/company-invoicing-info/modify', params)
	},
	//保理企业用户查看运营上传的本企业的发票列表
	queryMyInvoice(params) {
		return post('/pledge-config/company-invoice/list', params)
	},
	//保理企业操作员查看是否完善开票信息
	isFillInvoicingInfo() {
		return get('/pledge-config/company-invoicing-info/isFill')
	},
}
export default invoicingManageModuleApi
