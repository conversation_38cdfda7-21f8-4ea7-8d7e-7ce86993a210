import { post } from './apiMethod'

enum DestroyStatus {
	WAITING_AUDIT = 'WAITING_AUDIT',
	REJECTED = 'REJECTED',
	CONFIRMED = 'CONFIRMED',
}

const destroyModuleApi = {
	// 核销列表
	getDestroyList(params: { pageNum: number; pageSize: number; couNo?: string; destroyNo?: string; destroyStatus?: DestroyStatus }) {
		return post('/pledge-asset/destroy/list4Client', params)
	},
	getCouList(params: { pageNum: number; pageSize: number; couNo?: string }) {
		return post('/pledge-asset/destroy/couLeaves/query', params)
	},
	checkSelectCou(params: { couUuid: string }) {
		return post('/pledge-asset/destroy/couLeaves/check', params)
	},
	createDestroy(params: { couUuid: string; payPdfUrl: string }) {
		return post('/pledge-asset/destroy/create', params)
	},
	getAuditDestroyList(params: any) {
		return post('/pledge-asset/process/task/loadCurrentUserTask', params)
	},
	getDetail(params: { uuid: string }) {
		return post('/pledge-asset/destroy/detail', params)
	},
}

export default destroyModuleApi
