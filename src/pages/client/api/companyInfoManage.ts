import { post } from './apiMethod'

const companyInfoManageModuleApi = {
	//查询银行卡列表
	getBankcard(params: { companyUuid: string }) {
		return post('/pledge-config/bankcard/query/list', params)
	},
	//获取所有企业信息
	getCompanyInfoList(params: any) {
		return post('/pledge-config/company/list', params)
	},
	//修改/添加银行卡
	bankcardModify(params: {
		accountBank: string
		accountName: string
		accountNum: string
		businessType: string
		cardType: string
		companyUuid: string
		relationFi: string
	}) {
		return post('/pledge-config/bankcard/modify', params)
	},
	//删除银行卡
	deleteCard(params: { companyUuid: string; id: any }) {
		return post('/pledge-config/bankcard/delete', params)
	},

	// 查询企业圈激活列表
	getCompanyCircleList(params: { pageNum: number; pageSize: number }) {
		return post('/pledge-config/company/circle/list', params)
	},
	//精确查找授信公司名称
	searchComByFullName(params) {
		let data = new FormData()
		data.append('companyFullName', params.companyName)
		return post('/pledge-config/company/searchByFullName', data)
	},
	getCompanyInfo(data) {
		return post('/pledge-config/company/getCompanyInfo', data)
	},
	//根据uuid获取普通企业信息
	getCompanyDetailByUuid(params: any) {
		return post('/pledge-config/company/detail', params)
	},

	/**
	 * 通过卡号获取江阴银行卡信息
	 * @param params
	 * @returns
	 */
	queryJrBankInfoByNo(params) {
		return post('/pledge-config/bankcard/jr-account/by-name', params)
	},

	getOpeningBank(params) {
		return post('/pledge-config/bank/condition/query', params)
	},
}

export default companyInfoManageModuleApi
