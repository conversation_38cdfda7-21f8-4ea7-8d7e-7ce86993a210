import { post } from './apiMethod'

const refundModuleApi = {
	// 支付列表
	getPayList(params: any) {
		return post('/pledge-asset/transfer/list-for-refund', params)
	},
	// 创建退款申请（支付）
	createRefundToS(params: any) {
		return post('/pledge-asset/refund/applyToS', params)
	},
	// 创建退款申请（开立）
	createRefundToC(params: any) {
		return post('/pledge-asset/refund/applyToC', params)
	},
	// 退款申请列表
	getRefundList(params: any) {
		return post('/pledge-asset/refund/list', params)
	},
	// 查询退款详情
	getRefundDetail(params: any) {
		return post('/pledge-asset/refund/detail', params)
	},
	//查询开立退款链上明细
	getCreateRefoundChainInfo(params: { refundUuid: string }) {
		return post('/pledge-asset/refund/createChainInfo', params)
	},
	//合同列表
	getList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset/contract/list', params)
	},
	//根据交易uuid查询对应的退款记录总金额(rmb:元)
	getRefoundAmount(params: { transferUuid: string }) {
		return post('/pledge-asset/refund/totalAmount/transferUuid', params)
	},
}

export default refundModuleApi
