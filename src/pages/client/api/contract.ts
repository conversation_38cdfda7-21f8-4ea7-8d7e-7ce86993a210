import { post } from './apiMethod'

const contractModuleApi = {
	// 查询项目组合同列表
	getContractTeamList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset/contract/team/list', params)
	},
	//查询项目组合同列表
	getTeamContractList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset/contract/team/list', params)
	},
	//合同列表
	getList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset/contract/list', params)
	},
	//限额操作员合同查看
	getBuildList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset/contract/build/list', params)
	},
	//项目操作员查看合同
	getTeamOperatorList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset/contract/team/list', params)
	},
	//创建合同
	create(params: any) {
		return post('/pledge-asset/contract/create', params)
	},
	//修改合同
	modify(params: any) {
		return post('/pledge-asset/contract/modify', params)
	},
	// 根据企业名称搜索企业
	modsearchByName(params: { companyName: string; limit: number }) {
		return post('/pledge-user-service/org/searchByName', params)
	},
}

export default contractModuleApi
