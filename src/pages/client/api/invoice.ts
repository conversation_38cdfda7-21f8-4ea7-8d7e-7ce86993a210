import { post, postBlob, get } from './apiMethod'

const invoiceModuleApi = {
	//查询项目组的发票列表
	getInvoiceTeamList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset/invoice/team/list', params)
	},
	//发票列表 （操作员）
	getList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset/invoice/list', params)
	},
	//发票列表 （限额操作员）
	getBuildList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset/invoice/build/list', params)
	},
	//得到全部可用的发票列表
	getAvailableList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset/invoice/available/list', params)
	},
	getcouAvailableList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset//invoice/cou-create/list', params)
	},
	getbyAvailableList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset//invoice/by-operate/list', params)
	},
	//得到限额操作员可用的发票列表
	getBuildAvailableList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset/invoice/build/available/list', params)
	},
	//得到项目组全部可用的发票列表
	getTeamAvailableList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset/invoice/team/available/list', params)
	},
	//删除发票
	deleteInvoice(params: { uuid: string }) {
		return post('/pledge-asset/invoice/delete', params)
	},
	//发票识别
	ocr(params: { fileName: string; invoiceBase64Str: string }) {
		return post('/pledge-asset/ocr/async/invoice?handleResponse', params)
	},
	//获取发票结果
	ocrInvoiceResult(params: string[]) {
		return post('/pledge-asset/ocr/async/result', params)
	},
	//提交发票
	batchCreate(params: any) {
		return post('/pledge-asset/invoice/batchCreate?handleResponse', params)
	},
	//查询当前公司下面的购买方名称列表
	getBuyerNames(params: { companyUuid: string; limit: number }) {
		const paramsData = new FormData()
		for (const key in params) {
			paramsData.append(key, params[key])
		}
		return post('/pledge-asset/invoice/buyerNames', paramsData)
	},
	//查看发票(全部/异常)记录列表
	getInvoiceAlarmList(params: any) {
		return post('/pledge-asset/invoice-alarm/query', params)
	},
	//查看发票(全部/异常)记录列表
	exportInvoiceAlarm(params: any) {
		return postBlob('/pledge-asset/common/file/export?getblob', params)
	},
	// 发票校验
	getrealtimecheck(params: any) {
		return post('/pledge-asset/invoice/real-time-check', params)
	},
	// 发票实时校验
	getFetchCheckResult(params: any) {
		return post('/pledge-asset/invoice/fetch/check-result', params)
	},
	getInvoiceCfg() {
		return get('/pledge-config/get-finance-business-config')
	},
}

export default invoiceModuleApi
