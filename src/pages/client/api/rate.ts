/*
 * @Date: 2022-09-08 16:08:30
 * @LastEditors: jcl
 * @FilePath: \frontend-web\src\pages\client\api\rate.ts
 * @Description: In User Settings Edit
 */
import { post } from './apiMethod'
//融资授信模块API 管理

interface IModifyParm {
	companyName?: string
	factoringRate: number
	id?: number | string
	interestRate: number
}

export default {
	/* 利率列表 */
	getList(params: { companyName?: string; interestPayWay?: string; pageNum: number; pageSize: number }) {
		return post('/pledge-config/special/rate/list', params)
	},
	/* 修改利率 */
	modify(params: IModifyParm) {
		return post('/pledge-config/special/rate/modify', params)
	},
	/* 新增利率 */
	add(params: IModifyParm) {
		return post('/pledge-config/special/rate/save', params)
	},
	/* 删除利率 */
	del(params: any) {
		const paramsData = new FormData()
		for (const key in params) {
			paramsData.append(key, params[key])
		}
		return post('/pledge-config/special/rate/delete', paramsData)
	},
}
