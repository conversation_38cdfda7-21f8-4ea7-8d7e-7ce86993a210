import { post } from './apiMethod'
//融资授信模块API 管理

interface CreditInfo {
	companyUuid: string
	creditDueDate: string
	factoringRate: number
	interestPayWay: string
	interestRate: number
	quotaInCent: number
}
const creditModuleApi = {
	//融资授信列表
	getList(params: { companyName?: string; interestPayWay?: string; pageNum: number; pageSize: number }) {
		return post('/pledge-config/credit/query/list', params)
	},
	//新增或者更改融资授信设置
	modify(params: CreditInfo) {
		return post('/pledge-config/credit/modify', params)
	},

	//母公司获取 子公司（金融机构的授信信息）
	getCreditChildren(params: any) {
		return post('/pledge-config/credit/query/children', params)
	},
}

export default creditModuleApi
