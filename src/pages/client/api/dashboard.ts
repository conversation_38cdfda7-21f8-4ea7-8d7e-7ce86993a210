import { post, get } from './apiMethod'

const DashboardApi = {
	//------核心企业start--------------
	// 待办查询
	getTodos() {
		return get('/pledge-asset/workbench/coreCompany/query/todos')
	},
	// 开立纪录
	getOpenings() {
		return get('/pledge-asset/workbench/coreCompany/query/create')
	},
	// 开立额度
	getOpeningCredit() {
		return get('/pledge-asset/workbench/coreCompany/query/create-amount')
	},
	// 到期兑付统计查询
	getCouDueStatistical() {
		return get('/pledge-asset/workbench/coreCompany/query/couDueStatistical')
	},
	/**
	 * 开立月统计
	 * @param params createDateEnd,createDateStart 2024-06-13
	 * @returns
	 */
	getCouCreateStatsData(params) {
		return post('/pledge-asset/workbench/coreCompany/query/couCreateStatsData', params)
	},
}
export default DashboardApi
