import { get, post, postBlob } from './apiMethod'

interface UserInterface {
	username: string
	email: string
	roleIds: number[]
	uuid?: string
	mobile: number
}

const userAuthModuleApi = {
	//获取用户列表
	getNonTeamList(params: any) {
		return post('/pledge-user-service/non-team/list', params)
	},
	//新增用户
	creatUser(params: UserInterface) {
		return post('/pledge-config/user/create', params)
	},
	//更新用户信息
	updateUser(params: UserInterface) {
		return post('/pledge-config/user/modify', params)
	},
	//删除用户
	removeUser(params: any) {
		return post('/pledge-config/user/delete', params)
	},

	//获取所有业务组
	getAllPrivilegeList(params: any) {
		return post('/pledge-config/group/list', params)
	},
	//查询角色id列表（开发使用，不过率角色权限） 可创建角色
	searchRoleListDev() {
		return get('/pledge-config/sys/role/list')
	},
	//查询角色id列表可创建角色
	searchRoleList(params: { companyType: string }) {
		return get('/pledge-config/user/role/relation/sub-role-list', params)
	},
	//更新业务组
	updateBizGroup(params: any) {
		return post('/pledge-config/group/modify', params)
	},
	//获取核心企业限额操作员
	getCBuildOperatorList(params: any) {
		return post('/pledge-config/user/cb/list', params)
	},
	//获取供应商限额操作员
	getSBuildOperatorList(params: any) {
		return post('/pledge-config/user/sb/list', params)
	},
	//设置限额操作员开立额度
	setQuota(params: any) {
		return post('/pledge-config/user/setQuota', params)
	},
	//查询限额操作员历史已用额度
	usedAmount(params: any) {
		return post('/pledge-config/user/usedAmount', params)
	},

	//查询 项目组list
	getTeamList(params: any) {
		return post('/pledge-config/team/query/list', params)
	},

	//新建项目组
	addNewTeam(params: any) {
		return post('/pledge-config/team/add', params)
	},
	// 添加team 用户
	addTeamUser(params: any) {
		return post('/pledge-config/team/user/create', params)
	},

	// 添加team 用户
	getTeamUserList(params: any) {
		return post('/pledge-config/team/user/list', params)
	},

	// 删除 team 用户
	delTeamUserList(params: any) {
		return post('/pledge-config/team/user/delete', params)
	},

	// 删除 team 用户
	updateTeamUser(params: any) {
		return post('/pledge-config/team/user/moidfy', params)
	},

	//根据项目组编号查询单个项目组额度
	teamQuotaQuery(params: any) {
		return post('/pledge-config/team/quota/team/no', params)
	},
	//新增项目组额度
	addTeamQuota(params: any) {
		return post('/pledge-config/team/quota/add', params)
	},
	//编辑项目组额度
	editTeamQuota(params: any) {
		return post('/pledge-config/team/quota/edit', params)
	},
	//项目组额度列表查询
	getTeamQuotaList(params: any) {
		return post('/pledge-config/team/quota/list', params)
	},
	//项目组额度列表导出
	teamQuotaExport(params: any) {
		return postBlob('/pledge-config/team/quota/export?getblob', params)
	},

	// 获取对应企业类型的角色 - new
	// 2024/5/01 改地址
	getSupportOrgTypeRole(params: any) {
		return post('/pledge-user-service/role/list/by-type', params)
	},
	// 为当前企业创建账号
	addAccountForOrg(params: any) {
		return post('/pledge-user-service/account/add-account-role', params)
	},
	// 为当前企业更新账号
	updateAccountForOrg(params: any) {
		return post('/pledge-user-service/account/role-update', params)
	},
	// 移除账号
	removeAccountForOrg(params: any) {
		return post('/pledge-user-service/rabia/account/removeAccount', params)
	},
}

export default userAuthModuleApi
