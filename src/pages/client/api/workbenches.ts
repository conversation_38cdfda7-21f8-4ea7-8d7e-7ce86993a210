import { get, post, postBlob } from './apiMethod'

const workbenchesModuleApi = {
	getNeedDealData(params: any) {
		return get('/pledge-asset/workbench/supplier/query/receive', params)
	},
	getPaymentData(params: any) {
		return get('/pledge-asset/workbench/supplier/query/pay', params)
	},
	getCreditAndFinanceData(params: any) {
		return get('/pledge-asset/workbench/supplier/query/credit-statistics', params)
	},
	getCashingData(params: any) {
		return get('/pledge-asset/workbench/supplier/query/cou-cash/stats', params)
	},
	getFuseCreditData(params: any) {
		return get('/pledge-asset/workbench/supplier/query/cou-statistics', params)
	},
	getMonthFinanceData(params: any) {
		return post('/pledge-asset/workbench/supplier/query/financing/stats', params)
	},
}

export default workbenchesModuleApi
