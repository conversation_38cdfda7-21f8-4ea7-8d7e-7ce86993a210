import { creditStatus } from '../config/companyCreditManage'
import { get, post, postBlob } from './apiMethod'

const transferModuleApi = {
	//获取C操作员单位授信信息
	cOperatorCouAmount(params: { companyName: string; enable: creditStatus }) {
		return post('/pledge-config/credit/query/list', params)
	},
	//获取C限额操作员单位授信信息
	cBuildOperatorCouAmount(params: { companyName: string; enable: creditStatus }) {
		return post('/pledge-config/credit/query/build/list', params)
	},
	//获取C项目操作员单位授信信息
	cTeamOperatorCouAmount(params: { companyName: string; enable: creditStatus }) {
		return post('/pledge-config/credit/query/team/list', params)
	},

	//根据名称查询供应商公司
	getCompanyOfsupplier(params: { companyName: string }) {
		return post('/pledge-user-service/org/searchByName', {
			companyName: params.companyName,
			limit: 5,
			filterSelfCompany: true,
			companyType: 'S',
		})
	},
	//cou开立
	create(params: any) {
		return post('/pledge-asset/cou/create', params)
	},
	checkBankcard(params: any) {
		return post('/pledge-config/bankcard/query/available-by-name', params)
	},
	//项目组操作员融信支付接口
	sTeamOperatorPay(params: any) {
		return post('/pledge-asset/cou/team/pay', params)
	},
	pay(params: any) {
		return post('/pledge-asset/cou/pay', params)
	},
	//供应商限额操作员
	sBuildOperatorPay(params: any) {
		return post('/pledge-asset/cou/build/pay', params)
	},
	//查看原始cou列表
	getOriginalList(params: any) {
		return post('/pledge-asset/cou/originalList', params)
	},
	//查看cash 兑付列表
	getCashList(params: any) {
		return post('/pledge-asset/cou/originalList', params)
	},
	//导出cash 兑付列表
	exportCashList(params: any) {
		return postBlob('/pledge-asset/c/cou/originalList/export?getblob', params)
	},
	//查看日历特殊 日
	getDateSpecial(params: any) {
		return post('/pledge-asset/specialdate/list', params)
	},

	//查询收款方 列表
	getToPubKeyList() {
		return post('/pledge-asset/transfer/toList')
	},
	//查询开立方 列表
	getPublishlist() {
		return post('/pledge-asset/cou/publish/credit/List')
	},

	//流转管理
	getTransferList(params: any) {
		return post('/pledge-asset/transfer/list', params)
	},

	// 查看流转详情
	getTransferDetail(params: { transferUuid: string }) {
		return post('/pledge-asset/transfer/detail', params)
	},
	// 获取开立的时候的发票
	getTransferInvoices(params: any) {
		return post('/pledge-asset/transfer/getInvoices', params)
	},
	//S角色金额查询
	sCouAmount() {
		return post('/pledge-asset/query/couAmount')
	},
	//判断是否节假日
	isWorkDay(params: { paymentDay: number }) {
		return post('/pledge-asset/specialdate/dueDate', params)
	},

	//流转管理 审核Cou
	couAudit(params: any) {
		return post('/pledge-asset/cou/audit', params)
	},

	//
	getCouList(params: any) {
		return post('/pledge-asset/cou/availableList', params)
	},
	getPledgingCouList(params: any) {
		return post('/pledge-asset/cou/pledgeList', params)
	},
	getPledgedCouList(params: any) {
		return post('/pledge-asset/cou/cash/list ', params)
	},
	getNewCouList(params: any) {
		return post('/pledge-asset/cou/listForRefund', params)
	},

	//查看链上信息（开立退款详情查看链上明细不适用此接口）
	getChainInfo(params: { transferUuid: string }) {
		return post('/pledge-asset/transfer/chainInfo', params)
	},
	//供应商融信导出使用
	supplierExport(params: any) {
		return postBlob('/pledge-asset/cou/supplier/export?getblob', params)
	},
	//核心企业融信导出使用
	companyCExport(params: any) {
		return postBlob('/pledge-asset/cou/originalList/export?getblob', params)
	},
	//获取叶子结点cou
	getLeaves(params: any) {
		return post('/pledge-asset/cou/leaves', params)
	},
	//审核（内审）
	loadCurrentUserTask(params: any) {
		return get('/pledge-asset/process/task/loadCurrentUserTask', params)
	},
	readResource(params: any) {
		return get('/pledge-asset/process/image/read-resource?handleResponse', params)
	},
	//查询持有人明细
	getHolderDetailList(params: any) {
		return post('/pledge-asset/query/cou/holderList', params)
	},
	//导出持有人明细
	exportHolderDetailList(params: any) {
		return postBlob('/pledge-asset/oo/holderList/export?getblob', params)
	},
	// 获取开立、支付融信流程图的数据
	getTransferChartData(params: any) {
		return get('/pledge-asset/process/image/progress', params)
	},
	// 获取融资到期日
	getWorkday(params: any) {
		return post('/pledge-asset/anybody/query/workday', params)
	},

	transferExport(params: any) {
		return postBlob('/pledge-asset/transfer/export?getblob', params)
	},
	// 预览付款承诺函
	previewCommitment(params: any) {
		return post('/pledge-asset/protocol/preview/commitment', params)
	},
	transferProtocol4PayView(params: any) {
		return post('/pledge-asset/protocol/preview/pay', params)
	},
	//跟据融信编号查询融信接口
	queryCouListByNoList(params: any) {
		return post('/pledge-asset/cou/list/byNoList?handleResponse', params)
	},
}

export default transferModuleApi
