import { post, get, postBlob } from './apiMethod'

const financeModuleApi = {
	//融资列表
	getFinanceList(params: { pageNum: number; pageSize: number; [str: string]: any }) {
		return post('/pledge-asset/pledge/financing/list', params)
	},
	getCompany(params: any) {
		return post('/pledge-config/company/get', params)
	},
	getRelatedFICompany(params?: any) {
		return post('/pledge-config/company/getRelatedFICompany', params)
	},
	getDueDate(params: { couUuid: string }) {
		return post('/pledge-asset/finance/duedate', params)
	},
	// 得到开立方和授信方列表
	getPublishAndCreditList() {
		return post('/pledge-asset/cou/publish/credit/List')
	},
	// 待签署协议
	getloadCurrentUserTask(params: any) {
		return post('/pledge-asset/process/task/loadCurrentUserTask', params)
	},
	// 金融机构列表
	getFinanceCreditList() {
		return post('/pledge-asset/finance/credit/list')
	},
	//导出融资列表
	exportFinanceList(params: any) {
		return postBlob('/pledge-asset/finance/export?getblob', params)
	},
	//供应商操作员创建融资申请
	sOperatorCreateFinance(params: any) {
		return post('/pledge-asset/pledge-finance/create?handleResponse', params)
	},
	//供应商限额操作员创建融资申请
	sBuildOperatorCreateFinance(params: any) {
		return post('/pledge-asset/finance/build/create?handleResponse', params)
	},
	//供应商项目操作员创建融资申请
	sTeamOperatorCreateFinance(params: any) {
		return post('/pledge-asset/finance/team/create?handleResponse', params)
	},
	// 得到融资申请详情
	getFinanceDetail(params: { uuidOrNumber: string }) {
		return post('/pledge-asset/pledge/financing/detail', params)
	},
	// 根据融信uuid下载全量流转合同文件
	allContractDownload(params: { couUuid: string }) {
		return post('/pledge-asset/finance/allContract/download', params)
	},
	// 根据融信uuid下载融信浏转单（全路径）
	allTransferDownload(params: { couUuid: string }) {
		return post('/pledge-asset/finance/allTransferProtocol/download', params)
	},
	//查询额度
	couAmount() {
		return post('/pledge-asset/query/couAmount')
	},
	//发起质押前的预检
	couPrecheck(params: any) {
		return get('/pledge-config/pledge/pre-check', params)
	},
	//精确查找授信公司名称
	searchComByFullName(params) {
		let data = new FormData()
		data.append('companyFullName', params.companyName)
		return post('/pledge-config/company/searchByFullName', data)
	},
	// 融资二次提交接口
	secondSubmit(params: { uuidOrNumber: string }) {
		return post('/pledge-asset/finance/second/submit?handleResponse', params)
	},
	// 根据开立方、授信方、本公司 uuid 获取平台服务费率
	getServiceFeeRate(params: { centerUuid: string; fiUuid: string; supplierUuid: string }) {
		return post('/pledge-config/finance/get/serviceFeeRate', params)
	},
	getFiConfigDetail(params: { fiUuid: string }) {
		return post('/pledge-config/fiConfig/by/fiUuid', params)
	},
	//获取C单位授信信息
	getCreditInfo(params: { companyUuid: string; fiUuid: string; type: any }) {
		let data = new FormData()
		data.append('companyUuid', params.companyUuid)
		data.append('fiUuid', params.fiUuid)
		data.append('type', params.type)
		return post('/pledge-config/credit/query/company', data)
	},
	//获取融信流转路径表
	getTransferPathExcel(params: { uuidList: string[]; firstCouList: any[] }) {
		return post('/pledge-asset/cou/transferPathExcel', params)
	},
	//获取应收账款转让通知列表
	getTransferNotice(params) {
		return post('/pledge-asset/receivable/transfer/notice/query', params)
	},
	getContractCodesByCouUuid(parmas: any) {
		return post('/pledge-asset/cou/contractCodeList', parmas)
	},
	// 单据一键下载
	billDownload(params: { financeNo: string; fileUrl: string; couUuids: any[] }) {
		return post('/pledge-asset/finance/allIn/download', params)
	},
	//  取特殊利率
	getRatesDetail(params: { companyUuid: string; fiUuid: string }) {
		return post('/pledge-config/special/rate/detail', params)
	},

	//获取保理协议预览
	previewFinanceFactor(params: any) {
		return post('/pledge-asset/protocol/preview/financeFactor', params)
	},
	previewPledge(params: any) {
		return post('/pledge-asset/protocol/preview/pledge', params)
	},
	previewpledgeLoan(params: any) {
		return post('/pledge-asset/protocol/preview/pledge-loan', params)
	},
	//查询当前登录企业的直连融资是否有审核员
	financeHasAuditor() {
		return post('/pledge-asset/flow-audit/financeHasAuditor')
	},
	//获取平台服务协议预览
	previewPlatformProtocol(params: any) {
		return post('/pledge-asset/protocol/preview/platform/fee', params)
	},
	// /pledge/financing/check-time 检测是否是工作日
	getWorkTime() {
		return get('/pledge-asset/pledge/financing/check-time')
	},
	//获取融信开立方的融信质押率
	/**
	 *
	 * @param params publishPubKey 对应cou的发布方的pubkey
	 * @returns
	 */
	getCouOpenerPledgeInterest(params: any) {
		return get(`/pledge-config/cc/credit/get-by-publishPubKey`, params)
	},
	/**
	 *获取质押通知列表
	 */
	getPledgeReminderList() {
		return post(`/pledge-asset/receivable/known/transfer/notice/query`)
	},
	/**
	 *更新质押通知列表为已悉知状态
	 @param params idList:[]
	 */
	updatePledgeReminderList(params: any) {
		return post(`/pledge-asset/receivable/known/transfer/notice`, params)
	},
	/**
	 * 获取供应商在核心企业的最大累计流转额度
	 * @param params applicationOrgId：发起融资的公司ID；relationOrgId 关联公司id
	 * @returns
	 */
	getSupplierCreditFormCenter(params: any) {
		return post(`/pledge-asset/pledge-finance/founding/total-amount`, params)
	},

	/**
	 *质押融资检验征信报告校验结果
	 */
	getCheckCreditReport() {
		return get(`/pledge-config/pledge/check-credit-report`)
	},
	/**
	 *获取银行卡信息
	 */
	getBankcardByCompanyId(params: any) {
		return get(`/pledge-config/bankcard/account/by-org-id`, params)
	},
	/**
	 *生成无纸化合同
	 */
	getSyntheticContract(params: any) {
		return post(`/pledge-asset/paperless/synthetic/contract`, params)
	},
	/**
	 *获取当前企业的ukey列表
	 */
	getUkeyList(params: any) {
		return get(`/pledge-asset/paperless/queryUkey`, params)
	},
	/**
	 *无纸化合同盖章
	 */
	getUkeySealContract(params: any) {
		return post(`/pledge-asset/paperless/seal/contract`, params)
	},
	/**
	 *合同盖章加签
	 */
	getUkeySignContract(params: any) {
		return post(`/pledge-asset/paperless/sign/contract`, params)
	},
	/**
	 *ueky信息保存
	 */
	getUkeySave(params: any) {
		return post(`/pledge-asset/paperless/info/save`, params)
	},
	/**
	 *ueky审核校验
	 */
	getUkeyCheck(params: any) {
		return get(`/pledge-asset/paperless/server/check/key`, params)
	},
}

export default financeModuleApi
