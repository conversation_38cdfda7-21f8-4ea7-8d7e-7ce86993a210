import { get, post } from './apiMethod'

/**
 * @description c 密钥确认函 p 平台服务章程 u 平台服务以及用户操作规则
 */
type protocolType = 'companyKey' | 'platformServer' | 'userRule' | 'finance' | 'transfer'

// 补充协议签署参数
type ProtocolVersionPara = {
	protocolUrl?: string
	protocolVersion?: string
} | null
const loginModuleApi = {
	// 获取页面title 待联调
	// 需修改地址
	login(params) {
		return post('/pledge-config/account/login?handleResponse', params)
	},
	/*
	//登录接口
	login(params: { userName: string; password: string; loginType: number; captcha: string; type: number }) {
		return post('/pledge/gw/account/login', params)
	},
	*/
	//登出接口
	logout() {
		return post('/logout')
	},
	//获取当前用户权限
	getBaseInfo(params: { loginCompanyType: any }) {
		return post('/pledge-config/user/loginCompanyType', params)
	},
	//校验邮箱
	isEmailExist(params: { email: string; captcha: string }) {
		return post('/pledge-config/isEmailExist', params)
	},
	//获取图形验证码
	getCaptcha() {
		return get('/pledge/gw/captcha')
	},
	//获取手机验证码
	getPhoneCode(params: { mobile: string; type?: string }) {
		return post('/pledge-config/anybody/user/sendPhoneCode?handleResponse', params)
	},
	//注册接口
	register(params: any) {
		return post('/pledge-config/anybody/companyAdmin/register', params)
	},
	//激活用户
	activeUser(params: any) {
		return post('/pledge-config/user/active', params)
	},
	// 获取邮箱验证码
	getEmailCode(params: { email: string }) {
		return post('/pledge-config/anybody/user/sendEmailCode?handleResponse', params)
	},
	//重置密码
	resetPassword(params: any) {
		return post('/pledge-config/user/resetPassword', params)
	},
	checkReckeyUpdate() {
		return post('/pledge-config/user/checkReckeyUpdate')
	},
	saveReckey(params: any) {
		return post('/pledge-config/user/saveReckey', params)
	},
	//获取公司上有的FI企业
	getRelatedFICompany() {
		return post('/pledge-config/company/getRelatedFICompany')
	},
	//密钥升级
	keyUpgrade(params: { companyUuid: string; companyPriv: string; companyPubKey: string }) {
		return post('/pledge-config/company/keyUpgrade?handleResponse', params)
	},
	// //普通公司重新认证
	// modifyCommon(params: any) {
	// 	return post('/pledge-config/company/common/modify?handleResponse', params)
	// },
	// //金融机构重新认证
	// modifyFI(params: any) {
	// 	return post('/pledge-config/company/fi/modify?handleResponse', params)
	// },
	// 企业第一次认证相关接口
	certifyCompany(params: any) {
		return post('/pledge-config/client/certify/first?handleResponse', params)
	},
	// 企业重新认证相关接口
	reCertifyCompany(params: any) {
		return post('/pledge-config/client/certify/again?handleResponse', params)
	},
	//普通公司注册接口
	registerCommon(params: any) {
		return post('/pledge-config/company/registerCommon?handleResponse', params)
	},
	//金融机构注册接口
	registerFI(params: any) {
		return post('/pledge-config/company/registerFI?handleResponse', params)
	},
	//检查公司是否存在
	checkCompanyIsExist(params: { adminEmail?: string; companyName?: string; companyShortName?: string; socialCreditCode?: string }) {
		return post('/pledge-config/anybody/company/exist?handleResponse', params)
	},
	//测试邮箱验证码
	testEmailCheckCode(params: { key: string }) {
		return get('/pledge-config/anybody/manager/getKey', params)
	},
	// 激活账号
	activateAccount(params: { email: string; mobile: string; mobileCode: string; password: string; protocolVersionId?: number }) {
		return post('/pledge-config/anybody/user/activateAccount', params)
	},
	//新的重置密码
	newResetPassword(params: { email: string; mobile: string; mobileCode: string; password: string; protocolVersionId?: number }) {
		return post('/pledge-config/anybody/user/resetPassword?handleResponse', params)
	},
	//完善企业信息
	perfectInformation(params: { accountBank: string; accountName: string; accountNum: any }) {
		return post('/pledge-config/bankCard/supplement', params)
	},
	signProtocol(params: { keyProtocol: ProtocolVersionPara; platformProtocol: ProtocolVersionPara; userRuleProtocol: ProtocolVersionPara }) {
		return post('/pledge-config/protocol/sign', params)
	},
	getProtoUrl(params: { file: string; type: protocolType }) {
		const data = new FormData()
		data.append('file', params.file)
		data.append('type', params.type)
		return post('/pledge-config/anybody/uploadHtmlFile', data)
	},
	// home页模块二查询统计信息
	queryStatisticsInfo(params: {}) {
		return post('/pledge-asset/anybody/query/statistics?handleResponse', params)
	},

	// 根据运营机构uuid  获取 机构详情
	getOoNamesByUuids(params: { uuids: Array<string> }) {
		return post('/pledgeAdmin/ooNames/byUuids', params)
	},

	// 检查公司状态
	checkCompany() {
		return get('/pledge-user-service/account/check-company')
	},

	// 获取baseInfo
	getNewBaseInfo() {
		return get('/pledge-user-service/account/ext-base-info')
	},

	/**
	 * 通过卡号获取江阴银行卡信息
	 * @param params
	 * @returns
	 */
	queryJrBankInfoByNo(params) {
		return post('/pledge-config/bankcard/jr-account/by-name', params)
	},
}

export default loginModuleApi
