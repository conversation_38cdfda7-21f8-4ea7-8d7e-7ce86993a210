import { post, get, postBlob } from './apiMethod'

const CooperateProtocolAPI = {
	//下载协议- 使用通用接口

	//获取签署协议列表
	getSignedProtocols(params: any) {
		return post('/pledge-asset/protocol-sign/list', params)
	},

	//发起签协议前的授信方预检
	creditorPrecheck(params: any) {
		return get('/pledge-config/pledge/pre-check', params)
	},

	/**获取签署页面字段
	 * @params showFlag:是否脱敏显示，前端一直脱敏
	 */
	getBeforeSignProtocolInfo() {
		return get('/pledge-config/credit/core/agreement-fields', { showFlag: false })
	},
	/**预览签署的协议*/
	previewSignCooperateProtocol() {
		return post('/pledge-asset/protocol/preview/credit-effect')
	},
	/**签署的协议*/
	signCooperateProtocol() {
		return post('/pledge-asset/protocol-sign/sign-credit-effect')
	},
}
export default CooperateProtocolAPI
