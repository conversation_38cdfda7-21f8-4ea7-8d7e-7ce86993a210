import { Button, Result } from 'antd'
import React from 'react'
import { useNavigate } from 'react-router-dom'

const NotFound: React.FC = () => {
	const navigate = useNavigate()
	console.log('hello~~')
	return (
		<Result
			status="403"
			title="403"
			subTitle="Sorry, you are not authorized to access this page."
			extra={
				<Button
					type="primary"
					onClick={() => {
						navigate('/login')
					}}
				>
					Back Login
				</Button>
			}
		/>
	)
}
export default NotFound
