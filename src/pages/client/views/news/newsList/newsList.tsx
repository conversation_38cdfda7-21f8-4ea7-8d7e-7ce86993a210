import React, { useEffect, useState, useCallback } from 'react'
import { <PERSON>read<PERSON>rumb, Pagin<PERSON>, PaginationProps } from 'antd'
import { logoApi } from '@src/pages/client/api/index'
import './newsList.less'
import SubscriptionSide from '@factor/components/subscription-side'
import useSetPageTitleAndIcon from '@src/pages/client/hooks/useSetPageTitleAndIcon'
const newsHeaderImg = require('@src/assets/images/news/news-header.png')

const NewsList = () => {
	const [qrCodeUrlStr, setQrCodeUrlstr] = useState('')
	const [newsList, setNewsList] = useState<any[]>([])
	const [pageConfig, setPageConfig] = useState({
		//第几页
		pageNum: 1,
		//一页的数量
		pageSize: 10,
		total: 0,
		id: '',
		pages: 0,
	})

	const showTotal: PaginationProps['showTotal'] = total => `共搜索到 ${total} 条记录`

	const getPageNewsList = (id, pageSize, pageNum) => {
		logoApi
			.getNewsList({
				// "39"
				platformId: id,
				pageSize: pageSize,
				pageNum: pageNum,
			})
			.then(res => {
				if (Array.isArray(res.list)) {
					setNewsList(res.list)
				}
				let { pageNum, pageSize, total, pages } = res
				setPageConfig({
					id,
					pageNum: Number(pageNum),
					pageSize: Number(pageSize),
					total: Number(total),
					pages: Number(pages),
				})
			})
	}

	useSetPageTitleAndIcon()

	useEffect(() => {
		logoApi
			.getHomePageInfo()
			.then(res => {
				let { tabTitle, qrCodeUrl: qrCodeUrlStr, id } = res
				if (tabTitle) {
					document.title = tabTitle
				}
				if (qrCodeUrlStr) {
					setQrCodeUrlstr(qrCodeUrlStr)
				}
				return id
			})
			.then(id => {
				getPageNewsList(id, pageConfig.pageSize, pageConfig.pageNum)
			})
	}, [])

	const generateNewsList = useCallback(() => {
		if (newsList.length === 0) {
			return <div className="no-data">暂无数据</div>
		} else {
			return (
				<ul className="article-list">
					{newsList.map((item, index) => {
						let { title, link, createTime } = item
						return (
							<li key={`${link}#${index}`}>
								<a href={link} target="_blank" title={title}>
									<span className="title">{title}</span>
									<span className="create-time">{createTime} &gt;</span>
								</a>
							</li>
						)
					})}
				</ul>
			)
		}
	}, [newsList])

	const pageChange = (page, pageSize) => {
		let pageNum = page
		getPageNewsList(pageConfig.id, pageSize, pageNum)
	}

	return (
		<div className="news-wrapper">
			<header className="news-header">
				<img src={newsHeaderImg} />
			</header>
			<div className="news-content">
				<Breadcrumb className="breadcrumb" separator="/">
					<Breadcrumb.Item>
						<a href="#/">首页</a>{' '}
					</Breadcrumb.Item>
					<Breadcrumb.Item>资讯动态</Breadcrumb.Item>
				</Breadcrumb>

				<div className="news-list">
					{generateNewsList()}
					{pageConfig.pages > 1 && (
						<div className="pagination-wrapper">
							<Pagination
								showSizeChanger={true}
								current={pageConfig.pageNum}
								onChange={pageChange}
								pageSizeOptions={[10, 25, 50, 100]}
								pageSize={pageConfig.pageSize}
								total={pageConfig.total}
								showTotal={showTotal}
							/>
						</div>
					)}
				</div>
				<div className="place-hold-bottom"></div>
			</div>
			{qrCodeUrlStr && <SubscriptionSide qrCodeUrlStr={qrCodeUrlStr} />}
		</div>
	)
}

export default NewsList
