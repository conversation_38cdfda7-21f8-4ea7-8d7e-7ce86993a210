.news-wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background: rgba(242, 242, 242, 0.59);
	text-align: center;
	overflow: auto;
	.news-header {
		height: auto;
		img {
			margin: 0;
			width: 100%;
			height: auto;
		}
	}
	.news-content {
		width: 80%;
		margin: 0 auto;
		padding-top: 30px;
		text-align: left;
		flex-grow: 0.98;
		.breadcrumb {
			padding-bottom: 10px;
		}
		.news-list {
			height: calc(100% - 60px);
			background: #fff;
			padding-bottom: 20px;
			.article-list {
				padding: 0 16px 20px 16px;
				padding-top: 24px;
				li a {
					cursor: pointer;
					color: rgb(90, 90, 90);
					line-height: 46px;
					border-bottom: 1px solid rgb(195, 195, 195);
					border-style: dashed;
					display: flex;
					justify-content: space-between;
					padding-right: 5px;
					.title {
						padding-left: 5px;
						width: 80%;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}
				li:hover {
					color: rgb(73, 120, 238);
					background: rgba(242, 242, 242, 0.59);
				}
				li a:hover {
					color: rgb(73, 120, 238);
					background: rgba(242, 242, 242, 0.59);
				}
			}
		}
		.no-data {
			line-height: 120px;
			text-align: center;
		}
		.place-hold-bottom {
			height: 30px;
		}
		.pagination-wrapper {
			text-align: right;
			padding-right: 20px;
		}
	}
}
