import React from 'react'
import styled from 'styled-components'
import Texty from 'rc-texty'
import 'rc-texty/assets/index.css'

type Props = {}

export default function HomePart3(props: Props) {
	const [textShow0, setTextShow0] = React.useState(false)
	const [textShow1, setTextShow1] = React.useState(false)
	const [textShow2, setTextShow2] = React.useState(false)
	const [textShow3, setTextShow3] = React.useState(false)
	const [textShow4, setTextShow4] = React.useState(false)

	const textRef1 = React.useRef<any>(null)
	const textRef2 = React.useRef<any>(null)
	const textRef3 = React.useRef<any>(null)
	const textRef4 = React.useRef<any>(null)
	const textRef5 = React.useRef<any>(null)
	const [modeluVisibilityArr, setModeluVisibilityArr] = React.useState([true, false, false, false, false])

	const myRef = React.useRef<any>()
	var visibilityChangeArr = [
		[true, false, false, false, false],
		[false, true, false, false, false],
		[false, false, true, false, false],
		[false, false, false, true, false],
		[false, false, false, false, true],
	]
	var index: number = 0

	React.useEffect(() => {
		let mounted = true
		const animationFunc = () => {
			let i = index % 5
			setTextWeatherShow((i + 4) % 5, false)
			setModeluVisibilityArr(visibilityChangeArr[i])
			setTextWeatherShow(i, true)
			index += 1
		}
		myRef.current = setInterval(() => {
			animationFunc()
		}, 2000)

		function handleVisibilityChange() {
			clearInterval(myRef.current)

			//浏览器切换事件
			if (document.visibilityState == 'visible') {
				//状态判断
				console.log('homePart3Inner')
				myRef.current = setInterval(function () {
					if (mounted) {
						animationFunc()
					}
				}, 2000)
			}
		}

		document.addEventListener('visibilitychange', handleVisibilityChange)

		return () => {
			mounted = false
			clearInterval(myRef.current)
			document.removeEventListener('visibilitychange', handleVisibilityChange)
		}
	}, [])

	const setTextWeatherShow = (textIndex, flag) => {
		switch (textIndex) {
			case 0:
				setTextShow0(flag)
				break
			case 1:
				setTextShow1(flag)
				break
			case 2:
				setTextShow2(flag)
				break
			case 3:
				setTextShow3(flag)
				break
			case 4:
				setTextShow4(flag)
				break
			default: //可选
		}
	}

	return (
		<HomePart3Wrapper>
			<div className="module module0" style={{ opacity: modeluVisibilityArr[0] ? '1' : '0.15' }}>
				<div className="block">
					<img src={require('@images/home/<USER>')} alt="" />
				</div>
				<div className="content">
					<div className="text animation" ref={textRef1} style={{ color: '#9cefff', fontSize: 20, fontWeight: 600 }}>
						<Texty type="left" mode="smooth">
							{textShow0 ? '资产数字化与多元金融底座' : ''}
						</Texty>
					</div>
					<p className="text static">资产数字化与多元金融底座</p>
				</div>
			</div>

			<div className="module module1" style={{ opacity: modeluVisibilityArr[1] ? '1' : '0.15' }}>
				<div className="block">
					<img src={require('@images/home/<USER>')} alt="" />
				</div>
				<div className="content">
					<div className="text animation" ref={textRef2} style={{ color: '#9cefff', fontSize: 20, fontWeight: 600 }}>
						<Texty type="left" mode="smooth">
							{textShow1 ? '资产数字化与多元金融底座' : ''}
						</Texty>
					</div>
					<p className="text static">资产数字化与多元金融底座</p>
				</div>
			</div>

			<div className="module module2" style={{ opacity: modeluVisibilityArr[2] ? '1' : '0.15' }}>
				<div className="block">
					<img src={require('@images/home/<USER>')} alt="" />
				</div>
				<div className="content">
					<div className="text animation" ref={textRef3} style={{ color: '#9cefff', fontSize: 20, fontWeight: 600 }}>
						<Texty type="left" mode="smooth">
							{textShow2 ? '资产数字化与多元金融底座' : ''}
						</Texty>
					</div>
					<p className="text static">资产数字化与多元金融底座</p>
				</div>
			</div>
			<div className="module module3" style={{ opacity: modeluVisibilityArr[3] ? '1' : '0.15' }}>
				<div className="block">
					<img src={require('@images/home/<USER>')} alt="" />
				</div>
				<div className="content">
					<div className="text animation" ref={textRef4} style={{ color: '#9cefff', fontSize: 20, fontWeight: 600 }}>
						<Texty type="left" mode="smooth">
							{textShow3 ? '资产数字化与多元金融底座' : ''}
						</Texty>
					</div>
					<p className="text static">资产数字化与多元金融底座</p>
				</div>
			</div>
			<div className="module module4" style={{ opacity: modeluVisibilityArr[4] ? '1' : '0.15' }}>
				<div className="block">
					<img src={require('@images/home/<USER>')} alt="" />
				</div>
				<div className="content">
					<div className="text animation" ref={textRef5} style={{ color: '#9cefff', fontSize: 20, fontWeight: 600 }}>
						<Texty type="left" mode="smooth">
							{textShow4 ? '资产数字化与多元金融底座' : ''}
						</Texty>
					</div>
					<p className="text static">资产数字化与多元金融底座</p>
				</div>
			</div>
		</HomePart3Wrapper>
	)
}

const HomePart3Wrapper = styled.div`
	width: 100%;
	height: 100%;
	background-image: url(${require('@images/home/<USER>')});
	background-size: 1920px 1080px;
	background-position: center;
	position: relative;
	.module {
		position: absolute;
		transition: all 1s;
		.block {
			width: 292px;
			height: 342px;
			img {
				width: 100%;
			}
		}
		.content {
			width: 440px;
			height: 60px;
			position: absolute;
			display: flex;
			align-items: center;
			justify-content: center;
			.animation {
			}
			.static {
				position: absolute;
				opacity: 0;
			}
			p {
				width: 396px;
				height: 22px;
				font-size: 20px;
				font-weight: 600;
				color: #9cefff;
				line-height: 22px;
				text-align: center;
			}
		}
	}
	.module:hover {
		opacity: 1 !important;
		.animation {
			opacity: 0;
		}
		.static {
			opacity: 1;
		}
	}
	.module0 {
		left: calc(50vw - 376px);
		top: calc(50% - 230px);

		.content {
			top: -75px;
			left: -80px;
		}
	}

	.module1 {
		left: calc(50vw + 90px);
		top: calc(50% - 230px);
		.content {
			top: -75px;
			left: -80px;
		}
	}

	.module2 {
		left: calc(50vw + 358px);
		top: calc(50% - 48px);
		.content {
			bottom: -20px;
			left: -60px;
		}
	}

	.module3 {
		left: calc(50vw - 134px);
		top: calc(50% - 49px);
		.content {
			bottom: -20px;
			left: -60px;
		}
	}

	.module4 {
		left: calc(50vw - 621px);
		top: calc(50% - 61px);
		.content {
			bottom: -20px;
			left: -60px;
		}
	}

	@media only screen and (max-width: 1280px) {
		.module0 {
			/* left: 303px; */
			left: 257px;
		}
		.module1 {
			/* left: 772px; */
			left: 725px;
		}
		.module2 {
			/* left: 1042px; */
			left: 990px;
		}
		.module3 {
			/* left: 550px; */
			left: 502px;
		}
		.module4 {
			/* left: 60px; */
			left: 18px;
		}
	}

	.span-animation {
		display: inline-block;
		animation: spa 100ms ease;
	}
	@keyframes spa {
		from {
			opacity: 0;
			transform: translateX(300px);
		}
		to {
			opacity: 1;
			transform: translateX(0);
		}
	}
`
