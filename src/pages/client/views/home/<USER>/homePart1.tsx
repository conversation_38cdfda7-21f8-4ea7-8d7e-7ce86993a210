/*
 * @Date: 2022-06-09 15:00:27
 * @LastEditors: jcl
 * @LastEditTime: 2022-06-20 10:58:40
 * @FilePath: \frontend-web\src\pages\client\views\home\components\homePart1.tsx
 * @Description: file content
 */

import React from 'react'
import styled from 'styled-components'
import { history } from '@src/utils/router'
const defaultLogo = require('@src/assets/images/logos/qiyeTitle_1.png')
const fromUserLogo = require('@src/assets/images/logos/jiangyinlogo_1.png')

const defaultLoginBg = require('@images/logos/qiyebg_1.jpg')
const userLoginBg = require('@images/logos/perfectInformationBg.png')

const guidanceIcon = require('@images/home/<USER>')
type Props = {
	title?: string
	desc?: string
	isFromUser: boolean
}

export default function HomePart1({ isFromUser }: Props) {
	return (
		<>
			<HomePart1Wrapper>
				<div className="bg" style={{ backgroundImage: `url(${isFromUser ? userLoginBg : defaultLoginBg})` }} />
				<div className="content-wrapper">
					<div className="center">
						<div className="titleBg">
							<img src={isFromUser ? fromUserLogo : defaultLogo} alt="" />
						</div>
					</div>
					<div className="guidanceRoot" onClick={() => history.push('/home/<USER>')}>
						<img src={guidanceIcon} alt="" />
						<span>操作指导</span>
					</div>
				</div>
			</HomePart1Wrapper>
		</>
	)
}

const HomePart1Wrapper = styled.div`
	position: relative;
	width: 100%;
	height: 100%;
	.clientLogo {
		width: 108px;
		height: 80px;
		position: absolute;
		left: 100px;
		top: 20px;
		img {
			width: 100%;
		}
	}
	.content-wrapper {
		height: 100%;
		width: 100%;
		position: relative;
		display: flex;
		justify-content: center;
		.guidanceRoot {
			position: fixed;
			display: flex;
			align-items: center;
			right: 60px;
			top: 30px;
			font-size: 16px;
			cursor: pointer;
			color: #ffffff;
			img {
				width: 30px;
				height: 30px;
				margin-right: 20px;
			}
		}
		.center {
			width: 100%;
			height: 48vh;
			display: flex;
			justify-content: center;
			align-items: flex-end;
			.titleBg {
				img {
					width: 480px;
					height: 136px;
					margin: 0 0 0 48px;
				}
			}
			.title {
				font-size: 98px;
				font-weight: 500;
				color: #d3eeff;
				line-height: 98px;
				margin-bottom: 17px;
				text-align: left;
			}
			.desc {
				padding-right: 53px;
				font-size: 40px;
				font-weight: 400;
				color: #d3eeff;
				line-height: 40px;
			}
		}
	}
	.bg {
		position: absolute;
		height: 100%;
		width: 100%;
		overflow: hidden;
		background-image: url(${require('@images/home/<USER>')});
		background-size: 100% 100%;
		.partImg {
			height: 100%;
			width: 100%;
		}
	}
	.information {
		/* width: 590px; */
		height: 40px;
		font-size: 18px;
		font-weight: 400;
		color: #99bfc8;
		line-height: 40px;
		margin-bottom: 20px;
		text-align: center;
		position: relative;
		bottom: 60px;
	}
`
