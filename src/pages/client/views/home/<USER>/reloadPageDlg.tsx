import { Modal, Button } from 'antd'
import React from 'react'
import styled from 'styled-components'
import { ExclamationCircleFilled } from '@ant-design/icons'

interface PropsStruck {
	reLoadVisible: boolean
	onCancel: () => void
}

function reloadPageDlg(props: PropsStruck) {
	return (
		<>
			<Modal
				open={props.reLoadVisible}
				title="版本更新提示"
				closable={false}
				maskClosable={false}
				footer={[
					<Button
						key="reload-button"
						type="primary"
						onClick={() => {
							props.onCancel()
							window.location.reload()
						}}
					>
						确定
					</Button>,
				]}
				className="wx_Modal wx_Modal_confirm"
			>
				<div className="wx_Modal_Main">
					<p style={{ margin: '24px 50px 52px 50px', textAlign: 'center' }} className="wx_Modal_Main_tips">
						<ExclamationCircleFilled style={{ color: '#F8AD15', marginRight: '5px' }} />
						当前版本较低，请点击确定按钮进行版本升级。
					</p>
				</div>
			</Modal>
		</>
	)
}

const detailModal = styled(reloadPageDlg)``
export default detailModal
