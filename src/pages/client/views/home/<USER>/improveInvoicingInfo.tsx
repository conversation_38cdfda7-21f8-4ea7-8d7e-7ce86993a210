import SmallTitle from '@src/globalComponents/SmallTitle'
import { getStorage } from '@src/pages/client/biz/bizIndex'
import { Alert, Button, Form, Input } from 'antd'
import React, { useEffect } from 'react'
import styled from 'styled-components'
import { invoicingManageModuleApi } from '@factor/api'
import { history } from '@src/utils/router'

const ImproveInvoicingInfo = () => {
	const [form] = Form.useForm()
	const { jumpPageMap } = history.location.state || { jumpPageMap: {} }
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')
	const [loading, setLoading] = React.useState(false)

	useEffect(() => {
		let bankNo = '',
			bankName = ''
		//如果登录时没有完善银行卡信息，则登录时取不到银行卡信息，登录之后会指示用户完善银行卡，完善银行卡之后缓存银行卡的信息在localStorage中，完善开票信息时，直接从缓存中获取银行卡信息，反之银行卡信息从本地的缓存的ext中获取通用卡的银行账号
		if (ext.bankCards?.length === 0) {
			bankNo = getStorage('accountNum')
			bankName = getStorage('accountBank')
		} else {
			for (let item of ext?.bankCards || []) {
				if (item['cardType'] === 'GENERAL') {
					bankNo = item.accountNum
					bankName = item.accountBank
				}
			}
		}
		form.setFieldsValue({
			companyName: ext?.user?.company?.companyName,
			address: ext?.user?.company?.address,
			socialCreditCode: ext?.user?.company?.socialCreditCode,
			bankNo,
			bankName,
			recipientEmail: ext?.user?.email,
		})
	}, [])

	/**
	 * 完善开票信息
	 */
	const onFinishInvoingInfo = () => {
		setLoading(true)
		form
			.validateFields()
			.then(values => {
				invoicingManageModuleApi
					.addInvoiceHeader(values)
					.then(res => {
						if (res) {
							history.push('/home/<USER>', {
								jumpPageMap: { ...jumpPageMap, ...{ isImproveInvoicingInfo: false } },
							})
							history.go(0)
						}
					})
					.catch(err => {
						console.log(err)
					})
				setLoading(false)
			})
			.catch(error => {
				setLoading(false)
			})
	}

	return (
		<ImproveInvoicingInfoWrap>
			<div className="invoicing-content">
				<div className="invoicing-title">完善企业开票信息</div>
				<Alert
					style={{ marginBottom: '20px' }}
					message={<div style={{ fontSize: '14px' }}>说明：请维护您的开票信息，并确保与税务局数据一致，若开票信息有变更，请及时更新</div>}
					description={
						<div>
							<div style={{ fontSize: '14px' }}>1、此发票信息用于平台运营方为您开具基于平台服务费等相关的发票</div>
							<div style={{ fontSize: '14px' }}>2、邮寄信息用于平台为您寄送纸质发票、协议文件等</div>
						</div>
					}
					type="info"
					showIcon
				/>
				<Form form={form} className="form" onFinish={onFinishInvoingInfo}>
					<SmallTitle text="开票内容"></SmallTitle>
					<ul className="form-list">
						<Form.Item className="form-item" label="发票抬头" name="companyName" rules={[{ required: true, message: '企业全称不能为空', whitespace: true }]}>
							<Input disabled={true} />
						</Form.Item>
						<Form.Item
							className="form-item"
							label="纳税人识别码"
							name="socialCreditCode"
							rules={[{ required: true, message: '纳税人识别码不能为空', whitespace: true }]}
						>
							<Input disabled={true} />
						</Form.Item>
						<Form.Item className="form-item" label="公司地址" name="address" rules={[{ required: true, message: '请输入公司地址', whitespace: true }]}>
							<Input placeholder="请输入" maxLength={64} />
						</Form.Item>
						<Form.Item className="form-item" label="公司电话" name="phone" rules={[{ required: true, message: '请输入公司电话', whitespace: true }]}>
							<Input placeholder="请输入" maxLength={20} />
						</Form.Item>
						<Form.Item className="form-item" label="开户银行" name="bankName" rules={[{ required: true, message: '请输入开户行', whitespace: true }]}>
							<Input placeholder="请输入" maxLength={64} />
						</Form.Item>
						<Form.Item
							className="form-item"
							label="银行账号"
							name="bankNo"
							rules={[
								{
									required: true,
									message: '请输入账号，由数字组成',
									pattern: /^[0-9]*$/,
									whitespace: true,
								},
							]}
						>
							<Input placeholder="请输入" maxLength={32} />
						</Form.Item>
					</ul>
					<SmallTitle text="邮寄信息"></SmallTitle>
					<ul className="form-list">
						<Form.Item
							className="form-item"
							label="收件人姓名"
							name="recipientName"
							rules={[{ required: true, message: '请输入收件人姓名', whitespace: true }]}
						>
							<Input placeholder="请输入" maxLength={20} />
						</Form.Item>
						<Form.Item
							className="form-item"
							label="收件人电话"
							name="recipientPhone"
							rules={[{ required: true, message: '请输入收件人电话', whitespace: true }]}
						>
							<Input placeholder="请输入" maxLength={20} />
						</Form.Item>
						<Form.Item
							className="form-item"
							label="收件人地址"
							name="recipientAddress"
							extra="该地址用于邮寄发票等信息，请准确填写包含省、市的详细地址"
							rules={[{ required: true, message: '请输入收件人地址', whitespace: true }]}
						>
							<Input placeholder="请输入" maxLength={64} />
						</Form.Item>
					</ul>
					<SmallTitle text="其他"></SmallTitle>
					<ul className="form-list">
						<Form.Item
							className="form-item"
							label="接收邮箱"
							name="recipientEmail"
							extra="该邮箱用于接收电子发票，请填写正确的邮箱"
							rules={[
								{
									required: true,
									message: '请输入电子邮箱',
									whitespace: true,
								},
								{
									type: 'email',
									message: '请输入邮箱，并确认格式正确',
									whitespace: true,
								},
							]}
						>
							<Input placeholder="请输入" maxLength={64} />
						</Form.Item>
					</ul>
					<div className="invoicing-btn">
						<Button type="primary" htmlType="submit" loading={loading}>
							提交
						</Button>
					</div>
				</Form>
			</div>
		</ImproveInvoicingInfoWrap>
	)
}

const ImproveInvoicingInfoWrap = styled.div`
	width: 100%;
	height: 100%;
	overflow: auto;
	.invoicing-content {
		width: 860px;
		padding: 40px 0;
		margin: 0 auto;
		.invoicing-title {
			text-align: center;
			font-size: 30px;
			margin-bottom: 40px;
		}
		.form-list .form-item:last-child {
			width: 50%;
		}
		.ant-form-item-extra {
			white-space: nowrap;
		}
		.invoicing-btn {
			text-align: center;
		}
	}
`

export default ImproveInvoicingInfo
