import React, { useEffect } from 'react'
import { history } from '@utils/router'
import styled from 'styled-components'
import UserInfo from '@globalComponents/UserInfo'
import loginModuleApis from '@src/pages/client/api/login'
import { message } from 'antd'

function AuthException() {
	useEffect(() => {
		setTimeout(() => {
			const ext = JSON.parse(localStorage.getItem('ext') || '[]')
			const userInfo = ext.user || null
			if (!userInfo) {
				message.warning('未登录')
				history.push('/home')
			} else {
				setUserInfo(userInfo)
			}
		}, 200)
	}, [])
	const goHome = () => {
		history.push('/home')
	}
	const [getUserInfo, setUserInfo] = React.useState<any>(null)

	const handleLogout = async (cb: () => void) => {
		await loginModuleApis.logout().catch(error => {
			console.log(error)
		})
		message.success('退出成功')
		history.push('/home')
		cb()
	}
	return (
		<AuthStyle>
			<div>
				<div className="header">
					<span onClick={goHome}></span>
					<UserInfo user={getUserInfo} account={getUserInfo && getUserInfo.email ? getUserInfo.email : ''} onLogout={handleLogout} />
				</div>
				<div className="body">
					<p>您的账号暂无相关业务权限，请联系企业管理员或运营人员处理</p>
				</div>
			</div>
		</AuthStyle>
	)
}

const AuthStyle = styled.div`
	.header {
		width: 100%;
		height: 80px;
		line-height: 80px;
		background: rgb(231, 233, 236);
		display: flex;
		justify-content: space-between;
		padding: 0 80px;
		span {
			/* display: block; */
			cursor: pointer;
		}
	}
	.body {
		p {
			margin-top: 200px;
			text-align: center;
		}
	}
`

export default AuthException
