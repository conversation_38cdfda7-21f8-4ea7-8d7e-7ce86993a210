import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import { b64toBlob } from '@src/utils/util'
import { downloadFile2 } from '@src/utils/exportBlob'
import { Spin, Empty } from 'antd'
import { If, Then, Else } from 'react-if'
import { commonModuleApi, announcementManageApi } from '@src/pages/client/api'
import { history } from '@src/utils/router'

const logo = require('@src/assets/images/logos/logoblack.png')
const downloadIcon = require('@src/assets/images/home/<USER>')
const operationManual = require('@src/assets/images/home/<USER>')
const makeVideo = require('@src/assets/images/home/<USER>')
const operationbg = require('@src/assets/images/home/<USER>')

let fileUrlList = []

export default function guidepage() {
	const [fileUrl, setFileUrl] = useState('')
	const [useOperationManualList, setUseOperationManualList] = useState<Array<any>>([])
	const [useOperationVideoList, setUseOperationVideoList] = useState<Array<any>>([])
	const [viewLoading, setViewLoading] = useState<boolean>(false)
	const [current, setCurrent] = useState<any>(null)

	useEffect(() => {
		getGuidepageData()
		return () => {
			if (fileUrlList && fileUrlList.length) {
				fileUrlList.map(item => {
					URL.revokeObjectURL(item?.url)
				})
				fileUrlList = []
			}
			resetData()
		}
	}, [])

	const setClickState = (data: any) => {
		let _findUseOperationManualList = useOperationManualList.find(item => data?.id === item?.id)
		let _findUseOperationVideoList = useOperationVideoList.find(item => data?.id === item?.id)

		let updataList = null
		if (_findUseOperationManualList) {
			updataList = useOperationManualList.map(item => {
				if (item?.id === _findUseOperationManualList?.id) {
					return {
						...item,
						...data,
					}
				}
				return item
			})
			setUseOperationManualList(updataList)
			return
		}
		if (_findUseOperationVideoList) {
			updataList = useOperationVideoList.map(item => {
				if (item?.id === _findUseOperationVideoList?.id) {
					return {
						...item,
						...data,
					}
				}
				return item
			})
			setUseOperationVideoList(updataList)
		}
	}

	const getGuidepageData = async () => {
		setViewLoading(true)
		let res = await announcementManageApi.getOperationguideList().catch(err => {
			resetData()
			console.log(err)
		})
		let { operationManualList = [], operationVideoList = [] } = res || {}
		let currentItem = operationManualList.length ? operationManualList[0] : operationVideoList.length ? operationVideoList[0] : null
		setUseOperationManualList(operationManualList)
		setUseOperationVideoList(operationVideoList)
		setCurrent(currentItem)
		getFileUrl(currentItem)
	}

	const resetData = () => {
		setCurrent(null)
		setFileUrl('')
		setViewLoading(false)
		setUseOperationVideoList([])
		setUseOperationManualList([])
	}

	const getFileUrl = async (item: any) => {
		if (!item) {
			setViewLoading(false)
			return
		}
		let res = null
		res = await commonModuleApi.downloadFile({ fileUrl: item?.minioUrl }).catch(err => {
			resetData()
			setClickState({ ...item, isClick: false })
			console.log(err)
		})
		setClickState({ ...item, isClick: false })
		if (res) {
			const blob = b64toBlob(res)
			fileUrlList.push({ id: item.id, url: URL.createObjectURL(blob) })

			setFileUrl(URL.createObjectURL(blob))
		}
		setViewLoading(false)
	}

	const GuidepageTitle = () => {
		return (
			<div className="guidepage_title">
				<img src={logo} alt="" />
				<span onClick={() => history.push('/home')}>首页</span>
			</div>
		)
	}

	const menuClick = (item: any) => {
		let findItem = fileUrlList.find(i => item?.id === i?.id)
		setViewLoading(true)

		if (item?.isClick) {
			if (findItem) {
				setFileUrl(findItem?.url)
				setViewLoading(false)
			}
			setCurrent(item)
			return
		}
		setClickState({ ...item, isClick: true })
		setCurrent(item)
		setFileUrl('')
		if (findItem) {
			setFileUrl(findItem?.url)
			setViewLoading(false)
			return
		}
		getFileUrl(item)
	}

	const GuidepageContent = () => {
		return (
			<GuidepageContentWarp>
				<div
					className="downloadwarp"
					style={{ visibility: fileUrl ? 'visible' : 'hidden' }}
					onClick={() => {
						if (fileUrl) {
							downloadFile2(fileUrl, current?.type === 1 ? '操作指南' : '指导视频.mp4')
						}
					}}
				>
					<img src={downloadIcon} alt="" />
					<span>下载</span>
				</div>
				<div className="content">
					<div className="menu">
						<div className="head" style={{ marginBottom: !useOperationManualList.length ? '130px' : '10px' }}>
							<img src={operationManual} alt="" />
						</div>
						{useOperationManualList && useOperationManualList.length
							? useOperationManualList.map(item => {
									return (
										<div className={`item ${item.id === current?.id ? 'active' : 'dactive'}`} onClick={() => menuClick(item)}>
											{item.title}
										</div>
									)
							  })
							: null}
						<div className="head">
							<img src={makeVideo} alt="" />
						</div>
						{useOperationVideoList && useOperationVideoList.length
							? useOperationVideoList.map(item => {
									return (
										<div className={`item ${item.id === current?.id ? 'active' : 'dactive'}`} onClick={() => menuClick(item)}>
											{item.title}
										</div>
									)
							  })
							: null}
					</div>
					<Spin spinning={viewLoading} wrapperClassName="preview">
						<If condition={fileUrlList.find(item => item?.id === current?.id)?.url}>
							<Then>
								<If condition={current && current?.type === 1}>
									<Then>
										<iframe id="fileiframe" src={fileUrlList.find(item => item?.id === current?.id)?.url} frameBorder="0" scrolling="no" />
									</Then>
									<Else>
										<video src={fileUrlList.find(item => item?.id === current?.id)?.url} controls controlsList="nodownload" className="video-player"></video>
									</Else>
								</If>
							</Then>
							<Else>
								{current?.type === 2 ? (
									<img src={operationbg} alt="" style={{ width: '100%', height: '100%' }} />
								) : (
									<Empty style={{ paddingTop: 300 }} description={<span>{!viewLoading ? '' : ''}</span>} />
								)}
							</Else>
						</If>
					</Spin>
				</div>
			</GuidepageContentWarp>
		)
	}

	return (
		<GuidepageWarp>
			<GuidepageTitle></GuidepageTitle>
			<GuidepageContent></GuidepageContent>
		</GuidepageWarp>
	)
}

const GuidepageWarp = styled.div`
	padding: 20px 40px;
	width: 100%;
	height: 100%;
	overflow: hidden;
	background-image: url(${require('@images/home/<USER>')});
	background-size: 100% 100%;
	background-repeat: no-repeat;
	.guidepage_title {
		display: flex;
		align-items: center;
		font-size: 16px;
		color: rgba(0, 0, 0, 0.65);
		cursor: pointer;
		margin-bottom: 43px;
		img {
			width: 160px;
			height: 48px;
			margin-right: 60px;
		}
	}
`

const GuidepageContentWarp = styled.div`
	display: flex;
	flex-direction: column;
	padding: 0 110px;
	.downloadwarp {
		text-align: right;
		font-size: 14px;
		color: rgba(0, 0, 0, 0.65);
		cursor: pointer;
		img {
			width: 18px;
			height: 18px;
			margin-right: 6px;
		}
	}
	.content {
		display: flex;
		height: 70vh;
		margin-top: 20px;
		.menu {
			width: 180px;
			margin-right: 20px;
			overflow-y: auto;
			.head {
				padding-bottom: 20px;
				border-bottom: 1px solid #e5efff;
				margin-bottom: 10px;
				img {
					width: 116px;
					height: 40px;
				}
			}
			.item {
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 14px;
				padding: 10px 0;
				cursor: pointer;
			}
		}
		.active {
			background: #eff5ff;
			border-radius: 6px;
			color: #1890ff;
		}
		.dactive {
			color: rgba(0, 0, 0, 0.45);
		}
		.preview {
			flex: 1;
			position: relative;
			.ant-spin-container {
				width: 100%;
				height: 100%;
			}
			.video-icon {
				position: absolute;
				top: 50%;
				left: 50%;
				width: 32px;
				height: 32px;
				transform: translate(-50%, -50%);
				cursor: pointer;
				z-index: 1;
			}
			#fileiframe {
				width: 100%;
				height: 100%;
			}
			.video-player {
				width: 100%;
				height: 100%;
			}
		}
	}
`
