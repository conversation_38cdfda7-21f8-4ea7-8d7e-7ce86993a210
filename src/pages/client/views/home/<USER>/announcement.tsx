import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import ViewModal from '@src/pages/client/components/viewAnnouncementModal'

interface Props {
	/**
	 * 公告的列表
	 */
	dataList: Array<any>
	onchange: (data: any) => void
}

const ViewModalWrap = (props: Props) => {
	const { dataList, onchange } = props
	const [viewList, setViewList] = useState<Array<any>>(dataList)

	useEffect(() => {
		modalInitWithIndex()
	}, [])

	// 数据初始化 加上visible 给每一个公告modal
	const modalInitWithIndex = (id?: number) => {
		let array: Array<any> = viewList
		if (!id) {
			array.forEach((item, index) => {
				if (index === 0) item.visible = true
				else item.visible = false
			})
		} else {
			let currentModalIndex = -1
			array.forEach((item, index) => {
				if (id === item.id) {
					currentModalIndex = index
				}
			})
			array[currentModalIndex].visible = false
			//确认下一条公告存在
			if (currentModalIndex + 1 > 0 && currentModalIndex + 1 < array.length) array[currentModalIndex + 1].visible = true
		}
		setViewList([...array])
		onchange([...array.filter(item => item.visible)])
	}

	return (
		<Wrap>
			{viewList.length > 0
				? dataList.map((item: any) => {
						return (
							<ViewModal
								key={item.id}
								data={item}
								afterClose={(id: number) => {
									modalInitWithIndex(id)
								}}
							/>
						)
				  })
				: null}
		</Wrap>
	)
}

const Wrap = styled.div``

export default ViewModalWrap
