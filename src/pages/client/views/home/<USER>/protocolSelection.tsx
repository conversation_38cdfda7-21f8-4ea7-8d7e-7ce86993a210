/*
 * @Date: 2022-07-05 17:04:14
 * @LastEditors: jcl
 * @LastEditTime: 2022-07-22 13:43:52
 * @FilePath: \frontend-web\src\pages\client\views\home\base\protocolSelection.tsx
 * @Description: file content
 */
import { Button, Checkbox, Form } from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import styled from 'styled-components'
import { loginApis } from '@src/pages/client/api'
import { history } from '@src/utils/router'
import { addLogoutForDev } from '@src/pages/client/biz/bizIndex'
import ProtocolViewer from '@src/globalComponents/protocol-pdf-viewer'
import { uiData } from '@src/pages/client/store'
import { observer } from 'mobx-react-lite'
const sign = require('@src/assets/images/home/<USER>')
interface Props {}
const ProtocolSelection = () => {
	const jumpPageMap = uiData.jumpPageMap

	const [checkBoxIsClick, setCheckBoxIsClick] = React.useState<boolean>(false)
	const [aggreeAll, setAggreeAll] = useState(false)
	const [userRuleProtocolPdfUrl, setUserRuleProtocolPdfUrl] = React.useState<string>('')
	const [keyProtocolPdfUrl, setKeyProtocolPdfUrl] = React.useState<string>('')
	const [platformServerProtocolPdfUrl, setPlatformServerProtocolPdfUrl] = React.useState<string>('')

	const [protocolPdfUrls, setProtocolPdfUrls] = React.useState<string[]>([])
	const [isModalVisible, setIsModalVisible] = React.useState<boolean>(false)
	const [subLoding, setSubLoding] = React.useState(false)

	const [viewerTitle, setViewerTitle] = React.useState('')
	const [form] = Form.useForm()

	const keyProtocol = useRef<any>(null)
	const platformProtocol = useRef<any>(null)
	const userRuleProtocol = useRef<any>(null)
	// 初次进来加载协议并保存
	React.useEffect(() => {
		// 防止在组件卸载后更新state导致内存泄露
		let mounted = true
		// 得到协议
		const { signProtocol: protocols } = jumpPageMap
		const pdfArr = []
		protocols?.forEach(protocol => {
			if (mounted) {
				if (protocol.protocolType === 'userRule') {
					setUserRuleProtocolPdfUrl(protocol.pdfUrl)
					pdfArr.push(protocol.pdfUrl)
					userRuleProtocol.current = { protocolUrl: protocol.pdfUrl, protocolVersion: protocol.versionId }
				} else if (protocol.protocolType === 'platformServer') {
					setPlatformServerProtocolPdfUrl(protocol.pdfUrl)
					pdfArr.push(protocol.pdfUrl)
					platformProtocol.current = { protocolUrl: protocol.pdfUrl, protocolVersion: protocol.versionId }
				} else if (protocol.protocolType === 'companyKey') {
					setKeyProtocolPdfUrl(protocol.pdfUrl)
					pdfArr.push(protocol.pdfUrl)
					keyProtocol.current = { protocolUrl: protocol.pdfUrl, protocolVersion: protocol.versionId }
				}
				setProtocolPdfUrls(pdfArr)
			}
		})
		return () => {
			mounted = false
		}
	}, [])

	useEffect(() => {
		if (aggreeAll) {
			setCheckBoxIsClick(true)
		}
	}, [aggreeAll])

	const onFinish = () => {
		form
			.validateFields()
			.then(() => {
				// 验证通过则，往下走
				perfectInformationAll()
			})
			.catch(errorInfo => {
				console.log('errorInfo', errorInfo)
			})
	}

	const perfectInformationAll = () => {
		setSubLoding(true)

		const params = {
			userRuleProtocol: userRuleProtocol.current,
			keyProtocol: keyProtocol.current,
			platformProtocol: platformProtocol.current,
		}

		loginApis
			.signProtocol(params)
			.then(res => {
				history.push('/home/<USER>')
				history.go(0)
			})
			.catch(e => {
				console.log(e)
			})
			.finally(() => {
				setSubLoding(false)
			})
	}

	const onChange = e => {
		if (aggreeAll) {
			setCheckBoxIsClick(e.target.checked)
			console.log(`checked = ${e.target.checked}`)
		} else {
			setProtocolPdfUrls([userRuleProtocolPdfUrl, platformServerProtocolPdfUrl, keyProtocolPdfUrl].filter(item => item !== ''))
			setIsModalVisible(true)
		}
	}

	const clickShowView = async type => {
		let orderedUrls = []
		if (type === 'userRule') {
			orderedUrls = [userRuleProtocolPdfUrl, platformServerProtocolPdfUrl, keyProtocolPdfUrl].filter(item => item !== '')
		} else if (type === 'platformServer') {
			orderedUrls = [platformServerProtocolPdfUrl, keyProtocolPdfUrl, userRuleProtocolPdfUrl].filter(item => item !== '')
		} else if (type === 'companyKey') {
			orderedUrls = [keyProtocolPdfUrl, userRuleProtocolPdfUrl, platformServerProtocolPdfUrl].filter(item => item !== '')
		}
		// test
		setProtocolPdfUrls(orderedUrls)
		setIsModalVisible(true)
	}
	const getProtocolText = () => {
		let result = '同意并签署《平台服务及用户操作规则》、《平台服务章程》和《密钥确认函》'
		const list: [any?] = []
		Boolean(userRuleProtocolPdfUrl) && list.push('《平台服务及用户操作规则》')
		Boolean(platformServerProtocolPdfUrl) && list.push('《平台服务章程》')
		Boolean(keyProtocolPdfUrl) && list.push('《密钥确认函》')
		result = '同意并签署' + list.join('、')
		if (result.indexOf('、') !== result.lastIndexOf('、')) {
			const lastIndex = result.lastIndexOf('、')
			result = result.substring(0, lastIndex) + '和' + result.substring(lastIndex + 1)
		}
		return <span className="resultBox">{result}</span>
	}
	return (
		<PerfectInformationWrapper>
			<div className="perfectInformation">
				<div className="contentbox">
					<div className="signProtocol">
						<div className="title">签署平台协议</div>
						<div className="confirm">由于您是首次使用该平台，或平台协议有更新，请先签署以下协议</div>
						<div className="sign-box">
							<div className="sign-box-left">
								<img src={sign} alt="" />
							</div>
							<div className="sign-box-right">
								<div className="pdf">
									{userRuleProtocolPdfUrl ? (
										<div
											className="pdfItem"
											onClick={() => {
												clickShowView('userRule')
											}}
										>
											平台服务及用户操作规则.pdf
										</div>
									) : null}
									{platformServerProtocolPdfUrl ? (
										<div
											className="pdfItem"
											onClick={() => {
												clickShowView('platformServer')
											}}
										>
											平台服务章程.pdf
										</div>
									) : null}
									{keyProtocolPdfUrl ? (
										<div
											className="pdfItem"
											onClick={() => {
												clickShowView('companyKey')
											}}
										>
											密钥确认函.pdf
										</div>
									) : null}
								</div>
							</div>
						</div>
						<div className="checkbox">
							<Checkbox onChange={onChange} checked={checkBoxIsClick} style={{ marginRight: 10 }} />
							{getProtocolText()}
						</div>
					</div>
				</div>
				<div>
					<Button className="finish" type="primary" disabled={!checkBoxIsClick} onClick={onFinish} loading={subLoding} block>
						提交
					</Button>
					<br />
					<div className="loginout">{addLogoutForDev()}</div>
				</div>
			</div>

			{/* <PDFViewer
				onCancel={() => {
					setprotocolPdfUrl('')
					setIsModalVisible(false)
				}}
				title={viewerTitle}
				pdfUrl={protocolPdfUrl}
				visible={isModalVisible}
			/> */}
			{isModalVisible && (
				<ProtocolViewer
					visible={isModalVisible}
					pdfUrl={protocolPdfUrls}
					aggree={aggreeAll}
					onCancel={() => setIsModalVisible(false)}
					onFinish={() => {
						setAggreeAll(true)
					}}
				/>
			)}
		</PerfectInformationWrapper>
	)
}

export default observer(ProtocolSelection)
const PerfectInformationWrapper = styled.div`
	width: 100%;
	height: 100%;
	background-image: url(${require('@images/home/<USER>')});
	background-size: cover;
	// background-position: bottom;
	background-repeat: no-repeat;
	display: flex;
	justify-content: center;
	color: #d7ecff;
	font-size: 14px;
	padding-top: 50px;
	.perfectInformation {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 752px;
		padding: 20px;
		// padding-bottom: 93px;
		box-sizing: border-box;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: 110px;
		background-color: #fff;
		.bigTitle {
			color: #d7ecff;
			text-align: center;
			font-size: 30px;
			// margin-bottom: 40px;
		}
		.contentbox {
			// position: absolute;
			// left: 50%;
			// top: 50%;
			// transform: translate(-50%, -50%);
			width: 716px;
			// padding: 0 20px;
			// background-color: #fff;
			.signProtocol {
				color: rgba(0, 0, 0, 0.65);
				.title {
					font-size: 18px;
					text-align: center;
					color: rgba(0, 0, 0, 0.85);
				}
				.confirm {
					margin: 20px 0;
					text-align: center;
				}
				.sign-box {
					// width: 688px;
					padding: 40px;
					box-sizing: border-box;
					display: flex;
					justify-content: space-between;
					// margin-top: 15px;
					background-color: #f0f6ff;
					.sign-box-left {
						width: 170px;
						img {
							width: 170px;
						}
					}
					.sign-box-right {
						width: 378px;
					}
				}
				.pdf {
					color: black;
					display: flex;
					flex-direction: column;
					align-items: center;
					.pdfItem {
						width: 378px;
						height: 56px;
						background-image: url(${require('@images/home/<USER>')});
						background-size: cover;
						background-repeat: no-repeat;
						margin-bottom: 20px;
						border-radius: 6px;
						text-align: left;
						line-height: 56px;
						cursor: pointer;
						text-indent: 66px;
						// border: 1px dashed #ccc;
						:last-child {
							margin-bottom: 0;
						}
					}
				}
				.resultBox {
					color: #545559;
				}
				.checkbox {
					margin: 15px 0 15px;
					text-align: center;
					line-height: 30px;
					// 复选框字体颜色
					.ant-checkbox-wrapper {
						// color: #1890ff;
					}
					.ant-checkbox-disabled + span {
						// color: #1890ff;
					}
				}
			}
		}
		.loginout {
			text-align: center;
			margin-top: 10px;
		}
		.finish {
			width: 200px;
			height: 40px;
		}
	}
`
