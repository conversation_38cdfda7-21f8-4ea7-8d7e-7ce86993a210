import React from 'react'
import styled from 'styled-components'
import Texty from 'rc-texty'
import { useDebounce } from '@utils/optimization'

type Props = {}
export default function HomePart4(props: Props) {
	const [TextShow, setTextShow] = React.useState(true)
	const [text, setText] = React.useState('绿色金融解决方案')
	const mouseDebounceCallBack = useDebounce(handleMouseEnter, 500)

	function handleMouseEnter(initText) {
		// 设置先前的文本并让它消失
		setTextShow(false)

		// 设置现在的文本，并让它显示
		setText(initText)

		setTextShow(true)
	}

	return (
		<HomePart4Wrapper>
			<div className="bigModule">
				<Texty duration={100} interval={20}>
					{TextShow ? text : ''}
				</Texty>
			</div>
			<div className="beam">
				<div className="beam1"></div>
				<div className="beam2"></div>
				<div className="beam3"></div>
				<div className="beam4"></div>
				<div className="beam5"></div>
				<div className="beam6"></div>
			</div>
			<div className="smallModule">
				<div className="business business1" onMouseEnter={() => mouseDebounceCallBack('供应链金融解决方案')}>
					供应链金融解决方案
				</div>
				<div className="business business2" onMouseEnter={() => mouseDebounceCallBack('绿色金融解决方案')}>
					绿色金融解决方案
				</div>
				<div className="business business3" onMouseEnter={() => mouseDebounceCallBack('融资租赁解决方案')}>
					融资租赁解决方案
				</div>
				<div className="business business4" onMouseEnter={() => mouseDebounceCallBack('跨境贸易融资解决方案')}>
					跨境贸易融资解决方案
				</div>
				<div className="business business5" onMouseEnter={() => mouseDebounceCallBack('资产数字化多元金融底座')}>
					资产数字化多元金融底座
				</div>
			</div>
		</HomePart4Wrapper>
	)
}

const HomePart4Wrapper = styled.div`
	width: 100%;
	height: 100%;
	background-image: url(${require(`@images/home/<USER>
	background-size: cover;
	background-position: bottom;
	position: relative;
	.bigModule {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: 29vh;
		font-size: 80px;
		font-weight: 600;
		color: #9cefff;
		width: 100%;
		text-align: center;
		> h3 {
			font-size: 26px;
			color: #9cefff;
			margin-top: 15px;
		}
	}
	.beam {
		.beam1 {
			width: 38px;
			height: 499px;
			background-image: url(${require(`@images/home/<USER>
			position: absolute;
			left: 55%;
			top: 25%;
			-webkit-animation: beamKeyframes 550ms ease-in-out infinite alternate;
			animation: beamKeyframes 550ms ease-in-out infinite alternate;
		}
		.beam2 {
			width: 53px;
			height: 499px;
			background-image: url(${require(`@images/home/<USER>
			position: absolute;
			left: 60%;
			top: 25%;
			-webkit-animation: beamKeyframes 800ms ease-in-out infinite alternate;
			animation: beamKeyframes 800ms ease-in-out infinite alternate;
		}

		.beam3 {
			width: 47px;
			height: 499px;
			background-image: url(${require(`@images/home/<USER>
			position: absolute;
			left: 35%;
			top: 25%;
			-webkit-animation: beamKeyframes 700ms ease-in-out infinite alternate;
			animation: beamKeyframes 700ms ease-in-out infinite alternate;
		}

		.beam4 {
			width: 70px;
			height: 496px;
			background-image: url(${require(`@images/home/<USER>
			position: absolute;
			left: 40%;
			top: 25%;
			-webkit-animation: beamKeyframes 580ms ease-in-out infinite alternate;
			animation: beamKeyframes 580ms ease-in-out infinite alternate;
		}

		.beam5 {
			width: 78px;
			height: 494px;
			background-image: url(${require(`@images/home/<USER>
			position: absolute;
			left: 45%;
			top: 25%;
			-webkit-animation: beamKeyframes 620ms ease-in-out infinite alternate;
			animation: beamKeyframes 620ms ease-in-out infinite alternate;
		}

		.beam6 {
			width: 78px;
			height: 494px;
			background-image: url(${require(`@images/home/<USER>
			position: absolute;
			left: 50%;
			top: 25%;
			-webkit-animation: beamKeyframes 880ms ease-in-out linear infinite alternate;
			animation: beamKeyframes 880ms ease-in-out linear infinite alternate;
		}

		@keyframes beamKeyframes {
			0% {
				opacity: 0;
			}
			100% {
				opacity: 1;
			}
		}
	}
	.smallModule {
		.business {
			height: 18px;
			font-size: 16px;
			font-weight: 400;
			color: #9cefff;
			line-height: 18px;
			position: absolute;
			transition: all 3s;
			opacity: 0.5;
		}
		.business:hover {
			cursor: pointer;
			opacity: 1;
		}
		.business1 {
			left: 30%;
			top: 73%;
		}
		.business2 {
			left: 46%;
			top: 80%;
		}
		.business3 {
			right: 30%;
			top: 73%;
		}
		.business4 {
			left: 25%;
			top: 84%;
		}
		.business5 {
			right: 25%;
			top: 84%;
		}
	}

	@keyframes beamAnimation {
		0% {
		}
		100% {
			opacity: 0;
		}
	}
`
