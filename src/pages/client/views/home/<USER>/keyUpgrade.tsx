import { Progress } from 'antd'
import { useLocalStore } from 'mobx-react'
import React from 'react'
import styled from 'styled-components'

import { loginApis } from '@src/pages/client/api'
import stores from '@src/pages/client/sotre/store'
import { history } from '@src/utils/router'
import kunlun from '@utils/crypto'
import message from 'antd/lib/message'

const Buffer = require('buffer').Buffer

type keyUpgradeType = {
	companyUuid: string
	companyPriv: string
	companyPubKey: string
}
interface Props {}

export default function KeyUpgrade(props: Props) {
	const localStore = useLocalStore(() => stores)

	const [percent, setPercent] = React.useState(0)
	const [isFinish, setIsFinish] = React.useState(false)

	const timerRef = React.useRef<any>()

	const ext = JSON.parse(localStorage.getItem('ext') || '{}')

	const { jumpPageMap } = history.location.state || { jumpPageMap: {} }
	React.useEffect(() => {
		if (isFinish) {
			// 清楚定时器
			clearInterval(timerRef.current)
			setPercent(100) // 密钥升级完毕
			history.push('/home/<USER>', {
				jumpPageMap: { ...jumpPageMap, ...{ isKeyUpgrade: false } },
			})
			history.go(0)
		}
	}, [isFinish])

	React.useEffect(() => {
		if (ext.user && ext.user.companyPrivKeyEnc) {
			const userInfo = ext.user
			const password = localStore.loginStore.password
			getKeyUpgradeParams(userInfo, password)
		} else {
			message.warning('密钥已升级,跳转到登录页面')
			// 去登录页面
			history.push('/home', {
				Islogin: true,
			})
		}
		return () => {
			clearInterval(timerRef.current)
		}
	}, [])

	// 密钥升级接口
	const keyUpgrade = async (params: keyUpgradeType) => {
		console.log('params: ', params)
		const success = await loginApis.keyUpgrade(params).catch(err => {
			console.log('err', err)
		})
		if (success.returnCode % 1000 === 0) {
			console.log('密钥升级完毕', success)
			// 密钥升级完成手动更新ext（公司私钥清空）
			ext.user.companyPrivKeyEnc = null
			localStorage.setItem('ext', JSON.stringify(ext))
			// 接口请求完成, 解密钥完成
			setIsFinish(true)
		} else {
			clearInterval(timerRef.current)
			message.error('密钥升级异常。请重新登录尝试，或联系平台方')
			history.push('/home', {
				Islogin: true,
			})
		}
	}

	//得到密钥接口的参数
	const getKeyUpgradeParams = async (rawData: any, password: string) => {
		timerRef.current = setInterval(() => {
			let num: number = Math.round(Math.random() * 2)
			setPercent(percent => {
				if (percent >= 95) {
					// 定时器的时间控制解密钥完成
					clearInterval(timerRef.current)
				}
				return percent + num
			})
		}, 100)
		//解私钥
		const buffer = await kunlun.pbkdf2.getPbkdf2Key(password, rawData['uuid']).catch(err => {
			console.log('err', err)
		})

		try {
			let privKey, companyPrivKey, companyUuid, companyPubKey
			privKey = kunlun.aes.decryptWithPbkdf2key(rawData['privKeyEnc'], Buffer(buffer))
			companyPrivKey = kunlun.sm.decrypt(rawData['companyPrivKeyEnc'], privKey)
			companyUuid = rawData['company']['uuid']
			companyPubKey = rawData['company']['pubKey']
			// 调用密钥升级接口
			keyUpgrade({ companyUuid, companyPubKey, companyPriv: companyPrivKey })
		} catch (error) {
			clearInterval(timerRef.current)
			message.error('解密私钥失败,请联系平台方')
			history.push('/home', {
				Islogin: true,
			})
		}
	}

	return (
		<KeyUpgradeWrapper>
			<div className="keyWindow">
				<div className="title">密钥保护安全升级</div>
				<div className="loading">
					<div className="outerCircle">
						<img src={require('@images/home/<USER>')} alt="" />
						<div className="innerCircle">
							<img src={require('@images/home/<USER>')} alt="" />
						</div>
						<div className="progress">
							<Progress type="circle" percent={percent} strokeColor="rgb(99, 117, 254)" />
						</div>
					</div>
				</div>
				<div className="description">预计需要10秒钟完成升级，请勿中断操作</div>
			</div>
		</KeyUpgradeWrapper>
	)
}

const KeyUpgradeWrapper = styled.div`
	min-width: 1280px;
	width: 100%;
	height: 100%;

	background-image: url(${require('@images/home/<USER>')});
	background-size: cover;
	background-position: bottom;
	.keyWindow {
		width: 506px;
		height: 501px;
		position: fixed;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		text-align: center;
		.title {
			width: 506px;
			height: 22px;
			font-size: 24px;
			font-weight: 400;
			color: #d7ecff;
			line-height: 22px;
		}
		.loading {
			width: 506px;
			height: 457px;
			position: relative;
			.outerCircle {
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				width: 350px;
				height: 352px;
				margin-top: 36px;
				img {
					width: 100%;
					animation: myRotateOuter 10s linear infinite;
				}
				.innerCircle {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					img {
						animation: myRotateInner 10s linear infinite;
					}
				}
				.progress {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
				}
			}
		}
		.description {
			width: 506px;
			height: 22px;
			font-size: 14px;
			font-weight: 400;
			color: #d7ecff;
			line-height: 22px;
		}
	}
	.ant-progress-inner {
		overflow: inherit;
		.ant-progress-text {
			color: white;
			position: absolute;
			top: 270px;
		}
	}

	@keyframes myRotateOuter {
		0% {
			transform: rotate(0deg);
		}
		50% {
			transform: rotate(180deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
	@keyframes myRotateInner {
		0% {
			transform: rotate(0deg);
		}
		50% {
			transform: rotate(-180deg);
		}
		100% {
			transform: rotate(-360deg);
		}
	}
`
