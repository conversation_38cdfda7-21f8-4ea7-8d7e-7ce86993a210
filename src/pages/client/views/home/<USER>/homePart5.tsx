import React from 'react'
import styled from 'styled-components'

type Props = {}

export default function HomePart5(props: Props) {
	return (
		<HomePart5Wrapper>
			<div className="part5wrap">
				<div className="partner">
					<p> 合作伙伴</p>
				</div>
				<div className="partnerLogo">
					<div className="logo logoTop">
						<div className="imgWrap">
							<img src={require(`@images/home/<USER>"" />
							<img src={require(`@images/home/<USER>"" />
						</div>
					</div>
					<div className="logo logoMiddle">
						<div className="imgWrap">
							<img src={require(`@images/home/<USER>"" />
							<img src={require(`@images/home/<USER>"" />
						</div>
					</div>
					<div className="logo logoBottom">
						<div className="imgWrap">
							<img src={require(`@images/home/<USER>"" />
							<img src={require(`@images/home/<USER>"" />
						</div>
					</div>
				</div>
				<div className="information">
					<p> ©1692021 万向区块链 沪ICP备17019413号-1 沪公网安备31010902002886号</p>
				</div>
			</div>
			<div className="leftShadow"></div>
			<div className="rightShadow"></div>
		</HomePart5Wrapper>
	)
}

const HomePart5Wrapper = styled.div`
	width: 100%;
	height: 100%;
	position: relative;
	background-color: #070c28;
	.part5wrap {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
	}
	.leftShadow {
		width: 20vw;
		height: 100vh;
		position: absolute;
		left: 0;
		top: 0;
		background: linear-gradient(to right, rgba(7, 13, 40, 1), rgba(7, 13, 40, 0));
	}
	.rightShadow {
		width: 20vw;
		height: 100vh;
		position: absolute;
		right: 0;
		top: 0;
		background: linear-gradient(to left, rgba(7, 13, 40, 1), rgba(7, 13, 40, 0));
	}

	.partner {
		height: 40px;
		font-size: 46px;
		font-weight: 600;
		color: #9cefff;
		line-height: 40px;
		margin-top: 100px;
		text-align: center;
	}
	.partnerLogo {
		height: 414px;
		.logo {
			width: 100%;
			overflow-x: hidden;
			img {
				height: 138px;
			}
		}
		.logoTop {
			height: 138px;
			.imgWrap {
				width: 6796px;
				height: 138px;
				display: flex;
				-webkit-animation: 36s logoTopAnimation linear infinite normal;
				animation: 36s logoTopAnimation linear infinite normal;
			}
		}
		.logoMiddle {
			.imgWrap {
				// 此为两种logo图拼接的宽度
				width: 6796px;
				height: 138px;
				display: flex;
				-webkit-animation: 42s logoMiddleAnimation linear infinite normal;
				animation: 42s logoMiddleAnimation linear infinite normal;
			}
		}
		.logoBottom {
			.imgWrap {
				width: 6796px;
				height: 138px;
				display: flex;
				-webkit-animation: 48s logoBottomAnimation linear infinite normal;
				animation: 48s logoBottomAnimation linear infinite normal;
			}
		}
	}
	.information {
		/* width: 590px; */
		height: 40px;
		font-size: 18px;
		font-weight: 400;
		color: #99bfc8;
		line-height: 40px;
		margin-bottom: 20px;
		text-align: center;
	}

	@keyframes logoTopAnimation {
		0% {
			-webkit-transform: translate3d(0, 0, 0);
			transform: translate3d(0, 0, 0);
		}
		100% {
			-webkit-transform: translate3d(-3398px, 0, 0);
			transform: translate3d(-3398px, 0, 0);
		}
	}

	@keyframes logoMiddleAnimation {
		0% {
			-webkit-transform: translateX(calc(-6796px + 100vw));
			transform: translateX(calc(-6796px + 100vw));
		}
		100% {
			-webkit-transform: translateX(calc(-3398px + 100vw));
			transform: translateX(calc(-3398px + 100vw));
		}
	}

	@keyframes logoBottomAnimation {
		0% {
			-webkit-transform: translate3d(-0px, 0, 0);
			transform: translate3d(-0px, 0, 0);
		}
		100% {
			-webkit-transform: translate3d(-3398px, 0, 0);
			transform: translate3d(-3398px, 0, 0);
		}
	}
`
