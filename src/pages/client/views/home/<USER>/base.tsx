/*
 * @Date: 2022-07-06 16:58:12
 * @LastEditors: jcl
 * @LastEditTime: 2022-07-14 17:04:41
 * @FilePath: \frontend-web\src\pages\client\views\home\base\base.tsx
 * @Description: file content
 */
import React, { useEffect } from 'react'
import styled from 'styled-components'
import { Switch, Case } from 'react-if'
import PerfectInformation from './perfectInformation'
import ProtocolSelection from './protocolSelection'
import { history } from '@src/utils/router'
import { message } from 'antd'
import { getFirstPagePath, getFrontCodeListForAdmin, getRealMenuList, hasAuth, setStorage } from '@src/pages/client/biz/bizIndex'
import { uiData } from '@src/pages/client/store'
import Verify from './verify'
import loginApis from '@src/pages/client/api/login'
import { useNavigate } from 'react-router-dom'
import { observer } from 'mobx-react-lite'
import { financeModuleApi } from '@src/pages/client/api'

const Base = props => {
	const [pageType, setPageType] = React.useState<string>('')
	const [loading, setLoading] = React.useState<boolean>()
	const navigate = useNavigate()
	// const jumpPageMap = uiData.jumpPageMap
	// const ext = JSON.parse(localStorage.getItem('ext') || '{}')

	const getSelectedRoleId = () => {
		// 删除该页面，为了不影响业务错乱，当前业务只做转跳
		// 根据 prd 规则
		// 1、操作员-核心企业、2审核员-核心企业、3、操作员-一般企业、4、审核员-一般企业、5、管理员;
		const ext: any = JSON.parse(localStorage.getItem('ext') || '{}')
		const roleList = ext?.loginPerm?.roleList || []
		let newRoleList = []
		const roleOrderMap = {
			'roleCode-COperator': 0,
			'roleCode-CAuditor': 1,
			'roleCode-SOperator': 2,
			'roleCode-SAuditor': 3,
			// 管理员默认 index = 4;
		}
		roleList.forEach(item => {
			let roleOrderKey = `roleCode-${item.roleCode}`
			let roleOrderIndex = roleOrderMap[roleOrderKey]
			if (typeof roleOrderIndex === 'number') {
				//按prd顺排列
				newRoleList[roleOrderIndex] = item
			} else {
				// 管理员index = 4
				newRoleList[4] = item
			}
		})
		// 剔除，索引位置为undefined的元素
		newRoleList = newRoleList.filter(item => {
			if (item) {
				return item
			}
		})

		if (newRoleList?.length > 0) {
			return newRoleList[0]?.roleId
		} else {
			console.error(`找不到roleId,请检查`)
			return ''
		}
	}

	useEffect(() => {
		if (!localStorage.getItem('token')) {
			navigate('/login')
		} else {
			updatePageStatus()
		}
	}, [])

	// useEffect(() => {
	// 	updatePageStatus()
	// }, [])

	const updatePageStatus = async () => {
		setLoading(true)
		const ext = await loginApis.getNewBaseInfo()
		setLoading(false)
		localStorage.setItem('ext', JSON.stringify(ext))
		const isUserVerified = ext?.user?.verified && ext?.user?.faceAuthResult == 1 //用户认证状态  // 0是失败 1是成功 2 是没认证 默认好像是null
		const company = ext?.user?.company
		const isCompanyApproved = company?.id && company?.id !== '0' && company?.approveStatus === 'CONFIRM' // 企业认证通过
		const jumpPageMap = {
			isUserVerified,
			isCompanyApproved,
			// 没有银行卡信息
			isPerfectAccount: !Boolean(ext?.bankCards?.length > 0),
			// 未签署平台协议
			signProtocol: ext?.protocolSigns?.length ? ext?.protocolSigns : false,
			// 多角色
			identitySelection: ext?.loginPerm?.roleList?.length > 1,
		}
		uiData.updateJumpPageMap(jumpPageMap)
		if (!jumpPageMap.isUserVerified) setPageType('userVerified')
		else if (!jumpPageMap.isCompanyApproved) setPageType('companyApproved')
		else if (!!jumpPageMap.isPerfectAccount) setPageType('perfectInformation')
		else if (!!jumpPageMap.signProtocol) setPageType('protocolSelection')
		else {
			const selectedRoleId = getSelectedRoleId()
			// 如果有多角色，使用选择的角色权限，否则使用第一个角色权限
			const perm = ext?.loginPerm?.roleList.find(item => item.roleId === selectedRoleId) || ext?.loginPerm?.roleList?.[0]
			handleSetAuth(perm, ext?.user?.adminFlag)
		}
	}

	const handleSetAuth = (loginPerm: loginPermType, adminFlag) => {
		let { menuList: originMenuList, frontCodeList = [], roleList = [], roleId } = loginPerm || {}
		// 注释前端开发环境获取所有的权限
		// if (adminFlag || process_env === 'development') {
		// 	frontCodeList = getFrontCodeListForAdmin()
		// }
		if (adminFlag) {
			frontCodeList = getFrontCodeListForAdmin()
		}
		originMenuList = originMenuList || []
		frontCodeList = frontCodeList || []
		let realMenuList = getRealMenuList(frontCodeList, originMenuList)
		// 存储登录的一般信息
		message.success('登录成功')
		setStorage({ frontCodeList, originMenuList, menuList: realMenuList, roleList, roleId })
		// 更新路由
		uiData.updateFrontCodeList(frontCodeList)
		uiData.updateMenuList(realMenuList)
		// 跳转到第一个页面
		let targetPath = getFirstPagePath(realMenuList)
		history.push(targetPath || '/content/*')
		// 150 增加应收账款质押通知
		if (hasAuth('pledge_web:login:pledgeNotice')) {
			financeModuleApi.getPledgeReminderList().then(res => {
				localStorage.setItem('pledgeRemindData', JSON.stringify(res))
			})
		}
	}

	const handleCheckToken = (nowToken?: any, preToken?: any) => {
		// 检查是否有tokens
		if (nowToken) {
			updatePageStatus()
		}
	}

	useEffect(() => {
		// window.onstorage在另一个tab中执行
		window.onstorage = function (event) {
			if (event.key === 'token' && event.newValue !== event.oldValue) {
				// Token has been updated, refresh the page
				/**
				 * 当界面停留在正常菜单界面的时候
				 * 监听token变化的时候 需要重新获取用户信息
				 * 决定是否停留在当前界面还是去异常界面
				 */
				console.log('onstorage base page')
				handleCheckToken(event.newValue, event.oldValue)
			}
		}
	}, [])
	return (
		<>
			{loading ? (
				<div className="spinner-load">
					<div className="loader"></div>
					<p style={{ textAlign: 'center', color: 'rgb(146, 146, 144)' }}>加载中...</p>
				</div>
			) : (
				<BaseWrapper>
					<div className="topbox">
						<img src={require('@src/assets/images/logos/jiangyinlogo_1.png')} alt="" />
						{/* <span>壹链通供应链金融平台</span> */}
					</div>
					<Switch>
						<Case condition={pageType === 'userVerified'}>
							<Verify type="userVerified" />
						</Case>
						<Case condition={pageType === 'companyApproved'}>
							<Verify type="companyApproved" />
						</Case>
						{/* <Case condition={pageType === 'keyUpgrade'}>
				<KeyUpgrade />
			</Case> */}
						<Case condition={pageType === 'perfectInformation'}>
							<PerfectInformation />
						</Case>
						{/* <Case condition={pageType === 'improveInvoicingInfo'}>
				<ImproveInvoicingInfo></ImproveInvoicingInfo>
			</Case> */}
						<Case condition={pageType === 'protocolSelection'}>
							<ProtocolSelection />
						</Case>
						{/* <Case condition={pageType === 'identitySelection'}>
				<IdentitySelection />
					</Case>*/}
					</Switch>
				</BaseWrapper>
			)}
		</>
	)
}
export default observer(Base)

const BaseWrapper = styled.div`
	width: 100%;
	height: 100%;
	.topbox {
		position: absolute;
		left: 40px;
		top: 24px;
		img {
			width: 160px;
			height: 48px;
			vertical-align: center;
		}
		span {
			color: #000;
			margin-left: 10px;
			font-weight: 800;
		}
	}
`
