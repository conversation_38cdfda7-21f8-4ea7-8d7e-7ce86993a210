/*
江西银行金融机构母公司（开发用勿改）
<EMAIL> 管理员
<EMAIL> 操作员

江西银行分支行金融机构1（开发用勿改）
<EMAIL> 管理员
<EMAIL> 操作员

江西银行分支行金融机构2（开发用勿改）
<EMAIL> 管理员
<EMAIL> 操作员

江西核心企业供应商1（开发用勿改）
<EMAIL> 管理员
<EMAIL> 核心企业供应商操作员
<EMAIL> 核心企业供应商审核员

江西核心企业1（开发用勿改）
<EMAIL> 管理员
<EMAIL> 操作员
<EMAIL> 审核员

江西供应商1（开发用勿改）
<EMAIL> 管理员
<EMAIL> 操作员
<EMAIL> 审核员

江西供应商2（开发用勿改）
<EMAIL> 管理员
<EMAIL> 操作员
<EMAIL> 限额操作员
<EMAIL> 审核员



润楼银行分支行金融机构（开发用勿改）
<EMAIL> 管理员
<EMAIL> 操作员

润楼核心企业1（开发用勿改）
<EMAIL> 管理员
<EMAIL> 操作员
<EMAIL> 审核员

润楼供应商1（开发用勿改）
<EMAIL> 管理员
<EMAIL> 操作员
<EMAIL> 审核员

润楼供应商2（开发用勿改）
<EMAIL> 管理员
<EMAIL> 操作员
<EMAIL> 审核员


*/

export let npList = [
	['<EMAIL>', 'Vena1234##', '注册新企业测试【管理员】'],

	['<EMAIL>', 'Vena1234##', '江西银行金融机构母公司【管理员】'],
	['<EMAIL>', 'Vena1234##', '江西银行金融机构母公司【操作员】'],

	['<EMAIL>', 'Vena1234##', '江西银行分支行金融机构1【管理员】'],
	['<EMAIL>', 'Vena1234##', '江西银行分支行金融机构1【操作员】'],

	['<EMAIL>', 'Vena1234##', '江西银行分支行金融机构2【管理员】'],
	['<EMAIL>', 'Vena1234##', '江西银行分支行金融机构2【操作员】'],

	['<EMAIL>', 'Vena1234##', '核心企业供应商【管理员】'],
	['<EMAIL>', 'Vena1234##', '核心-供应商-【项目】-【审核员】'],
	['<EMAIL>', 'Vena1234##', '核心-供应商-【项目】-【操作员】'],
	['<EMAIL>', 'Vena1234##', '江西核心企业供应商1【核心企业供应商操作员】'],
	['<EMAIL>', 'Vena1234##', '江西核心企业供应商1【核心企业供应商审核员】'],

	['<EMAIL>', 'Vena1234##', '江西供应商1【管理员】'],
	['<EMAIL>', 'Vena1234##', '江西供应商1【审核员】'],
	['<EMAIL>', 'Vena1234##', '江西供应商1【操作员】'],

	['<EMAIL>', 'Vena1234##', '江西供应商2【管理员】'],
	['<EMAIL>', 'Vena1234##', '江西供应商2【项目】-【操作员】'],
	['<EMAIL>', 'Vena1234##', '江西供应商2【操作员】'],

	['<EMAIL>', 'Vena1234##', '江西供应商2【限额操作员】'],
	['<EMAIL>', 'Vena1234##', '江西供应商2【审核员】'],

	['<EMAIL>', 'Vena1234##', '江西核心企业1【管理员】'],
	['<EMAIL>', 'Vena1234##', '江西核心企业1【操作员】'],
	['<EMAIL>', 'Vena1234##', '江西核心企业1【审核员】'],
	['<EMAIL>', 'Vena1234##', '江西核心企业1【项目操作员】'],

	['<EMAIL>', 'Rocky=1234', '吴广的企业核心企业/供应商【管理员】'],
]
