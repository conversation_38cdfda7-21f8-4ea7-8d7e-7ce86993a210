import React, { useEffect } from 'react'
import { Form, Button, message, Popover, Col, Row } from 'antd'
import styled from 'styled-components'

import { loginApis } from '@src/pages/client/api'
import { perfectInfo } from '@src/pages/client/views/home/<USER>/formConfig'
import GetFormList from '@src/globalComponents/companyInfoForm/GetFormList'
import BankEnquiry from '@factorContent/companyInfoManage/bankAccountManage/components/bankEnquiry'
import { history } from '@src/utils/router'
import FormItem from 'antd/lib/form/FormItem'
import { addLogoutForDev, setStorage } from '@src/pages/client/biz/bizIndex'
import kunlun from '@src/utils/crypto'
import { RSA_PUBLIC_KEY } from '@src/constants/constants'
import { uiData } from '@src/pages/client/store'
import { observer } from 'mobx-react-lite'
import { toJS } from 'mobx'
const bankcard = require('@src/assets/images/home/<USER>')

const layout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 16 },
}

interface Props {}

export default observer(function PerfectInformation(props: Props) {
	const jumpPageMap = toJS(uiData.jumpPageMap)
	console.log(jumpPageMap, 'jumpPageMap')
	const [subLoding, setSubLoding] = React.useState(false)
	const [openingbankVisible, setOpeningbankVisible] = React.useState(false)
	const [form] = Form.useForm()
	const ext: any = JSON.parse(localStorage.getItem('ext') || '{}')

	useEffect(() => {
		// 默认回显公司名称
		const orgName = ext?.user?.company?.orgName
		if (orgName) {
			form.setFieldValue('username', orgName)
		}
	}, [])

	const onFinish = values => {
		const { username, bankAccount, openingBank } = values
		kunlun.jsEncrypt.setPublicKey(RSA_PUBLIC_KEY)
		const encrypt_accountNum = kunlun.jsEncrypt.encrypt(bankAccount)
		setSubLoding(true)
		const params = {
			accountName: username, //户名
			accountNum: encrypt_accountNum, // 银行账号
			accountBank: openingBank, // 开户行
		}

		loginApis
			.perfectInformation(params)
			.then(res => {
				setStorage({ accountNum: encrypt_accountNum ? encrypt_accountNum : '', accountBank: openingBank }) //4/8更改银行卡加密做的兼容，以避免空报错影响流程
				history.push('/home/<USER>')
				history.go(0)
			})
			.catch(e => {
				console.log(e)
			})
			.finally(() => {
				setSubLoding(false)
			})
	}

	const setAccountBank = item => {
		form.setFieldsValue({ openingBank: item.lname })
	}

	// 获取江阴银行卡信息
	const handleFetchBank = () => {
		const acctno = form.getFieldValue('bankAccount')
		if (acctno) {
			loginApis.queryJrBankInfoByNo({ acctno }).then(res => {
				if (res) {
					form.setFieldsValue({
						openingBank: res?.brchna,
					})
				} else {
					message.error('未查询到开户行，请手动填写')
				}
			})
		} else {
			message.warn('请输入银行账号')
		}
	}

	return (
		<PerfectInformationWrapper>
			<div className="perfectInformation">
				<div className="contentbox">
					<div className="bigTitle">
						<span>完善银行账户信息</span>
					</div>
					{/* <div className="loginoutbox">{addLogoutForDev()}</div> */}
					<div className="bankAccount">
						{/* <div className="title">银行账户</div> */}
						<div className="confirm">此账户用于核心企业融信付款账户、供应商融资收款账户、融信兑付收款账户。</div>
						<div className="confirm">请在咨询银行客户经理后，准确填写该账户信息！</div>
						<div className="bank-box">
							<div className="left-bank-box">
								<img src={bankcard} alt="" />
							</div>
							<div className="right-bank-box">
								<Form className="form-wrapper" form={form} name="PerfectInformationForm" layout="horizontal" onFinish={onFinish} {...layout}>
									<GetFormList configArr={perfectInfo().bankAccount} pageType="forget"></GetFormList>
									<Row>
										<Col span={6}></Col>
										<Col span={16}>
											<div className="link" style={{ textDecoration: 'underline' }} onClick={() => setOpeningbankVisible(true)}>
												查询开户行
											</div>
										</Col>
									</Row>
									<Popover content={'非江阴农商银行开户，请手动填写开户行'} title={null}>
										<Button className="fetch-bank-btn" onClick={handleFetchBank} style={{ color: '#1F7BF4' }}>
											获取开户行
										</Button>
									</Popover>
									<FormItem wrapperCol={{ offset: 1 }}>
										<Button type="primary" htmlType="submit" loading={subLoding} block className="finish">
											提交
										</Button>
										<div className="loginout ">{addLogoutForDev()}</div>
									</FormItem>
								</Form>
							</div>
						</div>
					</div>
				</div>
			</div>
			<BankEnquiry visible={openingbankVisible} onCancel={() => setOpeningbankVisible(false)} getAccountBank={setAccountBank}></BankEnquiry>
		</PerfectInformationWrapper>
	)
})

const PerfectInformationWrapper = styled.div`
	width: 100%;
	height: 100%;
	background-image: url(${require('@images/home/<USER>')});
	background-size: cover;
	background-position: bottom;
	display: flex;
	justify-content: center;
	color: #d7ecff;
	font-size: 14px;
	padding-top: 50px;
	.perfectInformation {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 752px;
		padding: 32px;
		padding-bottom: 115px;
		box-sizing: border-box;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: 124px;
		background-color: #fff;
		.contentbox {
			color: rgba(0, 0, 0, 0.85);
			width: 100%;
			.bigTitle {
				// color: #d7ecff;
				text-align: center;
				font-size: 18px;
				margin-bottom: 12px;
			}
			.bankAccount {
				// 输入框高度
				color: rgba(0, 0, 0, 0.65);
				font-size: 14px;
				.ant-input {
					height: 40px;
				}
				// label 的样式
				.ant-form-item-required {
					height: 40px;
					// color: #d7ecff;
					font-size: 14px;
				}
				.ant-input[disabled] {
					background-color: #fff !important;
				}
				.title {
					font-size: 24px;
					text-align: center;
				}
				.confirm {
					// margin-bottom: 12px;
					text-align: center;
					&:first-child {
						margin-bottom: 13px;
						line-height: 14px;
						height: 14px;
					}
				}
				.bank-box {
					display: flex;
					height: 256px;
					justify-content: space-between;
					margin-top: 32px;
					padding: 36px;
					padding-bottom: 0;
					padding-right: 0;
					box-sizing: border-box;
					background-color: #f0f6ff;
					position: relative;
					.left-bank-box {
						width: 145px;
						height: 167px;
						img {
							width: 145px;
							height: 167px;
						}
					}
					.right-bank-box {
						width: 430px;
					}
				}
				.form-wrapper {
					position: relative;
					.fetch-bank-btn {
						width: 102px;
						height: 40px;
						position: absolute;
						top: 64px;
						right: 39px;
					}
				}
			}
		}
		.loginoutbox {
			text-align: center;
		}

		.finish {
			position: absolute;
			left: 70px;
			transform: translateX(-50%);
			bottom: -62px;
			width: 200px;
			height: 40px;
		}
		.loginout {
			position: absolute;
			left: 42px;
			bottom: -95px;
		}
	}
`
