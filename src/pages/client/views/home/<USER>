import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import FullPage from '@globalComponents/FullPage'
import HomePart1 from './components/homePart1'
import Login from './components/login'
import { announcementManageApi } from '@src/pages/client/api'
import ViewModalWrap from './components/announcement'
import { useNavigate } from 'react-router-dom'
import SubscriptionSide from '@factor/components/subscription-side'
import { Spin, Button } from 'antd'
import { formatLoginUrl } from '@src/utils/jy-helper'
import useAppVersion from '@src/global-hooks/useAppVersion'

type homePageInfoType = {
	logoUrl?: string
	homePageTitle?: string
	homePageContent?: string
	homePageUrl?: string
}

export default function HomePage(props) {
	const navigator = useNavigate()
	const [loginFormModalVisible, setLoginFormModalVisible] = React.useState(false)
	const [announcementList, setAnnouncementList] = useState<Array<any>>([])
	const [hasMask, setHasMask] = useState<boolean>(false)
	const [homePageInfo, setHomePageInfo] = useState<homePageInfoType>({})
	const [qrCodeUrlStr, setQrCodeUrlStr] = useState<string>('')
	const searchParams = new URLSearchParams(window.location.search || window.location.href.split('?')[1] || '')
	const [loginError, setLoginError] = useState(null)
	const [loading, setLoading] = useState<boolean>(false)
	const [isTokenLogining, setIsTokenLogining] = useState(false)
	const [isFromUser, setIsFromUser] = useState(false)
	const token = searchParams.get('token')

	// 跳转至用户平台的企业中心
	// const handleGotoOrgCenter = () => {
	// 	window.open(formatLoginUrl({ token, path: encodeURIComponent('/auth/org-center/my-org') }))
	// }

	// 跳转至个人认证
	// const handleGotoVerify = () => {
	// 	console.log('chufabushu')
	// 	window.open(formatLoginUrl({ token, path: encodeURIComponent('/auth/user-center/base-info') }))
	// }

	// 登录报错信息
	// const LoginErrorContent = {
	// 	// 个人企业
	// 	noCompany: {
	// 		message: '个人用户无法使用保理平台，您可以：',
	// 		description: (
	// 			<>
	// 				<div>1、切换企业；</div>
	// 				<div>
	// 					2、未注册企业，请去
	// 					<Button type="primary" onClick={handleGotoOrgCenter}>
	// 						认证企业
	// 					</Button>
	// 					并联系平台运营开通保理平台；
	// 				</div>
	// 				<div>3、未找到本企业，请联系企业管理员为您的账号添加平台操作权限。</div>
	// 			</>
	// 		),
	// 	},
	// 	unVerified: {
	// 		message: '您的企业还未认证，请先完成个人认证：',
	// 		description: (
	// 			<Button type="primary" onClick={handleGotoVerify}>
	// 				去个人认证
	// 			</Button>
	// 		),
	// 	},
	// }

	// 检查路由是否带token，有的话检查token并进行登录
	const handleCheckToken = (nowToken?: any, preToken?: any) => {
		if (nowToken) {
			localStorage.setItem('token', nowToken)
			setIsFromUser(true)
			// setIsTokenLogining(true)
			// setLoading(true)
			// 替换路由上的token为空
			window.history.replaceState({}, '', window.location.origin)
			navigator('/home/<USER>')
		} else {
			setIsFromUser(false)
		}
	}
	useEffect(() => {
		window.onstorage = function (event) {
			if (event.key === 'token' && event.newValue !== event.oldValue) {
				console.log('onstorage home page')
				handleCheckToken(event.newValue, event.oldValue)
			}
			return () => {
				setLoginFormModalVisible(false)
			}
		}
		handleCheckToken(token)
		handleCloseLoginFormModal()
		return () => {
			setLoginFormModalVisible(false)
		}
	}, [token])

	useEffect(() => {
		if (announcementList.length > 0 && !loginFormModalVisible) setHasMask(true)
		else {
			setHasMask(false)
		}
	}, [announcementList])

	const getAnnouncementListOnPage = () => {
		// 获取公告列表,公告需要根据每个端获取自己的公告
		let params = {
			linkName: location.protocol + '//' + location.hostname + location.pathname,
		}
		announcementManageApi.getAnnouncementlList(params).then(
			res => {
				if (res.length > 0) {
					setAnnouncementList(res)
				}
			},
			err => {
				console.log('err', err)
			}
		)
	}

	const returnUrl = encodeURIComponent(`${window.location.origin}${window.location.pathname}`)

	const handleOpenLoginFormModal = () => {
		window.location.href = formatLoginUrl({ returnUrl })
		// setLoginFormModalVisible(true)
	}
	const handleCloseLoginFormModal = () => {
		setLoginFormModalVisible(false)
		getAnnouncementListOnPage()
	}

	const handleToRegister = () => {
		window.location.href = formatLoginUrl({ returnUrl, step: 'register' })
	}

	return (
		<HomeWrapper>
			<Spin spinning={loading} size="large">
				<div className={`${hasMask ? 'announcement_mask' : ''} home-content`}>
					<div className="header">
						<a className="left">{!homePageInfo?.logoUrl ? null : <img src={homePageInfo?.logoUrl} />}</a>
						<div className="right">
							{/* <a className="news" href="#/news" target="_blank">
								资讯动态
							</a>
							<div className="rl">
								<span onClick={handleToRegister}>注册</span>
							</div> */}
						</div>
					</div>

					{!isTokenLogining && (
						<div className="loginbutton">
							<Button style={{ width: '100px', marginRight: '40px' }} size="large" onClick={handleToRegister}>
								注册
							</Button>
							<Button style={{ width: '100px', marginLeft: '22px' }} size="large" type="primary" onClick={handleOpenLoginFormModal}>
								登录
							</Button>
						</div>
					)}

					<FullPage>
						<HomePart1 isFromUser={isFromUser} title={homePageInfo.homePageTitle} desc={homePageInfo.homePageContent || '江阴农商银行'} />
					</FullPage>

					<Login visible={loginFormModalVisible} onClose={handleCloseLoginFormModal} />

					{announcementList.length > 0 && !loginFormModalVisible && (
						<ViewModalWrap
							dataList={announcementList}
							onchange={(list: Array<any>) => {
								console.log('调用')
								setAnnouncementList(list)
							}}
						/>
					)}

					{qrCodeUrlStr && <SubscriptionSide qrCodeUrlStr={qrCodeUrlStr} />}
				</div>
				{useAppVersion()}
			</Spin>
		</HomeWrapper>
	)
}

const HomeWrapper = styled.div`
	.ant-spin-nested-loading > div > .ant-spin {
		top: 300px;
	}

	.home-content {
		height: 100%;
		width: 100%;
		position: relative;
	}

	.loginbutton {
		position: absolute;
		left: 50%;
		top: 55vh;
		transform: translateX(-50%);
		z-index: 1000;
	}
	.header {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20px;
		z-index: 99;
		.left {
			img {
				height: 44px;
				width: 154px;
			}
		}
		.right {
			font-size: 14px;
			.news {
				margin-right: 20px;
				color: #fff;
			}
			.rl {
				margin-right: 37px;
				display: inline-block;
				font-size: 14px;
				font-weight: 400;
				color: rgb(28, 23, 23);
				line-height: 22px;
				text-align: center;
				cursor: pointer;
			}
		}
	}
	.ant-btn-primary {
		border-color: #1f7bf4 !important;
		background: #1f7bf4 !important;
	}
`
