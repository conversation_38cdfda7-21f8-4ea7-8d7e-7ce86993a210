import React, { useState } from 'react'
import { Alert, Button, Space, Divider } from 'antd'
import { formatLoginUrl } from '@src/utils/jy-helper'
import styled from 'styled-components'
import { loginApis } from '@src/pages/client/api'
import { handleLogout } from '@src/pages/client/biz/bizIndex'
import { history } from '@src/utils/router'

interface IProps {
	type: 'userVerified' | 'companyApproved'
}

/**
 * 个人实名和企业认证
 * @returns
 */
const Verify = (props: IProps) => {
	const { type } = props
	const token = localStorage.getItem('token')
	const ext: any = JSON.parse(localStorage.getItem('ext') || '{}')
	const company = ext?.user?.company
	const isPersonlaCompany = company?.id === '0' // 是否是个人企业

	// 跳转至用户平台的企业中心
	const handleGotoOrgCenter = () => {
		window.open(formatLoginUrl({ token, path: encodeURIComponent('/auth/org-center/my-org') }))
	}

	// 跳转至个人认证
	const handleGotoVerify = () => {
		window.open(formatLoginUrl({ token, path: encodeURIComponent('/auth/user-center/base-info') }))
	}

	// 登录报错信息
	const LoginErrorContent = {
		// 个人企业
		companyApproved: {
			message: (
				<>
					<div
						style={{
							padding: '16px 24px',
							boxSizing: 'border-box',
							width: '100%',
							borderBottom: '1px solid rgba(0,0,0,0.06)',
							position: 'relative',
							color: 'rgba(0,0,0,0.85)',
							fontSize: '16px',
						}}
					>{`当前登录：${company?.orgName}`}</div>
				</>
			),
			description: isPersonlaCompany ? (
				<>
					<div style={{ padding: '24px', boxSizing: 'border-box' }}>
						<div>个人用户无法使用金融平台，您可以：</div>
						<div style={{ marginTop: '12px', padding: '16px', boxSizing: 'border-box', backgroundColor: '#F0F6FF' }}>
							<div>1、重新企业登录；</div>
							<div>
								2、企业未注册，请去
								<span onClick={handleGotoOrgCenter} style={{ textDecoration: 'underline', color: '#1F7BF4', padding: '0 4px	', cursor: 'pointer' }}>
									认证企业
								</span>
								{/* <Button type="link" onClick={handleGotoOrgCenter} style={{textDecoration: 'line'}}>
									认证企业
								</Button> */}
								并联系平台运营审核；
							</div>
							<div>3、企业已注册，请联系企业管理员为您的账号添加企业操作权限。</div>
						</div>
					</div>
					<div style={{ paddingTop: '16px', textAlign: 'center', marginTop: '20px', borderTop: '1px solid rgba(0,0,0,0.06)' }}>
						<Space>
							<Button onClick={handleGotoOrgCenter} style={{ border: '1px solid #e1eaff', width: '173px', height: '48px', color: '#1F7BF4' }}>
								认证企业
							</Button>
							<Button type="primary" onClick={handleLogout} style={{ width: '173px', height: '48px', backgroundColor: '#1F7BF4' }}>
								重新登录
							</Button>
						</Space>
					</div>
				</>
			) : (
				<>
					<div style={{ padding: '24px' }}>您的企业未通过审核或被冻结，请联系客户经理处理。</div>
					<div style={{ height: '112px', lineHeight: '112px', padding: ' 0 24px', textAlign: 'center' }}>
						<div style={{ backgroundColor: '#F0F6FF' }}>
							<Button type="primary" onClick={handleLogout} style={{ width: '173px', height: '48px' }}>
								重新登录
							</Button>
						</div>
					</div>
				</>
			),
		},
		userVerified: {
			message: (
				<>
					<div
						style={{
							padding: '16px 24px',
							boxSizing: 'border-box',
							width: '100%',
							borderBottom: '1px solid rgba(0,0,0,0.06)',
							position: 'relative',
							color: 'rgba(0,0,0,0.85)',
							fontSize: '16px',
						}}
					>
						个人实名认证
					</div>
				</>
			),
			description: (
				<div>
					<div style={{ padding: '24px' }}>您未进行个人实名认证： 请先进行个人实名认证</div>
					<div style={{ height: '112px', lineHeight: '112px', padding: ' 0 24px', textAlign: 'center' }}>
						<div style={{ backgroundColor: '#F0F6FF' }}>
							<Button type="primary" onClick={handleGotoVerify} style={{ height: '48px', width: '173px' }}>
								去实名认证
							</Button>
						</div>
					</div>
				</div>
			),
		},
	}

	return (
		<VerifynWrapper className="error-wrapper">
			<Alert
				style={{ backgroundColor: '#fff', border: 'none', padding: 0, width: '480px' }}
				showIcon={false}
				message={LoginErrorContent?.[type]?.message}
				description={
					<div style={{ paddingBottom: '16px' }}>
						<div>{LoginErrorContent?.[type]?.description}</div>
					</div>
				}
				type="warning"
			/>
		</VerifynWrapper>
	)
}

export default Verify

const VerifynWrapper = styled.div`
	width: 100%;
	height: 100%;
	background-image: url(${require('@images/home/<USER>')});
	// background-color: #fafafa;
	background-size: cover;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
`
