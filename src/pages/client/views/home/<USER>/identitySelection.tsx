import { history } from '@src/utils/router'
import React, { useEffect } from 'react'

// 改页面舍弃，已在base.tsx里做了处理，不调这个页面了，后期可以删除
export default function IdentitySelection(props: any) {
	const { jumpPageMap } = history.location.state || { jumpPageMap: {} }
	const ext: any = JSON.parse(localStorage.getItem('ext') || '{}')
	const gotoTargePage = selectedRoleId => {
		try {
			history.push('/home/<USER>', {
				// 将选择的roleId传过去
				jumpPageMap: { ...jumpPageMap, ...{ identitySelection: false, selectedRoleId } },
			})
			history.go(0)
		} catch (error: any) {
			console.log(`error  = ${error}`)
		}
	}

	useEffect(() => {
		// 删除该页面，为了不影响业务错乱，当前业务只做转跳
		// 根据 prd 规则
		// 1、操作员-核心企业、2审核员-核心企业、3、操作员-一般企业、4、审核员-一般企业、5、管理员;
		const roleList = ext?.loginPerm?.roleList || []
		let newRoleList = []
		const roleOrderMap = {
			'roleCode-COperator': 0,
			'roleCode-CAuditor': 1,
			'roleCode-SOperator': 2,
			'roleCode-SAuditor': 3,
			// 管理员默认 index = 4;
		}
		roleList.forEach(item => {
			let roleOrderKey = `roleCode-${item.roleCode}`
			let roleOrderIndex = roleOrderMap[roleOrderKey]
			if (typeof roleOrderIndex === 'number') {
				//按prd顺排列
				newRoleList[roleOrderIndex] = item
			} else {
				// 管理员index = 4
				newRoleList[4] = item
			}
		})
		// 剔除，索引位置为undefined的元素
		newRoleList = newRoleList.filter(item => {
			if (item) {
				return item
			}
		})

		if (newRoleList?.length > 0) {
			gotoTargePage(newRoleList[0]?.roleId)
		}
	}, [])

	return <div>{/*空白页面，为了跳转 */}</div>
}
