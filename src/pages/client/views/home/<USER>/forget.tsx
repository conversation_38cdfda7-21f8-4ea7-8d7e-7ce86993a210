import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Form, Button, message } from 'antd'
import GetFormList from '@src/globalComponents/companyInfoForm/GetFormList'
import { activateAndForgetPasswordFormItemConfig } from '@src/pages/client/views/home/<USER>/formConfig'
import styled from 'styled-components'
import { loginApis } from '@src/pages/client/api'
import SmEnde from '@utils/crypto/SmEnde'
import { history } from '@src/utils/router'

const sm = new SmEnde()

export default function Forget() {
	const [form] = Form.useForm()

	// 忘记密码
	const forgetPassword = async () => {
		console.log('forgetPassword')
		// 对密码进行sm3加密
		const sm3Password = sm.getHash(form.getFieldValue('newPassword'))
		//encryptValue  为加密后的字符串
		const params = {
			email: form.getFieldValue('email'),
			mobile: form.getFieldValue('mobile'),
			mobileCode: form.getFieldValue('mobileCode'),
			password: sm3Password,
		}
		const res = await loginApis.newResetPassword(params).catch(err => {
			console.log('err', err)
		})
		if (res.returnCode % 1000 === 0) {
			message.success('密码重置成功')
			history.push('/home', {
				Islogin: true,
			})
		} else {
			message.error(res.returnDesc)
		}
	}

	const onFinish = () => {
		forgetPassword()
	}
	return (
		<ForgetWrapper>
			<div className="forgetPasswordForm">
				<Form form={form} name="forgetPasswordForm" onFinish={onFinish}>
					{/* 首次登录信息 */}
					<div className="forgetPassword">
						<div className="title">忘记密码</div>
					</div>
					<GetFormList configArr={activateAndForgetPasswordFormItemConfig('forgetPassword')} pageType="forget"></GetFormList>

					{/* finish */}
					<Form.Item className="finish" name="finish">
						<Button className="oper-btn" type="primary" htmlType="submit">
							提交
						</Button>
					</Form.Item>
					<div className="returnLogin">
						<Link
							to={'/home'}
							state={{
								Islogin: true,
							}}
						>
							返回登录
						</Link>
					</div>
				</Form>
			</div>
		</ForgetWrapper>
	)
}

const ForgetWrapper = styled.div`
	width: 100%;
	height: 100%;
	background-image: url(${require('@images/home/<USER>')});
	background-size: cover;
	background-position: bottom;

	display: flex;
	justify-content: center;
	align-items: center;

	.forgetPasswordForm {
		width: 330px;
		color: #d7ecff;
		font-size: 14px;
		.forgetPassword {
			text-align: center;
			color: #d7ecff;
			margin: 16px 0 32px 0;
			.title {
				font-size: 24px;
			}
		}
		.finish {
			margin-bottom: 0;
			// 提交按钮的宽和高
			#forgetPasswordForm_finish {
				width: 100%;
				height: 40px;
			}
		}
		.returnLogin {
			text-align: right;
			a {
				color: #d7ecff;
			}
		}
	}

	// 改变所有label字体颜色
	.ant-form-item-label > label {
		color: #d7ecff;
	}
	// 改变所有提示字体的颜色
	.ant-form-item-extra {
		color: #d7ecff;
	}

	.ant-form-item-control-input-content {
		width: 330px;
		height: 48px;
		.ant-input-affix-wrapper {
			height: 48px;
		}
	}
	.login-form-button {
		width: 330px;
		height: 48px;
	}

	// 改变验证码输入框宽度
	.ant-col-10 {
		max-width: 100%;
		width: 100%;
	}
	// 改变验证码按钮的位置和高度
	.ant-col-6 {
		margin-left: 8px;
		.captcha {
			height: 48px;
			width: 102px;
		}
	}
`
