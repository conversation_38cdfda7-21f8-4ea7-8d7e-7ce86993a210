/*
 * @Date: 2022-07-04 17:19:39
 * @LastEditors: jcl
 * @LastEditTime: 2022-08-31 15:34:24
 * @FilePath: \frontend-web\src\pages\client\views\home\components\registerOrActivateAccount.tsx
 * @Description: 注册和激活公共页面
 */
import React, { useState, useEffect, useRef } from 'react'
import styled from 'styled-components'
import { Link } from 'react-router-dom'
import { Form, Button, message, Checkbox } from 'antd'
import GetFormList from '@src/globalComponents/companyInfoForm/GetFormList'
import { activateAndForgetPasswordFormItemConfig } from '@src/pages/client/views/home/<USER>/formConfig'
import { loginApis, commonModuleApi, logoApi } from '@src/pages/client/api'
import SmEnde from '@utils/crypto/SmEnde'
import { history } from '@src/utils/router'
import { platformProtocolMap } from '@src/globalConfig/protocolConfig'
import PDFViewer from '@src/globalComponents/PDFViewer'
import { getStorage, setStorage } from '@src/pages/client/biz/bizIndex'
import to from 'await-to-js'

const sm = new SmEnde()

interface IRegisterOrActivateAccount {
	pageType?: 'register' | 'active'
}
export default ({ pageType = 'register' }: IRegisterOrActivateAccount) => {
	const [form] = Form.useForm()
	const [protocolVersionId, setprotocolVersionId] = useState<number>(0)
	const [protocols, setprotocolUrls] = useState<Array<any>>([])
	const [checkBoxIsClick, setCheckBoxIsClick] = useState<boolean>(false)
	const [isModalVisible, setIsModalVisible] = useState(false)
	const [protocolPdfUrl, setProtocolPdfUrl] = useState<string>('')
	const [viewTitle, setViewTitle] = useState<string>('')
	const [loading, setLoading] = useState<boolean>(false)
	const pageHeader = useRef({
		title: pageType === 'active' ? '激活账号' : '企业管理员注册',
		message:
			pageType === 'active'
				? '您的账号若是首次登录，请在此完成账号激活，并设置密码。'
				: '您在此页面注册的账号，将作为企业管理员的账号，注册后可直接登录平台进行企业认证',
	})
	// 初次进来加载协议并保存
	useEffect(() => {
		console.log('pageHeader', pageHeader)
		getProtocol()
	}, [])

	// 激活账号
	const activateAccount = async () => {
		setLoading(true)
		// 对密码进行sm3加密
		const sm3Password = sm.getHash(form.getFieldValue('newPassword'))
		//encryptValue  为加密后的字符串
		const params = {
			email: form.getFieldValue('email'),
			mobile: form.getFieldValue('mobile'),
			mobileCode: form.getFieldValue('mobileCode'),
			password: sm3Password,
			protocolVersionId: protocolVersionId,
		}
		let res
		try {
			if (pageType === 'active') res = await loginApis.activateAccount(params)
			else res = await loginApis.register(params)
			setLoading(false)
			message.success(pageType === 'active' ? '激活成功' : '注册成功')
			history.push('/home', {
				Islogin: true,
			})
		} catch (e: any) {
			setLoading(false)
			throw new Error(e)
		}
	}

	// 得到协议
	const getProtocol = async () => {
		// 统一 OoInfo获取方式 1.8.8
		let { operatingOrganizationUuid } = getStorage('OoInfo') || {}
		if (!operatingOrganizationUuid) {
			let OoInfo = await logoApi.getHomePageInfo().catch(e => {
				console.log(e)
			})

			operatingOrganizationUuid = OoInfo?.operatingOrganizationUuid
			// 统一存储方式 1.8.8
			setStorage({
				OoInfo: {
					operatingOrganizationName: OoInfo?.operatingOrganizationUuid,
					operatingOrganizationUuid,
				},
			})
		}
		const res = await commonModuleApi.getProtocol({ protocolTypes: ['userRule', 'platformServer'], ooUuid: operatingOrganizationUuid }).catch(err => {
			console.log('err', err)
		})
		if (res?.returnCode % 1000 === 0) {
			// 设置protocolPdfUrl
			setprotocolUrls(res.data)
		}
	}
	// 查看协议
	const viewProtocol = (pdfUrl: string, title: string) => {
		setProtocolPdfUrl(pdfUrl)
		setViewTitle(title)
		setIsModalVisible(true)
	}

	const onChange = e => {
		setprotocolVersionId(protocols[0].versionId)
		setCheckBoxIsClick(e.target.checked)
	}

	const onFinish = () => {
		activateAccount()
	}

	const handleCancel = () => {
		setIsModalVisible(false)
	}

	const renderProtocolName = () => {
		return protocols.map(item => {
			item.title = platformProtocolMap.filter(p => item.protocolType === p.type)[0].protocolName
			return (
				<span onClick={() => viewProtocol(item.pdfUrl, item.title)} style={{ cursor: 'pointer', color: 'rgb(22, 144, 207)' }}>
					{item.title}
				</span>
			)
		})
	}

	return (
		<ActivateAccountWrapper>
			{console.log('activateAccountForm')}
			<div className="wrapper">
				<div className="activateAccountForm">
					<Form form={form} name="activateAccountForm" onFinish={onFinish}>
						{/* 首次登录信息 */}
						{!pageHeader.current ? null : (
							<div className="activateAccount">
								<div className="title">{pageHeader.current.title}</div>
								<div className="extra">{pageHeader.current.message}</div>
							</div>
						)}
						<GetFormList configArr={activateAndForgetPasswordFormItemConfig('activateAccount', pageType)} pageType={pageType} form={form} />
						{/* finish */}
						<div className="checkbox">
							<Checkbox onChange={onChange}></Checkbox>
							<div style={{ display: 'inline-block' }}>
								<span style={{ color: 'white', marginLeft: '8px' }}>我同意并遵守</span>
								{renderProtocolName()}
							</div>
						</div>
						<Form.Item className="finish" name="finish">
							<Button className="oper-btn" type="primary" htmlType="submit" disabled={!checkBoxIsClick} loading={loading}>
								提交
							</Button>
						</Form.Item>
						<div className="returnLogin">
							<Link to={'/home'} state={{ Islogin: true }}>
								返回登录
							</Link>
						</div>
					</Form>
				</div>
				<PDFViewer onCancel={handleCancel} title={viewTitle} pdfUrl={protocolPdfUrl} visible={isModalVisible} />
			</div>
		</ActivateAccountWrapper>
	)
}

const ActivateAccountWrapper = styled.div`
	width: 100%;
	height: 100vh;
	background-image: url(${require('@images/home/<USER>')});
	background-size: cover;
	background-position: bottom;
	overflow-y: auto;

	.wrapper {
		width: 100%;
		min-height: 700px;
		/* border: 1px solid red; */
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.activateAccountForm {
		/* width: 330px; */
		color: #d7ecff;
		font-size: 14px;
		.activateAccount {
			text-align: center;
			color: #d7ecff;
			.title {
				font-size: 24px;
			}
			.extra {
				font-size: 14px;
				margin: 16px 0 32px 0;
			}
		}
		.formWarp {
			width: 330px;
			margin: 0 auto;
		}
		.checkbox {
			width: 330px;
			margin: 0 auto 20px auto;
		}
		.finish {
			width: 330px;
			margin: 0 auto;
			#activateAccountForm_finish {
				width: 100%;
			}
		}
		.returnLogin {
			width: 330px;
			margin: 0 auto;
			text-align: right;
			a {
				color: #d7ecff;
			}
		}
	}

	.checkbox {
		display: flex;
	}
	// 改变复选框字体颜色
	.checkbox .ant-checkbox-wrapper {
		color: #d7ecff;
	}

	// 改变所有label字体颜色
	.ant-form-item-label > label {
		color: #d7ecff;
	}
	// 改变所有提示字体的颜色
	.ant-form-item-extra {
		color: #d7ecff;
	}

	.ant-form-item-control-input-content {
		width: 330px;
		height: 48px;
		.ant-input-affix-wrapper {
			height: 48px;
		}
	}
	.login-form-button {
		width: 330px;
		height: 48px;
	}

	// 改变验证码输入框宽度
	.ant-col-10 {
		max-width: 100%;
		width: 100%;
	}
	// 改变验证码按钮的位置和高度
	.ant-col-6 {
		margin-left: 8px;
		.captcha {
			height: 48px;
			width: 100px;
		}
	}

	// 提交按钮样式
	#activateAccountForm_finish {
		width: 100%;
		height: 40px;
	}
`
