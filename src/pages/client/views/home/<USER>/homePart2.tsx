import React from 'react'
import styled, { keyframes } from 'styled-components'
import TweenOne from 'rc-tween-one'
import 'rc-texty/assets/index.css'
import Children from 'rc-tween-one/lib/plugin/ChildrenPlugin'
import { IAnimObject } from 'rc-tween-one/typings/AnimObject'
import { loginApis } from '@src/pages/client/api'
import { message } from 'antd'
TweenOne.plugins.push(Children)
require('./part2canvas.js')

type Props = {}

type PageData = {
	financeAmountTotal: number
	transferAmountTotal: number
	cooperationCompanyCount: number
	blockChainHign: number
	[x: string]: any
} | null

let timer: any

export default function HomePart2(props: Props) {
	clearInterval(timer)

	const [backList, setBackList] = React.useState({
		item1Active: false,
		item2Active: true,
		item3Active: false,
		item4Active: false,
	})

	const [displayText, setDisplayText] = React.useState('累计交易金额')
	const [pageData, setPageData] = React.useState<PageData>(null)
	const [animation, setAnimation] = React.useState<IAnimObject | null>(null)

	React.useEffect(() => {
		// window.animationPlay()
		// getPageData()
	}, [])

	const getPageData = async () => {
		const res = await loginApis.queryStatisticsInfo({}).catch(err => {
			console.log('err', err)
		})
		if (res) {
			if (res.returnCode % 1000 === 0) {
				setPageData(res.data)
				// 设置初始金额千分位
				if (res.data.transferAmountTotal) {
					setAnimation({
						Children: {
							value: Number(res.data.transferAmountTotal),
							floatLength: 2,
							formatMoney: {
								thousand: ',',
								decimal: '.',
							},
						},
						duration: 300,
					})
				}
			} else {
				message.error(res.returnDesc)
			}
		}
	}

	//鼠标移入事件
	const handleMouseEnter = (key, idx, displayNumber, displayText, formatMoney) => {
		const obj = { ...backList }
		for (let k in obj) {
			if (key === k) {
				obj[k] = true
			} else obj[k] = false
		}
		setBackList(obj)
		setDisplayText(displayText)
		// 非空设置动画
		if (displayNumber) {
			setAnimation({
				Children: {
					value: Number(displayNumber),
					floatLength: formatMoney ? 2 : 0,
					formatMoney,
				},
				duration: 300,
			})
		}
	}

	return (
		<HomePart2Wrapper>
			<div className="bg">
				<canvas id="webgl"></canvas>
			</div>
			<div className="content">
				<div className="number-area">
					<TweenOne animation={animation as IAnimObject} style={{ fontSize: 120, marginBottom: 12 }}></TweenOne>
					<p style={{ fontSize: 32 }}>{displayText}</p>
				</div>

				<ul className="content-list">
					<li
						className={`item item1 ${backList.item1Active ? 'active' : ''}`}
						onMouseEnter={() => handleMouseEnter('item1Active', 0, pageData && pageData!.financeAmountTotal, '累计融资金额', true)}
					>
						<p className="bottom">累计融资金额</p>
					</li>
					<li
						className={`item item2 ${backList.item2Active ? 'active' : ''}`}
						onMouseEnter={() => handleMouseEnter('item2Active', 1, pageData && pageData!.transferAmountTotal, '累计交易金额', true)}
					>
						<p className="bottom">累计交易金额</p>
					</li>
					<li
						className={`item item3 ${backList.item3Active ? 'active' : ''}`}
						onMouseEnter={e => handleMouseEnter('item3Active', 2, pageData && pageData!.blockChainHign, '区块链高度', false)}
					>
						<p className="bottom">区块链高度</p>
					</li>
					<li
						className={`item item4 ${backList.item4Active ? 'active' : ''}`}
						onMouseEnter={e => handleMouseEnter('item4Active', 3, pageData && pageData!.cooperationCompanyCount, '合作机构', false)}
					>
						<p className="bottom">合作机构</p>
					</li>
				</ul>
			</div>
		</HomePart2Wrapper>
	)
}

const floatAnimate1 = keyframes`
	0%{
		transform: translate(0,0);
	}
	50%{
		transform: translate(0,${Math.random() * 25 + 'px'});
	}
	100%{
		transform: translate(0,0);
	}
`
const floatAnimate2 = keyframes`
	0%{
		transform: translate(0,0);
	}
	50%{
		transform: translate(0,${Math.random() * 25 + 'px'});
	}
	100%{
		transform: translate(0,0);
	}
`
const floatAnimate3 = keyframes`
	0%{
		transform: translate(0,0);
	}
	50%{
		transform: translate(0,${Math.random() * 25 + 'px'});
	}
	100%{
		transform: translate(0,0);
	}
`
const floatAnimate4 = keyframes`
	0%{
		transform: translate(0,0);
	}
	50%{
		transform: translate(0,${Math.random() * 25 + 'px'});
	}
	100%{
		transform: translate(0,0);
	}
`

const HomePart2Wrapper = styled.div`
	position: relative;
	width: 100%;
	height: 100%;
	background-image: url(${require(`@images/home/<USER>
	background-size: cover;
	background-position: bottom;
	.bg {
		position: absolute;
		height: 100%;
		width: 100%;
		overflow: hidden;
		#webgl {
			position: absolute;
			bottom: 0;
		}
	}
	.content {
		position: relative;
		width: 100%;
		height: 100%;
		color: #9cefff;
		padding-top: 260px;
		box-sizing: border-box;
		.number-area {
			text-align: center;
		}
		.content-list {
			.item {
				position: absolute;
				font-size: 18px;
				font-weight: 400;
				color: #9cefff;
				line-height: 20px;
				text-align: center;
				transition: all 0.5s ease-out;
				z-index: 1;
				cursor: pointer;
			}
			.back {
				font-size: 0;
				z-index: 0;
				transform: scale(0) translate(-50%, 0%);
				&.active {
					transform: scale(1) translate(-50%, -50%);
					.bottom {
						font-size: 34px;
						font-weight: 400;
						color: #9cefff;
						line-height: 40px;
						opacity: 0.8;
					}
				}
			}
			.item1,
			.item2,
			.item3,
			.item4 {
				&::before {
					position: relative;
					content: ' ';
					display: block;
					width: 31px;
					height: 12px;
					top: 0;
					left: -20px;
					opacity: 0;
					background-image: url(${require('@images/home/<USER>')});
					background-size: 31px 12px;
					transition: all 0.3s ease;
				}
				&::after {
					position: absolute;
					content: ' ';
					display: block;
					width: 31px;
					height: 12px;
					bottom: -10px;
					right: -20px;
					opacity: 0;
					background-image: url(${require('@images/home/<USER>')});
					background-position: center;
					background-size: cover;
					transition: opacity 0.3s ease;
				}
				&.active {
					&::before {
						opacity: 1;
					}
					&::after {
						opacity: 1;
					}
				}
			}
			.item1,
			.item1_back {
				animation: ${floatAnimate1} 3s infinite ease;
				bottom: 24%;
				left: 20%;
			}
			.item2,
			.item2_back {
				animation: ${floatAnimate2} 2s infinite ease;

				bottom: 20%;
				left: 39%;
			}
			.item3,
			.item3_back {
				animation: ${floatAnimate3} 3s infinite ease;
				bottom: 15%;
				left: 56%;
			}
			.item4,
			.item4_back {
				animation: ${floatAnimate4} 2s infinite ease;
				bottom: 25%;
				left: 72%;
			}
		}
	}
`
