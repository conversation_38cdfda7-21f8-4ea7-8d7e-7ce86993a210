import React, { useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Form, Input, Button, message, Select } from 'antd'
import { UserOutlined, LockOutlined } from '@ant-design/icons'
import { useLocalStore } from 'mobx-react'
import styled from 'styled-components'
import kunlun from '@utils/crypto'
import { loginApis, commonModuleApi } from '@src/pages/client/api'
import stores from '@src/pages/client/sotre/store'
import { history } from '@utils/router'
import { roleTypeMap } from '@src/globalConfig/codeMaps'
import { CONFIRM } from '@src/constants/commonStatus'
import { setStorage, getFrontCodeListForAdmin, getRealMenuList, getFirstPagePath, getStorage } from '@factor/biz/bizIndex'
import { uiData } from '@factor/store/index'
import { npList } from './login-config-dev'
import refreshTokenHelper from '@utils/request/refreshTokenHelper'

type PropsStructure = {
	visible: boolean
	onClose: () => any
}

//获取验证码信息
interface CaptchaType {
	img: string //图片banse 64
	captchaNo: string
	captcha: string //图片内容
}

// 目前未使用该页面进行登录，使用home页面接受登录回调函数
export default function Login(props: PropsStructure) {
	const localStore = useLocalStore(() => stores)
	const [loading, setLoading] = React.useState<boolean>(false)
	const [captchaInfo, setcaptcha] = React.useState<CaptchaType>({
		img: '',
		captchaNo: '',
		captcha: '',
	})
	let Ref = React.useRef<any>()
	const [loginForm] = Form.useForm()

	useEffect(() => {
		getCaptcha()
		const timer = setInterval(() => {
			getCaptcha()
		}, 120000)
		Ref.current = timer
		// console.log(
		// 	npList.map(item => {
		// 		console.log()
		// 		return {
		// 			value: `${item[0]}&&&&${item[1]}`,
		// 			label: item[2],
		// 		}
		// 	})
		// )
		return () => {
			clearInterval(Ref.current)
		}
	}, [])

	const refresh = () => {
		// clearInterval(Ref.current)
		// getCaptcha()
		// const timer = setInterval(() => {
		// 	getCaptcha()
		// }, 120000)
		// Ref.current = timer
	}

	const getCaptcha = async () => {
		// const res = await loginApis.getCaptcha().catch(err => {
		// 	console.log('err', err)
		// })
		// setcaptcha(res)
	}

	const handleCloseBtnClick = () => {
		props.onClose()
	}

	const onFinish = (values: any) => {
		//先清空ext缓存
		localStorage.setItem('ext', '{}')
		// 登录接口验证
		handleLogin(values)
	}

	const handleAuth = (loginPerm: loginPermType, account: loginAccountType) => {
		//baseInfo的内容提交给了
		let { menuList: originMenuList, frontCodeList = [], roleList = [] } = loginPerm || {}
		// account.adminFlag = true
		if (account?.adminFlag) {
			frontCodeList = getFrontCodeListForAdmin()
		}
		originMenuList = originMenuList || []
		frontCodeList = frontCodeList || []
		let realMenuList = getRealMenuList(frontCodeList, originMenuList)
		// 存储登录的一般信息
		setStorage({ frontCodeList, originMenuList, menuList: realMenuList, roleList })
		// 更新路由
		uiData.updateFrontCodeList(frontCodeList)
	}

	//登录逻辑
	async function handleLogin(values: any) {
		const { username, password, captcha } = values
		const params = {
			userName: username,
			password: kunlun.sm.getHash(password), //对passwd取sm3的hash
			captcha,
			captchaNo: captchaInfo?.captchaNo,
			projectCode: 'factor',
		}
		if (!loading) {
			setLoading(true) //登录按钮loading开
			const res = await loginApis.login(params).catch(e => {
				// 处理错误
				console.log(`e = ${e}`)
				setLoading(false)
				// getCaptcha()
			})
			let resData: LoginResp = res?.data
			if (resData) {
				const token = resData.token.accessToken
				const expireIn = resData.token?.expireIn
				const account = resData.account
				const ext: any = account.ext
				const user: any = ext.user
				const loginPerm = ext.loginPerm
				const loginRoleList = ext?.user?.roleList || []
				// 企业多身份
				const multiCompanyType: boolean = ext.multiCompanyType
				const company: any = user.company || {}
				const companyTypeList: string[] = company?.type?.toLowerCase().split(',') || []
				//roleNames继续有效
				const roleNames = user.roleNames ? user.roleNames : []
				// 核心企业、供应商、金融机构、金融机构母公司管理员
				const isAdmin = loginRoleList[0]?.roleType === roleTypeMap.ADMIN
				//企业认证是否通过,企业认证项目中未维护枚举值，故直接根据后端返回的值来判断'CONFIRM'为认证通过
				const isPassAuth = company?.status === 'CONFIRM'
				const isBusinessStatusConfirm = company.businessStatus
				// 密钥升级
				const isKeyUpgrade: boolean = ext.user['companyPrivKeyEnc']
				const isCorS = companyTypeList.includes('c') || companyTypeList.includes('s')
				// 完善银行账户（管理员、账户字段为空、公司是核心企业或供应商或双身份）
				const isPerfectAccount: boolean = !!(isAdmin && ext.bankCards?.length === 0 && isCorS && isBusinessStatusConfirm === CONFIRM && isPassAuth)
				//核心企业、供应商、或双身份企业管理员登录平台后完善企业开票信息
				const isNeedImproveInvoicingInfo = isAdmin && isCorS && isPassAuth && !ext.fillCompanyInvoicingInfo ? true : false
				// 签署平台协议
				const signProtocol: boolean = isAdmin && isBusinessStatusConfirm === CONFIRM && isPassAuth && ext.protocolSigns?.length ? ext.protocolSigns : false
				// 多身份就要选择企业
				const identitySelection = multiCompanyType && !isAdmin

				const jumpPageMap = {
					isKeyUpgrade,
					isPerfectAccount,
					isImproveInvoicingInfo: isNeedImproveInvoicingInfo,
					signProtocol,
					identitySelection,
				}

				// 存储登录信息
				localStorage.setItem('token', token)
				refreshTokenHelper.setExpireTimeAndToken(expireIn, token)
				localStorage.setItem('ext', JSON.stringify(ext))
				// 存储用户信息和密钥在mobx中，方便在密钥升级中使用
				localStore.loginStore.setPassword(password)

				//如何没有角色，跳异常页面
				if (roleNames.length == 0) {
					history.push('/home/<USER>')
					return
				}
				//调用接口判读该用户是否有未处理的兑付信息(1.7.6需求)
				const remindData = await commonModuleApi.getCouCashRemindData().catch(err => console.log(err))
				if (remindData?.data) {
					localStorage.setItem('couCashRemindData', JSON.stringify(remindData.data))
				}
				//存储权限信息
				handleAuth(loginPerm, account)

				if (company.operatingOrganizationUuid) {
					try {
						let names = await loginApis.getOoNamesByUuids({
							uuids: [company.operatingOrganizationUuid],
						})
						const OoInfo: any = {
							operatingOrganizationName: names.ooNameMap[company.operatingOrganizationUuid],
							operatingOrganizationUuid: company.operatingOrganizationUuid,
						}
						//存储运营机构信息
						setStorage({ OoInfo: OoInfo })
						// 统一用 setStorage 存储 OoInfo ,1.8.8
					} catch (err) {
						message.error('获取运营机构信息错误')
						setLoading(false) //登录按钮loading关
						return
					}
				}

				if (
					jumpPageMap.isKeyUpgrade ||
					jumpPageMap.isPerfectAccount ||
					jumpPageMap.isImproveInvoicingInfo ||
					jumpPageMap.signProtocol ||
					jumpPageMap.identitySelection
				) {
					// 需要进行密钥升级
					history.push('/home/<USER>', {
						jumpPageMap,
					})
				} else {
					message.success('登录成功')
					//获取内页第一个地址
					let targetPath = getFirstPagePath(getStorage('menuList'))
					history.push(targetPath || '/content/*')
				}
			} else {
				if (res?.returnDesc) {
					message.info(res?.returnDesc)
				}
				setLoading(false)
				// getCaptcha()
			}
		}
	}

	const handleSelectedChange = value => {
		console.log(`value = ${value}`)
		let [username, password] = value.split('&&&&')
		if (username && password) {
			loginForm.setFieldsValue({
				username,
				password,
			})
		}
	}

	return (
		<>
			{props.visible ? (
				<LoginWrapper>
					<div className="mask" />
					<div className="login">
						{process.env.NODE_ENV === 'development' ? (
							<div style={{ marginBottom: '20px' }}>
								<Select
									style={{ width: 365 }}
									onChange={handleSelectedChange}
									options={npList.map(item => {
										return {
											value: `${item[0]}&&&&${item[1]}`,
											label: item[2],
										}
									})}
								/>
							</div>
						) : null}
						<div className="form">
							<Form form={loginForm} name="normal_login" className="login-form" initialValues={{ remember: true }} onFinish={onFinish}>
								<Form.Item
									name="username"
									rules={[
										{
											required: true,
											type: 'email',
											whitespace: true,
											message: '请输入邮箱，并确认格式正确',
										},
									]}
								>
									<Input prefix={<UserOutlined className="site-form-item-icon" maxLength={64} />} placeholder="邮箱" />
								</Form.Item>
								<Form.Item name="password" rules={[{ required: true, message: '请输入密码', whitespace: true }]}>
									<Input.Password prefix={<LockOutlined className="site-form-item-icon" />} type="password" placeholder="密码" />
								</Form.Item>
								<div className="capItem">
									<Form.Item name="captcha" rules={[{ required: true, message: '请输入验证码', whitespace: false }]}>
										<Input type="text" placeholder="验证码" className="capInput" />
									</Form.Item>
									<div className="captcha" onClick={refresh}>
										<img src={'data:image/png;base64,' + captchaInfo?.img} alt="看不清？刷新一下" title="看不清？刷新一下" />
									</div>
								</div>

								<Form.Item>
									<Button type="primary" htmlType="submit" className="login-form-button" loading={loading}>
										登录
									</Button>
								</Form.Item>
								<div className="forgetAndFirstLogin">
									<div
										className="forget"
										onClick={() => {
											return history.push('/home/<USER>')
										}}
									>
										忘记密码
									</div>
									<Link to={'/home/<USER>'} style={{ color: 'white' }}>
										激活账号
									</Link>
								</div>
							</Form>
						</div>
						<div className="close" onClick={handleCloseBtnClick}>
							<img src={require('@images/home/<USER>')} />
						</div>
					</div>
				</LoginWrapper>
			) : null}
		</>
	)
}

const LoginWrapper = styled.div`
	/* width: 100%; */
	.mask {
		height: 100%;
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.85);
		transition: all 0.3s ease;
	}
	.login {
		width: 530px;
		// box-shadow: 0px 0px 25px 0px skyblue;
		border-radius: 20px;
		position: fixed;
		left: 50%;
		top: 50%;
		padding: 45px 90px;
		animation: aa 10s linear infinite;
		transform: translate(-50%, -50%);
		z-index: 999;
		@keyframes aa {
			0% {
				box-shadow: 0px 0px 15px 0px skyblue;
			}
			25% {
				box-shadow: 0px 0px 20px 0px skyblue;
			}
			50% {
				box-shadow: 0px 0px 25px 0px skyblue;
			}
			75% {
				box-shadow: 0px 0px 20px 0px skyblue;
			}
			100% {
				box-shadow: 0px 0px 15px 0px skyblue;
			}
		}
		.form {
			z-index: 9;
			.forgetAndFirstLogin {
				display: flex;
				justify-content: space-between;
				color: white;
				cursor: pointer;
			}
			.capItem {
				display: flex;
				flex-wrap: wrap;
				.ant-form-item-control-input-content {
					display: flex;
					margin-right: 10px;
					width: auto !important;
				}
				.ant-form-item {
					flex: 1;
				}
				.captcha {
					background-color: #e9eefb;
					background-image: none;
					border: 1px solid transparent;
					border-radius: 4px;
					color: blue;
					text-align: center;
					width: 100px;
					height: 48px;
					display: flex;
					align-items: center;
					img {
						width: 100%;
						height: 100%;
					}
				}
			}
		}
		.close {
			position: absolute;
			top: -10px;
			right: -10px;
			&:hover {
				cursor: pointer;
				opacity: 0.8;
			}
			img {
				width: 28px;
			}
		}
	}

	.ant-form-item-control-input-content {
		width: 330px;
		height: 48px;
		.ant-input-affix-wrapper {
			height: 48px;
		}
	}
	.login-form-button {
		height: 48px;
		width: 100%;
	}

	.capItem {
	}
`
