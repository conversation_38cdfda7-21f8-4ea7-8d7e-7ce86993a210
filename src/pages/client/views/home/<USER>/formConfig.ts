/**
 * @description:
 * @param {string} companyType
 * @param {boolean} isLook 是否是表单组件查看
 * @return {*}
 */
const registerFormItemConfig = (companyType: string, isLook?: boolean) => {
	let companyBaseInfo: any = []
	let managerAccount: any = [
		{ key: 'Email', span: 24, verifyExistence: true },
		{ key: 'Mobile', span: 24 },
		{ key: 'MobileCode', span: 24 },
	]
	let otherCompanyBaseInfo: any = []
	switch (companyType) {
		case 'CS':
			otherCompanyBaseInfo = [
				{ key: 'CompanyName', span: 12, verifyExistence: true, isLook, label: '企业名称' },
				{ key: 'Industry', span: 12, isLook, label: '所属行业' },
				{ key: 'CorporationScale', span: 12, isLook, label: '企业规模' },
				{ key: 'ProvinceCity', span: 12, isLook, label: '所在地区' },
				{ key: 'Address', span: 12, isLook, label: '公司地址' },
				{ key: 'LegalPerson', span: 12, isLook, label: companyType === 'CS' ? '法定代表人' : '法定代表人/负责人' },
				{ key: 'CertificateType', span: 12, isLook, label: '证件类型' },
				{ key: 'LegalPersonIdCode', span: 12, isLook, label: '证件号码' },
				{ key: 'SocialCreditCode', span: 12, verifyExistence: true, isLook, label: '统一社会信用代码' },
				{ key: '_blank', span: 12, isLook, label: '' },
				{ key: 'IDPortrait', span: 12, isLook, label: '身份证（人像）' },
				{ key: 'IDNationalEmblem', span: 12, isLook, label: '身份证（国徽）' },
				{ key: 'BusinessLicense', span: 12, isLook, label: '营业执照' },
				//{ key: 'OpeningPermit', span: 12, isLook, label: companyType === 'CS' ? '开户许可证' : '金融机构许可证' },
				{ key: 'OpeningPermit', span: 12, isLook, label: companyType === 'CS' ? '其他附件' : '金融机构许可证' },
				{ key: 'CFCACertificateApplicationForm', span: 12, isLook, label: 'CFCA证书申请表' },
				{ key: 'CFCACertificateOfAuthorization', span: 12, isLook, label: '企业授权书' },
				{ key: 'IDCardOfHandler', span: 12, isLook, label: '经办人身份证复印件' },
			]
			companyBaseInfo = [...companyBaseInfo, ...otherCompanyBaseInfo]
			break
		case 'FI':
			otherCompanyBaseInfo = [
				{ key: 'CompanyName', span: 12, verifyExistence: true, isLook },
				{ key: 'ProvinceCity', span: 12, isLook },
				{ key: 'Address', span: 12, isLook },
				{ key: 'LegalPerson', span: 12, isLook },
				{ key: 'CertificateType', span: 12, isLook },
				{ key: 'LegalPersonIdCode', span: 12, isLook },
				{ key: 'SocialCreditCode', span: 12, isLook, verifyExistence: true },
				{ key: 'FiCode', span: 12, isLook },
				{ key: 'IDPortrait', span: 12, isLook },
				{ key: 'IDNationalEmblem', span: 12, isLook },
				{ key: 'BusinessLicense', span: 12, isLook },
				{ key: 'OpeningPermit', span: 12, isLook },
				{ key: 'CFCACertificateApplicationForm', span: 12, isLook },
				{ key: 'CFCACertificateOfAuthorization', span: 12, isLook },
				{ key: 'IDCardOfHandler', span: 12, isLook },
			]
			companyBaseInfo = [...companyBaseInfo, ...otherCompanyBaseInfo]
			break
		default:
			break
	}
	return { companyBaseInfo, managerAccount }
}

const activateAndForgetPasswordFormItemConfig = (type: string, pageType?: string) => {
	let newPasswordTest = type === 'activateAccount' ? '设置密码' : '设置新密码'
	let EmailVerifyExistence = pageType === 'register'
	return [
		{ key: 'Email', span: 24, hiddenLabel: true, verifyExistence: EmailVerifyExistence },
		{ key: 'Mobile', span: 24, hiddenLabel: true },
		{ key: 'MobileCode', span: 24, hiddenLabel: true },
		{ key: 'NewPassword', span: 24, hiddenLabel: true, placeholder: `${newPasswordTest}` },
		{ key: 'Confirmpassword', span: 24, hiddenLabel: true, placeholder: '再次输入密码' },
	]
}

const perfectInfo = (companyType?: string) => {
	let bankAccount: any = []
	switch (companyType) {
		case 'FI':
		case 'PFI':
			bankAccount = []
			break
		default:
			bankAccount = [
				{ key: 'Username', span: 24, isLook: true },
				{ key: 'BankAccount', span: 24 },
				{ key: 'OpeningBank', span: 24 },
			]
			break
	}
	return { bankAccount }
}

export { registerFormItemConfig, perfectInfo, activateAndForgetPasswordFormItemConfig }
