import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON><PERSON>, Popconfirm, message, Modal, But<PERSON> } from 'antd'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import OperatingArea from '@src/globalComponents/OperatingArea'
import SearchBar from '@src/pages/client/components/SearchBar'
import PDFViewer from '@globalComponents/PDFViewer'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import { financeModuleApi, commonModuleApi } from '@src/pages/client/api'
import { getStorage, hasAuth, couDataOnChain } from '@src/pages/client/biz/bizIndex'
import { history } from '@src/utils/router'
import styled from 'styled-components'
interface NoticeProps {
	tablePageName: string
}

const TobeAgreement = (props: NoticeProps) => {
	let { tablePageName } = props
	const [loading, setLoading] = useState(false)
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [searchParams, setSearchParams] = useState<{
		financeCompanyUuid?: string
		financeNumber?: string
	}>({})
	const [showAuit, setShowAuit] = useState(false) //展示去网银签署弹窗
	const [record, setRecord] = useState({}) // 保存当前点击的数据

	//pdf预览弹框控制
	const [pdfVisible, setPdfVisible] = React.useState<boolean>(false)
	const [pdfUrl, setPdfUrl] = React.useState<string>('')

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	const getCurrentRole = () => {
		const currentRoleId = getStorage('roleId')
		// const currentRoleId = '12';
		const ext = JSON.parse(localStorage.getItem('ext') || '{}')
		return ext?.loginPerm?.roleList?.find(i => i?.roleId === currentRoleId)
	}
	const getList = async () => {
		setLoading(true)
		financeModuleApi
			.getloadCurrentUserTask({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				processDefinitionKeyList: ['pledgeFinance'],
				roleNameList: [getCurrentRole()?.roleCode || ''],
			})
			.then(
				res => {
					let data = []
					setLoading(false)
					console.log('getloadCurrentUserTask', res)
					// 需要从bizMap里面拿数据
					res.data.length > 0 &&
						res.data.map(item => {
							let obj = item.bizMap
							for (let key in obj) {
								item[key] = obj[key]
							}
							data.push(item)
						})
					pagination.total = res['count']
					setDataSource(data)
				},
				err => setLoading(false)
			)
	}
	function confirm(record) {
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let companyType = ext.user && ext.user.company ? ext.user.company.type : ''
		let companyPubKey = ext.user && ext.user.company ? ext.user.company.pubKey : ''
		let isAgree = false //这里只有取消操作，所以只传false
		if (companyType && companyPubKey) {
			let queryData = {
				isAgree: isAgree, //是否同意
				payCompanyType: companyType,
				toPubKey: companyPubKey,
				transferUuid: record.transferUuid,
				agreeReason: '',
				financeCancel: 'financeCancel',
			}
			let param = {}
			param = {
				businessKey: record.businessKey, // auditTransferData
				taskId: record.id,
				outcome: 'reject',
				jsonParamStr: JSON.stringify(queryData),
			}
			commonModuleApi
				.doTask(param)
				.then(item => {
					if (item.returnCode === 17000) {
						getList()
					} else if (item.returnCode === 30015) {
						message.error('重复操作，该项支付已完成处理')
					} else {
						// 删除关键字（银行接口异常）
						let returnDesc = item.returnDesc.replace('银行接口异常', '')
						message.error(returnDesc)
					}
				})
				.catch(err => {
					console.log('err', err)
				})
		}
	}

	function cancel(e) {
		console.log(e)
	}
	const showModal = (record: any) => {
		setRecord(record)
		setShowAuit(true)
	}

	const handleOk = e => {
		console.log(e)
		setShowAuit(false)
	}
	const onOperCallback = data => {
		// 存储融资申请详情，防止刷新数据丢失

		history.push('/content/couFinance/sFinanceDetail', {
			uuidOrNumber: data.uuid,
		})
	}
	const handleCancel = e => {
		setShowAuit(false)
	}
	//获取table的column
	const getColumns = () => {
		const columnsDic = {
			applicationNumber: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									onOperCallback(record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			operation: {
				render: (text: any, record: any) => {
					return (
						<div>
							{
								<>
									{hasAuth('bl_couFinance:financeAgreement:signagreement') && (
										<span className="link" style={{ textDecoration: 'underline' }} onClick={() => showModal(record)}>
											签署协议
										</span>
									)}
									{hasAuth('bl_couFinance:financeAgreement:canclecou') && (
										<Popconfirm title="确定取消融资？" onConfirm={() => confirm(record)} onCancel={cancel} okText="确定" cancelText="取消">
											<span className="link" style={{ color: 'red', textDecoration: 'underline' }}>
												取消融资
											</span>
										</Popconfirm>
									)}
								</>
							}
						</div>
					)
				},
			},
		}
		return getColumnsByPageName('tobeAgreement', columnsDic)
	}

	const handlePageChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	const onSubmit = (params: any) => {
		const financeCompanyUuid: string = params['uuid'] ? params['uuid'] : ''
		const financeNumber: string = params['financeNumber'] ? params['financeNumber'] : ''
		const paramsObj: any = {
			financeCompanyUuid,
			financeNumber,
		}
		pagination.current = 1
		setPagination({ ...pagination })
		setSearchParams(paramsObj)
	}
	const onClear = () => {
		setSearchParams({})
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar pageName="tobeAgreement" onSubmit={onSubmit} onClear={onClear} />
					</OperatingArea>
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						rowKey="id"
					/>
				</div>
			</LayoutSlot>
			<PDFViewer title="" visible={pdfVisible} pdfUrl={pdfUrl} onCancel={() => setPdfVisible(false)} />
			<Modal title="去网银签署" maskClosable={false} visible={showAuit} footer={null} onCancel={handleCancel}>
				<div style={{ padding: '50px 20px', textAlign: 'center' }}>
					<p>去网银签署您需要去网银签署《质押合同》、《贷款合同》。</p>
					<Button style={{ marginTop: '20px' }}>去网银签署</Button>
				</div>
			</Modal>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
`

export default TobeAgreement
