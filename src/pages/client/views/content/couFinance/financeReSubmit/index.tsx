import React, { useEffect, useState } from 'react'
import { message, Too<PERSON><PERSON>, Alert, Modal } from 'antd'
import { InfoCircleFilled } from '@ant-design/icons'
import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import { financeModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import styled from 'styled-components'
import { history } from '@src/utils/router'
import Feedback from '@src/pages/client/components/feedback'
import { hasAuth } from '@src/pages/client/biz/bizIndex'

function reSubmitPage() {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [loading, setLoading] = useState(false)
	const [feedbackVisible, setFeedbackVisible] = useState<boolean>(false)
	const [uuidOrNumber, setUuidOrNumber] = useState<string>('') // 融资申请详情的uuid或者number
	const [feedbackInfoObj, setFeedbackInfoObj] = useState<any>({
		status: 'success',
		title: '提交成功',
	})
	const [btnTxts, setBtnTxts] = useState<any>(['完成', '查看详情'])

	useEffect(() => {
		financeModuleApi
			.financeHasAuditor()
			.then(haveAuditor => {
				// 判断是否有审核员，没有审核员展示数据，有审核员不展示数据
				if (!haveAuditor) {
					getList()
				} else {
					setDataSource([])
				}
			})
			.catch(err => {
				console.log('err', err)
			})
	}, [pagination.current, pagination.pageSize])

	// 得到融资申请列表
	const getList = () => {
		setLoading(true)
		financeModuleApi
			.getFinanceList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				sendBankStatus: 'WAIT',
				status: 'AUDIT',
			})
			.then(
				(res: any) => {
					pagination.total = res['total']
					if (res.list) {
						setDataSource([...res.list])
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => {
					setLoading(false)
					pagination.total = 0
					setDataSource([])
				}
			)
	}

	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	const onOperCallback = (str: string, data: any) => {
		if (str === 'applicationNumber') {
			//根据按钮权限来判断跳转至对应的详情页面
			let url = ''
			if (hasAuth('bl_couFinance:financeDetail:cDetails')) {
				url = '/content/couFinance/cFinanceDetail'
			} else if (hasAuth('bl_couFinance:financeDetail:sDetails')) {
				url = '/content/couFinance/sFinanceDetail'
			} else if (hasAuth('bl_couFinance:financeDetail:fiDetails')) {
				url = '/content/couFinance/fiFinanceDetail'
			}
			// 存储融资申请详情，防止刷新数据丢失
			url &&
				history.push(url, {
					uuidOrNumber: data.uuid,
				})
		} else if (str === 'operation') {
			Modal.confirm({
				title: '确定提交到银行？',
				icon: null,
				cancelText: '取消',
				okText: '确定',
				content: (
					<div>
						<InfoCircleFilled style={{ color: '#1890ff', marginRight: '5px', fontSize: '16px' }} />
						确定提交后无法撤销
					</div>
				),
				onOk() {
					secondSubmitFinance(data)
				},
			})
		}
	}

	const secondSubmitFinance = data => {
		setLoading(true)
		financeModuleApi
			.secondSubmit({
				uuidOrNumber: data.uuid,
			})
			.then(value => {
				if (value.returnCode === 17000) {
					message.success('提交成功')
					getList()
				} else {
					if (value.returnDesc.indexOf('银行接口异常') !== -1) {
						// 银行接口异常
						// 删除关键字（银行接口异常）
						let returnDesc = value.returnDesc.replace('银行接口异常', '')
						setFeedbackInfoObj({
							status: 'warning',
							title: '提交金融机构失败',
							subTitle: (
								<div>
									<span>已创建成功，请联系平台运营重新提交到金融机构！</span>
									<br />
									<span>错误信息：{returnDesc}</span>
								</div>
							),
						})
						setBtnTxts(['完成', '查看详情'])
						// 存储融资申请编号，供在融资申请详情中使用
						setUuidOrNumber(data.uuid)
						setFeedbackVisible(true)
					} else {
						message.error(value.returnDesc)
					}
					setLoading(false)
				}
			})
			.catch(err => {
				setLoading(false)
			})
	}

	const getColumns = () => {
		const pageName = 'searchReSubmitFinance'
		const columnsDic = {
			applicationNumber: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									onOperCallback('applicationNumber', record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			operation: {
				width: 100,
				render: (text: any, record: any) => {
					return (
						<div>
							<span
								className="link"
								onClick={() => {
									onOperCallback('operation', record)
								}}
							>
								{record.sendBankStatus === 'WAIT' ? '提交到银行' : ''}
							</span>
						</div>
					)
				},
			},
		}
		return getColumnsByPageName(pageName, columnsDic)
	}

	function handlePageChange(current: number) {
		pagination.current = current
		setPagination({ ...pagination })
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<Alert
						message="当您在提交融资申请时，出现提交银行失败的情况下，请联系平台运营并按照运营的提示，在本页面将对应融资申请重新提交"
						type="info"
						showIcon
						className="financeListAlert"
					/>
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						rowKey="id"
					/>
				</div>
			</LayoutSlot>
			<Feedback
				onOk={() => {
					history.push('/content/couFinance/financeSearch')
				}}
				onDetail={() => {
					// 存储返回的uuid，供在融资申请详情中使用
					history.push('/content/couFinance/sFinanceDetail', {
						uuidOrNumber: uuidOrNumber,
					})
				}}
				visible={feedbackVisible}
				status={feedbackInfoObj.status}
				title={feedbackInfoObj.title}
				subTitle={feedbackInfoObj.subTitle}
				btnTxts={btnTxts}
			/>
		</Box>
	)
}

const Box = styled.div`
	.financeListAlert {
		margin-bottom: 10px;
	}
`

export default reSubmitPage
