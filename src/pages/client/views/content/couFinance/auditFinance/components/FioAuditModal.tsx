import React, { useEffect, useState } from 'react'
import { Modal, Form, Input, DatePicker, Button, Spin } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import moment from 'moment'
import PDFViewer from '@src/globalComponents/PDFViewer'

const { TextArea } = Input
export interface PropsStruct {
	/**
	 * @description 标题
	 */
	title: string
	/**
	 * @description 显示/隐藏
	 */
	visible: boolean
	/**
	 * @description 通过还是拒绝
	 */
	type: string
	/**
	 * @description 提交时加载
	 */
	confirmLoading: boolean
	/**
	 * @description 点击关闭事件
	 */
	onCancel: () => void
	/**
	 * @description 提交事件
	 */
	onSubmit: (values, any) => void
	//该融资信息
	record: any
}

const layout = {
	labelCol: { span: 8 },
	wrapperCol: { span: 14 },
}

export default function AuditModalPage(props: PropsStruct) {
	const { visible, title, type, confirmLoading, onCancel, onSubmit, record } = props
	const [protocolVisible, setProtocolVisible] = useState<boolean>(false)
	const [protocolHtmlStr, setProtocolHtmlStr] = useState<string>('')
	const [checkBoxIsSelect, setCheckBoxIsSelect] = useState<boolean>(false)

	const [form] = Form.useForm()
	const [form1] = Form.useForm()

	useEffect(() => {
		if (!confirmLoading) {
			form.resetFields()
		}
	}, [confirmLoading])

	useEffect(() => {
		if (type === 'agree') {
			setCheckBoxIsSelect(false)
		} else {
			setCheckBoxIsSelect(true)
		}
	}, [visible])

	// 当前日期 limitDay 最大账期 后的 日期不能选
	const disableDate = date => {
		return date < moment(record.createTime).startOf('day') || date > moment(record.financeDueDate).endOf('day')
	}

	const submitData = () => {
		// 1代表同意，2代表拒绝
		if (type === 'agree') {
			form1.validateFields().then(
				values => {
					values.coutcome = '1'
					values.financeValueDate = moment(values.financeValueDate).format('YYYY-MM-DD')
					onSubmit(values, () => {
						form1.resetFields()
					})
				},
				err => {
					console.log(err)
				}
			)
		} else {
			form.validateFields().then(
				values => {
					values.coutcome = '2'
					onSubmit(values, () => {
						form.resetFields()
					})
				},
				err => {
					console.log(err)
				}
			)
		}
	}

	return (
		<Modal
			title={title}
			open={visible}
			onCancel={() => {
				setCheckBoxIsSelect(false)
				onCancel()
				form.resetFields()
				form1.resetFields()
			}}
			closable={false}
			// onOk={submitData}
			width="500px"
			footer={null}
			bodyStyle={{
				// height: '135px',
				padding: '12px',
			}}
			// confirmLoading={confirmLoading}
			className="wx_Modal wx_Modal_confirm fi_audit_Modal"
		>
			<Spin tip="审核中提交中" spinning={confirmLoading}>
				<div className="wx_Modal_Main">
					{type === 'reject' ? (
						<Form form={form}>
							<div style={{ padding: '10px 20px' }}>
								<Form.Item name="comment">
									<TextArea showCount maxLength={50} rows={4} placeholder="请描述处理意见，最多输入50个字符" />
								</Form.Item>
							</div>
						</Form>
					) : (
						<div style={{ margin: '24px 50px 52px 50px', textAlign: 'center' }} className="wx_Modal_Main_tips">
							<ExclamationCircleOutlined style={{ color: '#1785ff', marginRight: '5px' }} /> 确定通过后无法撤销
							{/* <Form form={form1}>
							<Form.Item
								name="financeValueDate"
								label="起息日"
								rules={[{ required: true, message: '请选择起息日' }]}
								style={{ textAlign: 'left', padding: '10px 0', marginBottom: '10px' }}
							>
								<DatePicker disabledDate={disableDate} showToday={false} style={{ width: '220px' }} />
							</Form.Item>
							//江阴银行取消 起息日
						</Form> */}
							{/* 1.8.8 上饶银行 新增需求，前端不展示签署协议的勾选

					<div style={{ display: 'flex' }}>
						<Checkbox
							checked={checkBoxIsSelect}
							onChange={e => {
								setCheckBoxIsSelect(e.target.checked)
							}}
						></Checkbox>
							<div style={{ marginLeft: '5px', fontSize: '13px' }}>
								<span>同意并签署</span>
								<span
									style={{ color: 'rgb(22, 144, 207)', cursor: 'pointer' }}
									onClick={() => {
										let FileInfo: [] = JSON.parse(record.fileInfo)['protocolList']
										let fileUrl = ''
										if (FileInfo.length > 0) {
											fileUrl = FileInfo.filter((item: any) => 'financeContract' === item.type)[0]['url']
										}
										if (fileUrl) {
											setProtocolHtmlStr(fileUrl)
											setProtocolVisible(true)
										} else {
											message.error('获取协议失败')
										}
									}}
								>
									《保理融资合同及保理业务申请》
								</span>
							</div>
						
					</div>
						*/}
						</div>
					)}
					<div style={{ textAlign: 'right' }}>
						<Button
							onClick={() => {
								setCheckBoxIsSelect(false)
								onCancel()
								form.resetFields()
								form1.resetFields()
							}}
						>
							取 消
						</Button>
						<Button type="primary" style={{ marginLeft: 10 }} onClick={submitData}>
							确 定
						</Button>
					</div>
				</div>
			</Spin>
			<PDFViewer
				title="保理融资合同及保理业务申请"
				visible={protocolVisible}
				pdfUrl={protocolHtmlStr}
				onCancel={() => {
					setProtocolVisible(false)
					setProtocolHtmlStr('')
				}}
			/>
		</Modal>
	)
}
