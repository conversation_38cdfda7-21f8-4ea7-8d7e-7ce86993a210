import React, { useEffect, useState } from 'react'
import { message, Modal, Tooltip } from 'antd'
import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import { financeModuleApi, commonModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import AuditModal from '@src/globalComponents/AuditModal'
import styled from 'styled-components'
import { deepExtend } from '@utils/util'
import { history } from '@src/utils/router'
import FioAuditModal from '../components/FioAuditModal'
import { getStorage, hasAuth, couDataOnChain } from '@src/pages/client/biz/bizIndex'

interface IProps {
	isFIOperator?: boolean
}
function auditPage({ isFIOperator = false }: IProps) {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [auditFinancePagination, setAuditFinancePagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [loading, setLoading] = useState(false)
	const [auditVisible, setAuditVisible] = useState(false)
	const [auditType, setAuditType] = useState<any>('agree')
	const [auditTitle, setAuditTitle] = useState<any>('确定通过？')
	const [confirmLoading, setConfirmLoading] = React.useState(false)
	const [financeRowData, setFinanceRowData] = React.useState<any>({})
	//是否为 Fio 并且为非直连模式（润楼模式）
	const [isRlFioAndDisDirect, setIsRlFioAndDisDirectRlFio] = useState<boolean>(true) //江阴银行直接是直连模式
	//查看收款账号可见性
	const [receiveAccountVisible, setReceiveAccountVisible] = useState<boolean>(false)
	//查看收款账号信息
	const [receiveAccountData, setsetReceiveAccountData] = useState<any>(null)
	//判断是否是最后一个审核者
	const [isLastAuditorPerson, setIsLastAuditor] = React.useState(false)
	//	获取业务类型 江阴银行去掉
	const getBusinessSchema = () => {
		if (isFIOperator) {
			const { uuid } = JSON.parse(localStorage.getItem('ext') || '[]').user.company
			financeModuleApi
				.getFiConfigDetail({ fiUuid: uuid })
				.then(res => {
					//去配置逻辑 金融机构和母公司，没有则取母公司
					let fiCongfi = res.financeBusinessConfig || res.parentFinanceBusinessConfig || null
					if (!fiCongfi) message.error('金融机构配置信息不完善')
					if (2 == fiCongfi.businessSchema) {
						setIsRlFioAndDisDirectRlFio(true)
					}
				})
				.catch(err => {
					console.log('err', err)
				})
		}
	}

	useEffect(() => {
		getList()
	}, [isRlFioAndDisDirect, auditFinancePagination.current, auditFinancePagination.pageSize])

	useEffect(() => {
		// getBusinessSchema()
	}, [])
	const getCurrentRole = () => {
		const currentRoleId = getStorage('roleId')
		// const currentRoleId = '12';
		const ext = JSON.parse(localStorage.getItem('ext') || '{}')
		return ext?.loginPerm?.roleList?.find(i => i?.roleId === currentRoleId)
	}
	// 得到融资申请列表
	function getList(current?: any) {
		if (isFIOperator && !isRlFioAndDisDirect) return

		setLoading(true)
		commonModuleApi
			.loadCurrentUserTask({
				pageNum: current ? current : auditFinancePagination.current,
				pageSize: auditFinancePagination.pageSize,
				processDefinitionKeyList: ['pledgeFinanceFinal'],
				roleNameList: [getCurrentRole()?.roleCode || ''],
			})
			.then(
				(res: any) => {
					let financeData = deepExtend({}, res)
					let _auditTransferPagination = { ...auditFinancePagination }
					_auditTransferPagination.total = financeData['count']
					setAuditFinancePagination({ ..._auditTransferPagination })
					// auditFinancePagination.total = financeData['count']
					if (!financeData.data.length && auditFinancePagination.current !== 1) {
						_auditTransferPagination.current -= 1
						// getList()
						setAuditFinancePagination({ ..._auditTransferPagination })
					}
					if (financeData.data.length > 0) {
						let filterFinanceData: Array<any> = []
						filterFinanceData = financeData['data'].map(item => {
							//数据结构调整
							let auditFinanceData = {
								assignee: item.assignee,
								businessKey: item.businessKey,
								createTime: item.createTime,
								id: item.id,
								name: item.name,
							}
							return { ...item.bizMap, ...{ auditFinanceData } }
						})
						setDataSource(filterFinanceData)
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => {
					setLoading(false)
					auditFinancePagination.total = 0
					setDataSource([])
				}
			)
	}
	const handleSizeChange = (size: number) => {
		auditFinancePagination.current = 1
		auditFinancePagination.pageSize = size
		setAuditFinancePagination({ ...auditFinancePagination })
	}
	const onOperCallback = async (str: string, data: any, type?: string, title?: string) => {
		if (str === 'applicationNumber') {
			// 存储融资申请详情，防止刷新数据丢失
			//根据按钮权限来判断跳转至对应的详情页面
			let url = ''
			if (hasAuth('bl_couFinance:financeDetail:cDetails')) {
				url = '/content/couFinance/cFinanceDetail'
			} else if (hasAuth('bl_couFinance:financeDetail:sDetails')) {
				url = '/content/couFinance/sFinanceDetail'
			} else if (hasAuth('bl_couFinance:financeDetail:fiDetails')) {
				url = '/content/couFinance/fiFinanceDetail'
			}
			// 存储融资申请详情，防止刷新数据丢失
			url &&
				history.push(url, {
					uuidOrNumber: data.uuid,
					isFromAudit: true,
					auditData: data,
				})
		} else if (str === 'auditFinance') {
			let ext = JSON.parse(localStorage.getItem('ext') || '{}')
			let companyUuid: string = ext.user && ext.user.company ? ext.user.company.uuid : ''
			let isLastAuditor: any = await commonModuleApi
				.isLastFlowPerson({
					companyUuid,
					role: getCurrentRole()?.roleCode || '',
					taskId: data.auditFinanceData.id,
				})
				.catch(err => {
					console.log('err', err)
				})
			setIsLastAuditor(isLastAuditor)
			setFinanceRowData(data)
			setAuditVisible(true)
			setAuditType(type)
			setAuditTitle(title)
		}
	}
	const getColumns = () => {
		const pageName = isRlFioAndDisDirect ? 'fiauditFinanceList' : 'auditFinanceList'
		const columnsDic = {
			applicationNumber: {
				ellipsis: true,
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									onOperCallback('applicationNumber', record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			operation: {
				render: (text: any, record: any) => {
					return (
						<div>
							<span className="link" onClick={() => onOperCallback('auditFinance', record, 'agree', '确定通过？')}>
								通过
							</span>
							<span className="red" onClick={() => onOperCallback('auditFinance', record, 'reject', '确定拒绝？')}>
								拒绝
							</span>
						</div>
					)
				},
			},
		}
		if (isRlFioAndDisDirect) {
			columnsDic['receiveAccount'] = {
				render: (text: any, record: any) => {
					return (
						<div>
							<span
								className="link"
								onClick={() => {
									setReceiveAccountVisible(true)
									getBankCard(record.applicantUuid, record.fiUuid)
								}}
							>
								查看
							</span>
						</div>
					)
				},
			}
		}
		return getColumnsByPageName(pageName, columnsDic)
	}
	function handlePageChange(current: number) {
		auditFinancePagination.current = current
		setAuditFinancePagination({ ...auditFinancePagination })
	}

	const submitData = async (values: any) => {
		setConfirmLoading(true)
		let param = {}
		let jsonParamStr: any = {}
		if (isRlFioAndDisDirect) {
			jsonParamStr.loanResult = auditType === 'agree' ? 'AGREE_LOAN' : 'REJECT_LOAN'
			jsonParamStr.applicationNumber = financeRowData.applicationNumber
			// if (jsonParamStr.loanResult) jsonParamStr.financeValueDate = values.financeValueDate
		} else {
			if (auditType !== 'agree') {
				jsonParamStr.loanResult = 'REJECT_LOAN'
				jsonParamStr.applicationNumber = financeRowData.applicationNumber
			}
		}
		param = {
			businessKey: financeRowData.auditFinanceData.businessKey,
			taskId: financeRowData.auditFinanceData.id,
			jsonParamStr: JSON.stringify(jsonParamStr),
			outcome: auditType,
			comment: values.comment,
		}
		commonModuleApi
			.doTask(param)
			.then(item => {
				setConfirmLoading(false)
				if (item.returnCode === 17000) {
					message.success('处理成功')
					//如果是FI审核，需要cou存证上链
					// if (isFIOperator) {
					// 	forCouDataOnChain()
					// }
					// if (isLastAuditorPerson) {
					// 	//融信数据存证上链
					// 	let couNoList = []
					// 	couNoList = financeRowData.transferCous.map(cou => {
					// 		return cou['couNo']
					// 	})
					// 	if (auditType === 'agree') {
					// 		//如果是同意，开立或者支付都要存证上链
					// 		couDataOnChain(couNoList)
					// 	} else {
					// 		//如果是拒绝，只有支付存证上链，开立不存证上链
					// 		// if (!isPublishCou) {
					// 		// 	couDataOnChain(couNoList)
					// 		// }
					// 	}
					// }
				} else {
					if (item.returnCode === 30015) {
						message.error('重复操作，该项融资已完成处理')
					} else if (isRlFioAndDisDirect) {
						message.error(item.returnDesc)
					} else {
						// 删除关键字（银行接口异常）
						let returnDesc = item.returnDesc.replace('银行接口异常', '')
						message.error(returnDesc)
					}
				}
				setAuditVisible(false)
				getList()
			})
			.catch(err => {
				setConfirmLoading(false)
				setAuditVisible(false)
			})
	}

	const forCouDataOnChain = () => {
		financeModuleApi
			.getFinanceDetail({
				uuidOrNumber: financeRowData.uuid,
			})
			.then((res: any) => {
				let couList = res.couList
				let couNoList = []
				couNoList = couList.map(cou => {
					return cou['couNo']
				})
				couDataOnChain(couNoList)
			})
	}

	//获取银行账户信息
	const getBankCard = async (companyUuid, relationFi) => {
		let params = {
			businessType: 'FINANCING_RECEIVE',
			companyUuid,
			relationFi,
		}
		let querybankCard = await commonModuleApi.queryBankcard(params).catch(err => {
			console.log('err', err)
		})
		if (querybankCard) setsetReceiveAccountData(querybankCard)
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...auditFinancePagination}
						rowKey="id"
					/>
				</div>
			</LayoutSlot>
			{!isRlFioAndDisDirect ? (
				<AuditModal
					title={auditTitle}
					visible={auditVisible}
					onCancel={() => setAuditVisible(false)}
					onSubmit={submitData}
					confirmLoading={confirmLoading}
					type={auditType}
				/>
			) : (
				<FioAuditModal
					title={auditTitle}
					visible={auditVisible}
					onCancel={() => setAuditVisible(false)}
					onSubmit={submitData}
					confirmLoading={confirmLoading}
					type={auditType}
					record={financeRowData}
				/>
			)}

			<Modal
				open={receiveAccountVisible}
				title="融资企业收款账户信息"
				footer={false}
				onCancel={() => {
					setReceiveAccountVisible(false)
					setsetReceiveAccountData(null)
				}}
			>
				{(receiveAccountData && (
					<div style={{ padding: '20px', lineHeight: '30px' }}>
						<p>收款账户户名：{receiveAccountData.accountName}</p>
						<p>收款账户账号：{receiveAccountData.accountNum}</p>
						<p>收款账户开户行：{receiveAccountData.accountBank}</p>
					</div>
				)) || <div>信息获取失败</div>}
			</Modal>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
`

export default auditPage
