import { Modal } from 'antd'
import React, { useEffect, useState } from 'react'
import { transformFilter } from '@utils/handleJson'
interface Props {
	/**
	 * 可见
	 */
	visible: boolean
	/**
	 * 需要预览的信息
	 */
	data: any
	/**
	 * 关闭事件
	 */
	onCancel: () => void
}

const messageModal = (props: Props) => {
	const { visible, data, onCancel } = props
	const [formatJson, setFormatJson] = useState<string>()
	useEffect(() => {
		if (visible) {
			var jsonObj = JSON.parse(data)
			var result = {}
			transformFilter(jsonObj, result)
			setFormatJson(JSON.stringify(result, null, 4))
		}
	}, [visible])
	return (
		<Modal className="messageModal" title="接口报文信息" open={visible} onCancel={() => onCancel()} footer={null} width={600} bodyStyle={{ padding: '20px' }}>
			<pre>{formatJson}</pre>
		</Modal>
	)
}

export default messageModal
