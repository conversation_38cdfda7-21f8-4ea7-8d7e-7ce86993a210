import React from 'react'
import { Descriptions, Tooltip } from 'antd'
import { pledgeFinanceStatus } from '../../financeCommon'

import { renderTooltip, render2Fixed } from '@src/pages/client/config/TableColumnsRender'
import { ExclamationCircleOutlined } from '@ant-design/icons'

const repaymentInfo = (props: { data: any }) => {
	const { data } = props

	return (
		<div style={{ marginBottom: '20px' }}>
			<Descriptions bordered column={2}>
				<Descriptions.Item label="还款时间">{data?.refundDate || '--'}</Descriptions.Item>
				<Descriptions.Item label="本金还款金额">
					{data?.principalAmount !== null ? `￥${render2Fixed(`${data?.principalAmount / 100}`)}` : '--'}
				</Descriptions.Item>
				{/* <Descriptions.Item label="利息还款金额">
					{data?.interestAmount !== null ? `￥${render2Fixed(`${data?.interestAmount / 100}`)}` : '--'}
				</Descriptions.Item> */}
			</Descriptions>
		</div>
	)
}

export default repaymentInfo
