import React, { useEffect, useState } from 'react'
import { Table, message, Tooltip, Button } from 'antd'
import { renderAmount } from '@src/pages/client/config/TableColumnsRender'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { stampToTime, timeCalculate } from '@src/utils/timeFilter'
import { financeModuleApi, commonModuleApi, financeCheckManageModuleApi } from '@src/pages/client/api'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { history } from '@src/utils/router'
import PDFViewer from '@globalComponents/PDFViewer'
import SmallTitle from '@src/globalComponents/SmallTitle'
import BaseInfo from './baseInfo'
import CostInfo from './costInfo'
import RepaymentInfo from './repaymentInfo'
import MessageModal from './messageModal'
import FileSaver from 'file-saver'
import styled from 'styled-components'
import AttachmentTable from '@src/globalComponents/EditTable/AttachmentTable'
import { getCurrentRole, hasAuth } from '@src/pages/client/biz/bizIndex'
import { evenlyTableColunms, getTabTit, invalidVal } from '@globalBiz/gBiz'
import { observer } from 'mobx-react-lite'
import ChainInfoModal from '@src/globalComponents/chainInfoComponents/ChainInfoModal'
import { platformFeeModelEnum } from '@src/globalConfig/codeMaps'
import { DuplicateCheck, DuplicateCheckProgress, duplicateCheckMap, duplicateCheckProgressMap } from '@globalConfig/financeCheckManage'
import { getInvoicCheckStatusCols } from '@src/globalBiz/invoiceBizModule'
import { getNotNullVal } from '@src/utils/util'
import DetailModal from '@clientComponents/detailModal'
import { useLocation, useNavigate } from 'react-router-dom'
import AuditModal from '@src/globalComponents/AuditModal'
import moment from 'moment'
interface FinanceDetailsProps {
	columnsType: string
	isCompanyS?: boolean
	isFI?: boolean
}

function financeDetail(props: FinanceDetailsProps) {
	const navigate = useNavigate()
	const { columnsType, isCompanyS, isFI } = props
	const attachmentTable = React.useRef(null as any)
	const { state } = useLocation() as any
	const transferUuid = state?.DetailForTransfer
	const isFromAudit = state?.isFromAudit || false
	const auditData = state?.auditData || {}
	const [attachmentInfoList, setAttachmentInfoList] = useState<object[]>([])
	const [baseInfoDataSource, setBaseInfoDataSource] = useState<any>({}) // 基本信息表格中的数据
	const [costDataSource, setCostDataSource] = useState<any>({}) // 基本信息表格中的数据
	const [financeCouDataSource, setFinanceCouDataSource] = useState<any[]>([]) // 融信融信表格中的数据
	const [invoiceDataSource, setInvoiceDataSource] = useState<any[]>([]) // 发票表格中的数据
	const [financeContractInfo, setFinanceContractInfo] = useState<any>({}) // 融资合同
	const [refundInfo, setRefundInfo] = useState<any>({}) // 还款信息

	const [interestPayWay, setInterestPayWay] = useState<string>('') // 付息方式
	const [isShowFinanceRate, setIsShowFinanceRate] = useState<boolean>(true) // 是否显示融资利率
	const [financeCouAllTableLoading, setFinanceCouAllTableLoading] = useState<boolean>(false)
	const [billLoading, setBillLoading] = useState<boolean>(false)
	const [chainInfoModalVisible, setChainInfoModalVisible] = useState<boolean>(false)
	const [onChainData, setOnChainData] = useState({})
	const [platformProtocol, setPlatformProtocol] = useState(null)
	const [zdCheckList, setZdCheckList] = useState([])
	const [detailVisible, setDetailVisible] = useState(false)
	const [invoiceData, setInvoiceData] = useState<any>({})
	// 处理pdf预览
	const [path, setPath] = useState('')
	const [pdfVisible, setPdfVisible] = useState(false)

	// 处理请求报文预览
	const [messageVisible, setMessageVisible] = useState(false)
	//判断是否是最后一个审核者
	const [isLastAuditorPerson, setIsLastAuditor] = React.useState(false)
	const [apiMessage, setApiMessage] = useState<any>('')
	const [auditVisible, setAuditVisible] = useState(false)
	const [auditType, setAuditType] = useState('agree')
	const [auditTitle, setAuditTitle] = useState('确定通过？')
	const [confirmLoading, setConfirmLoading] = React.useState(false)

	const financeCostData = [
		{
			key: '1',
			project: '融资利息',
			rateType: '年化',
			rate: costDataSource.interestRate,
			// 审批前：=融资金额*（融信到期日-创建融资申请的日期）*利率/360
			// 审批后：=融资金额*（融信到期日-起息日）*利率/360
			cost: costDataSource.interestCost,
			platformFeeModel: costDataSource.platformFeeModel ? '银行扣取' : '--',
		},
	]
	useEffect(() => {
		// 获取uuid，得到融资申请详情
		const uuidOrNumber = history.location.state?.uuidOrNumber
		if (uuidOrNumber) getFinanceDetail(uuidOrNumber)
		if (uuidOrNumber && isFI) getCheckTaskListByFinanceUuid(uuidOrNumber)

		return () => {
			// 组件卸载时清除uuid
			localStorage.removeItem('uuidOrNumber')
		}
	}, [])

	useEffect(() => {
		if (isCompanyS && interestPayWay === 'center') {
			// 如果是供应商 且 核心企业付息，则不显示融资费率
			setIsShowFinanceRate(false)
		}
	}, [interestPayWay])

	const getFinanceDetail = financeDetailUuid => {
		const hide = message.loading('加载中...', 0)
		financeModuleApi
			.getFinanceDetail({
				uuidOrNumber: financeDetailUuid,
			})
			.then(
				(res: any) => {
					// 设置api报文
					setApiMessage(res.apiMessage)
					setTimeout(hide, 0)
					// 存储付息方式
					setInterestPayWay(res.interestPayWay)
					//financeZdw该字段如果有，则使用该对象中的中登网数据，反之使用最外层数据,兼容老数据
					if (res?.financeZdw) {
						res.zdwRegNo = res?.financeZdw?.zdwRegNo
						res.zdwAmendNo = res?.financeZdw?.zdwAmendNo
					}
					let dateDiff = 0
					let m1 = moment(res.financeDueDate)
					let m2 = moment(res.financeValueDate)
					dateDiff = res.financeDueDate ? m1.diff(m2, 'day') : 0
					let _ratenow = res.factoringRate ? ((Number(res.inputFinanceAmountInYuan || 0) * Number(res.factoringRate || 0) * dateDiff) / 360).toFixed(2) : 0 // 试算利息
					let _extralMount = res.factoringRate
						? Number(res.inputFinanceTransferAmountInYuan || 0) - Number(res.inputFinanceAmountInYuan || 0) - Number(_ratenow || 0)
						: 0 // 余额试算利息

					// 设置基本信息表格中的数据
					setBaseInfoDataSource({
						applicationNumber: res.applicationNumber, // 融资申请编号
						status: res.status, // 融资申请状态
						inputFinanceTransferAmountInYuan: res.inputFinanceTransferAmountInYuan, // 转让融信金额
						discount: res.discount, // 融资比例
						inputFinanceAmountInYuan: res.inputFinanceAmountInYuan, // 融资金额
						fiCompanyName: res.fiCompanyName, // 金融机构
						financeValueDate: res.financeValueDate, // 起息日
						financeDueDate: res.financeDueDate, // 融资到期日
						applicantCompanyName: res.applicantCompanyName, // 融资企业
						zdwRegNo: res.zdwRegNo, // 中登网登记编号
						zdwAmendNo: res.zdwAmendNo, // 变更中登网登记编号
						createTime: res.createTime, // 创建日期
						interestPayWay: res.interestPayWay, // 付息方式
						auditReason: res.auditReason, // 审核意见
						factoringRate: res.factoringRate, // 融资年华利率
						dateDiff: dateDiff, //融资天数
						ratenow: _ratenow, // 试算利息
						extralMount: _extralMount, // 试算余额
					})
					let diffDay: number = 0
					// 当融资申请被确认前，计算费用的时间是从创建融资的时间(createTime) ---> 兑付到期日
					// 当融资申请被确认后，计算费用的时间是从起息日(financeValueDate) ---> 兑付到期日
					if (res.status === 'CONFIRM') {
						diffDay = timeCalculate(res.financeValueDate, res.financeDueDate, 'day') || 0
					} else {
						diffDay = timeCalculate(res.createTime, res.financeDueDate, 'day') || 0
					}
					// 设置融资费用表格中的数据
					setCostDataSource({
						factoringRate: res.factoringRate, // 保理手续利率
						factoringCost: (Number(res.factoringRate) * Number(res.inputFinanceAmountInYuan)) / 100,
						interestRate: res.interestRate, // 融资利率
						interestCost: (Number(res.inputFinanceAmountInYuan) * Number(res.interestRate) * diffDay) / 100 / 360,
						serviceFeeRate: res.serviceFeeRate, // 平台服务费率
						serviceFeeCost: (Number(res.inputFinanceAmountInYuan) * Number(res.serviceFeeRate) * diffDay) / 100 / 360,
						platformFeeModel: res.platformFeeModel ? platformFeeModelEnum.filter(item => res.platformFeeModel === item.value)?.[0]['label'] : null,
					})
					// 设置融资融信表格中的数据
					setFinanceCouDataSource(res.couList)
					// 设置发票表格中的数据
					setInvoiceDataSource(res.invoices)
					// 设置融资合同
					// debugger
					setFinanceContractInfo(res.protocolList)
					//设置平台协议
					setPlatformProtocol(res?.platformFeePdfUrl)
					setRefundInfo(res?.refundInfo)
					// 附件
					const { attachment: attachmentStr } = res
					setAttachmentInfoList(JSON.parse(attachmentStr || '[]'))
				},
				reason => {
					setTimeout(hide, 0)
					console.log('reason: ', reason)
				}
			)
	}
	// 从协议数组中获取对应协议的url
	const getProtocolUrlByType = (arr, type) => {
		// debugger
		const files = arr.filter(element => {
			return element.type === type
		})
		return files[0]?.url || null
	}
	const getContructByType = (arr, type) => {
		// debugger
		const files = arr.filter(element => {
			return element.protocolType === type
		})
		return files[0]?.protocolMinioUrl || null
	}
	// 显示融资合同  pledge  pledge_finance
	const showFinanceContract = async type => {
		const fileUrl = getContructByType(financeContractInfo, type)
		setPath(fileUrl)
		setPdfVisible(true)
	}

	// 点击查看文件(融信流转单、付款承诺函)
	const clickSeeFile = (record, type) => {
		let fileUrl = ''
		if (type === 'paymentUndertaking') {
			fileUrl = getProtocolUrlByType(JSON.parse(record.commitFile).protocolList, 'commitment')
		} else if (type === 'couFlowSheet') {
			fileUrl = getProtocolUrlByType(JSON.parse(record.transferFile).protocolList, 'transfer')
		}
		setPath(fileUrl)
		setPdfVisible(true)
	}
	// 单据一键下载
	const clickDownloadBill = async () => {
		setBillLoading(true)
		let couUuids: any = []
		financeCouDataSource.forEach((cou, index) => {
			couUuids.push({
				originalUuid: cou.originalUuid,
				couUuid: cou.uuid,
				couNo: cou.couNo,
			})
		})
		const fileUrl =
			getProtocolUrlByType(financeContractInfo.protocolList, 'financeContract') || getProtocolUrlByType(financeContractInfo.protocolList, 'financeContractToC')
		const params: any = {
			fileUrl,
			couUuids,
		}
		const res: any = await financeModuleApi.billDownload(params).catch(err => {
			console.log('err', err)
		})
		if (res) {
			const fileblob = b64toBlob(res)
			FileSaver.saveAs(fileblob, `${baseInfoDataSource?.applicationNumber}.zip`)
		}
		setBillLoading(false)
	}
	// 点击下载文件
	const clickDownloadFile = (record: any, type: string) => {
		setFinanceCouAllTableLoading(true)
		if (type === 'flowContract') {
			financeModuleApi
				.allContractDownload({ couUuid: record.uuid })
				.then(
					value => {
						const fileblob = b64toBlob(value)
						FileSaver.saveAs(fileblob, `${record.couNo}_合同.zip`)
						setFinanceCouAllTableLoading(false)
					},
					() => {
						setFinanceCouAllTableLoading(false)
					}
				)
				.catch(err => setFinanceCouAllTableLoading(false))
		} else if (type === 'couFlowSheetAll') {
			financeModuleApi
				.allTransferDownload({ couUuid: record.uuid })
				.then(
					value => {
						const fileblob = b64toBlob(value)
						FileSaver.saveAs(fileblob, `${record.couNo}_流转单（全路径）.zip`)
						setFinanceCouAllTableLoading(false)
					},
					() => {
						setFinanceCouAllTableLoading(false)
					}
				)
				.catch(err => setFinanceCouAllTableLoading(false))
		} else if (type === 'couFlowTable') {
			const fileUrl = JSON.parse(record.transferFile).transferPath
			// 兼容老数据，老数据的融信浏转路径表是svg、png
			const extName = fileUrl.split('.').pop().toLowerCase()
			commonModuleApi.downloadFile({ fileUrl }).then(
				url => {
					const fileblob = b64toBlob(url)
					FileSaver.saveAs(fileblob, `${record.couNo}_流转路径表.${extName}`)
					setFinanceCouAllTableLoading(false)
				},
				() => {
					setFinanceCouAllTableLoading(false)
				}
			)
		}
	}
	//base64转Blob方法
	const b64toBlob = (b64Data, contentType?: '') => {
		const sliceSize = 512
		const byteCharacters = atob(b64Data)
		const byteArrays: any[] = []

		for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
			const slice = byteCharacters.slice(offset, offset + sliceSize)
			const byteNumbers = new Array(slice.length)
			for (let i = 0; i < slice.length; i++) {
				byteNumbers[i] = slice.charCodeAt(i)
			}
			const byteArray = new Uint8Array(byteNumbers)
			byteArrays.push(byteArray)
		}
		return new Blob(byteArrays, { type: contentType })
	}
	// 点击合同编号
	const clickInvonceCode = (record, type, title) => {
		console.log('record', record)
		setDetailVisible(true)
		setInvoiceData(record)
	}
	const getColumns = type => {
		const columnsDic = {
			invoiceNumber: {
				dataIndex: 'invoiceNumber',
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text} className="link">
							<span
								onClick={() => {
									clickInvonceCode(record, 'invoice', '查看发票')
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			checkState: getInvoicCheckStatusCols().checkState,
			discount: {
				render: (text: any, record: any) => {
					return text * 100 + '%'
				},
			},
			transferPracticalAmountInYuan: {
				render: (text: any, record: any) => {
					return renderAmount(record.transferPracticalAmountInYuan)
				},
			},
			// 融信流转单
			couFlowSheet: {
				dataIndex: 'couFlowSheet',
				render: (text: any, record: any) => {
					return (
						<span
							className="link"
							onClick={() => {
								clickSeeFile(record, 'couFlowSheet')
							}}
						>
							查看
						</span>
					)
				},
			},
			// 付款承诺函
			paymentUndertaking: {
				dataIndex: 'paymentUndertaking',
				render: (text: any, record: any) => {
					return (
						<span
							className="link"
							onClick={() => {
								clickSeeFile(record, 'paymentUndertaking')
							}}
						>
							查看
						</span>
					)
				},
			},
			// 流转合同
			circulationContract: {
				dataIndex: 'circulationContract',
				render: (text: any, record: any) => {
					return (
						<span
							className="link"
							onClick={() => {
								clickDownloadFile(record, 'flowContract')
							}}
						>
							下载
						</span>
					)
				},
			},
			// 融信浏转路径表
			couFlowTable: {
				dataIndex: 'couFlowTable',
				render: (text: any, record: any) => {
					return (
						<span
							className="link"
							onClick={() => {
								clickDownloadFile(record, 'couFlowTable')
							}}
						>
							下载
						</span>
					)
				},
			},
			// 融信流转单（全路径）
			couFlowSheetAll: {
				title: <Tooltip title="融信流转单（全路径）">融信流转单（全路径）</Tooltip>,
				dataIndex: 'couFlowSheetAll',
				render: (text: any, record: any) => {
					return (
						<span
							className="link"
							onClick={() => {
								clickDownloadFile(record, 'couFlowSheetAll')
							}}
						>
							下载
						</span>
					)
				},
			},

			// cost: {
			// 	dataIndex: 'cost',
			// 	title: (
			// 		<div>
			// 			费用(￥)&nbsp;&nbsp;
			// 			<Tooltip placement="top" title="以创建日期作为起息日估算，实际费用以金融机构实际放款日计算。">
			// 				<ExclamationCircleOutlined className="costConfirm" style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer' }} />
			// 			</Tooltip>
			// 		</div>
			// 	),
			// },
			onChainCertificate: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => showCertificate(record)}>
							查看证书
						</span>
					)
				},
			},
		}
		return evenlyTableColunms(getColumnsByPageName(type, columnsDic))
	}

	const showCertificate = record => {
		commonModuleApi
			.queryCouProof({ couNo: record.couNo })
			.then(data => {
				if (data && data.status === 'DONE') {
					setOnChainData(data)
					setChainInfoModalVisible(true)
				} else {
					message.warning('证书未生成，完成审批且上链后生成证书')
				}
			})
			.catch(err => {
				console.log(err)
			})
	}

	const getCheckTaskListByFinanceUuid = async (financeUuid: string) => {
		let res = await financeCheckManageModuleApi.getCheckTaskListByFinanceUuid({ financeUuid }).catch(e => {
			console.log(e)
		})
		if (res) {
			setZdCheckList(res)
		}
	}

	const zDColumns = [
		getTabTit(['查重任务编号', 'taskNo'], text => {
			if (text === undefined || text === null) {
				return invalidVal
			}
			return <Tooltip title={text}>{text}</Tooltip>
		}),
		getTabTit(['任务创建时间', 'createTime', 160, true], text => {
			if ([undefined, null].includes(text)) {
				return invalidVal
			}
			return stampToTime(text, 4)
		}),
		getTabTit(
			[
				<div>
					查重进度&nbsp;&nbsp;
					<Tooltip placement="top" title="查重进度更新存在延迟，请刷新页面查看最新的查重进度">
						<ExclamationCircleOutlined className="costConfirm" style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer' }} />
					</Tooltip>
				</div>,
				'status',
				null,
				true,
			],
			text => {
				if ([null, undefined].includes(text)) {
					return invalidVal
				}
				return duplicateCheckProgressMap[text]?.value
			}
		),
		getTabTit(['查重结果', 'riskStatus', null, true], text => {
			if (DuplicateCheck.Risks === text && duplicateCheckMap[text]?.value) {
				return <span className="red">{duplicateCheckMap[text]?.value}</span>
			}
			return duplicateCheckMap[text]?.value || getNotNullVal(text)
		}),
		getTabTit(['命中发票条数', 'highRiskCount'], (text, record) => {
			let { status, riskStatus } = record
			if ([DuplicateCheckProgress.Fail, DuplicateCheckProgress.OnGoing, undefined, null].includes(status)) {
				return invalidVal
			}
			if (DuplicateCheck.Risks === riskStatus) {
				return <span className="red">{text}</span>
			}
			return text
		}),
		getTabTit(
			[
				<>
					<div>未命中发票</div>
					<div>有异议登记条数</div>
				</>,
				'missHitObjectionCount',
			],
			(text, record) => {
				let { status } = record
				if ([DuplicateCheckProgress.Fail, DuplicateCheckProgress.OnGoing].includes(status)) {
					return invalidVal
				}
				return getNotNullVal(text)
			}
		),
		getTabTit(
			[
				<>
					<div>未命中发票</div>
					<div>无异议登记条数</div>
				</>,
				'missHitNotObjectionCount',
			],
			(text, record) => {
				let { status } = record
				if ([DuplicateCheckProgress.Fail, DuplicateCheckProgress.OnGoing].includes(status)) {
					return invalidVal
				}
				return getNotNullVal(text)
			}
		),
		getTabTit(['操作', 'status'], (text, record) => {
			if ([DuplicateCheckProgress.Completed].includes(text)) {
				return (
					<span
						className="link"
						onClick={() => {
							let url = ''
							if (hasAuth('bl_financeCheckManage:pfiFinanceCheckDetail:detail')) {
								url = '/content/financeCheckManage/pfiFinanceCheckDetail'
							} else if (hasAuth('bl_financeCheckManage:fiFinanceCheckDetail:detail')) {
								url = '/content/financeCheckManage/fiFinanceCheckDetail'
							}
							//跳转至查重任务详情
							history.push(url, {
								taskNo: record?.taskNo,
							})
						}}
					>
						详情
					</span>
				)
			}
			return invalidVal
		}),
	]
	const auditFinance = async (title: string, type: string) => {
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let companyUuid: string = ext.user && ext.user.company ? ext.user.company.uuid : ''
		let isLastAuditor: any = await commonModuleApi
			.isLastFlowPerson({
				companyUuid,
				role: getCurrentRole()?.roleCode || '',
				taskId: auditData.auditFinanceData.id,
			})
			.catch(err => {
				console.log('err', err)
			})
		setIsLastAuditor(isLastAuditor)
		setAuditVisible(true)
		setAuditType(type)
		setAuditTitle(title)
	}
	const submitData = async (values: any) => {
		setConfirmLoading(true)
		let param = {}
		let jsonParamStr: any = {}

		if (auditType !== 'agree') {
			jsonParamStr.loanResult = 'REJECT_LOAN'
			jsonParamStr.applicationNumber = auditData.applicationNumber
		}

		param = {
			businessKey: auditData.auditFinanceData.businessKey,
			taskId: auditData.auditFinanceData.id,
			jsonParamStr: JSON.stringify(jsonParamStr),
			outcome: auditType,
			comment: values.comment,
		}
		commonModuleApi
			.doTask(param)
			.then(item => {
				setConfirmLoading(false)
				if (item.returnCode === 17000) {
					message.success('处理成功')
					navigate('/content/couFinance/sAuditFinance')
				} else {
					if (item.returnCode === 30015) {
						message.error('重复操作，该项融资已完成处理')
					} else {
						// 删除关键字（银行接口异常）
						let returnDesc = item.returnDesc.replace('银行接口异常', '')
						message.error(returnDesc)
					}
				}
				setAuditVisible(false)
			})
			.catch(err => {
				setConfirmLoading(false)
				setAuditVisible(false)
			})
	}

	return (
		<Box>
			<div className="card-wrapper">
				{/* 融资信息 */}
				<div className="financeInfo">
					<div className="boxContentWrap">
						<SmallTitle text="融资信息" />
						<div className="boxContent">
							<div className="baseInfo">
								<div className="baseInfoHeader" style={{ marginBottom: '10px' }}>
									<div className="title">
										<span>基本信息</span>
									</div>
									{/* {hasAuth('bl_couFinance:financeDetail:detailsViewResponse') && (
										<div className="message" style={{ marginLeft: '50px' }}>
											<Button
												onClick={() => {
													if (!apiMessage) {
														message.error('无报文信息')
													} else {
														setMessageVisible(true)
													}
												}}
												type="primary"
											>
												查看接口报文
											</Button>
										</div>
									)} */}

									{/* {hasAuth('bl_couFinance:auditFinance:detailsBillDownload') && (
										<div className="bill" style={{ marginLeft: '50px' }}>
											<Button
												onClick={() => {
													clickDownloadBill()
												}}
												type="primary"
												loading={billLoading}
											>
												单据一键下载
											</Button>
										</div>
									)} */}
								</div>
								<BaseInfo data={baseInfoDataSource}></BaseInfo>
							</div>
						</div>
					</div>
					{/* display: isShowFinanceRate ? 'block' : 'none' */}
					{/* <div className="financExpenses" style={{ marginBottom: '40px' }}>
						<div className="financExpensesTitle" style={{ marginBottom: '10px' }}>
							融资费用
						</div>
						<Table dataSource={financeCostData} columns={getColumns('financeCost')} pagination={false} />
					</div> */}
					{/* <div style={{ marginBottom: '40px', display: isFI && zdCheckList.length > 0 ? 'block' : 'none' }}>
						<div style={{ marginBottom: '10px' }}>中登查重信息</div>
						<Table dataSource={zdCheckList} columns={zDColumns} pagination={false} />
					</div> */}
					<div className="boxContentWrap">
						<SmallTitle text="融资费用" />
						<div className="boxContent">
							<CostInfo data={baseInfoDataSource}></CostInfo>
						</div>
					</div>
					{baseInfoDataSource?.status === 'REPAID' && refundInfo ? (
						<div className="boxContentWrap">
							<SmallTitle text="还款信息" />
							<div className="boxContent">
								<RepaymentInfo data={refundInfo}></RepaymentInfo>
							</div>
						</div>
					) : (
						''
					)}
				</div>
				{/* 融资协议 */}
				{/* <div className="financeAgree">
					<SmallTitle text="融资协议" />
					<div className="financePdf" style={{ marginBottom: '40px', backgroundColor: 'rgb(240, 240, 240)', color: '#49a9ee', padding: '20px' }}>
						<span style={{ cursor: 'pointer' }} onClick={() => showFinanceContract('pledge')}>
							借款合同.pdf
						</span>
						<div style={{ cursor: 'pointer', marginTop: '8px' }} onClick={() => showFinanceContract('pledgeFinance')}>
							质押合同.pdf
						</div>
					</div>
				</div> */}
				{/* 融资融信 */}
				<div className="financeCou">
					<div className="boxContentWrap">
						<SmallTitle text="融资融信" />
						<div className="boxContent">
							<Table
								loading={financeCouAllTableLoading}
								rowKey="id"
								dataSource={financeCouDataSource}
								columns={getColumns(columnsType)}
								pagination={false}
								style={{ marginBottom: '40px' }}
							/>
						</div>
					</div>
				</div>
				{/* 融资发票 */}
				<div className="financeInvoice">
					<div className="boxContentWrap">
						<SmallTitle text="发票" />
						<div className="boxContent">
							<Table rowKey="id" dataSource={invoiceDataSource} columns={getColumns('financeInvoice')} pagination={false} style={{ marginBottom: '40px' }} />
						</div>
					</div>
				</div>
				<div className="boxContentWrap">
					<AttachmentTable ref={attachmentTable} isEdit={false} initialValues={attachmentInfoList} />
				</div>
				{isFromAudit && (
					<div className="options_btn">
						<Button type="primary" danger onClick={() => auditFinance('确定拒绝？', 'reject')} style={{ marginRight: 10 }}>
							拒绝
						</Button>
						<Button type="primary" onClick={() => auditFinance('确定通过？', 'agree')}>
							通过
						</Button>
					</div>
				)}
			</div>
			<PDFViewer title="" visible={pdfVisible} pdfUrl={path} onCancel={() => setPdfVisible(false)} />
			<MessageModal visible={messageVisible} data={apiMessage} onCancel={() => setMessageVisible(false)} />
			<ChainInfoModal visible={chainInfoModalVisible} onCancel={() => setChainInfoModalVisible(false)} chainData={onChainData} />
			<DetailModal title="查看发票" pageName="invoice" onCancel={() => setDetailVisible(false)} visible={detailVisible} dataSource={invoiceData} />
			<AuditModal
				title={auditTitle}
				visible={auditVisible}
				onCancel={() => setAuditVisible(false)}
				onSubmit={submitData}
				confirmLoading={confirmLoading}
				type={auditType}
			/>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.boxContent {
		border: 1px solid #e9e9e9;
		// border-radius: 4px 4px 0px 0px;
		border-top: 0;
		padding: 20px 10px;
	}
	.boxContentWrap {
		margin-bottom: 10px;
		position: relative;
		.invoiceBox {
			position: absolute;
			top: 10px;
			left: 142px;
			color: #1f7bf4;
			font-weight: bold;
		}
	}
	.anticon-exclamation-circle:hover {
		color: red;
	}
	.financExpenses {
		position: relative;
	}
	.baseInfoHeader {
		display: flex;
		align-items: center;
	}
	.options_btn {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 40px;
		span {
			padding: 0 10px;
		}
	}
`

export default observer(financeDetail)
