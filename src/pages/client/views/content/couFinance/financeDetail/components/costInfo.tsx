import React from 'react'
import { Descriptions, Tooltip } from 'antd'
import { pledgeFinanceStatus } from '../../financeCommon'
import { numberToThousands, stampToTime } from '@src/utils/timeFilter'
import { renderTooltip } from '@src/pages/client/config/TableColumnsRender'
import { ExclamationCircleOutlined } from '@ant-design/icons'

const costInfo = (props: { data: any }) => {
	const { data } = props
	return (
		<div style={{ marginBottom: '20px' }}>
			<Descriptions bordered column={2}>
				<Descriptions.Item label="实际放款金额">
					{data.inputFinanceAmountInYuan ? `￥${numberToThousands(data.inputFinanceAmountInYuan)}` : '--'}
				</Descriptions.Item>
				<Descriptions.Item label="融资利率（年化）">
					{data.factoringRate ? (Number(data.factoringRate) * 100).toFixed(2) + '%' || '--' : '--'}
				</Descriptions.Item>
				<Descriptions.Item label="融资天数">{data.dateDiff ? data.dateDiff + '天' : '--'}</Descriptions.Item>
				<Descriptions.Item
					label={
						<>
							<Tooltip placement="top" title="实际利息以还款时实际的借款时长为准">
								利息（试算）
								<ExclamationCircleOutlined style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer', marginLeft: '3px', verticalAlign: 'middle' }} />
							</Tooltip>
						</>
					}
				>
					{data.ratenow ? <>{`￥${numberToThousands(data.ratenow) || '--'}`}</> : '--'}
				</Descriptions.Item>
				<Descriptions.Item
					label={
						<Tooltip placement="top" title="融信到期偿还银行借款后,本企业可以收到的剩余金额,以实际还款扣除的利息为准">
							清算余额（试算）
							<ExclamationCircleOutlined style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer', marginLeft: '3px', verticalAlign: 'middle' }} />
						</Tooltip>
					}
				>
					<>{`￥${numberToThousands(data.extralMount)}`}</>
				</Descriptions.Item>
			</Descriptions>
		</div>
	)
}

export default costInfo
