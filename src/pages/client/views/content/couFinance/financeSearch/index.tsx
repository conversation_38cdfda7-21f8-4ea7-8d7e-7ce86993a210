import React, { useEffect, useState } from 'react'
import { But<PERSON>, message, Tooltip } from 'antd'
import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import { financeModuleApi, commonModuleApi } from '@src/pages/client/api'
import { stampToTime } from '@src/utils/timeFilter'
import OperatingArea from '@src/globalComponents/OperatingArea'
import SearchBar from '@clientComponents/SearchBar'
import FlowChartModal from '@src/globalComponents/FlowChartModal'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { pledgeFinanceStatusList } from '../financeCommon'
import { timeToStamp } from '@utils/timeFilter'
import { getFlowElementStatus, addFIStatus } from '@utils/dataClean'
import styled from 'styled-components'
import { history } from '@src/utils/router'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import { hasAuth } from '@src/pages/client/biz/bizIndex'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import { useLocation } from 'react-router-dom'

function searchPage() {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [optionData, setOptionData] = useState<any>({})
	const [loading, setLoading] = useState(false)
	const [exportLoading, setExportLoading] = React.useState(false)

	const [flowChartVisible, setFlowChartVisible] = useState(false) // 审核流传图的弹窗
	const [flowChartData, setFlowChartData] = useState<any[]>([]) // 流转图数据
	const [locationVisiable, setLocationVisiable] = useState(true)

	const location = useLocation()
	const state = location?.state as any
	const statusList = state?.statusList
	const [searchParams, setSearchParams] = useState<any>({ statusList })
	const initValues = { statusList, needClear: true }

	// useEffect(() => {
	// 	getFinanceCreditList()
	// }, [])

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	// const getFinanceCreditList = () => {
	// setOptionData({ statusList: pledgeFinanceStatusList })
	// financeModuleApi
	// 	.getFinanceCreditList()
	// 	.then(res => {
	// 		console.log(res)
	// 		if (res) {
	// 			setOptionData({ statusList: pledgeFinanceStatusList, financeList: res })
	// 		}
	// 	})
	// 	.catch(err => console.log(err))
	// }

	// const getLocationParam = () => {
	// 	if (locationVisiable && state?.statusList && Object.prototype.toString.call(state?.statusList) == '[object Array]') {
	// 		const paramsObj = {
	// 			statusList: state?.statusList,
	// 		}
	// 		return paramsObj
	// 	}
	// 	return {}
	// }

	// 得到融资申请列表
	function getList() {
		setLoading(true)
		// const params = getLocationParam()
		financeModuleApi
			.getFinanceList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...searchParams,
				// ...params,
			})
			.then(
				(res: any) => {
					pagination.total = res['total']
					if (res.list) {
						setDataSource([...res.list])
					} else {
						setDataSource([])
					}
					setLoading(false)

					// if (locationVisiable) {
					// 	const values = { statusList: params['statusList'][0] }
					// 	setInitValues(values)
					// }
					// setLocationVisiable(false)
				},
				err => {
					setLoading(false)
					pagination.total = 0
					setDataSource([])
				}
			)
	}
	// 导出融资申请列表
	const handleExportClick = async () => {
		setExportLoading(true)
		let serverTime = await commonModuleApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`

		financeModuleApi
			.exportFinanceList({
				...searchParams,
				pageNum: 1,
				pageSize: 2147483646,
			})
			.then((response: any) => {
				let type = 'application/vnd.ms-excel'
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}
	// 获取融信流转的流程图数据
	const getFinanceFlowChartData = record => {
		let businessKey = record.applicationNumber
		commonModuleApi.getTransferChartData({ businessKey }).then(
			res => {
				if (res.returnCode == 17000) {
					setFlowChartVisible(true)
					let result = getFlowElementStatus(res.data)
					// 江阴质押融资
					if (res.data.processDefKey === 'pledgeFinanceFinal') {
						setFlowChartData(result)
					}
					// 直连模式（江西）和非直连模式（润楼）
					else if (res.data.processDefKey === 'finance') {
						let resultAddFI = addFIStatus(result, record)
						setFlowChartData(resultAddFI)
					} else if (res.data.processDefKey === 'rlFinance') {
						setFlowChartData(result)
					}
				} else {
					message.error('此项数据暂不支持查看流程图')
				}
			},
			reason => {
				console.log('reason', reason)
			}
		)
	}
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	const onOperCallback = (str: string, data: any) => {
		//根据按钮权限来判断跳转至对应的详情页面
		let url = ''
		if (hasAuth('bl_couFinance:financeDetail:cDetails')) {
			url = '/content/couFinance/cFinanceDetail'
		} else if (hasAuth('bl_couFinance:financeDetail:sDetails')) {
			url = '/content/couFinance/sFinanceDetail'
		} else if (hasAuth('bl_couFinance:financeDetail:fiDetails')) {
			url = '/content/couFinance/fiFinanceDetail'
		}
		// 存储融资申请详情，防止刷新数据丢失
		url &&
			history.push(url, {
				uuidOrNumber: data.uuid,
			})
	}
	const getColumns = () => {
		const pageName = 'searchCouFinance'
		const columnsDic = {
			applicationNumber: {
				unit: 20,
				ellipsis: true,
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									onOperCallback('applicationNumber', record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			financeDueDate: { unit: 12 },
			createTime: { unit: 12 },
			operation: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => getFinanceFlowChartData(record)}>
							审核流程
						</span>
					)
				},
			},
		}
		const colns = getColumnsByPageName(pageName, columnsDic)
		const evenlyColns = evenlyTableColunms(colns)
		return evenlyColns
	}
	const onSubmit = (params: any) => {
		const statusList = params['statusList']
			? Object.prototype.toString.call(params['statusList']) === '[object Array]'
				? params['statusList']
				: params['statusList'].includes(',')
				? params['statusList'].split(',')
				: [params['statusList']]
			: null
		const fiUuid = params['financeList'] ? params['financeList'] : ''
		const applicationNumber = params['applicationNumber'] ? params['applicationNumber'] : ''
		let startDate, endDate
		if (params && params.createDate && params.createDate.length === 2) {
			endDate = timeToStamp(params.createDate[1], 'end')
			startDate = params.createDate[0]
		}
		let fiUuids: any = []
		if (fiUuid) {
			fiUuids = [fiUuid]
		}
		const paramsObj = {
			statusList,
			applicationNumber,
			startDate,
			endDate,
			fiUuids,
		}
		pagination.current = 1
		setSearchParams(paramsObj)
	}
	const onClear = () => {
		setSearchParams({})
	}
	function handlePageChange(current: number) {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const handleCreateClick = () => {
		if (hasAuth('bl_couFinance:sBuildOperatorCreateFinance:create')) {
			history.push('/content/couFinance/sOperatorCreateFinance')
		}
		// else if (hasAuth('bl_couFinance:sBuildOperatorCreateFinance:create')) {
		// 	history.push('/content/couFinance/sBuildOperatorCreateFinance')
		// } else if ('bl_couFinance:sTeamOperatorCreateFinance:create') {
		// 	history.push('/content/couFinance/sTeamOperatorCreateFinance')
		// }
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<div style={{ margin: '0 0 10px 15px' }}>
							{hasAuth('bl_couFinance:sBuildOperatorCreateFinance:create') && (
								<Button onClick={handleCreateClick} type="primary" style={{ display: 'inline-block' }}>
									去融资
								</Button>
							)}
							{/* <Button onClick={handleExportClick} type="primary" style={{ marginLeft: '15px' }} loading={exportLoading}>
								导出
							</Button> */}
						</div>
						<SearchBar
							optionData={{ statusList: pledgeFinanceStatusList }}
							onSubmit={onSubmit}
							onClear={onClear}
							pageName="searchFinance"
							initValues={initValues}
						/>
					</OperatingArea>
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						rowKey="id"
						// scroll={{ x: 1300 }}
					/>
				</div>
				<FlowChartModal
					type="finance"
					data={flowChartData}
					visible={flowChartVisible}
					onCancel={() => {
						setFlowChartVisible(false)
					}}
				></FlowChartModal>
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
`

export default searchPage
