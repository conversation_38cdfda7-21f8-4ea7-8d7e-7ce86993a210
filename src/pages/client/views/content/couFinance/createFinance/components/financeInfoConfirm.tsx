import React, { useState, useEffect } from 'react'
import { Descriptions, Modal, Table, Tooltip } from 'antd'
import { InfoCircleOutlined } from '@ant-design/icons'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { commonModuleApi, invoicingManageModuleApi } from '@src/pages/client/api'
import { stampToTime, timeCalculate } from '@src/utils/timeFilter'
import { formatNumber } from '@utils/format'
import styled from 'styled-components'
import moment from 'moment'
import SmallTitle from '@src/globalComponents/SmallTitle'
import { InvoiceHeaderInfoIP } from '@src/@types/invoice'
import { invalidVal } from '@src/pages/client/biz/bizIndex'
import WxDetailDisplay from '@src/globalComponents/wxDetailDisplay/wxDetailDisplay'
import { platformFeeModelEnum } from '@src/globalConfig/codeMaps'
interface Props {
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 融信数据
	 */
	couData?: any[]
	/**
	 * @description 费率对象
	 */
	financeInfoObj: any
	/**
	 *  @description 实际填写的申请金额
	 */
	actualFinanceSumAmountInYuan: any
	/**
	 * @description 授信对象信息
	 */

	precheckData: any
	/**
	 * @description 提交确定操作
	 */
	onOk: () => void
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
}

const FinanceInfoConfirm = (props: Props) => {
	const { visible, financeInfoObj, onOk, onCancel, couData } = props
	const fields = [
		{
			key: 'actualFinanceSumAmountInYuan',
			note: '融资申请金额:', //(融资申请金额为试算*)
			value: '',
			span: 12,
			format: text => {
				return '￥' + (text ? formatNumber.addThousandsSign(text) : '')
			},
		},
		{
			key: 'copyactualFinanceSumAmountInYuan',
			note: '实际放款金额:', //(融资申请金额为试算*)
			value: '',
			span: 12,
			format: text => {
				return '￥' + (text ? formatNumber.addThousandsSign(text) : '')
			},
		},
		{
			key: 'transferCouSumAmountInYuan',
			note: '质押融信金额:',
			value: '',
			span: 12,
			format: text => {
				return '￥' + (text ? formatNumber.addThousandsSign(text) : '')
			},
		},
		// {
		// 	key: 'financeDiscount',
		// 	note: '质押率:',
		// 	value: '',
		// 	span: 12,
		// 	format: text => {
		// 		return accMul(Number(couData?.[0]?.discount || 0), 100).toFixed(2) + '%' || '--'
		// 	},
		// },
		{
			key: 'actualDiscount',
			note: '实际质押率:',
			value: '',
			span: 12,
			format: text => {
				return (Number(text) * 100).toFixed(2) + '%' || '--'
			},
		},
		{
			key: 'creditDueDate',
			note: '融资到期日:',
			value: '',
			span: 12,
			format: text => {
				return text
			},
		},
		{
			key: 'creaditDays',
			note: '融资天数:',
			value: '',
			span: 12,
			format: text => {
				return text
			},
		},
		{
			key: 'interestRate',
			note: '融资利率（年化）:',
			value: '',
			span: 12,
			format: text => {
				return (text * 100).toFixed(2) + '%'
			},
		},
		{
			key: 'ratenow',
			note: '利息（试算）：',
			value: '',
			span: 12,
			format: text => {
				return '￥' + text
			},
		},
		{
			key: 'extralMount',
			note: '清算余额（试算）：',
			value: '',
			span: 12,
			format: text => {
				return '￥' + text
			},
		},
	]
	const [financeInfoDataSource, setFinanceInfoDataSource] = useState<any[]>([]) // 表格中的融资信息数据
	const [loading, setLoading] = useState(false) // 金融机构uuid、pubkey，关联企业uuid
	const [invoiceHeaderInfo, setInvoiceHeaderInfo] = useState<InvoiceHeaderInfoIP | null>()
	const [baseInfoField, setBaseInfoField] = useState(fields)

	useEffect(() => {
		// 得倒系统时间和多种费率
		if (visible) {
			getSyncTimeAndVariousRates()
			getInvoiceHeaderInfo()
		}
		console.log(financeInfoDataSource)
	}, [visible])

	/**
	 * 获取发票抬头信息
	 */
	const getInvoiceHeaderInfo = async () => {
		let res = await invoicingManageModuleApi.queryInvoicingInfo().catch(err => {
			console.log(err)
		})
		if (res) {
			setInvoiceHeaderInfo(res)
		}
	}

	// 获取系统时间和费率
	const getSyncTimeAndVariousRates = () => {
		setLoading(true)
		commonModuleApi.syncTime().then(
			res => {
				setLoading(false)
				if (res) {
					// 存储兑付到期日于创建融资日期的天数差值
					// financeDiscount 授信比率
					//actualFinanceSumAmountInYuan实际融资金额
					console.log('financeInfoObj.actualFinanceSumAmountInYuan', financeInfoObj.actualFinanceSumAmountInYuan)
					const beginTime = moment(res).valueOf()
					const endTime = moment(financeInfoObj.financeDueDate).valueOf()
					const dayDiff = Number(timeCalculate(beginTime, endTime, 'day') || 0)
					const platformFeeModel = financeInfoObj.platformFeeModel
						? platformFeeModelEnum.filter(item => financeInfoObj.platformFeeModel === item.value)[0]['label']
						: null
					setFinanceInfoDataSource([
						{
							key: '1',
							project: '融资利息',
							rateType: '年化',
							rate: props?.precheckData?.credit?.financeDiscount || '--',
							cost: (dayDiff * Number(props?.precheckData?.credit?.financeDiscount || 0) * Number(financeInfoObj.actualFinanceSumAmountInYuan)) / 100 / 360,
							platformFeeModel: platformFeeModel ? '银行扣取' : '--',
						},
						// {
						// 	key: '2',
						// 	project: '保理手续费',
						// 	rateType: '单笔',
						// 	rate: financeInfoObj.factoringRate,
						// 	cost: (Number(financeInfoObj.factoringRate) * Number(financeInfoObj.actualFinanceSumAmountInYuan)) / 100,
						// 	platformFeeModel: platformFeeModel ? '银行扣取' : '--',
						// },
						// {
						// 	key: '3',
						// 	project: '平台服务费',
						// 	rateType: '年化',
						// 	rate: financeInfoObj.serviceFeeRate,
						// 	cost: (dayDiff * Number(financeInfoObj.serviceFeeRate) * Number(financeInfoObj.actualFinanceSumAmountInYuan)) / 100 / 360,
						// 	platformFeeModel: platformFeeModel || '--',
						// },
					])
					for (let field of fields) {
						field.value = financeInfoObj[field.key]
						if (field.format) {
							field.value = field.format(financeInfoObj[field.key])
						}
						// if (field.key === 'actualFinanceSumAmountInYuan') {
						// 	field.value = props.actualFinanceSumAmountInYuan
						// }
					}

					setBaseInfoField([...fields])
				}
			},
			reason => {
				setLoading(false)
				console.log('reason', reason)
			}
		)
	}
	return (
		<Modal
			open={visible}
			title="融资信息确认"
			width="900px"
			onCancel={() => {
				onCancel()
			}}
			onOk={() => {
				// 传递数据到创建融资申请页面
				onOk()
			}}
		>
			<Box>
				<SmallTitle text="基础信息"></SmallTitle>
				<WxDetailDisplay fields={baseInfoField} colNum={2}></WxDetailDisplay>
				<div style={{ marginTop: '10px', color: '#999', fontSize: '12px' }}>
					<div>
						<InfoCircleOutlined style={{ color: 'orange', marginRight: '5px' }} />
						说明
					</div>
					<div style={{ paddingLeft: '17px' }}>
						<div>1、利息:实际利息以还款时实际的借款时长为准;</div>
						<div>2、清算余额:融信到期偿还银行借款后,本企业可以收到的剩余金额,以实际还款扣除的利息为准;</div>
					</div>
				</div>
				{/* {financeInfoObj.interestPayWay === 'supplier' ? ( */}
				{/* <React.Fragment>
					<SmallTitle text="费用信息"></SmallTitle>
					<Table loading={loading} dataSource={financeInfoDataSource} columns={getColumnsByPageName('financeCost')} pagination={false} />
					<div style={{ margin: '10px 0', color: '#aaa' }}>费用以创建日期作为起息日估算，实际费用以金融机构实际放款日计算。</div>
				</React.Fragment> */}
				{/* ) : null} */}

				{/* <SmallTitle text="开票信息"></SmallTitle>
				<Descriptions bordered column={2}>
					<Descriptions.Item label="发票抬头">{invoiceHeaderInfo?.companyName || invalidVal}</Descriptions.Item>
					<Descriptions.Item label="纳税人识别码">{invoiceHeaderInfo?.socialCreditCode || invalidVal} </Descriptions.Item>
					<Descriptions.Item label="公司地址">{invoiceHeaderInfo?.address || invalidVal}</Descriptions.Item>
					<Descriptions.Item label="公司电话">{invoiceHeaderInfo?.phone || invalidVal}</Descriptions.Item>
					<Descriptions.Item label="开户银行">{invoiceHeaderInfo?.bankName || invalidVal}</Descriptions.Item>
					<Descriptions.Item label="开户银行账号">{invoiceHeaderInfo?.bankNo || invalidVal}</Descriptions.Item>
				</Descriptions> */}
				{/* <div style={{ margin: '10px 0', color: '#aaa' }}>若开票信息不正确，请联系企业管理员修改</div> */}
			</Box>
		</Modal>
	)
}
const Box = styled.div`
	padding: 10px 24px;
	.title {
		padding: 5px 10px;
		background: #ececec;
		border-radius: 3px;
		margin-bottom: 10px;
	}
	.selected {
	}
	.ant-descriptions-item-label {
		width: 140px;
	}
`

export default FinanceInfoConfirm
