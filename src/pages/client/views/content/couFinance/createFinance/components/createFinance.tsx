import React, { useState, useEffect, useRef } from 'react'
import { Button, message, Card, Table, Alert, Spin, Modal, Tooltip, Input, Popconfirm } from 'antd'
import { FileOutlined, ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons'
import { commonModuleApi, financeModuleApi, invoiceModuleApi, transferModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { numberToThousands, timeCalculate } from '@src/utils/timeFilter'
import { renderAmountInCent, renderAmount } from '@src/pages/client/config/TableColumnsRender'
import { history } from '@src/utils/router'
import { formatNumber } from '@src/utils/format'
import { WxModal, WxModalProps } from '@cnpm/wx-rc/components/index'
import SelectCou from '@src/pages/client/components/SelectCou'
import SelectInvoice from './selectInvoice'
import FinanceInfoConfirm from './financeInfoConfirm'
import SmallTitle from '@src/globalComponents/SmallTitle'
import Feedback from '@src/pages/client/components/feedback'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import moment from 'moment'
import styled from 'styled-components'
import AttachmentTable from '@src/globalComponents/EditTable/AttachmentTable'
import PDFViewer from '@src/globalComponents/PDFViewer'
import { couDataOnChain, getStorage, setStorage } from '@src/pages/client/biz/bizIndex'
import CompanyNameChangTip from '@src/pages/client/components/companyNameChangTip'
import CompanyCreatTip from '@src/pages/client/components/companyCreatTip'
import CompanyNotOpentTip from '@src/pages/client/components/companyNotOpentTip'
import NumericInput from '@src/pages/client/components/NumberInput'
// import IsFillInvoicingInfoTip from '@src/pages/client/components/isFillInvoicingInfoTip'
import { getInvoicCheckStatusCols, invoiceCheckFlagOptions, ViewReasonModal } from '@src/globalBiz/invoiceBizModule'
import NewUploadInvoice from '../../../couManage/couCreate/components/newCreateInvoice'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import { removeDuplicate } from '@src/utils/util'
const couBg = require('@src/assets/images/cou/cou_bg.png')
interface IProps {
	createFinanceFunc: Function
	isSBuildOperator?: boolean
	isSTeamOperator?: boolean
}
interface PollOptions {
	initialDelay?: number
	maxDelay?: number
	timeout?: number
}

let timerOut
let checkLists: any[] = []
let isPolling = false

function index({ createFinanceFunc, isSBuildOperator = false, isSTeamOperator = false }: IProps) {
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')
	const [modal, contextHolder] = Modal.useModal()
	const attachmentTable = React.useRef(null as any)
	const [checkBoxIsSelect, setCheckBoxIsSelect] = useState(true) //2024/3/30默认同意签署 根据prd
	const [selectCouVisible, setSelectCouVisible] = useState<boolean>(false)
	const [selectInvoiceVisible, setSelectInvoiceVisible] = useState<boolean>(false)
	const [financeInfoConfirmVisible, setFinanceInfoConfirmVisible] = useState<boolean>(false)
	const [feedbackVisible, setFeedbackVisible] = useState<boolean>(false)
	const [loading, setLoading] = useState(false)
	const [tableloading, setTableLoading] = useState(false)
	const [submitFinanceLoading, setSubmitFinanceLoading] = useState(false)
	const [financeConfig, setFinanceConfig] = useState<any>({}) // 金融服务配置
	const [isWorkday, setsWorkday] = useState(false) // 金融服务配置
	const [couDataSource, setCouDataSource] = useState<any[]>([]) // 融信明细表格中的数据
	const [invoiceDataSource, setInvoiceDataSource] = useState<any[]>([]) // 业务凭证发票表格中的数据
	const [hasNCheckFlag, sethasNCheckFlag] = useState<boolean>(false) // 是否包含为验真的发票
	const [totalPretaxAmount, setTotalPretaxAmount] = useState<number>(0) // 含税总金额
	const [applyAmountAmount, setApplyAmountAmount] = useState<number>(0) //可用金额总金额
	const [uuidOrNumber, setUuidOrNumber] = useState<string>('') // 融资申请详情的uuid或者number
	const [financeNo, setFinanceNo] = useState<string>('')

	//协议处理部分
	const [splitDetailList, setSplitDetailList] = useState<Array<any>>([])
	const [protocolVisible, setProtocolVisible] = useState(false)
	const [protocolTitle, setProtocolTitle] = useState('')
	const [protocolUrl, setProtocolUrl] = React.useState<string>('')
	const [financeInfoObj, setFinanceInfoObj] = useState<any>({}) // 费率对象
	const [assetSumInYuan, setAssetSumInYuan] = useState<number>(0) //融信总金额
	const [actualFinanceSumAmountInYuan, setActualFinanceSumAmountInYuan] = useState<number>(0) //实际融资总金额
	const [transferCouSumAmountInYuan, setTransferCouSumAmountInYuan] = useState<number>(0) //转让融信总金额
	const [btnTxts, setBtnTxts] = useState<any>(['完成'])
	const [feedbackInfoObj, setFeedbackInfoObj] = useState<any>({
		status: 'success',
		title: '提交成功',
	}) //融资金额
	const [canUseAmount, setCanUseAmount] = useState<number>(0) //供应商限额操作员的可用额度
	const [showSpin, setShowSpin] = useState<any>(false)
	const [showProtocolLoading, setShowProtocolLoading] = useState<boolean>(false)
	const [platformProtocolUrl, setPlatformProtocolUrl] = useState(null)
	const [pdfVisible, setPdfVisible] = useState<boolean>(false)
	const [precheckData, setPrecheckData] = useState<any>({})
	const [showCreateInvoinve, setShowCreateInvoinve] = useState(false)
	const [applyMaxMount, setApplyMaxMount] = useState<number>(0)
	const [financeInputIsRight, setFinanceInputIsRight] = useState(true)
	const [maxAmountEvery, setMaxAmountEvery] = useState({ amount: 0, isPlatformCfg: true })
	const [creditDueDate, setCreditDueDate] = useState<any>() // 融资到期日
	const [creaditDays, setCreaditDays] = useState(0)
	const [ratenow, setRatenow] = useState<any>() // 试算利息 2024/5/24 新增
	const [extralMount, setExtralMount] = useState<any>() // 余额试算利息 2024/5/24  新增
	const [invoiceBtnDisabled, setInvoiceBtnDisabled] = useState<boolean>(false)

	const [isInvoicecCheck, setIsInvoicecCheck] = useState(false) //实时验真开关是否打开
	const [invoicCheckResults, setInvoicCheckResults] = useState<any[]>([]) // 发票验真结果

	const [checkReportMsg, setCheckReportMsg] = React.useState<string>('')
	const [returnDesc, setReturnDesc] = React.useState<string>('')
	const [hasCreditReport, setHasCreditReport] = useState(false)
	const [isFirsthand, setIsFirsthand] = useState(false)
	const [couInputTotalAmountInCent, setCouInputTotalAmountInCent] = useState<number>(0)

	const viewResultModalProps: WxModalProps = {
		modalProps: {
			title: '提示',
			width: 520,
			bodyStyle: {
				padding: '80px 20px',
			},
			footer: null,
			onCancel: () => {
				showFinanceInfoConfirm()
			},
		},
	}

	const viewReasonModalRef = useRef(null)
	const resultModalRef = useRef(null)
	const handleGotoAddInvoice = () => {
		// window.open(`${window.location.origin}/#/content/couAssets/uploadInvoice`)
		setShowCreateInvoinve(true)
	}
	useEffect(() => {
		preCheck()
		getConfig()
		getCreditInvestigation()
		if (isSBuildOperator || isSTeamOperator) {
			// 调用接口，得到可用额度,测试sonar修改
			getCanUseAmount()
		}
		return () => {
			clearTimer()
		}
	}, [])
	useEffect(() => {
		if (invoicCheckResults && invoicCheckResults.length) {
			const mergedData = invoiceDataSource.reduce((acc, item) => {
				const foundItem = invoicCheckResults.find(f => f.id === item.id)
				acc.push({
					...item,
					...(foundItem || {}),
				})
				return acc
			}, [])
			setInvoiceDataSource(mergedData)
		}
	}, [invoicCheckResults])

	const getConfig = async () => {
		const res = await financeModuleApi.getWorkTime()
		console.log('res', res)
		let startTime = res.financeStartTime
		let entTime = res.financeEndTime
		setFinanceConfig({ ...res, startTime, entTime })
		setsWorkday(res.allowFlag) //是否允许提交 暂时没用 5/6
	}

	const getCreditInvestigation = async () => {
		let res = await financeModuleApi.getCheckCreditReport().catch(err => console.log(err))
		setHasCreditReport(!!res?.hasCreditReport)
		setCheckReportMsg(res?.message || '')
		setReturnDesc(res?.returnDesc || '')
	}
	const preCheck = async () => {
		const res = await financeModuleApi.couPrecheck({ companyUuid: ext?.user?.company?.uuid, type: 2 }) //2 - 授信(供应商)
		const { availableQuotaInCent, financeDiscount } = res?.credit || {}
		console.log('availableQuotaInCent', availableQuotaInCent)
		console.log('financeDiscount', financeDiscount)
		setPrecheckData(res)
		setMaxAmountEvery({ amount: res?.financeSignalLimit, isPlatformCfg: true })
	}
	// 得到可用额度
	const getCanUseAmount = async () => {
		const res = await financeModuleApi.couAmount().catch(err => {
			console.log('err', err)
		})
		if (res) {
			let { availableCouAmountInCent, userAvailableQuotaInCent, teamAvailableQuotaInCent } = res
			if (isSBuildOperator) {
				availableCouAmountInCent > userAvailableQuotaInCent
					? setCanUseAmount(Number(userAvailableQuotaInCent))
					: setCanUseAmount(Number(availableCouAmountInCent))
			} else if (isSTeamOperator) {
				availableCouAmountInCent > teamAvailableQuotaInCent
					? setCanUseAmount(Number(teamAvailableQuotaInCent))
					: setCanUseAmount(Number(availableCouAmountInCent))
			}
		}
	}

	// 得到金融服务商的配置信息
	const getFiConfigDetail = async dataSource => {
		const params = {
			fiUuid: dataSource[0].creditUuid,
		}
		const fiConfigDetail = await financeModuleApi.getFiConfigDetail(params).catch(err => {
			console.log('err', err)
		})
		return fiConfigDetail
	}
	// 根据金融服务商中的配置信息（n），计算融资到期日
	const getWorkday = async (dataSource, configOffsetDay) => {
		const params = {
			beginDate: dataSource[0].dueDate,
			configOffsetDay,
		}
		const workday = await transferModuleApi.getWorkday(params).catch(err => {
			console.log('err', err)
		})
		return workday
	}
	// 获取平台服务费率费率
	const getServiceFeeRate = async (dataSource, ext, creditInfo) => {
		// 获取公司uuid
		const centerUuid = dataSource[0].publishUuid
		const fiUuid = dataSource[0].creditUuid
		const supplierUuid = ext?.user?.company?.uuid
		const params: any = {
			centerUuid,
			fiUuid,
		}
		// 如果是供应商付息，则需要加入供应商的 uuid
		if (creditInfo.interestPayWay === 'supplier') {
			params.supplierUuid = supplierUuid
		}
		const serviceFeeRate = await financeModuleApi.getServiceFeeRate(params).catch(err => {
			console.log('err', err)
		})
		return serviceFeeRate
	}
	// 获取金融机构对核心企业的授信信息
	const getCreditInfo = async dataSource => {
		// 获取授信信息接口参数
		const companyUuid = dataSource[0].publishUuid
		const fiUuid = dataSource[0].creditUuid
		const params = { companyUuid, fiUuid, type: 1 }
		// 获取授信信息（融资费率、保理费率...）
		const creditInfo = await financeModuleApi.getCreditInfo(params).catch(err => {
			console.log('err', err)
		})
		return creditInfo
	}
	// v1.51 设置特殊利率
	const _setFinanceInfo = async (financeInfo, ext) => {
		const supplierUuid = ext?.user?.company?.uuid
		let interestRate: number, factoringRate: number
		try {
			const ratesDetail = await financeModuleApi.getRatesDetail({ companyUuid: supplierUuid, fiUuid: financeInfo.relationFi })
			if (!ratesDetail) return setFinanceInfoObj({ ...financeInfo, financeDiscount: precheckData?.credit?.financeDiscount || 0 })
			;({ interestRate, factoringRate } = ratesDetail)

			return setFinanceInfoObj({ ...financeInfo, financeDiscount: precheckData?.credit?.financeDiscount || 0, factoringRate })
		} catch (error) {
			return setFinanceInfoObj({ ...financeInfo, financeDiscount: precheckData?.credit?.financeDiscount || 0 })
		}
	}

	const handleFinanceData = async (dataSource, creditInfo, serviceFeeRate, workday, platformFeeModel, dateDiff) => {
		const ext = JSON.parse(localStorage.getItem('ext') || '{}')
		const params = { numberPrefix: 'COU', count: dataSource.length * 2 }
		//后端融信编号获取
		const res = await commonModuleApi.getNo(params).catch(err => {
			console.log('err', err)
		})
		// 获取供应商在核心企业的最大累计流转额度
		let supplierTotalAmout = await financeModuleApi
			.getSupplierCreditFormCenter({ applicationOrgId: ext?.user?.company?.id, relationOrgId: dataSource?.[0]?.publishUuid })
			.catch(err => {
				console.log('err', err)
			})
		if (supplierTotalAmout < 0) {
			supplierTotalAmout = 0
		}
		let i: number = 0
		// 计算资产总金额、融资金额、融信转让金额
		let getAssetSumInYuan: number = 0
		let getActualFinanceSumAmountInYuan: number = 0
		let getTransferCouSumAmountInYuan: number = 0
		dataSource.forEach(item => {
			getAssetSumInYuan += Number(item.couAmountInCent) / 100
			getActualFinanceSumAmountInYuan += Number(item.actualAmountInYuan)
			getTransferCouSumAmountInYuan += Number(item.inputAmountInCent) / 100
			item['toCouNo'] = res[i++]
			item['changeCouNo'] = res[i++]
		})
		console.log('dataSource1111', dataSource)

		// 在这里判断 可申请最高额
		//授信可用金额转成元
		let mount = Number(precheckData?.credit?.availableQuotaInCent) / 100
		// 通过公式计算融资金额
		const calcFinalSumAmountByFormula = (360 * getTransferCouSumAmountInYuan) / (360 + dateDiff * Number(precheckData?.credit?.interestRate))
		// 质押金额*质押率，授信可用金额，与公式计算的融资金额三个取最小 保留两位小数只舍不入
		const finaicingAmount = Math.floor(Math.min(supplierTotalAmout, mount, getActualFinanceSumAmountInYuan, calcFinalSumAmountByFormula) * 100) / 100
		console.log('供应商累计流转额度' + supplierTotalAmout + '，授信可用金额转成元' + mount + '，输入融信*质押率' + getActualFinanceSumAmountInYuan)
		setApplyMaxMount(Number(finaicingAmount))
		setActualFinanceSumAmountInYuan(Number(finaicingAmount))
		setAssetSumInYuan(getAssetSumInYuan)
		setTransferCouSumAmountInYuan(getTransferCouSumAmountInYuan)
		updateFreeMount(finaicingAmount, getTransferCouSumAmountInYuan, dateDiff) // 更新最新的费用显示 利息 试算余额
		let splitDetailListArr: Array<any> = []
		// 取出所有选择融信的uuid
		const firstCouList: any[] = []
		const uuidList: string[] = []
		const fromCompanyName = ext?.user?.company?.companyName
		for (const couData of dataSource) {
			let obj = { curCouUUid: couData.uuid, childCouNo: couData.toCouNo, fromCompanyName, toCompanyName: couData.creditName }
			firstCouList.push(obj)
			uuidList.push(couData.uuid)
		}
		// 得到uuid和url的对应关系
		const uuidUrlPair = await financeModuleApi.getTransferPathExcel({ uuidList, firstCouList }).catch(err => {
			console.log('err', err)
		})
		let sumAmountInCent = 0
		dataSource.forEach(couData => {
			couData['couReceiveSocialCode'] = financeInfoObj.relationSocialCreditCode
			let couObj: any = {}
			couObj.toCouNo = couData.toCouNo
			couObj.changeCouNo = couData.changeCouNo
			couObj.dueDate = couData.dueDate
			couObj.fromCouUuid = couData.uuid
			couObj.toCouAmountInCent = couData.inputAmountInCent
			couObj.originalUuid = couData.originalUuid
			couObj.discount = couData.discount
			couObj.transferAmountInYuan = couData.inputAmountInCent / 100
			couObj.transferPracticalAmountInYuan = couData.actualAmountInYuan
			couObj.transferPath = uuidUrlPair[couData.uuid]
			splitDetailListArr.push(couObj)
			sumAmountInCent += couData.inputAmountInCent
		})

		setSplitDetailList(splitDetailListArr)
		setCouDataSource(dataSource)
		setTableLoading(false)
		_setFinanceInfo(
			{
				...creditInfo,
				...{
					serviceFeeRate,
					actualFinanceSumAmountInYuan: actualFinanceSumAmountInYuan,
					transferCouSumAmountInYuan: getTransferCouSumAmountInYuan,
					financeDueDate: workday,
					platformFeeModel: platformFeeModel,
				},
			},
			ext
		)
		setCouInputTotalAmountInCent(sumAmountInCent)
	}

	// 获取融资发票实时校验开关is打开
	const getFinancingCheck = async () => {
		const data = await commonModuleApi.getGeneralConfiguration({ configKeys: ['real_time_invoice_check_switch'] }).catch(err => {
			console.log('err', err)
		})
		if (data && data.length) {
			let isOn = data.find(item => item.configKey === 'real_time_invoice_check_switch')?.configValue === 'ON'
			if (isOn) {
				getInvoiceCheckResult()
			}
			setIsInvoicecCheck(isOn)
		}
	}

	const transformListForApi = list => {
		return list.map(item => Number(item.id))
	}

	const getUpdateCheckFlag = async idList => {
		try {
			let res = await invoiceModuleApi.getrealtimecheck(idList)
			return res
		} catch (err) {
			throw err
		}
	}

	const getInvoiceCheckResult = async () => {
		let idList = []
		if (checkLists && checkLists.length) {
			idList = transformListForApi(checkLists)
			try {
				// 先去调用更新当前发票状态
				let res = await getUpdateCheckFlag({ idList })
				// 拿到最新的发票状态并更新到页面的表格中
				await setInvoicCheckResults(res)
				// 开始轮询校验发票
				if (!isPolling) {
					await pollingInvoiceCheckResult()
				}
			} catch (err) {
				console.log('err', err)
			}
		}
	}

	const clearTimer = () => {
		if (timerOut) {
			clearInterval(timerOut)
			timerOut = null
		}
	}

	const pollingInvoiceCheckResult = async () => {
		clearTimer()
		timerOut = setInterval(async () => {
			const response = await invoiceModuleApi.getFetchCheckResult({ idList: transformListForApi(checkLists) }).catch(err => console.log('err', err))
			if (response && response.length) {
				setInvoicCheckResults(response)
				let checkList = response.filter(item => item.checkFlag === 9)
				let successIdlist = response.filter(item => item.checkFlag !== 9).map(item => item.id)
				checkLists = checkLists.filter(item => !successIdlist.includes(item.id))
				console.log('successIdlist', successIdlist, checkLists)
				let isCheckList = checkList && checkList.length
				isPolling = true
				if (!isCheckList) {
					checkLists = []
					isPolling = false
					clearTimer()
				}
			}
		}, 1500)
	}

	// 点击发起融资
	const createFinance = async () => {
		const { factoringRate, interestRate, serviceFeeRate } = financeInfoObj
		const value = couDataSource
		await checkCreditInfo(value)
		// 出现遮罩层，无法点击其他选项
		setLoading(true)
		setSubmitFinanceLoading(true)

		let serverTime = await commonModuleApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const invoiceList = !isFirsthand ? invoiceDataSource.filter(item => item.applyAmount !== 0) : invoiceDataSource

		//计算平台服务费
		const beginTime = moment(serverTime).valueOf()
		const endTime = moment(creditDueDate).valueOf() // financeDueDate -- creditDueDate
		const dayDiff = Number(timeCalculate(beginTime, endTime, 'day') || 0)

		let serviceFee = (dayDiff * Number(serviceFeeRate) * Number(financeInfoObj.actualFinanceSumAmountInYuan)) / 100 / 360

		const params = {
			financeCreateBasePara: {
				applicationNumber: financeNo,
				assetSumInYuan,
				attachment: JSON.stringify(attachmentTable?.current?.getValues()),
				discount: couDataSource[0].discount,
				fiUuid: financeInfoObj.relationFi,
				financeDueDate: +new Date(creditDueDate), // 5/24新增 financeDueDate -- creditDueDate
				inputFinanceAmountInYuan: Number(actualFinanceSumAmountInYuan).toFixed(2),
				inputFinanceTransferAmountInYuan: transferCouSumAmountInYuan,
				relationUuid: couDataSource[0].publishUuid,
				toFiPubKey: financeInfoObj.relationFiPubKey,
				financeType: 0, //融资类型 0-质押，1-保理
			},
			financeCreateRatePara: {
				// factoringRate: 0, // 江阴暂时为05/24移除
				factoringRate: precheckData?.credit?.interestRate, //5/24新增
				interestRate: precheckData?.credit?.interestRate,
				serviceFee,
				serviceFeeRate,
				creditType: 2,
			},
			invoiceList,
			splitDetails: splitDetailList,
			firsthand: isFirsthand,
		}

		//创建融资请求
		createFinanceFunc(params)
			.then(result => {
				if (result.returnCode % 1000 === 0) {
					// 设置反馈信息对象
					setFeedbackInfoObj({
						status: 'success',
						title: '提交成功！审批通过后请在网银签署协议',
					})

					setBtnTxts(['返回融资列表'])
					// 存储返回的uuid，供在融资申请详情中使用
					// setUuidOrNumber(financeNo)
					// setUuidOrNumber(result.data.applicationUuid)
					setFeedbackVisible(true)
					// setTimeout(() => {
					// 	history.push('/content/couFinance/financeSearch')
					// }, 5000);
					//融信数据存证上链
					let couNoList = []
					couNoList = couDataSource.map(cou => {
						return cou['changeCouNo']
					})
					couDataOnChain(couNoList)
				} else {
					if (result.returnDesc.indexOf('银行接口异常') !== -1) {
						// 银行接口异常
						// 删除关键字（银行接口异常）
						let returnDesc = result.returnDesc.replace('银行接口异常', '')
						setFeedbackInfoObj({
							status: 'warning',
							title: '提交金融机构失败',
							subTitle: (
								<div>
									<span>已创建成功，请联系平台运营重新提交到金融机构！</span>
									<br />
									<span>错误信息：{returnDesc}</span>
								</div>
							),
						})
						setBtnTxts(['完成', '查看详情'])
						// 存储融资申请编号，供在融资申请详情中使用
						setUuidOrNumber(financeNo)
						// 存储返回的uuid，供在融资申请详情中使用
						// setUuidOrNumber(result.data.applicationUuid)
						setFeedbackVisible(true)
					} else if (result.returnDesc.indexOf('所选发票使用金额总和需要大于等于支付金额') !== -1) {
						message.error(result.returnDesc)
						setInvoiceDataSource([])
						setTotalPretaxAmount(0)
					} else {
						message.error(result.returnDesc)
					}
				}
				setLoading(false)
				setSubmitFinanceLoading(false)
			})
			.catch(() => {
				setLoading(false)
				setSubmitFinanceLoading(false)
			})
	}
	const showFinanceContract = async (type, title) => {
		if (invoiceDataSource.length > 0) {
			setShowProtocolLoading(true)
			let serverTime = await commonModuleApi.syncTime().catch(err => {
				console.log('err', err)
			})

			//计算平台服务费
			const beginTime = moment(serverTime).valueOf()
			const endTime = moment(financeInfoObj.creditDueDate).valueOf() // 524 新增 financeDueDate
			const dayDiff = Number(timeCalculate(beginTime, endTime, 'day') || 0)
			const { factoringRate, interestRate, serviceFeeRate } = financeInfoObj
			let serviceFee = (dayDiff * Number(serviceFeeRate) * Number(financeInfoObj.actualFinanceSumAmountInYuan)) / 100 / 360
			const params = {
				financeCreateBasePara: {
					applicationNumber: financeNo,
					assetSumInYuan,
					discount: couDataSource[0].discount,
					fiUuid: financeInfoObj.relationFi,
					financeDueDate: creditDueDate, // 524 新增 financeDueDate
					inputFinanceAmountInYuan: actualFinanceSumAmountInYuan,
					inputFinanceTransferAmountInYuan: transferCouSumAmountInYuan,
					relationUuid: couDataSource[0].publishUuid,
					toFiPubKey: financeInfoObj.relationFiPubKey,
					attachment: JSON.stringify(attachmentTable?.current?.getValues()),
				},
				financeCreateRatePara: {
					factoringRate: 0, //江阴银行未0
					interestRate: precheckData?.credit?.interestRate,
					serviceFee,
					serviceFeeRate,
				},
				invoiceList: invoiceDataSource,
				splitDetails: splitDetailList,
			}
			let res = ''
			try {
				res = await financeModuleApi[type](params)
			} catch (e) {
				setShowProtocolLoading(false)
				message.warning(`${title}预览失败`)
				return
			}

			setShowProtocolLoading(false)
			setProtocolUrl(res)
			setProtocolTitle(title)
			setProtocolVisible(true)
		} else {
			message.warning('请完善融资信息和业务凭证')
		}
	}
	const showPlatformProtocol = async () => {
		setShowProtocolLoading(true)
		let type = 'platformFee'
		if (financeInfoObj?.platformFeeModel === 'ELEC_ACCOUNT_PAY') {
			type = 'platformFeeElec4S'
		}
		if (financeInfoObj?.platformFeeModel === 'OFFLINE_PAY') {
			type = 'platformFeeOffline4S'
		}
		let params = {
			applicationNumber: financeNo,
			financeDueDate: creditDueDate, // 524 新增financeDueDate ==》 creditDueDate
			inputFinanceAmountInYuan: actualFinanceSumAmountInYuan,
			serviceFeeRate: financeInfoObj?.serviceFeeRate,
			protocolType: type,
		}
		let res = ''
		try {
			res = await financeModuleApi.previewPlatformProtocol(params)
		} catch (e) {
			setShowProtocolLoading(false)
			message.warning('协议预览失败')
			return
		}
		setShowProtocolLoading(false)
		setProtocolUrl(res)
		setProtocolTitle('《平台服务协议》')
		setProtocolVisible(true)
	}

	// 选择融信
	const handleSelectCou = () => {
		setSelectCouVisible(true)
	}
	// 选择发票的判断
	const handleSelectInvoice = () => {
		// if (!financeInfoObj.interestPayWay) return message.warning('请完善融资信息') //暂时隐藏
		if (couDataSource.length) {
			// 融信已经选择
			setSelectInvoiceVisible(true)
		} else {
			message.warning('请完善融资信息')
		}
	}
	// 改变选择发票弹窗中发票记录前的复选框
	const checkoutChange = e => {
		setCheckBoxIsSelect(e.target.checked)
	}
	// 点击选择发票
	const handleSelectInvoiceOk = (filterInvoiceList, firsthandFlag?: boolean) => {
		console.log('******', firsthandFlag)
		const isFirsthandFlag = firsthandFlag !== undefined ? firsthandFlag : isFirsthand
		let remainingAmount = Number(couInputTotalAmountInCent / 100)
		let remainingAmountTmp = Number(couInputTotalAmountInCent / 100)
		//判断是否包含未验真的发票
		sethasNCheckFlag(filterInvoiceList.filter(item => item.checkFlag === 0).length > 0)
		let getTotalPretaxAmount = 0
		let getApplyAmountAmount = 0
		!isFirsthandFlag && filterInvoiceList.sort((a, b) => a.availableAmount - b.availableAmount)
		filterInvoiceList.forEach(invoice => {
			// 防止含税金额为null
			getTotalPretaxAmount += Number(invoice.pretaxAmount) ? Number(invoice.pretaxAmount) - 0 : 0
			if (!isFirsthandFlag) {
				getApplyAmountAmount += Number(invoice.availableAmount) ? Number(invoice.availableAmount) - 0 : 0
				if (remainingAmount > 0) {
					const availableAmount = Number(invoice.availableAmount)
					invoice.applyAmount = Math.min(remainingAmount, availableAmount)
					remainingAmount -= invoice.applyAmount
				} else {
					invoice.applyAmount = 0
				}
			}
		})
		console.log('%%%%%1', isFirsthandFlag)
		console.log('%%%%%2', getApplyAmountAmount)
		console.log('%%%%%3', remainingAmountTmp)
		console.log('%%%%%4', !isFirsthandFlag && getApplyAmountAmount > remainingAmountTmp)
		if (!isFirsthandFlag && getApplyAmountAmount > remainingAmountTmp) {
			modal.info({
				title: '提示',
				content: (
					<div>
						勾选的发票可用金额合计【￥{getApplyAmountAmount}】大于所需金额【￥{remainingAmountTmp}
						】，为避免浪费发票，系统将按照可用金额占用发票的可用金额，自动剔除无需占用的发票
					</div>
				),
				okText: '确定',
			})
		}

		setTotalPretaxAmount(getTotalPretaxAmount)
		setApplyAmountAmount(getApplyAmountAmount)
		setInvoiceDataSource(filterInvoiceList)
		isPolling = false
		clearTimer()
		checkLists = filterInvoiceList
		getFinancingCheck()

		setSelectInvoiceVisible(false)
	}
	const checkCreditInfo = async value => {
		// 获取授信信息
		const creditInfo = await getCreditInfo(value)
		//如果授信信息
		if (!creditInfo) {
			setShowSpin(false)
			setLoading(false)
			setSubmitFinanceLoading(false)
			return
		}
		const checkStatus = getCheckStatus(creditInfo)
		if (!checkStatus) {
			setShowSpin(false)
			setLoading(false)
			setSubmitFinanceLoading(false)
			message.warn('融信开立方的授信记录未审核通过，请联系平台运营处理')
		} else {
			return creditInfo
		}
	}

	const getProtocol = async platformFeeModel => {
		//统一 OoInfo 获取方式 1.8.8
		const { operatingOrganizationUuid } = getStorage('OoInfo') || {}
		let type = 'platformFee'
		if (platformFeeModel === 'ELEC_ACCOUNT_PAY') {
			type = 'platformFeeElec4S'
		}
		if (platformFeeModel === 'OFFLINE_PAY') {
			type = 'platformFeeOffline4S'
		}
		const res = await commonModuleApi
			.getProtocol({
				protocolTypes: [type],
				ooUuid: operatingOrganizationUuid,
			})
			.catch(err => {
				console.log('err', err)
			})
		if (res.returnCode % 1000 === 0) {
			// 设置protocolPdfUrl
			if (res?.data && Array.isArray(res?.data) && res?.data.length > 0) {
				let { pdfUrl } = res.data[0]
				pdfUrl && setPlatformProtocolUrl(res.data[0])
			}
		}
	}

	// 点击选择融信弹窗中的确认按钮
	const handleSelectCouOk = async value => {
		setInvoiceDataSource([])
		setInvoicCheckResults([])
		setIsInvoicecCheck(false)
		setTotalPretaxAmount(0)
		setApplyAmountAmount(0)
		try {
			//当前登录用户如果是限额操作员或者项目操作员时，所输入的融信金融总和不能超过可用额度

			if (isSBuildOperator || isSTeamOperator) {
				let inputAmountInCentTotal = 0
				if (Array.isArray) {
					for (let item of value) {
						inputAmountInCentTotal += Number(item.inputAmountInCent)
					}
				} else {
					inputAmountInCentTotal = value?.inputAmountInCent
				}
				if (canUseAmount === 0 || Number(canUseAmount) < Number(inputAmountInCentTotal)) {
					message.warning('可用额度不足')
					return
				}
			}
			//5.8质押率取值变为cou的开立方的质押率
			// const discount = await financeModuleApi.getCouOpenerPledgeInterest({ publishPubKey: value?.[0]?.publishPubKey })
			// value?.forEach(item => {
			// 	item.discount = discount?.financeDiscount //赋值给discount避免改动后面用到的地方
			// 	// item.financeDiscount = discount?.financeDiscount
			// })
			console.log(value, 'eee')
			setShowSpin(true) //added by john 7-18
			getCrontractCodes(value)
			const ext = JSON.parse(localStorage.getItem('ext') || '{}')
			const creditInfo = await checkCreditInfo(value)

			// 获取费平台服务费率
			const serviceFeeRate = await getServiceFeeRate(value, ext, creditInfo)
			// 此时说明公司设置了服务费率
			if (serviceFeeRate !== null) {
				// 得到前缀、n
				let financeDueDateOffset, financeNumberPrefix, platformFeeModel
				const { financeBusinessConfig, parentFinanceBusinessConfig } = await getFiConfigDetail(value)
				if (financeBusinessConfig) {
					financeDueDateOffset = financeBusinessConfig.financeDueDateOffset
					financeNumberPrefix = financeBusinessConfig.financeNumberPrefix
					platformFeeModel = financeBusinessConfig.platformFeeModel
				} else if (parentFinanceBusinessConfig) {
					financeDueDateOffset = parentFinanceBusinessConfig.financeDueDateOffset
					financeNumberPrefix = parentFinanceBusinessConfig.financeNumberPrefix
					platformFeeModel = parentFinanceBusinessConfig.platformFeeModel
				} else {
					message.error('该金融机构的配置信息不完善，请联系运营处理')
					return
				}
				//如果是供应商付息，需要查询平台协议 江阴银行不需要这个判断
				if (creditInfo.interestPayWay === 'supplier' && platformFeeModel) {
					getProtocol(platformFeeModel)
				}
				// 根据对应的银行配置获取融资申请编号的前缀
				const res = await commonModuleApi.getNo({ numberPrefix: financeNumberPrefix }).catch(err => {
					console.log('err', err)
				})
				setFinanceNo(res[0])
				// 根据对应的银行配置计算融资到期日（T+n）
				const workday = await getWorkday(value, financeDueDateOffset)
				// 5/24 新增融资到期日 重新计算
				/// 融信到期日+金融服务商配置的融资到期日天数;生产配的是: 10天取自然日
				const _creditDueDate = moment(value[0].dueDate).add(financeDueDateOffset, 'days').format('YYYY-MM-DD')
				// const _creditdays = moment()
				// 融资到期日-当前日期的天数;(即头尾只算一个) 5/24 新增
				let dateDiff = 0
				let m1 = moment(_creditDueDate)
				let m2 = moment(+new Date()).format('YYYY-MM-DD')
				dateDiff = m1.diff(m2, 'day')
				setCreditDueDate(_creditDueDate)
				setCreaditDays(dateDiff)

				// 处理保存其他融资相关的数据
				console.time()
				handleFinanceData(value, creditInfo, serviceFeeRate, workday, platformFeeModel, dateDiff)
				console.timeEnd()

				// 处理最大融资额度(转成分)
				if (value[0]?.creditSingleLimitInCent < Number(precheckData?.financeSignalLimit) * 100) {
					setMaxAmountEvery({ amount: Number((Number(value[0]?.creditSingleLimitInCent || 0) / 100).toFixed(2)), isPlatformCfg: false })
				} else {
					setMaxAmountEvery({ amount: precheckData?.financeSignalLimit, isPlatformCfg: true })
				}

				setSelectCouVisible(false)
				setShowSpin(false) //added by john 7-18
				setTableLoading(true)

				// 添加一手融信发票逻辑,value是选择的couData

				const couList = value.map(item => {
					return {
						couUuid: item.uuid,
						originalUuid: item.originalUuid,
					}
				})
				invoiceModuleApi
					.getcouAvailableList({
						pageNum: 1,
						pageSize: 9999999,
						couList,
					})
					.then(res => {
						setInvoicCheckResults([])
						setIsInvoicecCheck(false)
						setIsFirsthand(res.firsthand)
						console.log('@@@@@@@1', res.firsthand)
						console.log('@@@@@@@2', isFirsthand)
						if (res.firsthand) {
							if (res.invoicePageInfo.list && res.invoicePageInfo.list.length) {
								res.invoicePageInfo.list.forEach(i => {
									// 给每一个发票添加唯一的 key
									i.key = i['invoiceNumber']
								})
								const arr = res.invoicePageInfo.list
								const filtered = removeDuplicate(arr)
								// console.log(filtered)
								// setInvoiceDataSource([...filtered])
								handleSelectInvoiceOk([...filtered], res.firsthand)
								setInvoiceBtnDisabled(true)
							}
						} else {
							setInvoiceDataSource([])
							setInvoiceBtnDisabled(false)
						}
					})
				console.log('&&&&&&&&&&', isFirsthand)
			} else {
				setShowSpin(false)
				setLoading(false)
				setSubmitFinanceLoading(false)
				message.warning('操作失败，请联系平台运营设置平台服务费率')
			}
			clearTimer()
		} catch (error) {
			console.log('error', error)
		}
	}
	//根据Cou uuidList获取合同code list
	const getCrontractCodes = (cous: any[]) => {
		let uuids: string[] = []
		cous.forEach(item => {
			uuids.push(item.uuid)
		})
		financeModuleApi
			.getContractCodesByCouUuid({
				uuids,
				pageNum: 0,
				pageSize: 0,
			})
			.then((res: any[]) => {
				cous.forEach(cou => {
					cou['contractCode'] = res.filter(item => item['couUuid'] === cou['uuid'])[0]['contractCode']
				})
			})
	}
	// 点击融资信息确认弹窗中的确认按钮
	const handleFinanceInfoConfirmOk = () => {
		setFinanceInfoConfirmVisible(false)
	}
	const fenToYuan = (val: any) => {
		//金额转换 分->元 保留2位小数 并每隔3位用逗号分开 1,234.56
		let num = Number(val)
		var str = (num / 100).toFixed(2) + ''
		var intSum = str.substring(0, str.indexOf('.')).replace(/\B(?=(?:\d{3})+$)/g, ',') //取到整数部分
		var dot = str.substring(str.length, str.indexOf('.')) //取到小数部分搜索
		var ret = intSum + dot
		return ret
	}
	const getColumns = () => {
		const columnsDic = {
			inputAmountInCent: {
				width: '25%',
				ellipsis: true,
				title: '质押融信金额(￥) ',
				// title: (
				// 	<div>
				// 		质押融信金额(￥)&nbsp;&nbsp;
				// 		<Tooltip placement="top" title="转让融信金额表示您转让给金融机构的应收账款金额">
				// 			<ExclamationCircleOutlined style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer' }} />
				// 		</Tooltip>
				// 	</div>
				// ),
				render: (text: any, record: any) => {
					return <Tooltip title={renderAmountInCent(record.inputAmountInCent)}>{renderAmountInCent(record.inputAmountInCent)}</Tooltip>
				},
			},
			publishName: { width: '25%', ellipsis: true },
			// dueDate: { width: '20%', ellipsis: true },
			// discount: {
			// 	render: (text: any, record: any) => {
			// 		return text * 100 + '%'
			// 	},
			// },
			// actualAmountInYuan: { // 5/6需求隐藏
			// 	title: (
			// 		<div>
			// 			实际融资申请金额(￥)&nbsp;&nbsp;
			// 			<Tooltip placement="top" title="实际融资申请金额为试算">
			// 				<ExclamationCircleOutlined style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer' }} />
			// 			</Tooltip>
			// 		</div>
			// 	),
			// 	render: (text: any, record: any) => {
			// 		return renderAmount(record.actualAmountInYuan)
			// 	},
			// },
			// couFlowSheet: {
			// 	dataIndex: 'couFlowSheet',
			// 	render: (text: any, record: any) => {
			// 		return (
			// 			<span className="link" style={{ fontSize: '24px' }} onClick={() => showTransferProtocol(record)}>
			// 				<FileOutlined />
			// 			</span>
			// 		)
			// 	},
			// },
		}
		const a = getColumnsByPageName('selectCouF', columnsDic)
		return a
	}
	const showTransferProtocol = async record => {
		if (!financeInfoObj.interestPayWay) return message.info('企业未授信')
		console.log(record)
		let data: any = {
			creditUuid: record.creditUuid,
			publishUuid: record.publishUuid,
			receiverUuid: record.creditUuid,
			splitDetail: {
				changeCouNo: record.changeCouNo,
				dueDate: record.dueDate,
				fromCouUuid: record.uuid,
				toCouAmountInCent: record.inputAmountInCent,
				toCouNo: record.toCouNo,
			},
		}
		const res = await transferModuleApi.transferProtocol4PayView(data)

		setProtocolUrl(res)
		setProtocolTitle('《融信流转单》')
		setProtocolVisible(true)
	}
	// 融资申请创建
	const getCheckStatus = info => {
		return info?.status === 'confirmed'
	}

	const numericInputValidator = value => {
		const reg = /^(?!0+(?:\.0+)?$)(?:[1-9]\d{0,8}|0)(?:\.\d{1,2})?$/
		// 最高可融资金额
		// applyMaxMount
		if (reg.test(value + '') && Number(value) <= Number(applyMaxMount)) {
			setFinanceInputIsRight(true)
			return true
		} else {
			setFinanceInputIsRight(false)
			return false
		}
	}

	const handleNumericInputChange = value => {
		// 校验输入内容
		numericInputValidator(value)
		// 无论true或false 都更新值
		setActualFinanceSumAmountInYuan(value)
		updateFreeMount(value, transferCouSumAmountInYuan, creaditDays)
	}
	const updateFreeMount = (value, couSumAmountInYuan, days) => {
		/**
		 * value: 用户填写的实际申请金额
		 * couSumAmountInYuan： 融资申请总金额
		 * days: 融资天数
		 */
		let _ratenow = ((Number(value) * Number(precheckData?.credit?.interestRate) * days) / 360).toFixed(2) // 试算利息
		let _extralMount = Number(couSumAmountInYuan) - Number(value) - Number(_ratenow) // 余额试算利息
		setRatenow(_ratenow)
		setExtralMount(_extralMount)
	}

	const getInvoiceColumns = () => {
		const columnsDic = {
			checkState: getInvoicCheckStatusCols().checkState,
			checkFlag: getInvoicCheckStatusCols(record => {
				viewReasonModalRef?.current?.show({ reason: record?.checkMsg })
			}).checkFlagWithReason,
			operation: {
				dataIndex: 'operation',
				title: '操作',
				render: (text, record) => {
					return record?.checkFlag + '' === '0' && isInvoicecCheck ? (
						<span
							className="link"
							onClick={() => {
								checkLists.push(record)
								getInvoiceCheckResult()
							}}
						>
							重新校验
						</span>
					) : (
						''
					)
				},
			},
		}

		return isFirsthand ? getColumnsByPageName('selectInvoice', columnsDic) : getColumnsByPageName('selectInvoiceMultiple', columnsDic)
	}

	const showFinanceInfoConfirm = () => {
		// 融资信息确认弹窗显示
		let obj = { ...financeInfoObj }
		obj['actualFinanceSumAmountInYuan'] = Number(actualFinanceSumAmountInYuan)
		obj['copyactualFinanceSumAmountInYuan'] = Number(actualFinanceSumAmountInYuan)
		obj['actualDiscount'] = Number(actualFinanceSumAmountInYuan) / transferCouSumAmountInYuan
		obj['creditDueDate'] = creditDueDate
		obj['creaditDays'] = creaditDays
		obj['interestRate'] = Number(precheckData?.credit?.interestRate)
		obj['ratenow'] = renderAmount(ratenow)
		obj['extralMount'] = renderAmount(extralMount)
		// obj['shijizhiyalv'] = ((Number(actualFinanceSumAmountInYuan) / Number(transferCouSumAmountInYuan)) * 100).toFixed(2)
		setFinanceInfoObj(obj)
		setFinanceInfoConfirmVisible(true)
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					{/* <IsFillInvoicingInfoTip
						message="企业开票信息未完善，请尽快完善"
						description="您的企业开票信息未录入，企业开票信息用于签署平台服务费协议以及邮寄发票使用，请联系企业管理员登录系统完善企业开票信息"
					></IsFillInvoicingInfoTip> */}
					<CompanyNameChangTip ishidden={precheckData.orgNameUnChangeFlag}></CompanyNameChangTip>
					<CompanyCreatTip ishidden={precheckData.creditFlag}></CompanyCreatTip>
					<CompanyNotOpentTip ishidden={precheckData.bankFlag}></CompanyNotOpentTip>
					{!isWorkday ? (
						<Alert
							message={`请在${financeConfig.financeTimeEnable ? '工作日' : ''}${financeConfig?.startTime}--${financeConfig?.entTime}提交`}
							type="warning"
							description=" "
							showIcon
							style={{ marginBottom: '32px' }}
						></Alert>
					) : null}
					{!hasCreditReport && checkReportMsg ? (
						<Alert message={checkReportMsg} type="warning" description=" " showIcon style={{ marginBottom: '32px' }}></Alert>
					) : null}
					<div>
						<Card className="status">
							<div className="content">
								<div className="leftImg">
									<img width={118} height={116} src={require('@src/assets/images/cou/cou_icon.png')} alt="" />
								</div>
								<div className="rightBox">
									<div className="line"></div>
									<div className="title">
										<img width={22} src={require('@src/assets/images/cou/time_icon.png')} alt="" />
										<span style={{ marginTop: '2px', color: '#6578A1' }}>
											授信到期日：<span style={{ fontWeight: 'bold' }}> {precheckData?.credit?.creditDueDate || '--'}</span>
										</span>
									</div>
									<div className="detailContent" style={{ color: '#000000' }}>
										<div className="item">
											<div>
												<p className="item_title">授信金额</p>
												<p>¥ {renderAmountInCent(Number(precheckData?.credit?.quotaInCent) || 0) || '--'}</p>
											</div>
											<div>
												<p className="item_title">可用金额</p>
												<p>¥ {renderAmountInCent(Number(precheckData?.credit?.availableQuotaInCent) || 0) || '--'}</p>
											</div>
											<div>
												<p className="item_title">融资利率（年化）</p>
												<p>
													{Number.isNaN(Number(precheckData?.credit?.interestRate))
														? '--'
														: (Number(precheckData?.credit?.interestRate) * 100).toFixed(2) || '--'}
													%
												</p>
											</div>
										</div>
									</div>
								</div>
							</div>
						</Card>
					</div>
					<div className="financeInfo">
						<div className="boxContentWrap">
							<SmallTitle text="融资信息" />
							<div className="boxContent">
								<div className="fiAndMoney">
									{/* <span>
										<span>金融机构：</span>
										<span>{couDataSource[0] ? couDataSource[0].creditName : <span style={{ color: 'rgb(206, 206, 206)' }}>请选择融信</span>}</span>
									</span> */}
									<div style={{ width: '100%' }}>
										{/* <div style={{ marginBottom: '10px' }}>
											<InfoCircleOutlined style={{ color: 'orange', marginRight: '5px' }} />
											<span style={{ color: '#999', fontSize: '12px' }}>融资申请金额为试算，实际放款金额以网银实际签署的《借款合同》 为准</span>
										</div> */}
										<div style={{ display: 'flex', lineHeight: '32px', fontSize: '14px' }}>
											<span style={{ width: '126px' }}>融资申请金额￥：</span>
											<NumericInput
												disabled={couDataSource.length === 0}
												style={{
													width: 120,
												}}
												maxLength={9}
												placeholder="请输入融资申请金额"
												value={'' + actualFinanceSumAmountInYuan}
												onChange={handleNumericInputChange}
											/>
											<span style={{ width: '320px', marginLeft: '30px' }}>
												可申请最高额￥:
												<span style={{ fontWeight: 'bold', marginLeft: '10px' }}>{applyMaxMount ? Number(applyMaxMount).toFixed(2) : '0'}</span>
											</span>
											<span style={{ width: '320px', marginLeft: '30px' }}>
												最高质押率:
												<span style={{ fontWeight: 'bold', marginLeft: '10px' }}>
													{couDataSource.length > 0 ? (Number(couDataSource[0]?.discount) * 100).toFixed(2) + '%' : '--'}
												</span>
											</span>
										</div>
										{!financeInputIsRight && (
											<div style={{ color: 'red', fontSize: '12px', marginTop: '5px' }}> 请输入金额，整数最多9位，小数最多2位，不大于可申请最高额度。</div>
										)}

										<div
											style={{
												marginTop: '10px',
												height: '40px',
												lineHeight: '40px',
												backgroundColor: '#FFFBE6',
												paddingLeft: '10px',
												width: '100%',
											}}
										>
											<InfoCircleOutlined style={{ color: 'orange', marginRight: '5px' }} />
											<span style={{ color: 'rgba(0,0,0,0.65)', fontSize: '14px' }}>
												申请金额，单笔{maxAmountEvery.isPlatformCfg ? '小于' : '不大于'}
												{(Number(maxAmountEvery.amount || 0) / 10000).toFixed(2) || '--'}W；不大于质押率 * 质押的融信金额； 不大于授信可用金额；
											</span>
										</div>
									</div>
								</div>
								{/* <div className="threeRate" style={{ display: financeInfoObj.interestPayWay === 'supplier' ? 'flex' : 'none' }}> */}
								{/* <span>融资利率（年化）：{financeInfoObj.interestRate ? financeInfoObj.interestRate : '0'} %</span> */}
								{/* <span>保理业务手续费率（单笔）：{financeInfoObj.factoringRate ? financeInfoObj.factoringRate : '0'} %</span>
									<span>平台服务费率（年化）：{financeInfoObj.serviceFeeRate ? financeInfoObj.serviceFeeRate : '0'} %</span> */}
								{/* </div> */}
								{couDataSource.length > 0 ? (
									<div className="shiSuan">
										<div style={{ paddingTop: '22px' }}>
											<div>质押融信金额：￥{renderAmount('' + transferCouSumAmountInYuan)}</div>
											<div>实际质押率：{((Number(actualFinanceSumAmountInYuan) / Number(transferCouSumAmountInYuan)) * 100).toFixed(2)}%</div>
											<div>实际放款金额：￥{renderAmount('' + actualFinanceSumAmountInYuan)}</div>
											<div>融资到期日：{creditDueDate}</div>
										</div>
										<div style={{ paddingBottom: '22px' }}>
											<div>融资天数：{creaditDays}天</div>
											<div>
												<Tooltip placement="top" title="实际利息以还款时实际的借款时长为准">
													<ExclamationCircleOutlined style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer', marginRight: '3px' }} />
													利息（试算）：￥{renderAmount(ratenow)}
												</Tooltip>
											</div>
											<div style={{ width: '30%' }}>
												<Tooltip placement="top" title="融信到期偿还银行借款后,本企业可以收到的剩余金额,以实际还款扣除的利息为准">
													<ExclamationCircleOutlined style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer', marginRight: '3px' }} />
													清算余额（试算）：￥{renderAmount(extralMount)}
												</Tooltip>
											</div>
										</div>
									</div>
								) : null}

								{/* 融信明细 */}
								<div className="couDetail">
									{/* 融信明细 */}
									<div className="title">
										{/* <div className="detail">融信明细</div> */}
										<Button
											type="primary"
											onClick={handleSelectCou}
											disabled={!(isWorkday && precheckData.orgNameUnChangeFlag && precheckData.creditFlag && precheckData.bankFlag)}
										>
											选择融信
										</Button>
										<div
											style={{
												color: 'rgb(206, 206, 206)',
											}}
										>
											{/* &nbsp;&nbsp;&nbsp;{isSBuildOperator || isSTeamOperator ? `可用额度：¥ ${renderAmountInCent(Number(canUseAmount))}` : ''} */}
										</div>
									</div>
									{/* <div className="amount">
									<div>已选融信：￥ {renderAmount(transferCouSumAmountInYuan + '')}</div>
									<div>
										实际融资金额总和：￥
										{renderAmount(actualFinanceSumAmountInYuan + '')}
									</div>
								</div> */}
									<div className="table">
										<Table dataSource={couDataSource} columns={getColumns()} pagination={false} loading={tableloading} />
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="businessVoucher">
						<div className="boxContentWrap">
							<SmallTitle text="业务凭证" />
							<div className="boxContent">
								<div className="info">
									<div className="top">
										{/* <div className="invoice">发票</div> */}
										<Button type="primary" onClick={handleSelectInvoice} disabled={!isWorkday || invoiceBtnDisabled}>
											选择发票
										</Button>
										<Button onClick={handleGotoAddInvoice} style={{ marginLeft: 10 }} disabled={!isWorkday}>
											新建发票
										</Button>
									</div>
									<div className="invoiceBox">
										<div className="bottom">
											<div className="number">数量：{invoiceDataSource.length}</div>
											<div style={{ marginLeft: 22 }}>含税总金额：￥{numberToThousands(totalPretaxAmount)}</div>
										</div>
									</div>
								</div>

								{hasNCheckFlag && (
									<Alert message={'当前选择的发票中包含验真失败或发票异常的发票，请注意检查'} type="warning" showIcon style={{ marginTop: '20px' }} />
								)}

								<div className="table">
									<Table dataSource={invoiceDataSource} columns={getInvoiceColumns()} pagination={false} />
								</div>
								<ViewReasonModal ref={viewReasonModalRef} />
							</div>
						</div>
						<NewUploadInvoice
							visible={showCreateInvoinve}
							invoiceDayOffset={precheckData?.invoiceDayOffset}
							closeModal={value => {
								setShowCreateInvoinve(value)
							}}
						/>
						<div className="boxContentWrap">
							<AttachmentTable ref={attachmentTable} isWorkday={isWorkday} isFinance={true} />
						</div>

						<div className="agreement">
							{/* 2024/3/30 删除签署《质押合同》《借款合同》 同意签署功能删除 */}
							{/* <Checkbox onChange={checkoutChange}></Checkbox> */}
							<div style={{ marginLeft: '5px' }}>
								{/* <span>同意并签署</span>
								<span style={{ color: 'rgb(22, 144, 207)', cursor: 'pointer' }} onClick={() => showFinanceContract('previewPledge', '《质押合同》')}>
									《质押合同》
								</span>
								<span>、</span>
								<span style={{ color: 'rgb(22, 144, 207)', cursor: 'pointer' }} onClick={() => showFinanceContract('previewpledgeLoan', '《融资合同》')}>
									《融资合同》
								</span> */}
								{/* {platformProtocolUrl?.pdfUrl && financeInfoObj?.interestPayWay === 'supplier' ? (
									<React.Fragment>
										<span>、</span>
										<span
											style={{ color: 'rgb(22, 144, 207)', cursor: 'pointer' }}
											onClick={
												// () => platformProtocolUrl?.pdfUrl && setPdfVisible(true)
												showPlatformProtocol
											}
										>
											《平台服务协议》
										</span>
										<PDFViewer title="" visible={pdfVisible} pdfUrl={platformProtocolUrl?.pdfUrl} onCancel={() => setPdfVisible(false)} />
									</React.Fragment>
								) : (
									''
								)} */}
							</div>
						</div>
						<div style={{ marginTop: '20px', marginBottom: '20px' }}>
							<Alert
								message={`提交后您需要去网银签署《质押合同》和《融资合同》融资申请提交时间需要为${financeConfig.financeTimeEnable ? '工作日' : ''}${
									financeConfig?.startTime
								}--${financeConfig?.entTime}提交`}
								type="warning"
								showIcon
								style={{ marginBottom: '10px' }}
							/>
						</div>
						<div className="submit">
							<Button
								type="primary"
								disabled={!checkBoxIsSelect || !isWorkday}
								onClick={() => {
									let _canUseAmount = Number(precheckData?.credit?.availableQuotaInCent) / 100
									getCreditInvestigation()
									// if (Number(actualFinanceSumAmountInYuan) < 0 && Number(actualFinanceSumAmountInYuan) > precheckData.configPara.configValue) {
									// 	message.warning(`融资申请金额必须大于0且小于${(Number(precheckData?.financeSignalLimit || 0)/ 10000).toFixed(2) || '--'}W`)
									// } // 2024/5/21注释掉，因为precheckData.configPara.configValue 已经不存在
									if (!invoiceDataSource.length || !couDataSource.length) {
										message.warning('请完善融资信息和业务凭证')
									} else {
										if (!financeInputIsRight) {
											// 融资金额格式校验不对
											return
										}
										// if (totalPretaxAmount < Number(actualFinanceSumAmountInYuan)) {
										if (totalPretaxAmount < Number(transferCouSumAmountInYuan)) {
											//0512需求
											// 判断发票含税总金额是否大于等于融资金额
											message.warning('发票含税总金额应大于等于融信金额。')
										} else if (
											// (isSBuildOperator || isSTeamOperator) &&
											_canUseAmount === 0 ||
											Number(_canUseAmount) < Number(actualFinanceSumAmountInYuan)
										) {
											// Number(precheckData?.credit?.availableQuotaInCent)
											message.warning('授信可用额度不足')
											return
											// 170需求
											// 实时验真开关打开，发票验真状态为成功 发票状态都要是正常
										} else if (
											isInvoicecCheck &&
											(!invoiceDataSource.every(item => item.checkFlag === 1) || !invoiceDataSource.every(item => item.checkState === '0'))
										) {
											message.warning('发票状态异常，不可提交')
										} else if (!hasCreditReport) {
											// 180需求
											// 校验征信报告
											message.warning(returnDesc || checkReportMsg || '征信校验报告出错')
										}
										// else if (!isFirsthand && invoiceDataSource.some(item => item.applyAmount === 0)) {
										// 	resultModalRef?.current.show()
										// }
										else {
											showFinanceInfoConfirm()
										}
									}
								}}
								loading={loading}
							>
								提交
							</Button>
						</div>
					</div>
				</div>
				<SelectCou
					pageName="FINANCE"
					queryType={1}
					searchPageName="selectCouModalF"
					tablePageName="selectCouModelF"
					alertMessage="提示：可以选择多个融信 ，但多个融信必须是同一原始融信，您可以在输入质押全额， 拆分使用"
					visible={selectCouVisible}
					dataInit={couDataSource}
					onOk={value => {
						console.log(value)
						setPlatformProtocolUrl(null)
						handleSelectCouOk(value)
					}}
					onCancel={() => {
						console.log('index--->couDataSource', couDataSource)
						setSelectCouVisible(false)
					}}
					amountTabCallback={(selectCouAmount, selectCouActualAmount) => {
						let mount = Number(selectCouAmount) * Number(precheckData.credit?.financeDiscount || 0)
						return (
							<div className="title">
								<div className="selected">已选融信：￥{renderAmount(selectCouAmount + '')}</div>
								{/* <div className="selected">融资比例：{Number(precheckData.credit?.financeDiscount) * 100 || '--'} %</div>
								<div className="selected">融资申请金额：¥{renderAmount(mount + '')} </div> */}
							</div>
						)
					}}
				/>
				<SelectInvoice
					visible={selectInvoiceVisible}
					couDataSource={couDataSource}
					dataInit={invoiceDataSource}
					invoiceDayOffset={precheckData?.invoiceDayOffset}
					actualFinanceSumAmountInYuan={couInputTotalAmountInCent / 100}
					onOk={value => {
						handleSelectInvoiceOk(value)
					}}
					onCancel={() => setSelectInvoiceVisible(false)}
				/>
				<PDFViewer
					visible={protocolVisible}
					pdfUrl={protocolUrl}
					title={protocolTitle}
					onCancel={() => {
						setProtocolUrl('')
						setProtocolTitle('')
						setProtocolVisible(false)
					}}
				/>
				<FinanceInfoConfirm
					visible={financeInfoConfirmVisible}
					couData={couDataSource}
					precheckData={precheckData}
					actualFinanceSumAmountInYuan={actualFinanceSumAmountInYuan}
					financeInfoObj={financeInfoObj}
					onOk={() => {
						handleFinanceInfoConfirmOk()
						// 流转路径图换成流转路径表,则前端不用生成流转路径图了
						createFinance()
					}}
					onCancel={() => setFinanceInfoConfirmVisible(false)}
				/>
				<Feedback
					onOk={() => {
						history.push('/content/couFinance/financeSearch')
					}}
					onDetail={() => {
						// 存储返回的uuid，供在融资申请详情中使用
						history.push('/content/couFinance/sFinanceDetail', {
							uuidOrNumber: uuidOrNumber,
						})
					}}
					visible={feedbackVisible}
					status={feedbackInfoObj.status}
					title={feedbackInfoObj.title}
					subTitle={feedbackInfoObj.subTitle}
					btnTxts={btnTxts}
				/>
			</LayoutSlot>

			<Modal title={null} open={loading} footer={null} maskClosable={false} closable={false} bodyStyle={{ padding: 0 }}>
				<LoadingStyle>
					<Spin tip="请停留等待，否则融资将会失败！" spinning={true} className="submitLoading"></Spin>
				</LoadingStyle>
			</Modal>

			<Modal title={null} open={showProtocolLoading} footer={null} maskClosable={false} closable={false} bodyStyle={{ padding: 0 }}>
				<LoadingStyle>
					<Spin tip="协议生成中，请稍等！" spinning={true} className="submitLoading"></Spin>
				</LoadingStyle>
			</Modal>
			<Spin className="drawerSpin" spinning={showSpin} style={{ position: 'absolute', top: '50%', left: '50%', zIndex: '999999999' }}></Spin>
			<WxModal {...viewResultModalProps} ref={resultModalRef}>
				<div>{`勾选的发票可用金额合计【${applyAmountAmount}】大于融资金额【${actualFinanceSumAmountInYuan}】，为避免浪费发票。系统将按照可用金额从小到大顺序占用发票的可用金额，自动剔除无需占用的发票`}</div>
			</WxModal>
			{contextHolder}
		</Box>
	)
}

const Box = styled.div`
	height: 100%;
	.status {
		margin-bottom: 32px;
		.detailContent {
		}
		.ant-card-body {
			font-size: 14px;
			color: #fff;
			padding: 12px 24px;
			.content {
				height: 116px;
				display: flex;
				.rightBox {
					width: 100%;
					margin-left: 32px;
					padding-left: 32px;
					box-sizing: border-box;
					position: relative;
					.line {
						position: absolute;
						top: 11px;
						left: 0px;
						width: 1px;
						height: 96px;
						border-left: 1px solid rgba(180, 190, 213, 1);
					}
					.title {
						display: flex;
						align-items: center;
						color: rgba(255, 255, 255, 0.85);
						img {
							margin-right: 5px;
						}
						margin-bottom: 20px;
					}
					.item {
						padding-left: 5px;
						// width: 480px;
						display: flex;
						div {
							margin-right: 80px;
							.item_title {
								color: rgba(0, 0, 0, 0.38);
								margin-bottom: 10px;
							}
							p:nth-child(2) {
								font-size: 24px;
								font-weight: 500;
							}
							p {
								margin-bottom: 0;
								color: rgba(23, 23, 23, 0.85);
							}
						}
					}
					.btnbox {
						position: absolute;
						top: 50%;
						right: 60px;
						transform: translateY(-50%);
					}
				}
			}
		}
	}
	.ant-card {
		width: 100%;
		background: url(${couBg});
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}
	.ant-card-bordered {
		border: none !important;
	}
	.card-wrapper {
		.topCard {
			background-color: rgb(242, 242, 242);
			.content {
				display: flex;
			}
		}
		.boxContentWrap {
			margin-bottom: 10px;
			position: relative;
			.boxContent {
				border: 1px solid #e9e9e9;
				// border-radius: 4px 4px 0px 0px;
				border-top: 0;
				padding: 20px 10px;
			}
			.invoiceBox {
				position: absolute;
				top: 12px;
				left: 142px;
				color: #1f7bf4;
				font-weight: bold;
			}
		}
		.financeInfo {
			.fiAndMoney {
				display: flex;
				justify-content: space-between;
				padding-left: 25px;
				box-sizing: border-box;
			}
			.threeRate {
				display: flex;
				justify-content: space-around;
				padding: 10px 0 10px 0;
				background-color: rgb(240, 240, 240);
			}
			.shiSuan {
				padding-left: 25px;
				box-sizing: border-box;
				color: #202020;
				font-size: 14px;
				> div {
					display: flex;
					background: #fbfbf9;

					// justify-content: space-between;
					padding: 11px 32px;
					div {
						width: 25%;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}
				}
			}
			.couDetail {
				.title {
					display: flex;
					align-items: center;
					margin: 20px 0 20px 0;
					padding-left: 25px;
					box-sizing: border-box;
					.detail {
						width: 112px;
					}
				}
				.amount {
					background-color: #efefef;
					border-radius: 5px;
					display: flex;
					line-height: 40px;
					padding: 10px;
					/* text-align: center; */
					div {
						flex: 1;
					}
					margin-bottom: 10px;
				}
				.table {
					margin-bottom: 20px;
				}
			}
		}
		.businessVoucher {
			.info {
				padding-left: 25px;
				box-sizing: border-box;
				.top {
					display: flex;
					align-items: center;
					.invoice {
						width: 112px;
					}
				}
				.bottom {
					display: flex;
					// .number {
					// 	width: 112px;
					// }
				}
			}
			.table {
				/* height: 200px; */
				margin: 20px 0 20px 0;
			}
			.agreement {
				display: flex;
				justify-content: center;
				margin-bottom: 20px;
			}
			.submit {
				display: flex;
				justify-content: center;
			}
		}
	}
`

/*
const LoadingStyle = styled.div`
	background-color: rgba(0, 0, 0, 0.1);
	width: 100%;
	height: 400px;
	.submitLoading {
		position: absolute;
		top: 160px;
		left: 50%;
		transform: translateX(-50%);
	}
	.ant-spin-dot-item {
		background-color: #09e3fb;
	}
	.ant-spin-text {
		color: #09e3fb;
		font-weight: 900;
		font-size: 16px;
	}
`
*/

const LoadingStyle = styled.div`
	background-color: rgba(0, 0, 0, 0.1);
	.submitLoading {
		position: absolute;
		top: 160px;
		right: 40px;
	}
	.ant-spin-dot-item {
		background-color: #09e3fb;
	}
	.ant-spin-text {
		color: #09e3fb;
		font-weight: 900;
		font-size: 16px;
	}
`

export default index
