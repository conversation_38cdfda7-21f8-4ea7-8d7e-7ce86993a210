import React, { useState, useEffect } from 'react'
import { message, Alert } from 'antd'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import BaseTable from '@src/globalComponents/BaseTable'
import SearchBar from '@src/pages/client/components/SearchBar'
import BaseDrawer from '@src/globalComponents/BaseDrawer'
import OperatingArea from '@src/globalComponents/OperatingArea'
import { invoiceModuleApi } from '@src/pages/client/api'
import { timeToStamp } from '@utils/timeFilter'
import { deepExtend, removeDuplicate } from '@utils/util'
import styled from 'styled-components'
import { hasAuth } from '@src/pages/client/biz/bizIndex'
import { getInvoicCheckStatusCols } from '@src/globalBiz/invoiceBizModule'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
interface Props {
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 用于 回显
	 */
	dataInit: any[]
	couDataSource: any[] //用于查看已选融信的uuid
	/**
	 * @description 提交确定操作
	 */
	onOk: (value: any) => void
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
	/**
	 * invoiceDayOffset 发票有效期
	 */
	invoiceDayOffset?: number
	/**
	 * invoiceDayOffset 融资申请金额
	 */
	actualFinanceSumAmountInYuan?: any
}

const SelectInvoice = (props: Props) => {
	// const couUuid = props?.couDataSource[0]?.uuid
	const [loading, setLoading] = useState(false)
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 }) // current: 4, pageSize: 20
	const [searchParams, setSearchParams] = useState({})
	const [invoicePretaxAmount, setInvoicePretaxAmount] = useState<number>(0)
	const [dataSource, setDataSource] = useState<any[]>([])
	const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]) // 表格中选中的row的key
	const [selectedDataSource, setSelectedDataSource] = useState<any[]>([]) // 表格中选中的数据源
	const [hasNCheckFlag, sethasNCheckFlag] = useState<boolean>(false) // 是否包含为验真的发票

	const getInvoiceListFunc = () => {
		let func = null
		if (hasAuth('bl_couFinance:sOperatorCreateFinance:create')) {
			func = invoiceModuleApi.getbyAvailableList
		} else if (hasAuth('bl_couFinance:sBuildOperatorCreateFinance:create')) {
			func = invoiceModuleApi.getbyAvailableList
		} else if (hasAuth('bl_couFinance:sTeamOperatorCreateFinance:create')) {
			func = invoiceModuleApi.getbyAvailableList
		}
		return func
	}
	useEffect(() => {
		if (visible) {
			console.log('props.dataInit', props.dataInit)
			// 回显
			if (props.dataInit.length) {
				//去下重，防止key重复bug
				let dataInit = removeDuplicate(props.dataInit)
				let arr: any = []
				let amount = 0
				dataInit.forEach(item => {
					arr.push(item.key)
					amount += Number(item.availableAmount)
				})
				setInvoicePretaxAmount(amount)
				setSelectedRowKeys(arr)
				setSelectedDataSource(dataInit)
			} else {
				setInvoicePretaxAmount(0)
				setSelectedRowKeys([])
				setSelectedDataSource([])
			}

			getList()
		}
	}, [props.visible])

	useEffect(() => {
		// 此时应该金额归0，清楚回显
		// setInvoicePretaxAmount(0)
		// setSelectedRowKeys([])
		if (visible) {
			getList()
		}
	}, [searchParams, pagination.current, pagination.pageSize])

	useEffect(() => {
		sethasNCheckFlag(selectedDataSource.filter(item => item.checkFlag === 0).length > 0)
		console.log(`selectedDataSource = ${selectedDataSource.length}`)
	}, [selectedDataSource])
	const { visible, onOk, onCancel } = props

	// 得到发票列表
	const getList = () => {
		let couList = []
		props?.couDataSource &&
			props.couDataSource.length > 0 &&
			props.couDataSource.map(item => {
				let obj: any = {}
				obj.couUuid = item.uuid
				obj.originalUuid = item.originalUuid
				couList.push(obj)
			})
		setLoading(true)
		getInvoiceListFunc &&
			getInvoiceListFunc()({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				couList,
				...searchParams,
			}).then(
				(res: any) => {
					pagination.total = res['total']
					if (res.list && res.list.length) {
						res.list.forEach(i => {
							// 给每一个发票添加唯一的 key
							i.key = i['invoiceNumber']
						})
						var arr = res.list

						const filtered = removeDuplicate(arr)
						// console.log(filtered)
						setDataSource([...filtered])
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => {
					setDataSource([])
					setLoading(false)
				}
			)
	}
	const getColumns = () => {
		const columnsDic = {
			checkState: getInvoicCheckStatusCols().checkState,
			/*
			invoiceCode: {
				fixed: 'left',
			},
			invoiceNumber: {
				fixed: 'left',
			},
			*/
			invoiceType: {
				width: 150,
			},
		}
		const colns = getColumnsByPageName('selectInvoiceModel', columnsDic)
		const evenlyColns = evenlyTableColunms(colns)
		return evenlyColns
	}
	const onSearchInvoice = (params: { invoiceDate: any; invoiceNumber: string }) => {
		pagination.current = 1
		let endDate, startDate
		if (params && params.invoiceDate && params.invoiceDate.length === 2) {
			endDate = timeToStamp(params.invoiceDate[1], 'end')
			startDate = params.invoiceDate[0]
			delete params.invoiceDate
		}

		setSearchParams({
			...params,
			startDate,
			endDate,
		})
	}
	const onResetInvoice = () => {
		pagination.current = 1
		setSearchParams({})
	}
	// 复选框选中的行发生改变
	const handleSelectChange = (record, selected, selectedRows, nativeEvent) => {
		console.log(record)

		if (selected) {
			setSelectedRowKeys(rowKeys => {
				return [...rowKeys, record.key]
			})
			setSelectedDataSource(lastDataSource => {
				return [...lastDataSource, record]
			})
			setInvoicePretaxAmount(lastInvoicePretaxAmount => Number(lastInvoicePretaxAmount) + Number(record.availableAmount))
		} else {
			let index = selectedRowKeys.indexOf(record.key)
			let seleRowKeys = deepExtend([], selectedRowKeys)
			let seleDataSource = deepExtend([], selectedDataSource)
			seleRowKeys.splice(index, 1)
			seleDataSource.splice(index, 1)
			setSelectedRowKeys(seleRowKeys)
			setSelectedDataSource(seleDataSource)

			setInvoicePretaxAmount(lastInvoicePretaxAmount => Number(lastInvoicePretaxAmount) - Number(record.availableAmount))
		}
	}
	const handleSelectAllChange = (selected, selectedRows, changeRows) => {
		let sumPretaxAmount = 0
		// 得到所有改变你的keys
		let changeRowkeys = changeRows.reduce((pre, next) => {
			sumPretaxAmount += Number(next.availableAmount)
			pre.push(next.key)
			return pre
		}, [])

		if (selected) {
			setSelectedDataSource(lastDataSource => {
				return [...lastDataSource, ...changeRows]
			})
			setSelectedRowKeys(rowKeys => {
				return [...rowKeys, ...changeRowkeys]
			})
			setInvoicePretaxAmount(lastInvoicePretaxAmount => lastInvoicePretaxAmount + sumPretaxAmount)
		} else {
			let seleRowKeys = deepExtend([], selectedRowKeys)
			let seleDataSource = deepExtend([], selectedDataSource)
			changeRowkeys.forEach(key => {
				let index = seleRowKeys.indexOf(key)
				if (index !== -1) {
					seleRowKeys.splice(index, 1)
					seleDataSource.splice(index, 1)
				}
			})
			setSelectedRowKeys(seleRowKeys)
			setSelectedDataSource(seleDataSource)

			setInvoicePretaxAmount(lastInvoicePretaxAmount => lastInvoicePretaxAmount - sumPretaxAmount)
		}
	}
	const handlePageNumChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const handlePageSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	return (
		<BaseDrawer
			visible={visible}
			title={'选择发票'}
			width="1000px"
			onClose={onCancel}
			onOk={() => {
				if (selectedDataSource.length) {
					if (props.actualFinanceSumAmountInYuan && invoicePretaxAmount < props.actualFinanceSumAmountInYuan) {
						message.error('所选发票可用金额总和需要大于等于质押融信金额')
						return
					}
					const filtered = removeDuplicate(selectedDataSource)
					onOk(deepExtend([], filtered))
				} else {
					message.warning('请选择发票')
				}
			}}
		>
			<Box>
				<OperatingArea>
					<SearchBar
						optionData={{
							checkFlag: [
								{ key: 0, name: '验真失败' },
								{ key: 1, name: '验真成功' },
								{ key: 9, name: '验真中' },
							],
						}}
						pageName="selectInvoiceModalF"
						showLabel={true}
						onSubmit={onSearchInvoice}
						onClear={onResetInvoice}
					/>
				</OperatingArea>
				<div className="title">
					<div className="selected">已选发票金额：￥{invoicePretaxAmount}</div>
				</div>
				{hasNCheckFlag && <Alert message={'当前选择的发票中包含验真失败或发票异常的发票，请注意检查'} type="warning" showIcon style={{ marginTop: '10px' }} />}
				{props?.invoiceDayOffset > 0 && (
					<Alert message={`发票有有效期限制，仅可使用开票日期在${props?.invoiceDayOffset}天的发票`} type="warning" showIcon style={{ marginTop: '10px' }} />
				)}
				<div style={{ overflowX: 'scroll' }}>
					<BaseTable
						rowSelection={{
							selectedRowKeys: selectedRowKeys,
							onSelect: handleSelectChange,
							onSelectAll: handleSelectAllChange,
						}}
						columns={getColumns()}
						onPageChange={handlePageNumChange}
						onSizeChange={handlePageSizeChange}
						{...pagination}
						dataSource={dataSource}
						// scroll={{ x: 1200, y: 159 }}
						// scroll={{ x: 1200 }}
						loading={loading}
					/>
				</div>
			</Box>
		</BaseDrawer>
	)
}

const Box = styled.div`
	/* padding: 10px 24px; */
	.ant-alert {
		margin-bottom: 10px;
	}
	.title {
		padding: 5px 10px;
		display: flex;
		background: #ececec;
		border-radius: 3px;
		margin-bottom: 10px;
		div {
			flex: 1;
		}
	}
	.ant-table-body {
		height: 150px;
	}
	.ant-table-container table > thead > tr:first-child th:first-child {
		padding: 0 !important;
	}
`

export default SelectInvoice
