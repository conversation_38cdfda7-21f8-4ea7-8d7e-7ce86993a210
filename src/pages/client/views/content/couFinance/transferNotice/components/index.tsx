import React, { useEffect, useState } from 'react'
import { Tooltip } from 'antd'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import OperatingArea from '@src/globalComponents/OperatingArea'
import SearchBar from '@src/pages/client/components/SearchBar'
import PDFViewer from '@globalComponents/PDFViewer'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import { financeModuleApi } from '@src/pages/client/api'
import styled from 'styled-components'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
interface NoticeProps {
	tablePageName: string
}

const LogsList = (props: NoticeProps) => {
	let { tablePageName } = props
	const [loading, setLoading] = useState(false)
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [searchParams, setSearchParams] = useState<{
		financeCompanyUuid?: string
		financeNumber?: string
	}>({})

	//pdf预览弹框控制
	const [pdfVisible, setPdfVisible] = React.useState<boolean>(false)
	const [pdfUrl, setPdfUrl] = React.useState<string>('')

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	function getList() {
		setLoading(true)

		financeModuleApi
			.getTransferNotice({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...searchParams,
			})
			.then(
				res => {
					pagination.total = res['total']
					if (res.list && res.list.length > 0) {
						setDataSource([...res.list])
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => {
					pagination.total = 0
					setLoading(false)
					setDataSource([])
				}
			)
	}

	//获取table的column
	const getColumns = () => {
		const columnsDic = {
			fromName: {
				title: '发送方',
				render: (text: any, record: any) => {
					return (
						<Tooltip placement="topLeft" title={record.fromName}>
							{record.fromName}
						</Tooltip>
					)
				},
			},
			toName: {
				title: '接收方',
			},
			createTime: {
				title: '发送日期',
			},
			operation: {
				unit: 10,
				render: (text: any, record: any) => {
					return (
						<div>
							{
								<span
									className="link"
									onClick={() => {
										setPdfUrl(record.fileInfo)
										setPdfVisible(true)
									}}
								>
									查看
								</span>
							}
						</div>
					)
				},
			},
		}

		// return getColumnsByPageName(tablePageName, columnsDic)
		return evenlyTableColunms(getColumnsByPageName(tablePageName, columnsDic))
	}

	const handlePageChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	const onSubmit = (params: any) => {
		const financeCompanyUuid: string = params['uuid'] ? params['uuid'] : ''
		const financeNumber: string = params['financeNumber'] ? params['financeNumber'] : ''
		const paramsObj: any = {
			financeCompanyUuid,
			financeNumber,
		}
		pagination.current = 1
		setPagination({ ...pagination })
		setSearchParams(paramsObj)
	}
	const onClear = () => {
		setSearchParams({})
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar pageName="tansferNotice" onSubmit={onSubmit} onClear={onClear} />
					</OperatingArea>
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						rowKey="id"
					/>
				</div>
			</LayoutSlot>
			<PDFViewer title="" visible={pdfVisible} pdfUrl={pdfUrl} onCancel={() => setPdfVisible(false)} />
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
`

export default LogsList
