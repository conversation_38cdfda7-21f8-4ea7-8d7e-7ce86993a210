import React from 'react'
import RongXinCreate from '../../components/rongXinCreate'
import { transferModuleApi } from '@src/pages/client/api'
import { creditStatus } from '@src/pages/client/config/companyCreditManage'

const CBuildOperatorRongXinCreate = () => {
	return <RongXinCreate isCBuildOperator={true} isCTeamOperator={false} getCardDataFunc={transferModuleApi.cBuildOperatorCouAmount} />
}
export default CBuildOperatorRongXinCreate
