import React, { useRef, useState, useEffect } from 'react'
import { But<PERSON>, DatePicker, Form, Input, message, Select, Spin, Alert, Modal } from 'antd'
import { commonModuleApi, invoiceModuleApi } from '@src/pages/client/api'
import { invoiceInfo, getInvoiceData, invoiceType, olderInvoiceInfo } from '@factor/config/invoice/invoiceCommon'
import { useNavigate } from 'react-router-dom'
import { UploadOutlined } from '@ant-design/icons'
import { timeToStamp, sleep } from '@utils/timeFilter'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import FileViewer from '../components/FileViewer'
import moment from '@utils/moment'
import styled from 'styled-components'
export interface PropsStruct {
	/**
	 * 展示弹窗
	 */
	visible: boolean
	/**
	 * 发票有限期
	 */
	invoiceDayOffset: number
	/**
	 * 取消显示
	 */
	closeModal?: (value: any) => void
}

const FormItem = Form.Item
const Option = Select.Option

export enum OcrInvoiceState {
	GOING = 1, //处理中
	SUCCESS = 2, //识别成功
	FAIL = 3, //识别失败
}

export default function NewUploadInvoice(props: PropsStruct) {
	const [modal, contextHolder] = Modal.useModal()
	const naviGator = useNavigate()
	const [form] = Form.useForm()
	const [invoiceList, setInvoiceList] = useState<any[]>([])

	const [filePath, setFilePath] = useState<string>('')
	const [fileTitle, setFileTitle] = useState<string>('')
	const [fileType, setFileType] = useState<string>('')
	const [fileVisible, setFileVisible] = useState<boolean>(false)

	const [loading, setLoading] = useState<boolean>(false)
	const [tip, setTip] = useState<string>('')
	const inputRef = useRef<any>(null)
	const [currentTime, setCurrentTime] = useState<Date | null>(null)

	let loopData = useRef({
		loopStartTime: null, //轮询开始计时时间
		ocrFinishTaskIds: [], //ocr识别发票完成的任务Id集合
		ocrGoingTaskIds: [], //ocr识别发票进行中的任务Id集合
		ocrInvoiceMap: new Map(), //ocr识别发票信息
		intervalTime: 2000, //轮询的间隔时间
	})

	useEffect(() => {
		getCurrentTime()
	}, [])

	const getCurrentTime = () => {
		commonModuleApi.syncTime().then(res => {
			setCurrentTime(new Date(res))
		})
	}
	//手动触发input事件
	function check() {
		const ie: boolean = navigator.appName == 'Microsoft Internet Explorer' ? true : false
		if (ie) {
			inputRef.current.click()
		} else {
			const a = document.createEvent('MouseEvents') //FF的处理
			a.initEvent('click', true, true)
			inputRef.current.dispatchEvent(a)
		}
	}

	function showPreview(source: any) {
		if (!window['FileReader']) {
			message.warning('此浏览器版本过低，请升级后使用')
			return
		}
		const uploadFileList: any = source.target.files

		const filesDefer: any[] = []

		if (uploadFileList.length + invoiceList.length > 30) return message.warning('最多添加30张发票')
		setLoading(true)
		setTip('添加发票中')
		for (let i = 0, len = uploadFileList.length; i < len; i++) {
			const ext = uploadFileList[i].name.split('.').pop().toLowerCase()
			if (['jpeg', 'jpg', 'pdf'].indexOf(ext) === -1) {
				message.error('文件格式错误，只支持pdf、jpeg、jpg格式')
				setLoading(false)
				return
			} else if (uploadFileList[i].size > 1024 * 1024) {
				message.error('文件大小不能超过1M')
				setLoading(false)
				return
			} else {
				filesDefer.push(
					new Promise((resolve, reject) => {
						const fileRender = new FileReader()
						fileRender.readAsDataURL(uploadFileList[i])
						fileRender.onload = function (e: any) {
							const arr = e.target.result.split(',')
							let base64 = ''
							if (e.target.result) base64 = arr[arr.length - 1]

							// 如果发票文件已存在于发票列表中，则可以继续添加
							const key: any = Math.random() * 10 + uploadFileList[i].name
							invoiceModuleApi
								.ocr({
									fileName: uploadFileList[i].name,
									invoiceBase64Str: base64,
								})
								.then((result: any) => {
									if (result?.returnCode % 1000 === 0) {
										resolve({
											taskId: result.data,
											invoiceMap: {
												fileObj: uploadFileList[i],
												key,
												fileName: uploadFileList[i].name,
												filePath: uploadFileList[i].path,
												base64,
											},
										})
									} else {
										reject(result.returnDesc)
										message.error(result.returnDesc)
										setLoading(false)
									}
								})
								.catch(e => {
									reject(e)
									message.error('发票文件上传失败')
									setLoading(false)
								})
						}
					})
				)
				// ocr识别结束
			}
		}

		Promise.all(filesDefer).then(
			async res => {
				if (uploadFileList.length > res.length) message.error(`文件重复，有${uploadFileList.length - res.length}个文件上传失败`)
				let currentTime = await commonModuleApi.syncTime().catch(err => {
					console.log(err)
				})
				//设置轮询的开始时间
				loopData.current.loopStartTime = currentTime ? new Date(currentTime) : new Date()
				let { ocrGoingTaskIds, ocrInvoiceMap } = loopData.current
				for (let item of res || []) {
					ocrGoingTaskIds.push(item.taskId)
					ocrInvoiceMap.set(item.taskId, item.invoiceMap)
				}
				getOcrInvoiceResult()
				// 解决两次上传的文件相同事件不触发
				source.target.value = ''
			},
			reason => {
				console.log('reason: ', reason)
			}
		)
	}

	//查询轮询时间是否超时,轮询最长时间设置为2分钟
	const onCheckTimeout = async () => {
		let { loopStartTime } = loopData.current
		if (!loopStartTime) {
			return false
		}
		let currentTime = await commonModuleApi.syncTime().catch(err => {
			console.log(err)
		})
		currentTime = currentTime ? new Date(currentTime) : new Date()
		const time = currentTime.getTime() - loopStartTime.getTime()
		return time < 120000
	}

	//查询发票结果
	const getOcrInvoiceResult = async () => {
		try {
			let { ocrFinishTaskIds, ocrGoingTaskIds, ocrInvoiceMap } = loopData.current
			let list = await invoiceModuleApi.ocrInvoiceResult(ocrGoingTaskIds).catch(e => {
				console.log(e)
			})
			let invoiceList = [],
				invoiceGoingList = []
			if (Array.isArray(list)) {
				for (let item of list) {
					let { taskId, taskStatus, invoiceMap } = item
					if ([OcrInvoiceState.SUCCESS, OcrInvoiceState.FAIL].includes(taskStatus)) {
						if (!ocrFinishTaskIds.includes(taskId)) {
							ocrFinishTaskIds.push(taskId)
							let invoiceInfo = ocrInvoiceMap.get(taskId)
							invoiceInfo['result'] = Array.isArray(invoiceMap?.result?.object_list) ? invoiceMap.result.object_list[0] : []
							invoiceList.push(invoiceInfo)
						}
						if (ocrGoingTaskIds.includes(taskId)) {
							ocrGoingTaskIds = ocrGoingTaskIds.filter(item => item !== taskId)
						}
					} else if ([OcrInvoiceState.GOING].includes(taskStatus)) {
						let invoiceInfo = ocrInvoiceMap.get(taskId)
						invoiceGoingList.push(invoiceInfo)
					}
				}
				invoiceList.length > 0 && setInvoiceValue(invoiceList)
			}
			//若还存在进行中的任务且没有超过总轮询时间，需要继续轮询
			if (ocrGoingTaskIds.length > 0 && (await onCheckTimeout())) {
				await sleep(loopData.current.intervalTime)
				await getOcrInvoiceResult()
				//轮询结束，还有数据处理处理中时，添加空的发票表单
			} else if (invoiceGoingList.length > 0) {
				invoiceGoingList.length > 0 && setInvoiceValue(invoiceGoingList)
			}
		} catch (e) {
			console.log(e)
			setLoading(false)
		} finally {
			setLoading(false)
		}
	}

	//发票识别回填值
	const setInvoiceValue = (res: any) => {
		if (res && res.length) {
			res.forEach((i: any) => {
				if (i.result && i.result.item_list && i.result.item_list.length > 0) {
					let invoicType: invoiceType = isOurType(i.result.type)
					const invoiceData = getInvoiceData(invoicType, i.result.item_list)
					const obj: any = {}
					//发票类型
					if (invoicType) setInvoiceType(invoicType, obj)
					obj.initialValue = {
						[`invoiceCode${i.key}`]: invoiceData.invoiceCode,
						[`invoiceNumber${i.key}`]: invoiceData.invoiceNumber,
						[`ivOpenDate${i.key}`]: invoiceData.date,
						[`pretaxAmount${i.key}`]: invoiceData.amount,
						[`amount${i.key}`]: invoiceData.amountExcludeTax,
						[`checkCode${i.key}`]: invoiceData.checkCode ? invoiceData.checkCode.slice(length - 6) : '',
						[`invoiceType${i.key}`]: invoicType,
						[`buyerTaxName${i.key}`]: invoiceData.buyerTaxName,
						[`sellerTaxName${i.key}`]: invoiceData.sellerTaxName,
					}
					obj.fileName = i.fileName
					obj.filePath = i.filePath
					obj.key = i.key
					obj.fileObj = i.fileObj
					obj['invoiceCode'] = invoiceData.invoiceCode
					obj['invoiceNumber'] = invoiceData.invoiceNumber
					obj['ivOpenDate'] = invoiceData.date
					obj['pretaxAmount'] = invoiceData.amount
					obj['amount'] = invoiceData.amountExcludeTax
					obj['checkCode'] = invoiceData.checkCode ? invoiceData.checkCode.slice(length - 6) : ''
					obj['invoiceType'] = invoicType
					obj[`buyerTaxName`] = invoiceData.buyerTaxName
					obj[`sellerTaxName`] = invoiceData.sellerTaxName

					// 给每个发票对象添加一个 base64 字段，便于后期预览
					obj[`base64`] = i.base64
					invoiceList.push(obj)
				} else invoiceList.push({ key: i.key, fileName: i.fileName, filePath: i.filePath, fileObj: i.fileObj, base64: i.base64 })
			})
		}
		setInvoiceList([...invoiceList])
	}
	//识别填入发票类型 修改对应必选项
	const setInvoiceType = (value: invoiceType, obj: any) => {
		//增值税专用票（）  增值税电子专用发票   	//机动车销售统一发票（）
		if (value === 'VATSpecial' || value === 'VehicleSales') {
			obj['invoiceVerifyCodeRequire'] = false //是否需要展示校验码后六位
			obj['invoiceAmountNoTaxRequire'] = true //是否需必填不含税金额
			obj['invoiceCodeRequire'] = true //是否需必填发票代码
		} else if (value === 'VATCommon') {
			//增值税普通发票 增值税电子普通发票
			obj['invoiceVerifyCodeRequire'] = true
			obj['invoiceAmountNoTaxRequire'] = false
			obj['invoiceCodeRequire'] = true
		} else if (value === 'ELEI_SVATI' || value === 'ELEI_OI') {
			//增值税普通发票 增值税电子普通发票
			obj['invoiceVerifyCodeRequire'] = true
			obj['invoiceAmountNoTaxRequire'] = false
			obj['invoiceCodeRequire'] = false
		}
	}
	//根据发票类型切换相关必选项
	const handleSelectChange = (value: invoiceType, index: number) => {
		invoiceList[index]['invoiceType'] = value
		setInvoiceType(value, invoiceList[index])
		setInvoiceList([...invoiceList])
		// 如果不含税金额字段有值则进行表单验证
		if (form.getFieldValue(`amount${invoiceList[index].key}`) > 0) {
			form.validateFields([`amount${invoiceList[index].key}`])
		}
		// form.validateFields([`amount${invoiceList[index].key}`])
	}
	//公用的刷新multiInvoiceList方法
	const handleInputChange = (e: any, index: number, field: string, key?: string) => {
		// 如果含税金额（pretaxAmount）发生改变且不含税金额必传，则触发不含税金额的表单验证
		if (field === 'pretaxAmount') {
			form.validateFields(['amount' + key])
		}
		invoiceList[index][field] = e.target.value
		setInvoiceList(invoiceList)
	}
	//禁选时间的规则
	const handleDisabledDate = (current: any) => {
		return current && current >= moment(currentTime).endOf('day')
	}
	//兼容点击：此刻选项  添加时间
	const handleDatePickerChange = (dates: any, dateStrings: string, index: number) => {
		invoiceList[index]['ivOpenDate'] = dateStrings
		setInvoiceList(invoiceList)
	}
	const handleDeleteFile = (index: number) => {
		invoiceList.splice(index, 1)
		setInvoiceList([...invoiceList])
	}

	//判断发票类型是否为系统识别范围,并转换为 老方案发票的 code 传给后端
	const isOurType = (type: string) => {
		let result: any = null
		invoiceInfo.forEach(item => {
			if (item.code === type) {
				result = item.olderCode
			}
		})
		return result
	}

	const getInvoiceList = () => {
		return (
			<ul className="invoice-ul">
				{invoiceList.map((item: any, index: number) => {
					return (
						<li key={item.key} className="invoice-li">
							<div className="form-list orderNumber">发票序号：{index + 1}</div>
							<div className="form-list">
								<FormItem
									className="form-item"
									name={`invoiceType${item.key}`}
									initialValue={item.initialValue && item.initialValue[`invoiceType${item.key}`]}
									rules={[{ required: true, message: '请选择发票类型' }]}
									label="发票类型"
								>
									<Select
										id={`invoiceType${item.key}`}
										placeholder="请选择发票类型"
										onChange={(value: never) => handleSelectChange(value, index)}
										getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
									>
										{olderInvoiceInfo.map((invoice: any) => {
											return (
												<Option value={invoice.code} key={invoice.code}>
													{invoice.name}
												</Option>
											)
										})}
									</Select>
								</FormItem>
								<FormItem
									className="form-item"
									name={`ivOpenDate${item.key}`}
									rules={[{ required: true, message: '请选择开票日期' }]}
									initialValue={item.initialValue && item.initialValue[`ivOpenDate${item.key}`] && moment(item.initialValue[`ivOpenDate${item.key}`])}
									label="开票日期"
								>
									<DatePicker
										format="YYYY-MM-DD"
										disabledDate={handleDisabledDate}
										onChange={(dates, dateStrings) => handleDatePickerChange(dates, dateStrings, index)}
									/>
								</FormItem>
								<FormItem
									className="form-item"
									name={`buyerTaxName${item.key}`}
									rules={[
										{
											message: '请输入买方名称',
											whitespace: true,
											required: true,
										},
									]}
									initialValue={item.initialValue && item.initialValue[`buyerTaxName${item.key}`]}
									label="买方名称"
								>
									<Input placeholder="请输入买方名称" onChange={e => handleInputChange(e, index, 'buyerTaxName')} maxLength={64} />
								</FormItem>
								<FormItem
									className="form-item"
									name={`sellerTaxName${item.key}`}
									rules={[
										{
											message: '请输入卖方名称',
											whitespace: true,
											required: true,
										},
									]}
									initialValue={item.initialValue && item.initialValue[`sellerTaxName${item.key}`]}
									label="卖方名称"
								>
									<Input placeholder="请输入卖方名称" onChange={e => handleInputChange(e, index, 'sellerTaxName')} maxLength={64} />
								</FormItem>

								<FormItem
									className="form-item"
									name={`invoiceNumber${item.key}`}
									rules={[
										{
											required: true,
											type: 'string',
											pattern: /^[a-zA-Z\d]+$/,
											message: '请输入发票号码，数字、字母组成',
											whitespace: true,
										},
									]}
									initialValue={item.initialValue && item.initialValue[`invoiceNumber${item.key}`]}
									label="发票号码"
								>
									<Input placeholder="请输入发票号码" onChange={e => handleInputChange(e, index, 'invoiceNumber')} maxLength={25} />
								</FormItem>
								{item.invoiceCodeRequire ? (
									<FormItem
										className="form-item"
										name={`invoiceCode${item.key}`}
										initialValue={item.initialValue && item.initialValue[`invoiceCode${item.key}`]}
										rules={[
											{
												required: true,
												type: 'string',
												pattern: /^[a-zA-Z\d]+$/,
												message: '请输入发票代码，数字、字母组成',
												whitespace: true,
											},
										]}
										label="发票代码"
									>
										<Input placeholder="请输入发票代码" onChange={(e: any) => handleInputChange(e, index, 'invoiceCode')} maxLength={12} />
									</FormItem>
								) : (
									''
								)}

								<FormItem
									className="form-item"
									name={`pretaxAmount${item.key}`}
									rules={[
										{
											required: true,
											pattern: /^(?!0+(?:\.0+)?$)(?:[1-9]\d{0,8}|0)(?:\.\d{1,2})?$/,
											whitespace: true,
											message: '请输入含税金额，整数最多9位，小数最多2位',
										},
									]}
									initialValue={item.initialValue && item.initialValue[`pretaxAmount${item.key}`]}
									label="含税金额"
								>
									<Input placeholder="请输入含税金额" onChange={(e: any) => handleInputChange(e, index, 'pretaxAmount', item.key)} />
								</FormItem>
								<FormItem
									className="form-item"
									name={`amount${item.key}`}
									rules={[
										{
											required: item.invoiceAmountNoTaxRequire,
											// pattern: /^(?!0+(?:\.0+)?$)(?:[1-9]\d{0,8}|0)(?:\.\d{1,2})?$/,
											// whitespace: true,
											// message: '请输入不含税金额，整数最多9位，小数最多2位，且不大于含税金额。',
											// 自定义校验规则
											validator: (rule, value, callback) => {
												const reg = /^(?!0+(?:\.0+)?$)(?:[1-9]\d{0,8}|0)(?:\.\d{1,2})?$/
												const pretaxAmount = form.getFieldValue(`pretaxAmount${item.key}`)

												const pretaxAmountValue = pretaxAmount ? pretaxAmount : undefined
												const amountValue = value ? value : 0
												if (item.invoiceAmountNoTaxRequire) {
													// 必传则需验证
													if (pretaxAmountValue) {
														if (pretaxAmountValue - 0 >= amountValue - 0 && reg.test(amountValue)) {
															return Promise.resolve()
														} else {
															return Promise.reject('请输入不含税金额，整数最多9位，小数最多2位，且不大于含税金额。')
														}
													} else {
														if (reg.test(amountValue)) {
															return Promise.resolve()
														} else {
															return Promise.reject('请输入不含税金额，整数最多9位，小数最多2位，且不大于含税金额。')
														}
													}
												} else {
													if (amountValue) {
														if (pretaxAmountValue && pretaxAmountValue - 0 >= amountValue - 0 && reg.test(amountValue)) {
															return Promise.resolve()
														} else {
															return Promise.reject('请输入不含税金额，整数最多9位，小数最多2位，且不大于含税金额。')
														}
													} else {
														return Promise.resolve()
													}
												}
											},
										},
									]}
									initialValue={item.initialValue && item.initialValue[`amount${item.key}`]}
									label="不含税金额"
								>
									<Input placeholder="请输入不含税金额" onChange={e => handleInputChange(e, index, 'amount')} />
								</FormItem>

								{item.invoiceVerifyCodeRequire ? (
									<FormItem
										className="form-item"
										name={`checkCode${item.key}`}
										rules={[
											{
												required: item.invoiceType === 'ELEI_SVATI' || item.invoiceType === 'ELEI_OI' ? false : true,
												pattern: /^[\d]{6}$/,
												message: '请输入校证码后6位',
												whitespace: true,
											},
										]}
										initialValue={item.initialValue && item.initialValue[`checkCode${item.key}`]}
										label="校验码后6位"
									>
										<Input placeholder="校验码后6位" onChange={e => handleInputChange(e, index, 'checkCode')} maxLength={6} />
									</FormItem>
								) : (
									''
								)}
								<FormItem className="form-item">
									<div className="invoiceItem" style={{ width: '100%' }}>
										<div className="invoiceEnclosure">发票附件</div>
										<div className="show-file" key={item.key}>
											<span className="link" onClick={() => handlerViewFile(item)}>
												{item.fileName}
											</span>
										</div>
									</div>
								</FormItem>
								<div className="invoiceItem" style={{ width: '100%' }}>
									<div className="show-file" key={item.key}>
										<Button className="delete" danger onClick={() => handleDeleteFile(index)}>
											删除发票
										</Button>
									</div>
								</div>
								<div className="errorText">{item.errorText}</div>
							</div>
						</li>
					)
				})}
			</ul>
		)
	}
	//文件预览
	const handlerViewFile = (fileObj: any) => {
		setFileVisible(true)
		setFileTitle(fileObj.fileName)
		setFilePath(fileObj.base64)
		setFileType(fileObj.fileObj.type)
	}
	const getFailList = (failArr: any, failedData: any, type: string) => {
		if (failArr.length) {
			// added by John 2022-6-12，之前的两重循环
			invoiceList.forEach(item => {
				let code
				if (item['invoiceType'] !== 'ELEI_SVATI' && item['invoiceType'] !== 'ELEI_OI') {
					code = `${item.invoiceCode}_${item.invoiceNumber}`
				} else {
					code = `_${item.invoiceNumber}`
				}
				if (failArr.includes(code)) {
					if (type === 'error') item.errorText = '发票数据异常，无法提交'
					else if (type === 'repeat') item.errorText = '发票已存在，无法提交'
					else if (type === 'overdue') item.errorText = '发票超过有效期，无法提交'
					else item.errorText = '发票验真失败，无法提交'
					failedData.push(item)
				}
			})
		}
	}

	const invoiceSubmitFailMsg = (success, failed) => {
		// message.error({
		// 	content: '发票提交失败',
		// 	key: 'invoiceSubmitFail',
		// })
		modal.info({
			title: '提示',
			content: (
				<div>
					上传成功{success}张，上传失败{failed}张，请在页面查看失败原因
				</div>
			),
			okText: '已知悉',
		})
	}
	const handleCancel = () => {
		form.resetFields()
		// setVisible(false)
		setInvoiceList([])
		props?.closeModal(false)
	}
	//提交发票
	const handleSubmit = () => {
		form
			.validateFields()
			.then(values => {
				// added by john 2022-6-12,优化之前的发票code重复检查,array换成map，才是理论上的o(1)查找,O(n^2) => O(n)
				let len = invoiceList.length
				let map = new Map()
				for (let i = 0; i < len; i++) {
					let item = invoiceList[i]
					const code = item['invoiceCode'] + '_' + item['invoiceNumber']
					let checkIndex = map.get(code)
					if (checkIndex === undefined) {
						//不含有该code时
						map.set(code, i)
					} else {
						return message.warning(`第${checkIndex + 1}项、第${i + 1}项发票号码与代码重复`, 3.5)
					}
				}
				setLoading(true)
				setTip('上传发票中')
				const arr: any = []
				invoiceList.forEach((item: any) => {
					const fileObj = new File([item.fileObj], item.fileName, { type: 'application/json' })

					//新增逻辑 已经上传过则无需再次上传
					if (item.uploadUrl) arr.push(Promise.resolve({ fileUrl: item.uploadUrl, fileName: item.fileName }))
					else
						arr.push(
							new Promise(resolve => {
								commonModuleApi
									.uploadFile({ file: fileObj, type: 'invoice' }, true)
									.then((result: any) => {
										if (result) {
											item.uploadUrl = result
											resolve({ fileUrl: result, fileName: item.fileName })
										}
									})
									.finally(() => {
										// 上传失败后关闭loading 1.8.8 Clover-8466
										setLoading(false)
									})
							})
						)
				})
				Promise.all(arr)
					.then((resuleDataList: any) => {
						const params: { invoiceParaList: any } = {
							invoiceParaList: [],
						}
						setTip('提交发票中')
						for (let i = 0, len = invoiceList.length; i < len; i++) {
							const param: any = {}
							let invoiceItem = invoiceList[i]
							param['checkCode'] = invoiceItem.invoiceVerifyCodeRequire ? invoiceItem['checkCode'] : ''
							param['invoiceType'] = invoiceItem['invoiceType']
							param['buyerName'] = invoiceItem['buyerTaxName']
							param['supplierName'] = invoiceItem['sellerTaxName']
							param['invoiceNumber'] = invoiceItem['invoiceNumber']
							if (invoiceItem['invoiceType'] !== 'ELEI_SVATI' && invoiceItem['invoiceType'] !== 'ELEI_OI') {
								param['invoiceCode'] = invoiceItem['invoiceCode']
							} else {
								param['invoiceCode'] = ''
							}
							param['pretaxAmount'] = invoiceItem['pretaxAmount']
							param['amount'] = invoiceItem['amount']
							param['invoiceDate'] = timeToStamp(invoiceItem['ivOpenDate'], 'begin')
							param['fileUrl'] = JSON.stringify({
								// 发票文件数组改对象
								[resuleDataList[i]['fileName']]: resuleDataList[i]['fileUrl'],
							})
							params.invoiceParaList.push(param)
						}
						setLoading(true)
						invoiceModuleApi.batchCreate(params).then(
							res => {
								//res.returnCode = 17010;
								//res.data = null;
								setLoading(false)
								if (res.returnCode % 1000 === 0) {
									form.resetFields()
									message.success(`已提交${invoiceList.length}张发票`)
									setInvoiceList([])
									// naviGator('/content/couAssets/invoice')
									props?.closeModal(false)
								} else {
									const failedData: any = []
									if (
										Array.isArray(res.data?.errorList) &&
										Array.isArray(res.data?.repeatList) &&
										Array.isArray(res.data?.checkFailList) &&
										Array.isArray(res.data?.overdueList)
									) {
										let { errorList, repeatList, checkFailList, overdueList } = res.data
										getFailList(errorList, failedData, 'error')
										getFailList(repeatList, failedData, 'repeat')
										getFailList(checkFailList, failedData, 'check')
										getFailList(overdueList, failedData, 'overdue')
										if (invoiceList.length - failedData.length > 0) {
											// message.success(`已提交${invoiceList.length - failedData.length}张发票`)
											invoiceSubmitFailMsg(invoiceList.length - failedData.length, failedData.length)
										} else {
											invoiceSubmitFailMsg(invoiceList.length - failedData.length, failedData.length)
										}
										setInvoiceList(failedData)
									} else {
										// 1.8.8 Clover-4865
										invoiceSubmitFailMsg(invoiceList.length - failedData.length, failedData.length)
									}
								}
							},
							err => setLoading(false)
						)
					})
					.finally(() => {
						// 上传失败后关闭loading 1.8.8 Clover-8466
						setLoading(false)
					})
			})
			.catch(err => {
				console.log(err)
				// message.warning('请填写下方必填项')
				setLoading(false)
				message.error('发票信息有误，请检查后再提交')
				form.scrollToField(err['errorFields'][0]['name'][0], { behavior: 'smooth', block: 'start' })
			})
	}
	return (
		<Modal
			footer={null}
			bodyStyle={{
				paddingBottom: 0,
				maxHeight: '800px',
				overflow: 'hidden',
			}}
			style={{ top: 40 }}
			width={1200}
			open={props.visible}
			maskClosable={false}
			title={'新建发票'}
			onCancel={handleCancel}
		>
			<Box>
				<LayoutSlot>
					<Spin tip={tip} wrapperClassName="spin" spinning={loading}>
						<div className="card-wrapper">
							<div className="confirmPageButton">
								<Button className="big-btn" type="primary" onClick={handleSubmit} disabled={invoiceList.length ? false : true}>
									提交
								</Button>
								<Button size="large" style={{ fontSize: '14px', display: 'flex', alignItems: 'center', borderRadius: '5px' }} onClick={check}>
									<span className="icon">+</span>添加发票
								</Button>
								<input
									multiple
									id="myFileUpload"
									style={{ display: 'none' }}
									ref={inputRef}
									name="file"
									type="file"
									accept=".pdf, .jpeg, .jpg"
									onChange={showPreview}
								/>
								<p>当前页最多添加30张发票，支持PDF、JPEG、JPG，单个文件不超过1M</p>
							</div>
							<div className="overflowBox">
								<Alert
									message={`注意：请确保您上传的每个发票文件中，只包含一个发票的信息；${
										props?.invoiceDayOffset > 0 ? `发票有有效期限制，仅可使用开票日期在${props?.invoiceDayOffset}天的发票` : ''
									}`}
									type="warning"
									showIcon
									className="invoiceAlert"
								/>
								<div style={{ padding: '10px 0 15px', fontWeight: 500, fontSize: '16px', borderBottom: '1px solid #e9e9e9' }}>
									<span>发票数量：{invoiceList.length}</span>
								</div>
								<Form scrollToFirstError={true} form={form} layout="vertical">
									{getInvoiceList()}
								</Form>
							</div>

							<FileViewer title={fileTitle} visible={fileVisible} url={filePath} fileType={fileType} onCancel={() => setFileVisible(false)} />
						</div>
					</Spin>
					{contextHolder}
				</LayoutSlot>
			</Box>
		</Modal>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	min-height: 500px;
	.spin {
		height: 100%;
		background: #fff;
	}
	.confirmPageButton {
		font-size: 14px;
		display: flex;
		align-items: center;
	}
	.confirmPageButton p {
		margin-bottom: 0;
		color: rgba(0, 0, 0, 0.45);
	}
	.confirmPageButton .icon {
		width: 18px;
		height: 18px;
		line-height: 15px;
		font-weight: 500;
		text-align: center;
		border-radius: 50%;
		border: 1px solid #d0e2fa;
		background: #fff;
		color: #1f7bf4;
		font-size: 14px;
		text-align: center;
		display: inline-block;
		margin-right: 6px;
	}
	.text {
		font-size: 14px;
	}
	.show-file {
		display: flex;
		.delete {
			margin-left: auto;
		}
	}
	.errorText {
		color: #eb5b56;
	}

	// 绘制发票附件前面的必填符号
	.invoiceItem {
		position: relative;
		left: 10px;
		.invoiceEnclosure {
			margin-bottom: 8px;
			&::before {
				content: '*';
				position: absolute;
				left: -10px;
				font-size: 14px;
				color: red;
			}
		}
	}
	.form-list.orderNumber {
		margin-bottom: 0;
		position: relative;
		padding: 16px 0;
	}
	.invoice-ul {
		padding-top: 10px;
	}
	.invoice-li {
		border: 1px solid #e9e9e9;
		border-radius: 4px 4px 0px 0px;
		margin-bottom: 32px;
		.form-list {
			background: #fff;
			padding: 22px 50px;
			margin: 0;
			&.orderNumber {
				padding: 20px 50px;
				background: #fafafa;
				border-radius: 4px 4px 0px 0px;
			}
			.form-item {
				width: 44%;
				.ant-picker {
					width: 100%;
				}
			}
			.form-item:nth-child(2n + 1) {
				padding-right: 0;
				margin-right: 2%;
			}
			.form-item:nth-child(2n) {
				margin-left: auto;
			}
		}
	}
	.overflowBox {
		height: 714px;
		overflow-y: scroll;
	}
`
