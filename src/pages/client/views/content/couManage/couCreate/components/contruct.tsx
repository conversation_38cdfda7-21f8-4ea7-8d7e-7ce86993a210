import React, { useEffect, useState } from 'react'
import { Button, DatePicker, Form, Input, message, Modal, Upload, Tooltip } from 'antd'
import fileUploading from '@src/globalStore/fileUploading'
import { timeToStamp, preDisabledDate } from '@utils/timeFilter'
import styled from 'styled-components'
import { contractModuleApi } from '@src/pages/client/api'
import GetCompanyName from '@src/globalComponents/SearchComByName'
import { getUploadStatusValidator, customRequest } from '@src/globalBiz/gBiz'
import { formatUrl } from '@utils/format'
import { UploadOutlined } from '@ant-design/icons'
import moment from 'moment'

interface Props {
	/**
	 * 展示弹窗
	 */
	visible: boolean
	/**
	 * buyercompanyObj: 买方名称
	 */
	buyercompanyObj?: any
	/**
	 * companySellerName ： 卖方名称
	 */
	companySellerName?: any
	/**
	 * sellercompanyObj
	 */
	sellercompanyObj?: any
	/**
	 * 取消显示
	 */
	closeModal?: (value: any) => void
	/**
	 * 显示pdf
	 */
	showPdffn?: (value: any, isshow: boolean) => void
}
const CreateContruct = (props: Props) => {
	const ext: any = JSON.parse(localStorage.getItem('ext') || '{}')
	const company = ext?.user?.company
	const [confirmLoading, setConfirmLoading] = useState<boolean>(false)
	const [form] = Form.useForm()
	// const [visible, setVisible] = useState<boolean>(props.visible)
	const [contractFileList, setFileList] = useState<any[]>([])
	// 处理pdf预览
	const [path, setPath] = useState<string>('')
	const [pdfVisible, setPdfVisible] = useState<boolean>(false)

	const [contractData, setContractData] = useState<any>({
		contractCode: '', // 合同编号
		name: '', // 合同名称
		companySellerName: '',
		companyBuyerName: '',
		productName: '', // 产品名称
		amount: '', // 合同金额
		startDate: [], // 开始时间
		endDate: [], // 结束时间
		signDate: '', // 签订日期
	}) // 用于比较新建的合同买卖方不能为同一家
	const FormItem = Form.Item
	//新建/修改合同
	// 解构出来对应的属性
	const {
		contractCode = '', // 合同编号
		name = '', // 合同名称
		productName = '', // 产品名称
		amount = '', // 合同金额
		startDate = [], // 开始时间
		endDate = [], // 结束时间
		signDate = '', // 签订日期
	} = contractData

	useEffect(() => {
		if (props.visible) {
			form.setFieldsValue({
				amount: undefined,
				contractCode: undefined,
				companySellerName: props?.companySellerName || undefined,
				companyBuyerName: company?.id,
				name: undefined,
				productName: undefined,
				signDate: undefined,
				startEndDate: undefined,
			})
		}
	}, [props.visible])
	const createAndModifyContract = (values: any) => {
		console.log('新建和修改合同的values:', values)
		if (values['companyBuyerName'] === values['companySellerName']) {
			message.error('买方和卖方不能为同一公司')
			setConfirmLoading(false)
			return
		}
		const params: any = {}
		const curSignDate: number = timeToStamp(values['signDate'], 'begin')
		const curStartDate: number = timeToStamp(values['startEndDate'][0], 'begin')
		const curEndDate: number = timeToStamp(values['startEndDate'][1], 'begin')
		const fileArr: any = []
		contractFileList.forEach((i: any) => {
			if (i.url) {
				fileArr.push({ [i.name]: i.url })
			} else if (i.response?.data) {
				fileArr.push({ [i.name]: i.response?.data })
			}
		})
		if (!fileArr.length) {
			setConfirmLoading(false)
			return message.warning('请上传合同')
		}
		params['companySellerUuid'] = values['companySellerName']
		params['companyBuyerUuid'] = values['companyBuyerName']

		params['contractCode'] = values['contractCode']
		params['name'] = values['name']
		params['productName'] = values['productName']
		params['signDate'] = curSignDate
		params['startDate'] = curStartDate
		params['endDate'] = curEndDate
		params['amount'] = Number(values['amount']).toFixed(2)
		params['fileUrl'] = JSON.stringify(fileArr)
		setConfirmLoading(true)
		if (contractData['uuid']) {
			params['uuid'] = contractData.uuid
			contractModuleApi.modify(params).then(
				res => {
					form.resetFields()
					setFileList([])
					// getList()
					// setVisible(false)
					setContractData({})
					setConfirmLoading(false)
					message.success('修改成功')
				},
				() => {
					setConfirmLoading(false)
				}
			)
		} else {
			contractModuleApi.create(params).then(
				res => {
					form.resetFields()
					setFileList([])
					// getList()
					// setVisible(false)
					setContractData({})
					setConfirmLoading(false)
					message.success('创建成功')
					props?.closeModal(false)
				},
				() => {
					setConfirmLoading(false)
				}
			)
		}
	}

	// 新建和修改合同 的 确定和取消按钮
	const handleOk = () => {
		setConfirmLoading(true)
		form
			.validateFields()
			.then(
				values => {
					// if (!companyInfo['pubKey']) {
					// 	message.warning(`操作失败，${companyInfo['name']}尚未进行企业管理员注册`)
					// 	setConfirmLoading(false)
					// 	return
					// } // 2024/3/15 先注释 方便i调试
					if (!contractFileList || !contractFileList.length) {
						message.warning('请上传合同扫描件')
						setConfirmLoading(false)
						return
					}
					createAndModifyContract(values)
				},
				err => {
					setConfirmLoading(false)
				}
			)
			.finally(() => {
				setConfirmLoading(false)
			})
	}
	const handleCancel = () => {
		form.resetFields()
		// setVisible(false)
		props?.closeModal(false)
	}

	// 上传pdf的预览函数
	const handlePreview = (file: any) => {
		console.log('onPreview->file', file)
		// setPath(file.url)
		// setPdfVisible(true)
		props?.showPdffn(file.url, true)
	}
	const handleBeforeUpload = file => {
		// 如果文件列表的长度大于等于5则不能上传
		if (contractFileList.length >= 5) {
			message.error('最多只能上传5个文件')
			return Upload.LIST_IGNORE
		}
		const ext = file.name.split('.').pop().toLowerCase()
		const isPdf = ext === 'pdf'
		if (!isPdf) {
			message.error('文件格式错误，只支持pdf格式')
			return Upload.LIST_IGNORE
		}
		const isLt10M = file.size / 1024 / 1024 <= 10
		if (!isLt10M) {
			message.error('文件大小不能超过10M')
			return Upload.LIST_IGNORE
		}
		return Promise.resolve(file)
	}
	const handleUpload = (info: any) => {
		if (info?.file?.status) {
			const { file } = info
			const { status, uid } = file

			if (uid) {
				if (status === 'uploading') {
					fileUploading.addUploadingFile(uid)
				} else {
					fileUploading.removeUploadingFile(uid)
				}
			}
		}
		if (info.fileList && Array.isArray(info.fileList)) {
			let newFileList = []
			info.fileList.forEach(item => {
				if (!item.url && item.response?.data) {
					item.url = item.response?.data
				}
				newFileList.push(item)
			})
			setFileList(newFileList)
		}
	}
	return (
		<Modal
			confirmLoading={confirmLoading || fileUploading.uploadingNum > 0}
			onOk={handleOk}
			bodyStyle={{ paddingBottom: 0 }}
			style={{ top: 100 }}
			width={860}
			open={props.visible}
			maskClosable={false}
			title={'新建合同'}
			onCancel={handleCancel}
		>
			<ModalBox>
				<Form form={form} className="form" layout="vertical">
					<ul className="form-list contract-form-list">
						<FormItem
							className="form-item"
							name="contractCode"
							label="合同编号"
							initialValue={contractCode}
							rules={[
								{
									required: true,
									message: '请输入合同编号',
									whitespace: true,
								},
							]}
						>
							<Input placeholder="请输入合同编号" maxLength={50} />
						</FormItem>
						<FormItem
							className="form-item"
							name="name"
							label="合同名称"
							initialValue={name}
							rules={[
								{
									required: true,
									message: '请输入合同名称',
									whitespace: true,
								},
							]}
						>
							<Input placeholder="请输入合同名称" maxLength={100} />
						</FormItem>

						<GetCompanyName
							name="companyBuyerName"
							label="买方名称"
							placeholder="请输入买方名称"
							getCompany={contractModuleApi.modsearchByName}
							requireMsg="请选择买方"
							previewData={props?.buyercompanyObj}
							valuekey="id"
							companyType="C,S"
							key="companyBuyerName"
						/>

						<GetCompanyName
							name="companySellerName"
							label="卖方名称"
							placeholder="请输入卖方名称"
							getCompany={contractModuleApi.modsearchByName}
							requireMsg="请选择卖方"
							previewData={props?.sellercompanyObj}
							valuekey="id"
							companyType="C,S"
							key="companySellerName"
						/>
						<FormItem
							className="form-item"
							name="productName"
							label="产品名称"
							initialValue={productName}
							rules={[
								{
									required: true,
									message: '请输入产品名称',
									whitespace: true,
								},
							]}
						>
							<Input placeholder="请输入产品名称" maxLength={100} />
						</FormItem>
						<FormItem
							name="amount"
							label="合同金额"
							initialValue={amount}
							className="form-item"
							rules={[
								{
									required: true,
									pattern: /^(?!0+(?:\.0+)?$)(?:[1-9]\d{0,11}|0)(?:\.\d{1,2})?$/,
									max: 12,
									message: '请输入合同金额，整数最多12位，小数最多2位',
									whitespace: true,
								},
							]}
						>
							<Input placeholder="请输入合同金额" />
						</FormItem>
						<FormItem
							name="startEndDate"
							label="起止日期"
							className={'new form-item'}
							initialValue={contractData.length ? [moment(startDate), moment(endDate)] : null}
							rules={[{ required: true, message: '请设置起止日期' }]}
						>
							<DatePicker.RangePicker format="YYYY-MM-DD" getPopupContainer={(triggerNode: any) => triggerNode.parentNode} />
						</FormItem>
						<FormItem
							name="signDate"
							className="form-item"
							label="签订日期"
							initialValue={contractData.length ? moment(signDate) : null}
							rules={[{ required: true, message: '请设置签订日期' }]}
						>
							<DatePicker format="YYYY-MM-DD" disabledDate={preDisabledDate} getPopupContainer={(triggerNode: any) => triggerNode.parentNode} />
						</FormItem>
						<div className="line"></div>
						<FormItem
							name="fileUrl"
							label="合同扫描件"
							className="form-item"
							rules={[
								{
									required: true,
									message: '请上传合同文件',
								},
								{
									validator: getUploadStatusValidator('文件上传失败'),
								},
							]}
						>
							<Upload
								accept=".pdf"
								headers={{ 'wx-gw-target-system': 'factor' }}
								beforeUpload={handleBeforeUpload}
								onChange={handleUpload}
								data={{ type: 'transfer' }}
								action={formatUrl('/pledge-config/common/uploadFile')}
								fileList={contractFileList}
								customRequest={customRequest}
								onPreview={handlePreview}
							>
								<div className="fileBox">
									<Button icon={<UploadOutlined />}>上传</Button>
									<p style={{ color: '#7F7F7F' }}>pdf格式，最多5个文件，单个文件不超过10M。</p>
								</div>
							</Upload>
						</FormItem>
					</ul>
				</Form>
			</ModalBox>
		</Modal>
	)
}
const Box = styled.div`
	width: 100%;
	height: 100%;
`

const ModalBox = styled.div`
	.fileBox {
		display: flex;
		line-height: 32px;
		p {
			margin-left: 5px;
		}
	}
	// 起止时间中间的符号居中显示
	.new {
		.ant-picker-range-separator {
			position: relative;
			left: -18px;
		}
	}
	.edit {
		.ant-picker-range-separator {
			position: relative;
			left: -10px;
		}
	}

	.form {
		.form-list {
			padding: 8px;
		}
		.ant-upload-list-item-info .ant-upload-text-icon .anticon,
		.ant-upload-list-item-card-actions .anticon {
			color: #1890ff;
		}

		.ant-upload-list-item-name {
			color: #1890ff;
			cursor: pointer;
		}

		.ant-upload-list-item-error .ant-upload-list-item-name,
		.ant-upload-list-item-error .ant-upload-text-icon .anticon,
		.ant-upload-list-item-error .ant-upload-list-item-card-actions .anticon {
			color: #ff4d4f;
		}

		.ant-upload-list-item-card-actions-btn {
			opacity: 1;
		}
	}
	.contract-form-list {
		background: #fff;
	}
	.contract-form-list .form-item {
		width: 44%;
	}
	.contract-form-list .form-item:nth-child(2n + 1) {
		padding-right: 0;
		margin-right: 2%;
	}
	.contract-form-list .form-item:nth-child(2n) {
		margin-left: auto;
	}
	.contract-form-list .line {
		height: 1px;
		width: 100%;
		border-bottom: 1px solid #f0f0f0;
		margin-bottom: 20px;
	}
	.contract-form-list .ant-upload-list {
		width: 50%;
	}
	.contract-form-list .ant-picker-range,
	.ant-picker {
		width: 100%;
	}
`
export default CreateContruct
