import LayoutSlot from '@src/globalComponents/LayoutSlot'
import { commonModuleApi, financeModuleApi, transferModuleApi, contractModuleApi, invoiceModuleApi, companyInfoManageModuleApi } from '@src/pages/client/api'
import { Button, Form, Input, Checkbox, Row, Col, Table, Tooltip, message, Alert, Modal } from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import styled from 'styled-components'
import CalendarModal from '../components/Calendar'
import SmallTitle from '@src/globalComponents/SmallTitle'
import AttachmentTable from '@src/globalComponents/EditTable/AttachmentTable'
import Feedback from '@src/pages/client/components/feedback'
import { WxModal, WxModalProps } from '@cnpm/wx-rc/components/index'
import { sumitConfirmModal, getReceiverCompany, inputPayAmount } from '../components/createAndPayComponents'
import SelectContract from '@factor/components/pagesComponents/couPay/selectContract'
import { renderAmountInCent, renderAmount } from '@src/pages/client/config/TableColumnsRender'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import { stampToTime } from '@src/utils/timeFilter'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { history } from '@src/utils/router'
import PDFViewer from '@src/globalComponents/PDFViewer'
import { hasAuth, getStorage } from '@src/pages/client/biz/bizIndex'
import CompanyNameChangTip from '@factor/components/companyNameChangTip'
import IsFillInvoicingInfoTip from '@src/pages/client/components/isFillInvoicingInfoTip'
import SelectInvoice from './components/selectInvoice'
import { numberToThousands } from '@src/utils/timeFilter'
import { getInvoicCheckStatusCols } from '@src/globalBiz/invoiceBizModule'
import CreateContruct from './components/contruct'
import NewUploadInvoice from './components/newCreateInvoice'
import ProtocolViewer from '@src/globalComponents/protocol-pdf-viewer'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import { GetServiceCertList, GetServiceSignData, initPowerSign, GetServiceSignCert } from '@src/utils/ukey/writeObject'
import BaseTable from '@src/globalComponents/BaseTable'

const FormLayout = {
	labelCol: { span: 4 },
	wrapperCol: { span: 15 },
}

interface TdueDate {
	str: string
	date?: number
	days?: string | number
}
let timeout
const Index = () => {
	const ext: any = JSON.parse(localStorage.getItem('ext') || '{}')
	const company = ext?.user?.company
	const [form] = Form.useForm()
	const [modal, contextHolder] = Modal.useModal()
	const { uuid: companySellerUuid, openAcctStatus: openAcctStatus, id } = JSON.parse(localStorage.getItem('ext') || '{}')?.user?.company || {} //该用户的公司名称和类型
	// const { orgId } = JSON.parse(localStorage.getItem('ext') || '{}')?.user || {} //组织id
	//数据限制 支付金额  以及最大账期
	// 测试推送
	const {
		creditDueDate,
		quotaInCent,
		usedQuotaInCent,
		relationFiPubKey,
		frozenQuotaInCent,
		periodGraceCredit,
		limitAmountInCent,
		relationFi,
		teamAvailableQuotaInCent,
	} = getStorage('createCouInfo') || []
	//本融信的基础数据 从上级页面获取  用来获取最大支付额度 和到期日
	const [successVisible, setsuccessVisible] = useState<boolean>(false) //提交成功后的页面
	const [calendarVisible, setCalendarVisible] = useState<boolean>(false) //显示日历 选择账期
	const [dueDateObj, setDueDateObj] = useState<TdueDate>({ str: '' }) //兑付到期日的 数据  显示的 str  和提交的13位时间戳
	const [SelectContractVisible, setSelectContractVisible] = useState<boolean>(false) //选择合同弹窗
	const [contractData, setContractData] = useState<any>([]) //已选择合同数据
	const [hasBeneficiary, setHasBeneficiary] = useState<any>({}) //是否选择 收款方
	const [payAmount, setPayAmount] = useState<number>() //支付金额
	const [cCanUseAmount, setCCanUseAmount] = useState<number>(0) // 核心企业可用的额度
	const [agreemented, setAgreemented] = useState<boolean>(false) //同意协议
	const [loading, setLoading] = useState(false)
	const [protocolLoading, setProtocolLoading] = useState(false)
	const [couNo, setCouNo] = useState<string>('')
	const [goTransferId, setGoTransferId] = useState<string>()
	const [selectInvoiceVisible, setSelectInvoiceVisible] = useState<boolean>(false)
	const [invoiceDataSource, setInvoiceDataSource] = useState<any[]>([]) // 业务凭证发票表格中的数据
	const [hasNCheckFlag, sethasNCheckFlag] = useState<boolean>(false) // 是否包含为验真的发票
	const [totalPretaxAmount, setTotalPretaxAmount] = useState<number>(0) // 含税总金额
	const [totalAvailableAmount, setTotalAvailableAmount] = useState<number>(0) //可用金额总金额
	const [hasBankCard, setNoHasBankCard] = useState<boolean>(false) // 是否有江阴银行开户行
	const [ukeyInfo, setUkeyInfo] = useState<any>(null) //ukey信息
	const [agreement, setAgreement] = useState<any>(null) //无纸化合同协议信息
	const [ukeyError, setUkeyError] = useState<string>('')
	const [ischeckUkey, setIsCheckUkey] = useState<boolean>(false) //
	const [openingbankInfo, setOpeningbankInfo] = useState<any>({})
	const [inItUkeyMsg, setInItUkeyMsg] = useState('')
	// pdf预览弹框控制
	const [pdfVisible, setPdfVisible] = React.useState<boolean>(false)
	const [pdfUrl, setPdfUrl] = React.useState<string>('')
	const [showCreate, setShowCreate] = useState(false)
	const [showCreateInvoinve, setShowCreateInvoinve] = useState(false)
	const validateMessage = '请输入金额，整数最多9位，小数最多2位，不大于可用额度。'
	const validateReg = /^(?!0+(?:\.0+)?$)(?:[1-9]\d{0,8}|0)(?:\.\d{1,2})?$/
	const [buyercompanyObj, setBuyerCompanyObj] = useState<any>({}) // 公司买方数组
	const [sellercompanyObj, setSellerCompanyObj] = useState<any>({}) // 公司卖方数组
	const attachmentTable = React.useRef(null as any)
	const [precheckData, setPrecheckData] = useState<any>({})
	const [aggreeAll, setAggreeAll] = useState(false)
	const checkBoxRef = useRef(null)
	const paperSubmitMark = JSON.parse(localStorage.getItem('ext') || '{}').user?.company?.paperSubmitMark
	const resultModalRef = React.useRef(null)

	const [ukeyHaveList, setUkeyHaveList] = useState<any[]>([])
	const [errorUkeyArray, setErrorUkeyArray] = useState<any[]>([])
	const [errorUkeyModal, setErrorUkeyModal] = useState<boolean>(false)
	const [ukeyChooseModal, setUkeyChooseModal] = useState<boolean>(false)
	const [validity, setValidity] = useState<string>('')
	const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([])
	const [chooseKeyData, setChooseKeyData] = useState<string>('')

	let isOpened = false

	const viewResultModalProps: WxModalProps = {
		modalProps: {
			title: '提示',
			width: 520,
			bodyStyle: {
				padding: '80px 20px',
			},
			footer: null,
			onCancel: () => {
				cancelModal()
			},
		},
	}
	const cancelModal = () => {
		if (ischeckUkey) {
			submitUkeyConfirm()
			return
		}
		submitConfirm()
	}

	useEffect(() => {
		// getProtocolTemplate()
		// 得到C（操作员、限额操作员）可以使用的额度
		preCheck()
		getCCanUseAmount()
		getCodes()
		checkBankCard()
		getClientUkeyList()
		return () => {
			clearTimeout()
		}
	}, [])

	useEffect(() => {
		if (aggreeAll) {
			setAgreemented(true)
		}
	}, [aggreeAll])

	const getClientUkeyList = async () => {
		let isOn = false
		try {
			let checkResult = await commonModuleApi.getGeneralConfiguration({ configKeys: ['create_paperless_switch'] })
			if (checkResult && checkResult.length) {
				isOn = checkResult.find(item => item.configKey === 'create_paperless_switch')?.configValue === 'ON'
			}
			setIsCheckUkey(isOn)
			if (!isOn) {
				return
			}
			await $.when(initPowerSign($.Deferred()))
			let bankCards = await financeModuleApi.getBankcardByCompanyId({ orgId: ext?.user?.orgId })
			if (bankCards && bankCards.length) {
				// let openingbank = await companyInfoManageModuleApi.queryJrBankInfoByNo({ acctno: bankCards[0]?.accountNum })
				// setOpeningbankInfo(openingbank)
				// let ukeylist = await financeModuleApi.getUkeyList({ accountNo: bankCards[0]?.accountNum })
				let ukeylist = [
					{
						operId: '***************',
						mediaNbr: 'JRCBHBC100001093',
						mediaCompany: 'CFCA',
						certRefnoSm: 'CN=JRCBHBC100001093,OU=Organizational-3,OU=TPC-S3,O=OCA21,C=CN',
						certRefno: '**********',
						certDn: '**********',
					},
					{
						operId: '***************',
						mediaNbr: 'JRCBFTG100000094',
						mediaCompany: 'CFCA',
						certRefnoSm: 'CN=JRCBFTG100000094,OU=Organizational-3,OU=TPC-S3,O=OCA21,C=CN',
						certRefno: '**********',
						certDn: '**********',
					},
					{
						operId: '***************',
						mediaNbr: 'JRCBFTC100000091',
						mediaCompany: 'CFCA',
						certRefnoSm: 'CN=JRCBFTC100000091,OU=Organizational-3,OU=TPC-S3,O=OCA21,C=CN',
						certRefno: '**********',
						certDn: '**********',
					},
				]
				const mediaNbrList = []
				ukeylist.forEach(item => {
					mediaNbrList.push(item.mediaNbr)
				})
				setUkeyHaveList(mediaNbrList)
				if (ukeylist && ukeylist.length) {
					getUkeyInfo(ukeylist || [])
					return
				}
				// setInItUkeyMsg('open')
				setErrorUkeyModal(true)
				return
			}
			// setInItUkeyMsg('open')
			setErrorUkeyModal(true)
		} catch (err) {
			if (isOn) {
				// setInItUkeyMsg('open')
				setErrorUkeyModal(true)
			}
			console.log('err', err)
		}
	}
	const getUkeyNo = str => {
		if (str) {
			const pattern = /CN=(\w+),/
			const match = str.match(pattern)
			return match ? match[1] : ''
		}
		return ''
	}

	const getSingleUkeyNo = str => {
		if (str) {
			const pattern = /CN=(\w+)/
			const match = str.match(pattern)
			return match ? match[1] : ''
		}
		return ''
	}

	const getUkeyNumber = (str, index) => {
		if (str) {
			let numlist = str.split(';')
			if (numlist && numlist[index]) {
				return numlist[index].split(',')[1]
			}
			return ''
		}
		return ''
	}

	const getUkeyInfo = async list => {
		let isSetUkeyMsg = false
		clearTimeout()
		timeout = setInterval(() => {
			let dtd = $.Deferred()
			$.when(GetServiceCertList(dtd)).done(async function (info) {
				let tmpList, isJrcb, isFindUkey

				if (!info) {
					setUkeyInfo(null)
					setValidity('invalid')
					!isOpened && setErrorUkeyModal(true)
					isOpened = true
					// !isSetUkeyMsg && setInItUkeyMsg('open')
					// isSetUkeyMsg = true
					return
				}
				if (list && list.length) {
					isFindUkey = list.some(item => info.includes(item?.mediaNbr))
				}
				console.log('%%%%' + isFindUkey)
				let errorUkeyArray = []
				let keyList = []
				const map = new Map()
				tmpList = info.split(';')
				tmpList = tmpList.filter(item => !!item)
				tmpList.forEach((item, index) => {
					const tempArray = item.split(',')
					if (!keyList.includes(tempArray[0])) {
						errorUkeyArray.push({ key: index, mediaNbr: tempArray[0] })
						keyList.push(tempArray[0])
					}
					const tempItem = map.get(tempArray[0])
					map.set(tempArray[0], !!tempItem ? tempItem.concat(';').concat(item) : item)
				})
				setErrorUkeyArray(errorUkeyArray)

				if (isFindUkey) {
					keyList = keyList.filter(item => item.startsWith('CN=JRCBHB') || item.startsWith('CN=JRCBFTG'))
					console.log('$$$$$$', keyList)
					if (keyList.length === 0) {
						if (!isOpened) {
							setValidity('invalid')
							setErrorUkeyModal(true)
						}
						isOpened = true
					} else {
						if (keyList.length === 1) {
							info = map.get(keyList[0])
						}
						setUkeyInfo(info)
						setValidity('valid')
						isSetUkeyMsg = true
						return
					}
				} else {
					if (!isOpened) {
						setValidity('invalid')
						setErrorUkeyModal(true)
					}
					isOpened = true
				}
				console.log('@@@@@@')
				setUkeyInfo(null)
				// !isSetUkeyMsg && setInItUkeyMsg('open')
				isSetUkeyMsg = true
			})
		}, 2000)
	}

	const clearTimeout = () => {
		if (timeout) {
			clearInterval(timeout)
			timeout = null
		}
	}

	//获取融信编号（仅用于开立以及付款承诺函使用）
	const getCodes = async () => {
		//后端获取编号
		const res = await commonModuleApi.getNo({ numberPrefix: 'COU' }).catch(err => {
			console.log('err', err)
		})
		if (res && res.length) {
			setCouNo(res[0])
		}
	}
	// 点击选择发票
	const handleSelectInvoiceOk = filterInvoiceList => {
		console.log(filterInvoiceList)

		//判断是否包含未验真的发票
		sethasNCheckFlag(filterInvoiceList.filter(item => item.checkFlag === 0).length > 0)
		let getTotalPretaxAmount = 0
		let getTotalAvailableAmount = 0
		let remainingAmount = Number(payAmount)
		let remainingAmountTmp = Number(payAmount)
		filterInvoiceList.sort((a, b) => a.availableAmount - b.availableAmount)
		filterInvoiceList.forEach((invoice, index) => {
			// 防止含税金额为null
			getTotalPretaxAmount += Number(invoice.pretaxAmount) ? Number(invoice.pretaxAmount) - 0 : 0
			getTotalAvailableAmount += Number(invoice.availableAmount) ? Number(invoice.availableAmount) - 0 : 0
			if (remainingAmount > 0) {
				const availableAmount = Number(invoice.availableAmount)
				invoice.applyAmount = Math.min(remainingAmount, availableAmount)
				remainingAmount -= invoice.applyAmount
			} else {
				invoice.applyAmount = 0
			}
		})
		if (getTotalAvailableAmount > remainingAmountTmp) {
			modal.info({
				title: '提示',
				content: (
					<div>
						勾选的发票可用金额合计【￥{getTotalAvailableAmount}】大于所需金额【￥{remainingAmountTmp}
						】，为避免浪费发票，系统将按照可用金额占用发票的可用金额，自动剔除无需占用的发票
					</div>
				),
				okText: '确定',
			})
		}

		setTotalPretaxAmount(getTotalPretaxAmount)
		setTotalAvailableAmount(getTotalAvailableAmount)
		setInvoiceDataSource(filterInvoiceList)
		setSelectInvoiceVisible(false)
	}
	//获取 C用户 可用融信额度  = 总金额 - 已使用- 冻结
	const getCCanUseAmount = () => {
		if (quotaInCent) {
			// 如果有 limitAmountInCent 字段就说明是限额操作员
			const availableQuotaInCent = Number(quotaInCent) - (Number(usedQuotaInCent) + Number(frozenQuotaInCent))
			if (teamAvailableQuotaInCent) {
				// 取管理员设置的项目可用开立额度和金融机构授信的企业可用额度，谁小取谁
				setCCanUseAmount(Math.min(Number(availableQuotaInCent), Number(teamAvailableQuotaInCent)))
			} else if (limitAmountInCent) {
				// 取 管理员设置的可用开立额度 金融机构授信的企业可用额度 小者
				setCCanUseAmount(Math.min(Number(availableQuotaInCent), Number(limitAmountInCent)))
			} else {
				setCCanUseAmount(availableQuotaInCent)
			}
		} else {
			message.error('信息获取错误，返回上级页面')
		}
	}

	//获取最大可用额度 createCou
	const getCouAmountSufix = () => {
		return '可用额度：¥' + renderAmountInCent(Number(cCanUseAmount))
	}

	//选择合同的 ok 回调函数
	const selectContract = values => {
		setContractData([values])
		//关闭合同 弹窗
		setSelectContractVisible(false)
	}

	//清除 已经选择的 合同   （基本的 信息发生改变 的时候 && 已经选）
	const clearSelectContranct = () => {
		setContractData([])
	}
	const clearSelectInvoice = () => {
		setInvoiceDataSource([])
		setTotalPretaxAmount(0)
		setTotalAvailableAmount(0)
	}
	//输入框的之发生改变的 回调函数
	const recordFormItem = (name: 'payAmount' | 'receiverUuid' | 'dueDate', data: any) => {
		let isChangeBaseInfo = false
		if (name === 'payAmount') {
			if (payAmount && contractData && contractData.length > 0) {
				if (data !== payAmount) isChangeBaseInfo = true
			}
			setPayAmount(Number(data))
		} else if (name === 'dueDate') {
			if (dueDateObj && dueDateObj['str'] != '' && contractData && contractData.length > 0) {
				if (data['str'] !== dueDateObj['str']) isChangeBaseInfo = true
			}
			form.setFieldsValue({
				dueDatePay: data.str,
			})
			setDueDateObj(data)
		} else if (name === 'receiverUuid') {
			if (hasBeneficiary && hasBeneficiary['uuid'] != '' && contractData && contractData.length > 0) {
				if (data['uuid'] != hasBeneficiary['uuid']) isChangeBaseInfo = true
			}
			!data['uuid'] ? (data['uuid'] = data['id']) : (data['uuid'] = data['uuid']) // 江阴银行没有uuid 同id
			setHasBeneficiary(data)
		}
		if (isChangeBaseInfo) {
			message.warning('您已修改支付信息，需要重新选择合同和发票')
			clearSelectContranct()
			clearSelectInvoice()
		}
		if (agreemented) {
			message.warning('您已修改开立信息，需要重新勾选生成无纸化合同')
			setAggreeAll(false)
			setAgreemented(false)
		}
	}

	//点击选择合同 ｜｜ 融信按钮时候的 检验基本的支付信息
	const selectBtnCheck = async (type: any) => {
		form
			.validateFields()
			.then(res => {
				type == 'invoice' ? setSelectInvoiceVisible(true) : setSelectContractVisible(true)
			})
			.catch(err => {
				message.warning(`请先完善支付信息，再选择${type === 'invoice' ? '发票' : '合同'}`)
			})
	}
	// checkBankcard
	const checkBankCard = async () => {
		const res = await transferModuleApi.checkBankcard({ orgId: id })
		if (!res) {
			// message.warning('不为江阴银行的账户！！！')
			setNoHasBankCard(true)
		}
	}

	const getStampResult = async key => {
		const thisUkeyInfo = filterUkeyInfo()
		console.log('thisUkeyInfo', thisUkeyInfo)
		const param = {
			recordID: agreement?.recordId || null,
			oriFilePath: agreement?.fileName || null,
			publishCompanyName: ext?.user?.company?.orgName || null,
			publishSocialCode: ext?.user?.company?.socialCreditCode || null,
			ukeyNo: getUkeyNo(thisUkeyInfo),
			signKey: key,
			idType: openingbankInfo?.idTp || 'M',
		}
		return new Promise(async (resolve, reject) => {
			try {
				let signDatadtd = $.Deferred()
				let SignCertDtd = $.Deferred()
				let sealContractInfo = await financeModuleApi.getUkeySealContract(param)
				if (!sealContractInfo?.fileDigest) {
					setUkeyError(sealContractInfo?.returnDesc)
					reject(sealContractInfo?.returnDesc)
					throw new Error(sealContractInfo?.returnDesc)
				}
				let signature = await $.when(
					GetServiceSignData(
						getUkeyNumber(thisUkeyInfo, 1),
						'sm3',
						sealContractInfo?.fileDigest ? `jrcbsign=${encodeURIComponent(sealContractInfo?.fileDigest)}` : '',
						signDatadtd
					)
				)
				if (signature?.responseData === 'E:1000') {
					reject(signature?.err)
					return
				}
				let certificate = await $.when(GetServiceSignCert(getUkeyNumber(thisUkeyInfo, 1), 'sm3', SignCertDtd))
				if (certificate?.responseData === 'E:1000') {
					reject(certificate?.err)
					return
				}
				const signParam = {
					recordID: agreement?.recordId || null,
					oriFilePath: agreement?.fileName || null,
					signature: signature?.responseData || null,
					publicKey: certificate?.responseData || null,
					digest: sealContractInfo?.fileDigest || null,
				}
				let countersignInfo = await financeModuleApi.getUkeySignContract(signParam)
				if (!countersignInfo?.fileName) {
					setUkeyError(countersignInfo?.returnDesc)
					reject(countersignInfo?.returnDesc)
					throw new Error(countersignInfo?.returnDesc)
				}
				resolve(countersignInfo?.fileName)
			} catch (err) {
				setUkeyError(err?.message)
				reject(err)
			}
		})
	}
	const modalTopCenterItem = () => {
		return (
			<div className="wx_Modal_top_center">
				<div className="wx_Modal_top_center_item">
					<p className="text">￥ {renderAmount(payAmount + '')}</p>
					<p className="label">支付金额</p>
				</div>
				<div className="wx_Modal_top_center_line"></div>
				<div className="wx_Modal_top_center_item">
					<p className="text">{stampToTime(dueDateObj.date, 6)}</p>
					<p className="label">兑付到期日</p>
				</div>
			</div>
		)
	}

	const submitUkeyConfirm = async () => {
		const thisUkeyInfo = filterUkeyInfo()
		let invoiceList = []
		invoiceDataSource.map(item => {
			if (item.applyAmount) {
				invoiceList.push({
					uuid: item.uuid,
					pretaxAmount: item.pretaxAmount,
					availableAmount: item.availableAmount,
				})
			}
		})
		let params = {
			contractUuid: contractData[0].uuid,
			invoiceList,
			couNo: couNo,
			createBase: {
				createCouAmountInCent: Number(payAmount) * 100,
				createRemark: form.getFieldValue('createRemark') || '',
				dueDate: dueDateObj.date,
				receiverUuid: form.getFieldValue('receiverUuid'),
			},
			creditorPubKey: relationFiPubKey,
			attachment: JSON.stringify(attachmentTable?.current?.getValues()),
			paperlessInfoId: null,
		}
		let companyName = hasBeneficiary.orgName
		sumitConfirmModal(
			'融信开立信息确认',
			modalTopCenterItem(),
			companyName,
			async () => {
				setLoading(true)
				try {
					let resultsignArea1 = await getStampResult('signArea1')
					if (!resultsignArea1) {
						return
					}
					let resultsignArea2 = await getStampResult('signArea2')
					if (!resultsignArea2) {
						return
					}
					const saveParams = {
						couNo: couNo || null,
						ukeyNo: getUkeyNo(thisUkeyInfo),
						fullUkeyNo: thisUkeyInfo || null,
						fileName: resultsignArea2 || null,
					}
					let saveResult = await financeModuleApi.getUkeySave(saveParams)
					if (!saveResult) {
						message.error(`${saveResult?.returnDesc || '保存失败'}`)
						return
					}
					params.paperlessInfoId = saveResult?.id || null
					console.log({ saveResult })
					let res = await transferModuleApi.create(params)
					if (res) {
						setGoTransferId(res)
						setsuccessVisible(true)
						setLoading(false)
					}
				} catch (err) {
					if (err.returnDesc.indexOf('所选发票使用金额总和需要大于等于支付金额') !== -1) {
						// message.error(err.returnDesc)
						clearSelectInvoice()
					}
					setLoading(false)
				}
			},
			() => {
				setLoading(false)
			}
		)
	}

	const onFinishUkeyCheckForCreateCou = () => {
		let tmpList = ukeyInfo.split(';')
		tmpList = tmpList.filter(item => item.startsWith('CN=JRCBHB') || item.startsWith('CN=JRCBFTG'))
		const keySet = new Set()
		tmpList.forEach(item => {
			const tmpArray = item.split(',')
			keySet.add(tmpArray[0])
		})
		if (validity === 'valid') {
			console.log('****', ukeyInfo)
			if (keySet.size > 1) {
				setUkeyChooseModal(true)
				setSelectedRowKeys([])
				setChooseKeyData('')
			} else {
				sumitUkeyForCreatCou()
			}
		}
	}

	const onFinishUkeyForCreateCou = async () => {
		// if (!Number(openAcctStatus)) { return message.warning('您没有江阴银行的账户！！！') }
		const thisUkeyInfo = filterUkeyInfo()
		if (contractData && contractData.length === 1 && payAmount && invoiceDataSource.length > 0) {
			if (!thisUkeyInfo) {
				message.warning(
					'未检测到有效的企业UKEY，请插入UKEY并确认UKEY背部序列号是JRCBHB或JRCBFTG开头。（提示：提交后，本企业审核岗审核时，需要插入另一把企业UKEY）'
				)
				return
			}
			// let isEmptyInvoice = invoiceDataSource.find(item => item.applyAmount === 0)
			// if (isEmptyInvoice) {
			// 	resultModalRef.current.show()
			// 	return
			// }
			submitUkeyConfirm()
		} else {
			form.validateFields()
			message.destroy()
			message.warning('请完善付款信息并选择合同或者发票')
		}
	}

	const submitConfirm = () => {
		let invoiceList = []
		invoiceDataSource.map(item => {
			if (item.applyAmount) {
				invoiceList.push({
					uuid: item.uuid,
					pretaxAmount: item.pretaxAmount,
					availableAmount: item.availableAmount,
				})
			}
		})
		const params = {
			contractUuid: contractData[0].uuid,
			invoiceList,
			couNo: couNo,
			createBase: {
				createCouAmountInCent: Number(payAmount) * 100,
				createRemark: form.getFieldValue('createRemark') || '',
				dueDate: dueDateObj.date,
				receiverUuid: form.getFieldValue('receiverUuid'),
			},
			creditorPubKey: relationFiPubKey,
			usedQuotaInCent: Number(usedQuotaInCent) + Number(frozenQuotaInCent),
			attachment: JSON.stringify(attachmentTable?.current?.getValues()),
		}
		let companyName = hasBeneficiary.orgName
		sumitConfirmModal(
			'融信开立信息确认',
			modalTopCenterItem(),
			companyName,
			() => {
				setLoading(true)
				transferModuleApi
					.create(params)
					.then(res => {
						//获取成功后 交易信息
						setGoTransferId(res)
						setsuccessVisible(true)
						setLoading(false)
					})
					.catch(error => {
						console.log(error)
						if (error.returnDesc.indexOf('所选发票使用金额总和需要大于等于支付金额') !== -1) {
							// message.error(error.returnDesc)
							clearSelectInvoice()
						}
						setLoading(false)
					})
			},
			() => {
				setLoading(false)
			}
		)
	}

	const onFinishForCreateCou = async () => {
		// if (!Number(openAcctStatus)) { return message.warning('您没有江阴银行的账户！！！') }
		if (contractData && contractData.length === 1 && payAmount && invoiceDataSource.length > 0) {
			// let isEmptyInvoice = invoiceDataSource.find(item => item.applyAmount === 0)
			// if (isEmptyInvoice) {
			// 	resultModalRef.current.show()
			// 	return
			// }
			submitConfirm()
		} else {
			form.validateFields()
			message.destroy()
			message.warning('请完善付款信息并选择合同或者发票')
		}
	}

	//选择工作日 确定事件
	const handleCalendar = (values: TdueDate) => {
		recordFormItem('dueDate', values)
		setCalendarVisible(false)
		form.validateFields(['dueDatePay'])
	}

	//获取合同的表格列
	const getContractColumns = () => {
		const columnsDic = {}
		let colns = getColumnsByPageName('couContractInform', columnsDic)
		// 表格列长度的简写形式 均等长度
		let columnIndexLengthMap = {}
		colns = evenlyTableColunms(colns, columnIndexLengthMap)

		return colns
	}
	const getInvoiceColumns = () => {
		const columnsDic = {
			checkState: getInvoicCheckStatusCols().checkState,
		}

		let colns = getColumnsByPageName('creatInvoice', columnsDic)
		// 表格列长度的简写形式 均等长度
		let columnIndexLengthMap = {
			3: 19,
			4: 19,
		}
		colns = evenlyTableColunms(colns, columnIndexLengthMap)

		return colns
	}
	const showUkeyProtocol = async () => {
		setProtocolLoading(true)
		let isContractData = contractData && contractData.length > 0
		let isUkeyInfo = ukeyInfo && ukeyInfo.length > 0
		if (isContractData && isUkeyInfo) {
			const params = {
				couNo: couNo || null, //COU编号
				toCompanyName: hasBeneficiary?.orgName || null, //卖方（接收方）公司名称
				toSocialCode: hasBeneficiary?.socialCreditCode || null, //卖方（接收方）公司信用代码
				couAmountInYuan: payAmount || null, //应付账款（融信金额）
				dueDate: dueDateObj?.str || null, //承诺付款日
				publishCompanyName: ext?.user?.company?.orgName || null, //开立方公司名称
				publishSocialCode: ext?.user?.company?.socialCreditCode || null, //开立方公司信用代码
			}
			const res = await financeModuleApi.getSyntheticContract(params).catch(err => {
				setUkeyError(err)
			})
			setProtocolLoading(false)
			if (!!res) {
				console.log('res', res)
				setPdfUrl(res?.filePath || 'pledge/operation-guide/2024/08/07/1a79c843-f59f-4b41-bfd5-f39e7f6c3fd4.pdf')
				setAgreement(res)
				setTimeout(() => {
					setPdfVisible(true)
				}, 0)
			}
		} else {
			message.warning(
				!isContractData
					? '请完善付款信息并选择合同'
					: '未检测到有效的企业UKEY，请插入UKEY并确认UKEY背部序列号是JRCBHB或JRCBFTG开头。（提示：提交后，本企业审核岗审核时，需要插入另一把企业UKEY）'
			)
			setProtocolLoading(false)
		}
	}

	const showProtocol = async () => {
		setProtocolLoading(true)
		if (contractData && contractData.length > 0) {
			const params = {
				couNo,
				couAmountInCent: Number(payAmount) * 100,
				dueDate: dueDateObj.date,
				fiUuid: relationFi,
				receiverUuid: form.getFieldValue('receiverUuid'),
			}
			const res = await transferModuleApi.previewCommitment(params).catch(err => {
				console.log('err', err)
			})
			setProtocolLoading(false)
			if (res !== undefined) {
				console.log('res', res)
				setPdfUrl(res)
				setTimeout(() => {
					setPdfVisible(true)
				}, 0)
			}
		} else {
			message.warning('请完善付款信息并选择合同')
			setProtocolLoading(false)
		}
	}
	const getAmountSufix = () => {
		return (
			<div className="suffix">
				{getCouAmountSufix()}
				<Tooltip title="供应商尚未接收融信，占用开立额度">
					<span>
						<QuestionCircleOutlined style={{ color: '#02A7F0', marginLeft: '5px' }} />
					</span>
				</Tooltip>
			</div>
		)
	}
	const getContractListFunc = () => {
		let func = null
		if (hasAuth('bl_couManage:COperatorRongXinCreate:list')) {
			func = contractModuleApi.getList
		} else if (hasAuth('bl_couManage:CTeamOperatorRongXinCreate:list')) {
			func = contractModuleApi.getTeamOperatorList
		} else if (hasAuth('bl_couManage:cBuildOperatorRongXinCreate:list')) {
			func = contractModuleApi.getBuildList
		}
		return func
	}
	const getInvoiceLisfFunc = () => {
		let func = null
		func = invoiceModuleApi.getList
		return func
	}

	// 打开创建合同tab
	const handleGotoAddContract = () => {
		// const { receiverUuid } = form.getFieldsValue()
		// let qs = { add: 'true' }
		// if (receiverUuid && hasBeneficiary?.id === receiverUuid) {
		// 	qs['companySellerName'] = hasBeneficiary?.orgName
		// 	qs['companySellerId'] = receiverUuid
		// }
		// window.open(`${window.location.origin}/#/content/couAssets/contract?${new URLSearchParams(qs).toString()}`)
		// setBuyerCompanyObj({ id: company?.id, orgName: company?.orgName }) // 默认买方为自身企业

		setBuyerCompanyObj({ id: company?.id, orgName: company?.orgName }) // 默认买方为自身企业
		if (hasBeneficiary['uuid']) {
			setSellerCompanyObj({ id: hasBeneficiary['uuid'], orgName: hasBeneficiary.orgName })
		}
		setShowCreate(true)
	}
	// 打开新建发票tab
	const handleGotoAddInvoice = () => {
		// window.open(`${window.location.origin}/#/content/couAssets/uploadInvoice`)
		setShowCreateInvoinve(true)
	}
	const preCheck = async () => {
		const res = await financeModuleApi.couPrecheck({ companyUuid: ext?.user?.company?.uuid, type: 1 }) //1-开立(核心企业)
		setPrecheckData(res)
	}

	const handleProtocolCheckEvent = () => {
		if (!aggreeAll) {
			ischeckUkey ? showUkeyProtocol() : showProtocol()
			setAgreemented(false)
		} else {
			setAgreemented(!agreemented)
		}
	}

	const getCheckBoxState = record => {
		return {
			disabled:
				(!record.mediaNbr.startsWith('CN=JRCBHB') && !record.mediaNbr.startsWith('CN=JRCBFTG')) || !ukeyHaveList.includes(getSingleUkeyNo(record['mediaNbr'])),
		}
	}

	const handleOnChange = (onSelectedRowKeys, selectedRows) => {
		setSelectedRowKeys(onSelectedRowKeys)
		setChooseKeyData(selectedRows[0].mediaNbr)
	}

	const ukeyChooseColumn = () => {
		return [
			{
				dataIndex: 'mediaNbr',
				title: 'UKEY序列号',
				render: (text, record) => {
					return (
						<span
							style={
								validity === 'valid' &&
								((!record.mediaNbr.startsWith('CN=JRCBHB') && !record.mediaNbr.startsWith('CN=JRCBFTG')) ||
									!ukeyHaveList.includes(getSingleUkeyNo(record.mediaNbr)))
									? { opacity: 0.5 }
									: { opacity: 1 }
							}
						>
							{text}
						</span>
					)
				},
			},
			{
				dataIndex: 'action',
				title: validity === 'valid' ? '有效说明' : '无效说明',
				render: (text, record) => {
					const mediaNbr = record['mediaNbr']
					let content = ''
					if (!mediaNbr.startsWith('CN=JRCBHB') && !mediaNbr.startsWith('CN=JRCBFTG')) {
						content = '版本错误'
					} else if (!ukeyHaveList.includes(getSingleUkeyNo(mediaNbr))) {
						content = '非本企业UKEY'
					} else {
						content = '有效'
					}
					return (
						<span
							style={
								validity === 'valid' &&
								((!mediaNbr.startsWith('CN=JRCBHB') && !mediaNbr.startsWith('CN=JRCBFTG')) || !ukeyHaveList.includes(getSingleUkeyNo(mediaNbr)))
									? { opacity: 0.5 }
									: { opacity: 1 }
							}
						>
							{content}
						</span>
					)
				},
			},
		]
	}

	const filterUkeyInfo = () => {
		console.log('&&&&&&', chooseKeyData)
		if (chooseKeyData !== '') {
			let tmpList = ukeyInfo.split(';')
			tmpList = tmpList.filter(item => item.startsWith(chooseKeyData))
			const newUkeyInfo = tmpList.join(';')
			return newUkeyInfo
		} else {
			return ukeyInfo
		}
	}

	const sumitUkeyForCreatCou = () => {
		let keyList = []
		if (ukeyInfo) {
			let tmpList = ukeyInfo.split(';')
			tmpList.forEach((item, index) => {
				const tempArray = item.split(',')
				if (!keyList.includes(tempArray[0])) {
					keyList.push(tempArray[0])
				}
			})
		}

		if (chooseKeyData || keyList.length === 1) {
			setUkeyChooseModal(false)
			onFinishUkeyForCreateCou()
		} else {
			message.error('请选择需要使用的UKEY')
		}
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<div className="cardTitle">融信开立</div>
					{/* <IsFillInvoicingInfoTip
						message="企业开票信息未完善，请尽快完善"
						description="您的企业开票信息未录入，企业开票信息用于运营方为您开具、邮寄发票使用，请联系企业管理员登录系统完善开票信息"
					></IsFillInvoicingInfoTip> */}
					{ischeckUkey && !ukeyInfo ? (
						<Alert
							message="未检测到有效的企业UKEY，请插入UKEY并确认UKEY背部序列号是JRCBHB或JRCBFTG开头。（提示：提交后，本企业审核岗审核时，需要插入另一把企业UKEY）"
							type="warning"
							description=" "
							showIcon
							style={{ marginBottom: '15px' }}
						></Alert>
					) : null}
					{!precheckData.creditFlag ? (
						<Alert message="未查询到合作合度,请联系银行客户经理" type="warning" description=" " showIcon style={{ marginBottom: '15px' }}></Alert>
					) : null}
					{!precheckData.bankFlag ? (
						<Alert message="未录入江阴银行开户的银行卡,请先录入" type="warning" description=" " showIcon style={{ marginBottom: '15px' }}></Alert>
					) : null}
					{paperSubmitMark == 0 ? (
						<Alert
							message="您未提交纸质材料，无法提交开立申请，请联系银行客户经理"
							type="warning"
							description=" "
							showIcon
							style={{ marginBottom: '15px' }}
						></Alert>
					) : null}
					<CompanyNameChangTip ishidden={precheckData.orgNameUnChangeFlag}></CompanyNameChangTip>
					<Form {...FormLayout} form={form} layout="vertical">
						<div className="boxContentWrap">
							<SmallTitle text={'开立信息'} />
							<div className="boxContent" style={{ padding: '22px 30px' }}>
								<CalendarModal
									visible={calendarVisible}
									onCancel={() => setCalendarVisible(false)}
									onOk={handleCalendar}
									dueDate={creditDueDate}
									periodGraceCreate={precheckData?.maxCreateDateOffset}
									minPeriodGraceCreate={precheckData?.minCreateDateOffset}
									periodGraceCredit={periodGraceCredit}
									dueDateObj={dueDateObj}
								/>
								<Row>
									{getReceiverCompany(transferModuleApi.getCompanyOfsupplier, value => {
										recordFormItem('receiverUuid', value)
									})}

									{inputPayAmount(validateMessage, validateReg, cCanUseAmount, getAmountSufix, value => {
										if (value.target.value === '0' || value.target.value.match(/^0+$/)) {
											form.setFieldsValue({
												createCouAmountInCent: null,
											})
											form.validateFields(['createCouAmountInCent'])
										} else {
											recordFormItem('payAmount', value.target.value)
										}
									})}
									<Col span={12}>
										<Row className="dateBox">
											<Col className="coldatebox">
												<Form.Item
													labelCol={{ span: 12 }}
													wrapperCol={{ span: 15 }}
													label="兑付到期日"
													name="dueDatePay"
													validateFirst
													rules={[
														{ required: true, message: '请设置一个工作日为兑付到期日' },
														({ getFieldValue }) => ({
															validator(_, value) {
																if (dueDateObj.str) {
																	return Promise.resolve()
																} else {
																	return Promise.reject(new Error('请设置一个工作日为兑付到期日'))
																}
															},
														}),
													]}
												>
													<Input
														placeholder="请选择兑付到期日"
														style={{ width: 170 }}
														onClick={() => setCalendarVisible(true)}
														onChange={() => setCalendarVisible(true)}
													/>
												</Form.Item>
											</Col>
											<Col className="coldatebox_end">
												{/* <Button onClick={() => setCalendarVisible(true)}>选择日期</Button> */}
												{dueDateObj?.days && (
													<div style={{ color: 'rgba(0,0,0,0.85)' }}>
														账期：<u>{dueDateObj?.days} 天 </u>
													</div>
												)}
											</Col>
										</Row>
									</Col>

									<Col span={12}>
										<Form.Item label="支付说明" name="createRemark">
											<Input placeholder="请输入支付说明" maxLength={60} showCount />
										</Form.Item>
									</Col>
								</Row>
							</div>
						</div>
						<div className="boxContentWrap">
							<SmallTitle text={'业务凭证'} />
							<div className="boxContent">
								<div className="btnCou">
									<Button type="primary" onClick={() => selectBtnCheck('contract')}>
										选择合同
									</Button>
									<Button onClick={handleGotoAddContract} style={{ marginLeft: '22px' }}>
										新建合同
									</Button>
									{hasBeneficiary['uuid'] && contractData.length === 0 && <div>请选择合同</div>}
								</div>
								<Table columns={getContractColumns()} dataSource={contractData} pagination={false} className="tableBlock" />
							</div>
						</div>
						<div className="boxContentWrap">
							<SmallTitle text={'发票凭证'} />
							<div className="boxContent">
								<div className="btnCou">
									<Button type="primary" onClick={() => selectBtnCheck('invoice')}>
										选择发票
									</Button>
									<Button onClick={handleGotoAddInvoice} style={{ marginLeft: '22px' }}>
										新建发票
									</Button>
									{hasBeneficiary['uuid'] && contractData.length === 0 && <div>请选择发票</div>}
								</div>
								<div className="invoiceBox">
									<div className="bottom">
										<div className="number">数量：{invoiceDataSource.length}</div>
										<div style={{ marginLeft: 22 }}>含税总金额：￥{numberToThousands(totalPretaxAmount)}</div>
									</div>
								</div>
								<Table columns={getInvoiceColumns()} dataSource={invoiceDataSource} pagination={false} className="tableBlock" />
							</div>
						</div>
						<div className="boxContentWrap">
							<AttachmentTable ref={attachmentTable} />
						</div>
						<div className="checkbox">
							<div>
								<Checkbox onChange={handleProtocolCheckEvent} checked={agreemented} ref={checkBoxRef}></Checkbox>
								<span style={{ marginLeft: '8px' }}>
									同意并签署
									<Button
										type="link"
										style={{ color: '#49A9EE', cursor: 'pointer' }}
										onClick={ischeckUkey ? showUkeyProtocol : showProtocol}
										loading={protocolLoading}
									>
										《付款承诺函》
									</Button>
								</span>
							</div>
							<Button
								type="primary"
								disabled={!agreemented || hasBankCard || paperSubmitMark == 0}
								loading={loading}
								onClick={ischeckUkey ? onFinishUkeyCheckForCreateCou : onFinishForCreateCou}
							>
								提交
							</Button>
						</div>
					</Form>
				</div>
			</LayoutSlot>
			<CreateContruct
				visible={showCreate}
				buyercompanyObj={buyercompanyObj}
				sellercompanyObj={sellercompanyObj}
				companySellerName={hasBeneficiary['uuid']}
				closeModal={value => {
					setShowCreate(value)
				}}
				showPdffn={(contructUrl, show) => {
					setPdfUrl(contructUrl)
					setPdfVisible(show)
				}}
			/>
			<NewUploadInvoice
				visible={showCreateInvoinve}
				invoiceDayOffset={precheckData?.invoiceDayOffset}
				closeModal={value => {
					setShowCreateInvoinve(value)
				}}
			/>
			<SelectInvoice
				visible={selectInvoiceVisible}
				dataInit={invoiceDataSource}
				payAmount={payAmount}
				buyerName={company?.orgName}
				supplierName={hasBeneficiary?.orgName}
				invoiceDayOffset={precheckData?.invoiceDayOffset}
				onOk={value => {
					handleSelectInvoiceOk(value)
				}}
				onCancel={() => setSelectInvoiceVisible(false)}
			/>
			<SelectContract
				visible={SelectContractVisible}
				onOk={selectContract}
				availableAmountLimit={1}
				payAmount={payAmount}
				onCancel={() => setSelectContractVisible(false)}
				companyBuyerUuid={companySellerUuid}
				companySellerUuid={hasBeneficiary['uuid']}
				dataInit={contractData}
				getContractList={getContractListFunc()}
			/>
			<Feedback
				onOk={() => {
					if (hasAuth('bl_couManage:cBuildOperatorRongXinCreate:list')) {
						history.push('/content/couManage/cBuildOperatorRongXinCreate')
					} else if (hasAuth('bl_couManage:CTeamOperatorRongXinCreate:list')) {
						history.push('/content/couManage/CTeamOperatorRongXinCreate')
					} else if (hasAuth('bl_couManage:COperatorRongXinCreate:list')) {
						history.push('/content/couManage/COperatorRongXinCreate')
					}
				}}
				onDetail={() => {
					if (goTransferId) {
						history.push('/content/couTransfer/payDetails', {
							DetailForTransfer: goTransferId,
						})
					}
				}}
				visible={successVisible}
				title="提交成功"
				subTitle=""
				btnTxts={['完成', '查看详情']}
			/>
			{/* <PDFViewer title="" visible={pdfVisible} pdfUrl={pdfUrl} onCancel={() => setPdfVisible(false)} /> */}
			{pdfVisible && (
				<ProtocolViewer
					visible={pdfVisible}
					pdfUrl={[pdfUrl]}
					aggree={aggreeAll}
					onCancel={() => setPdfVisible(false)}
					onFinish={() => {
						setAggreeAll(true)
					}}
				/>
			)}
			<Modal title="异常提示" open={!!ukeyError} footer={null} onCancel={() => setUkeyError('')}>
				<div>
					<div style={{ textAlign: 'center' }}>签章异常</div>
					<div style={{ marginTop: '30px', marginBottom: '80px', paddingLeft: '20px' }}>{`异常信息：${ukeyError}`}</div>
				</div>
			</Modal>
			<Modal title="提示" open={inItUkeyMsg == 'open' ? true : false} footer={null} onCancel={() => setInItUkeyMsg('')}>
				<div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', paddingBottom: '80px', paddingTop: '20px' }}>
					<div style={{ display: 'flex', justifyContent: 'center', alignItems: 'flex-start', flexDirection: 'column' }}>
						<div>1、未检测到有效的企业UKEY,请插入UKEY并确认UKEY背部序列号是JRCBHB或JRCBFTG开头;</div>
						<div style={{ marginTop: '20px' }}>2、提交后,本企业审核岗审核时,需要插入另一把企业UKEY;</div>
					</div>
				</div>
			</Modal>
			<Modal title="提示" open={errorUkeyModal} maskClosable={false} footer={null} onCancel={() => setErrorUkeyModal(false)}>
				<div>
					<div>
						<div>未检测到有效的企业UKEY，请插入UKEY并确认UKEY背部序列号是JRCBHB或JRCBFTG开头;</div>
						<div style={{ marginTop: '20px' }}>UKEY检测：</div>
						<div style={{ marginTop: '20px' }}>
							<Table
								bordered
								locale={{ emptyText: '未插入UKEY' }}
								columns={ukeyChooseColumn()}
								dataSource={errorUkeyArray}
								pagination={false}
								className="tableBlock"
							/>
						</div>
					</div>
				</div>
			</Modal>
			<Modal
				title="选择UKEY"
				open={ukeyChooseModal}
				maskClosable={false}
				footer={
					validity === 'valid' ? (
						<Button type={'primary'} onClick={sumitUkeyForCreatCou}>
							确定
						</Button>
					) : null
				}
				onCancel={() => setUkeyChooseModal(false)}
			>
				<div>
					<div>
						<div>检测您插入了多把UKEY，请选择需要使用的UKEY</div>
						<div style={{ marginTop: '20px' }}>选择UKEY：</div>
						<div style={{ marginTop: '20px' }}>
							<BaseTable
								columns={ukeyChooseColumn()}
								dataSource={errorUkeyArray}
								havePagination={false}
								rowSelection={
									validity === 'valid'
										? {
												type: 'radio',
												getCheckboxProps: getCheckBoxState,
												onChange: handleOnChange,
												selectedRowKeys: selectedRowKeys,
										  }
										: {}
								}
							/>
						</div>
					</div>
				</div>
			</Modal>
			<WxModal {...viewResultModalProps} ref={resultModalRef}>
				<div>{`勾选的发票可用金额合计【${totalAvailableAmount}】大于所需金额【${payAmount}】，为避免浪费发票，系统将按照可用金额占用发票的可用金额，自动剔除无需占用的发票`}</div>
			</WxModal>
			{contextHolder}
		</Box>
	)
}

const Box = styled.div`
	/* width: 100%;
	height: 100%; */
	padding-top: 54px;
	.boxContent {
		border: 1px solid #e9e9e9;
		// border-radius: 4px 4px 0px 0px;
		border-top: 0;
		padding: 20px 10px;
	}
	.boxContentWrap {
		margin-bottom: 10px;
		position: relative;
		.invoiceBox {
			position: absolute;
			top: 10px;
			left: 142px;
			color: #1f7bf4;
			font-weight: bold;
		}
	}
	.dateBox {
		display: flex;
		align-items: center;
		.coldatebox {
			position: releative !important;
			#dueDatePay_help {
				position: absolute !important;
				bottom: 0 !important;
				left: 0 !important;
				width: 200px !important;
			}
		}
		.coldatebox_end {
			margin-top: 5px;
			margin-left: 10px;
		}
	}
	.cardTitle {
		width: 100%;
		height: 54px;
		line-height: 54px;
		border-bottom: 1px solid #e9e9e9;
		position: absolute;
		left: 0;
		top: 0;
		padding-left: 24px;
		box-sizing: border-box;
		font-size: 16px;
		font-weight: bold;
		text-align: left;
		color: rgba(0, 0, 0, 0.85);
	}
	.Payamount {
		.ant-form-item-control-input-content {
			display: flex;
			input {
				flex: 1;
			}
			.ant-alert {
				flex: 1;
			}
		}
	}
	.suffix {
		color: rgba(0, 0, 0, 0.85) !important;
		line-height: 30px !important;
	}
	.tableBlock {
		margin-bottom: 20px;
	}
	.ant-input-group-addon {
		background: none !important;
		padding: 0 10px 0 0 !important;
	}
	.selectCouTitle {
		display: flex;
		.title {
			font-size: 17px;
			margin-right: 10px;
			margin-left: 5px;
			line-height: 32px;
		}
	}
	.ant-modal-confirm-content {
		margin-left: 0 !important;
	}
	.selectCouStatus {
		background-color: #efefef;
		border-radius: 5px;
		display: flex;
		line-height: 40px;
		padding: 10px;
		text-align: center;
		div {
			flex: 1;
		}
		margin-bottom: 10px;
	}
	.bottom {
		display: flex;
		// .number {
		// 	width: 112px;
		// }
	}
	.btnCou {
		display: flex;
		margin-bottom: 20px;
		padding-left: 26px;
		div {
			color: red;
			line-height: 33px;
			margin-left: 30px;
		}
	}
	.checkbox {
		text-align: center;
		div {
			margin: 20px 0;
		}
	}
	.ant-input-textarea-show-count::after {
		position: absolute;
		right: 10px;
		bottom: 25px;
	}
`

export default Index
