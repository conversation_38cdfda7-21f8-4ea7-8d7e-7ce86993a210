import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import SmallTitle from '@src/globalComponents/SmallTitle'
import styled from 'styled-components'
import { Alert, Button, Checkbox, Descriptions, message } from 'antd'
import CooperateProtocolAPI from '@src/pages/client/api/protocol'
import { numberToThousands } from '@src/utils/timeFilter'
import PDFViewer from '@src/globalComponents/PDFViewer'
import ProtocolViewer from '@src/globalComponents/protocol-pdf-viewer'
import { useNavigate } from 'react-router-dom'
const errorTips = {
	NO_BANK: '未录入江阴银行开户的银行卡，请先录入',
	DUE_DATE: '额度已到期，请联系银行客户经理',
}
const SignCooperateProtocols = () => {
	const navigate = useNavigate()
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')
	const [showNoBank, setShowNoBank] = useState<boolean>(false)
	const [showDueDate, setShowDueDate] = useState<boolean>(false)
	const [data, setData] = useState<any>()
	const [pdfVisible, setPdfVisible] = useState<boolean>()
	const [pdfPath, setPdfPath] = useState<string>()
	const [agreement, setAgreement] = useState<boolean>(false)
	const [checked, setChecked] = useState<boolean>(false)
	const [loading, setLoading] = useState<boolean>(false)

	useEffect(() => {
		preCheck()
		getSignInfo()
	}, [])

	const getSignInfo = () => {
		CooperateProtocolAPI.getBeforeSignProtocolInfo()
			.then(res => {
				setData(res)
			})
			.catch(() => {})
	}

	const preCheck = async () => {
		const res = await CooperateProtocolAPI.creditorPrecheck({ companyUuid: ext?.user?.company?.uuid, type: 1 })
		const { credit = {}, bankFlag } = res
		const creditDueDate = credit?.creditDueDate
		if (new Date(creditDueDate) < new Date()) {
			setShowDueDate(true)
		} else if (!bankFlag) {
			setShowNoBank(true)
		} else {
			setShowNoBank(false)
			setShowDueDate(false)
		}
	}

	const agreementProtocol = e => {
		if (agreement) {
			setChecked(e.target.checked)
		} else {
			getPdfUrl()
		}
	}

	const getPdfUrl = () => {
		setLoading(true)
		if (pdfPath) {
			setPdfVisible(true)
			setLoading(false)
		} else {
			CooperateProtocolAPI.previewSignCooperateProtocol()
				.then(res => {
					if (res) {
						setPdfPath(res)
						setPdfVisible(true)
					}
				})
				.catch(() => {})
				.finally(() => {
					setLoading(false)
				})
		}
	}

	const signProtocol = () => {
		setLoading(true)
		CooperateProtocolAPI.signCooperateProtocol()
			.then(res => {
				message.success('签约成功')
				navigate('/content/couManage/COperatorRongXinCreate')
			})
			.catch(() => {})
			.finally(() => {
				setLoading(false)
			})
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<div className="protocol_header">
						<div className="title">签署协议</div>
						<Alert
							message={<div className="txt">签署说明：</div>}
							description={
								<div>
									<p className="desc">1、当合作额度信息变更或企业银行卡变更后，需要重新签署《供应链金融服务平台合作协议》； </p>
									<p className="desc">2、本协议为线上CFCA证书签署，签署前请仔细确认合作额度、到期日、银行账户等信息；如有异议请联系银行客户经理； </p>
								</div>
							}
							type="info"
							showIcon
						/>
						{showDueDate && <Alert message={errorTips['DUE_DATE']} type="warning" showIcon />}
						{showNoBank && <Alert message={errorTips['NO_BANK']} type="warning" showIcon />}
					</div>

					<SmallTitle text="信息" />
					<div className="protocol_body">
						<SubTitle>主体信息</SubTitle>
						<Descriptions bordered column={1} labelStyle={{ width: 180 }} size="small">
							<Descriptions.Item label="签署主体">{data?.orgName || '--'}</Descriptions.Item>
							<Descriptions.Item label="企业地址">{data?.address || '--'} </Descriptions.Item>
							<Descriptions.Item label="企业法人">{data?.legalPerson || '--'} </Descriptions.Item>
							<Descriptions.Item label="联系人">{data?.adminRealName || '--'} </Descriptions.Item>
							<Descriptions.Item label="联系电话">{data?.adminMobile || '--'} </Descriptions.Item>
						</Descriptions>

						<SubTitle>合作额度信息</SubTitle>
						<Descriptions bordered column={1} labelStyle={{ width: 180 }} size="small">
							<Descriptions.Item label="合作额度（¥）">{numberToThousands(data?.quotaInYuan) || '--'}</Descriptions.Item>
							<Descriptions.Item label="到期日">{data?.creditDueDate || '--'} </Descriptions.Item>
						</Descriptions>

						<SubTitle>清分账户信息</SubTitle>
						<Descriptions bordered column={1} labelStyle={{ width: 180 }} size="small">
							<Descriptions.Item label="户名">{data?.accountName || '--'}</Descriptions.Item>
							<Descriptions.Item label="账号">{data?.accountNum || '--'} </Descriptions.Item>
							<Descriptions.Item label="开户行">{data?.accountBank || '--'} </Descriptions.Item>
						</Descriptions>
					</div>
				</div>
				<div className="protocol_footer">
					<Checkbox disabled={showDueDate || showNoBank} onClick={agreementProtocol} checked={checked}>
						同意
						<Button disabled={showDueDate || showNoBank} type="link" onClick={getPdfUrl}>
							《供应链金融服务平台合作协议》
						</Button>
					</Checkbox>
					<Button disabled={showDueDate || showNoBank || !checked} type="primary" className="btn" onClick={signProtocol} loading={loading}>
						签署协议
					</Button>
				</div>
				{pdfVisible && (
					<ProtocolViewer
						aggree={agreement}
						visible={pdfVisible}
						pdfUrl={[pdfPath]}
						onCancel={() => setPdfVisible(false)}
						onFinish={() => {
							setAgreement(true)
							setChecked(true)
						}}
					/>
				)}
				{loading && (
					<div className="spinner-load">
						<div className="loader"></div>
						<p style={{ textAlign: 'center', color: 'rgb(146, 146, 144)' }}>加载中...</p>
					</div>
				)}
			</LayoutSlot>
		</Box>
	)
}
const Box = styled.div`
	font-family: PingFangSC, PingFangSC-Medium;
	.protocol_header {
		.title {
			font-size: 20px;
			font-weight: 500;
			color: #212d42;
			text-align: center;
			padding: 10px 10px 20px 10px;
		}
		.txt {
			font-size: 16px;
			font-weight: 500;
			color: rgba(0, 0, 0, 0.85);
		}
		.desc {
			font-size: 14px;
			color: rgba(0, 0, 0, 0.65);
			margin: 0;
		}
	}
	.protocol_body {
		padding: 0px 10px 20px 10px;
		border: 1px solid #f1f1f1;
		border-top: none;
	}
	.protocol_footer {
		display: flex;
		flex-direction: column;
		align-items: center;
		.btn {
			width: 100px;
			margin: 20px;
		}
	}
`
const SubTitle = styled.div`
	font-size: 14px;
	font-weight: 500;
	color: rgba(0, 0, 0, 0.85);
	border-bottom: 1px solid #f1f1f1;
	padding: 10px;
	margin: 10px 0;
`

export default SignCooperateProtocols
