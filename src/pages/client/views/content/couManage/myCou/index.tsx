import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { transferModuleApi, commonModuleApi } from '@src/pages/client/api'
import React, { useEffect, useState } from 'react'
import GetCouList from '../components/couListComponent'
import { SOperatorStatusCard } from '../components/MystatusCard'
import SearchBar from '@src/pages/client/components/SearchBar'
import { hasAuth } from '@factor/biz/bizIndex'
import ChainInfoModal from '@src/globalComponents/chainInfoComponents/ChainInfoModal'
import { message, Tabs } from 'antd'
import styled from 'styled-components'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import { couCashStatusList, pledgeStatusList } from '../couCommon'
import { useLocation } from 'react-router-dom'

export default function operation() {
	const [publishPubKey, setPublishPubKey] = useState([]) //收款方列表
	const [chainInfoModalVisible, setChainInfoModalVisible] = useState<boolean>(false)
	const [onChainData, setOnChainData] = useState({})
	const [cardData, setCardData] = useState<any>() //卡片数据
	const location = useLocation()
	const state = location?.state as any
	const initKey = state?.tabKey
	const cashStatusList = state?.cashStatusList
	const pledgeStatus = state?.pledgeStatus
	const [tabKey, setTabKey] = useState<string>(initKey ?? '1')
	const initValues = { cashStatusList, pledgeStatus, due: [state?.minDueDate, state?.maxDueDate], needClear: true }

	useEffect(() => {
		getPublishlist()
		getCardData()
	}, [])

	//获取开立方的列表 为了搜索框服务 S
	const getPublishlist = () => {
		transferModuleApi
			.getPublishlist()
			.then((res: any) => {
				if (res) {
					const { publishPubKeys } = res
					const toList: any = []
					if (publishPubKeys && publishPubKeys.length > 0) {
						publishPubKeys.forEach(item => {
							const obj: any = {}
							obj.name = item.name
							obj.key = item.key
							toList.push(obj)
						})
					}

					setPublishPubKey(toList)
				}
			})
			.catch(err => {
				console.log('err', err)
			})
	}

	const showCertificate = record => {
		commonModuleApi
			.queryCouProof({ couNo: record.couNo })
			.then(data => {
				if (data && data.status === 'DONE') {
					setOnChainData(data)
					setChainInfoModalVisible(true)
				} else {
					message.warning('证书未生成，完成审批且上链后生成证书')
				}
			})
			.catch(err => {
				console.log(err)
			})
	}

	const getColumns = () => {
		//我的融信
		const columnsDic1 = {
			couAmountInCent: {
				title: '金额(￥)',
			},
			onChainCertificate: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => showCertificate(record)}>
							查看证书
						</span>
					)
				},
			},
		}
		let pageName = ''
		let columnIndexLengthMap = {}
		switch (tabKey) {
			case '1': {
				pageName = 'myCouPage'
				columnIndexLengthMap = {
					0: 16,
					1: 12,
					2: 18,
					3: 18,
				}
				break
			}
			case '2': {
				pageName = 'myCouPageForPledgeing'
				columnIndexLengthMap = {
					0: 15,
					2: 14,
					3: 14,
					5: 18,
				}
				break
			}
			case '3': {
				pageName = 'myCouPageForPledged'
				columnIndexLengthMap = {
					0: 13,
					1: 9,
					2: 16,
					3: 16,
				}
				break
			}
		}

		let colns = getColumnsByPageName(pageName, columnsDic1)
		colns = evenlyTableColunms(colns, columnIndexLengthMap)
		return colns
	}

	let requireList = {
		getCardData: {
			requireApi: transferModuleApi.sCouAmount,
			params: {},
		},
		getListData: {
			requireApi: transferModuleApi.getCouList,
			params: {},
		},

		exportCouCsv: {
			requireApi: transferModuleApi.supplierExport,
			params: {
				pageNum: 1,
				pageSize: 2147483646,
			},
		},
	}
	let requireListForPledging = {
		getCardData: {
			requireApi: transferModuleApi.sCouAmount,
			params: {},
		},
		getListData: {
			requireApi: transferModuleApi.getPledgingCouList,
			params: {},
		},
		exportCouCsv: {
			requireApi: transferModuleApi.supplierExport,
			params: {
				pageNum: 1,
				pageSize: 2147483646,
			},
		},
	}
	let requireListForPledged = {
		getCardData: {
			requireApi: transferModuleApi.sCouAmount,
			params: {},
		},
		getListData: {
			requireApi: transferModuleApi.getPledgedCouList,
			params: {},
		},
		exportCouCsv: {
			requireApi: transferModuleApi.supplierExport,
			params: {
				pageNum: 1,
				pageSize: 2147483646,
			},
		},
	}
	//获取顶部卡片的数据
	const getCardData = async () => {
		const res = await transferModuleApi.sCouAmount()
		setCardData(res)
	}
	// let pageCard = cardData => {
	// 	return (
	// 		<>
	// 			{/* {hasAuth('bl_couManage:myCou:SBuildOperatorStatusCard') && <SBuildOperatorStatusCard data={cardData || {}} />} */}
	//      {hasAuth('bl_couManage:myCou:SOperatorStatusCard') && <SOperatorStatusCard data={cardData || {}} />}
	// 			{/* {hasAuth('bl_couManage:myCou:STeamOperatorStatusCard') && <STeamOperatorStatusCard data={cardData || {}} />} */}
	// 		</>
	// 	)
	// }

	let myCouCashGetSearchBar = (onClear, onSubmit) => {
		return (
			<SearchBar
				optionData={{ publishPubKey, cashStatusList: couCashStatusList, pledgeStatus: pledgeStatusList }}
				onClear={onClear}
				onSubmit={onSubmit}
				pageName="myCouCash"
				initValues={initValues}
			/>
		)
	}

	let myCouGetSearchBar = (onClear, onSubmit) => {
		return <SearchBar optionData={{ publishPubKey }} onClear={onClear} onSubmit={onSubmit} pageName="myCou" />
	}

	const changeTabs = key => {
		setTabKey(key)
	}
	return (
		<StyleWrapper>
			<SOperatorStatusCard data={cardData || {}} />
			<Tabs
				onChange={changeTabs}
				activeKey={tabKey}
				items={[
					{
						key: '1',
						label: '可用融信',
						children: (
							<GetCouList refreshDev={tabKey} defaultRefreshDev={'1'} requireList={requireList} getColumns={getColumns()} getSearchBar={myCouGetSearchBar} />
						),
					},
					{
						key: '2',
						label: '质押中的融信',
						children: (
							<GetCouList
								refreshDev={tabKey}
								defaultRefreshDev={'2'}
								requireList={requireListForPledging}
								getColumns={getColumns()}
								getSearchBar={myCouGetSearchBar}
								isShowExport={false}
							/>
						),
					},
					{
						key: '3',
						label: '兑付的融信',
						children: (
							<GetCouList
								refreshDev={tabKey}
								defaultRefreshDev={'3'}
								requireList={requireListForPledged}
								getColumns={getColumns()}
								getSearchBar={myCouCashGetSearchBar}
								isShowExport={false}
								initParams={state}
							/>
						),
					},
				]}
			/>
			<ChainInfoModal visible={chainInfoModalVisible} onCancel={() => setChainInfoModalVisible(false)} chainData={onChainData} />
		</StyleWrapper>
	)
}
const StyleWrapper = styled.div`
	.ant-tabs > .ant-tabs-nav,
	.ant-tabs > div > .ant-tabs-nav {
		margin: 0 !important;
	}
	.ant-card {
		border: none;
	}

	.ant-table-thead > tr > th {
		border-top: 1px solid #e8e8e8;
	}
`
