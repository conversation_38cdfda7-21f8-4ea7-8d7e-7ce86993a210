import React from 'react'
import Pay from '@src/pages/client/views/content/couManage/components/pay'
import useCanUseAmount from '@factor/hooks/useCanUseAmount'
import { contractModuleApi, transferModuleApi } from '@factor/api'

const SBuildOperatorPay = () => {
	let { sBuildCanUseAmount } = useCanUseAmount()
	let propsData: CouPayPropTypes = {
		amountSuffix: '可用额度：¥',
		canUseAmount: sBuildCanUseAmount,
		getContractList: contractModuleApi.getBuildList,
		pay: transferModuleApi.sBuildOperatorPay,
		validateMessage: '请输入金额，最多2位小数，不大于可用额度。',
	}
	return <Pay {...propsData} />
}

export default SBuildOperatorPay
