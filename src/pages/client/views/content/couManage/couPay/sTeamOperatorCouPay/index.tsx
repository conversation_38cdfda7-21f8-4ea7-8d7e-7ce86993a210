import React from 'react'
import Pay from '@src/pages/client/views/content/couManage/components/pay'
import useCanUseAmount from '@factor/hooks/useCanUseAmount'
import { contractModuleApi, transferModuleApi } from '@factor/api'

const STeamOperatorPay = () => {
	let { sTeamCanUseAmount } = useCanUseAmount()
	let propsData: CouPayPropTypes = {
		amountSuffix: '可用额度：¥',
		canUseAmount: sTeamCanUseAmount,
		getContractList: contractModuleApi.getTeamContractList,
		pay: transferModuleApi.sTeamOperatorPay,
		validateMessage: '请输入金额，最多2位小数，不大于可用金额。',
	}
	return <Pay {...propsData} />
}

export default STeamOperatorPay
