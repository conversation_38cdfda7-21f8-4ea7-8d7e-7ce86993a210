import React from 'react'
import Pay from '@src/pages/client/views/content/couManage/components/pay'
import useCanUseAmount from '@factor/hooks/useCanUseAmount'
import { contractModuleApi, transferModuleApi } from '@factor/api'

const SOperatorPay = () => {
	let { sCanUseAmount } = useCanUseAmount()
	let propsData: CouPayPropTypes = {
		amountSuffix: '可用融信：¥',
		canUseAmount: sCanUseAmount,
		getContractList: contractModuleApi.getList,
		pay: transferModuleApi.pay,
		validateMessage: '请输入金额，最多2位小数，不大于可用金额。',
	}
	return <Pay {...propsData} />
}

export default SOperatorPay
