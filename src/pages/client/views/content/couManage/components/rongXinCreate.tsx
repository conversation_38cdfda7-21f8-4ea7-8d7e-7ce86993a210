import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { transferModuleApi } from '@src/pages/client/api'
import React, { useEffect, useState } from 'react'
import GetCouList from '../components/couListComponent'
import CreateStatusCard from '../components/CreateStatusCard'
import { history } from '@src/utils/router'
import SearchBar from '@src/pages/client/components/SearchBar'
import { creditStatus } from '@src/pages/client/config/companyCreditManage'
import { searchCouPageList } from '@factor/config/cou/couAssetsConfig'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import { useLocation } from 'react-router-dom'
interface IProps {
	isCBuildOperator: boolean
	isCTeamOperator: boolean
	getCardDataFunc: Function
}
export default function operation({ isCBuildOperator = false, isCTeamOperator = false, getCardDataFunc }: IProps) {
	const [toPubKey, setToPubKey] = useState([]) //收款方列表
	const { companyName } = JSON.parse(localStorage.getItem('ext') || '{}')?.user?.company || {} //该用户的公司名称和类型
	const location = useLocation()
	const state = location?.state as any
	const transferStatus = state?.transferStatus
	const [searchParams, setSearchParams] = useState<any>({ transferStatus }) //searchbar 字段

	useEffect(() => {
		getToPubKeylist()
	}, [])

	//获取收款方的列表 为了搜索框服务 C
	const getToPubKeylist = () => {
		transferModuleApi.getToPubKeyList().then(
			(res: any) => {
				if (res) {
					const toList: any = []
					res.forEach(item => {
						const obj: any = {}
						obj.name = item.companyName
						obj.key = item.couPubKey
						toList.push(obj)
					})
					setToPubKey(toList)
				}
			},
			err => {
				console.log('err', err)
			}
		)
	}

	//查看数据详情
	const checkdetail = values => {
		const { transferUuid } = values
		history.push('/content/couTransfer/payDetails', {
			DetailForTransfer: transferUuid,
		})
	}

	const getColumns = () => {
		//融信开立
		const columnsDic = {
			couAmountInCent: {
				title: '金额(￥)',
			},
			operation: {
				dataIndex: 'operation',
				title: '支付信息',

				render: (text: any, record: any) => {
					return (
						<span className="link" onClick={() => checkdetail(record)}>
							详情
						</span>
					)
				},
			},
		}
		let colns = getColumnsByPageName('couCreateHistory', columnsDic)
		// 表格列长度的简写形式
		let columnIndexLengthMap = {
			0: 17,
			2: 15,
			3: 18,
			6: 7,
			7: 7,
		}
		colns = evenlyTableColunms(colns, columnIndexLengthMap)
		return colns
	}

	//请求列表(可以公共处理的表格)
	let requireList = {
		getCardData: {
			requireApi: getCardDataFunc,
			params: { companyName, enable: creditStatus.enable },
		},
		getListData: {
			requireApi: transferModuleApi.getOriginalList,
			params: {
				limitOperator: isCBuildOperator,
			},
		},
		exportCouCsv: {
			requireApi: transferModuleApi.companyCExport,
			params: {
				pageNum: 1,
				pageSize: 99999999,
				limitOperator: isCBuildOperator,
			},
		},
	}

	let pageCard = cardData => {
		let filtData = cardData && cardData.list ? cardData.list : []
		// 若后端返回的列表为空，则不需要显示卡片
		// if (filtData.length === 0) return null
		return <CreateStatusCard data={filtData} isCBuildOperator={isCBuildOperator} isCTeamOperator={isCTeamOperator} />
	}

	let couCreateGetSearchBar = (onClear, onSubmit) => {
		return (
			<SearchBar
				optionData={{ toPubKey, status: searchCouPageList }}
				onClear={onClear}
				onSubmit={onSubmit}
				pageName="createCou"
				initValues={{ transferStatus, needClear: true }}
			/>
		)
	}

	return (
		<div>
			<GetCouList
				pageCard={pageCard}
				listTitle="开立明细"
				requireList={requireList}
				getColumns={getColumns()}
				getSearchBar={couCreateGetSearchBar}
				initParams={searchParams}
			/>
		</div>
	)
}
