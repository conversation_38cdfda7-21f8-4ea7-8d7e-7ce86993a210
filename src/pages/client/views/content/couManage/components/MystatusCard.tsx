//这里是融信开立的状态 卡片组件
import React from 'react'
import { Button, Card, Tooltip, message } from 'antd'
import styled from 'styled-components'
import { renderAmountInCent } from '@src/pages/client/config/TableColumnsRender'
import { QuestionCircleFilled } from '@ant-design/icons'
import { history } from '@src/utils/router'
import { hasAuth } from '@src/pages/client/biz/bizIndex'

const couBg = require('@src/assets/images/cou/cou_bg.png')
interface Props {
	/**
	 * @description 传入卡片的数据
	 */
	data: any
	canUseAmount: number
	titleName: string
	waitCashAmount?: number
	waitCashTitleName?: string
	showTip?: Function
}

const CreateStatusCardOrigin = (props: Props) => {
	const { data, canUseAmount, titleName, waitCashAmount, waitCashTitleName, showTip } = props

	return (
		<Box>
			{data && (
				<Card className="status">
					<div className="content">
						<img width={118} src={require('@src/assets/images/cou/cou_icon.png')} alt="" />
						<div className="line"></div>
						<div className="item-box">
							<div className="item">
								<div>
									<p className="item_title">{titleName}</p>
									<p>¥ {renderAmountInCent(canUseAmount)}</p>
									{showTip && showTip()}
								</div>
							</div>
							{waitCashAmount && (
								<div className="item">
									<div>
										<p className="item_title">{waitCashTitleName}</p>
										<p>¥ {renderAmountInCent(waitCashAmount)}</p>
										{showTip && showTip()}
									</div>
								</div>
							)}
						</div>
					</div>
					<div className="btn">
						{hasAuth('bl_couManage:sOperatorPay:list') && (
							<Button
								type="primary"
								className="big-btn"
								disabled={Number(canUseAmount) <= 0}
								onClick={() => {
									history.push('/content/couManage/sOperatorPay')
								}}
							>
								去支付
							</Button>
						)}
						{hasAuth('bl_couFinance:sOperatorCreateFinance:create') && (
							<Button
								onClick={() => history.push('/content/couFinance/sOperatorCreateFinance')}
								type="primary"
								className="big-btn"
								style={{ marginLeft: '20px' }}
								disabled={Number(canUseAmount) <= 0}
							>
								去融资
							</Button>
						)}
					</div>
				</Card>
			)}
		</Box>
	)
}

type cardProps = Record<string, any>

export const SBuildOperatorStatusCard = (props: cardProps) => {
	const { data } = props
	let { availableCouAmountInCent, userAvailableQuotaInCent } = data
	let canUseAmount = Number(availableCouAmountInCent) > Number(userAvailableQuotaInCent) ? Number(userAvailableQuotaInCent) : Number(availableCouAmountInCent)
	let showTip = () => {
		return (
			<p style={{ marginLeft: '10px' }}>
				<Tooltip
					placement="right"
					title="可用额度为企业管理员设置的支付/融资额度；若企业剩余融信总金额低于管理员设置额度，则此处可用额度为企业剩余融信总金额。"
				>
					<QuestionCircleFilled className="costConfirm" style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer' }} />
				</Tooltip>
			</p>
		)
	}
	return <CreateStatusCardOrigin data={data} canUseAmount={canUseAmount} titleName={'可用额度'} showTip={showTip} />
}

export const SOperatorStatusCard = (props: cardProps) => {
	const { data } = props
	let { availableCouAmountInCent, waitCashCouAmountInCent } = data
	return (
		<CreateStatusCardOrigin
			data={data}
			canUseAmount={availableCouAmountInCent}
			titleName={'可用融信'}
			waitCashAmount={waitCashCouAmountInCent}
			waitCashTitleName={'待兑付的融信'}
		/>
	)
}

export const STeamOperatorStatusCard = (props: cardProps) => {
	const { data } = props
	let { teamAvailableQuotaInCent, availableCouAmountInCent } = data
	let canUseAmount = Number(teamAvailableQuotaInCent) > Number(availableCouAmountInCent) ? Number(availableCouAmountInCent) : Number(teamAvailableQuotaInCent)
	let showTip = () => {
		return (
			<p style={{ marginLeft: '10px' }}>
				<Tooltip
					placement="right"
					title="可用额度为企业管理员设置的支付/融资额度；若企业剩余融信总金额低于管理员设置额度，则此处可用额度为企业剩余融信总金额。"
				>
					<QuestionCircleFilled className="costConfirm" style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer' }} />
				</Tooltip>
			</p>
		)
	}
	return <CreateStatusCardOrigin data={data} canUseAmount={canUseAmount} titleName={'可用额度'} showTip={showTip} />
}

const Box = styled.div`
	margin-bottom: 30px;

	.ant-card-body {
		display: flex;
		font-size: 16px;
		.content {
			flex: 1;
			display: flex;
			align-items: center;
			color: #fff;
			.title {
				display: flex;
				margin-bottom: 15px;
				div {
					margin-right: 100px;
					span {
						font-weight: bold;
						padding-right: 5px;
					}
				}
			}
			.item-box {
				display: flex;
				flex-grow: 1;
			}
			.item {
				width: 45%;
				div {
					display: flex;
					flex-direction: column;
					.item_title {
						font-size: 14px;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.38);
						line-height: 22px;
					}
					p {
						margin: 0;
					}
					p:nth-child(2) {
						font-size: 24px;
						font-weight: 500;
						color: rgba(23, 23, 23, 0.85);
						line-height: 32px;
					}
				}
			}
		}
		.btn {
			align-items: center;
			display: flex;
		}
		.line {
			width: 1px;
			height: 96px;
			background: #afd3c8;
			margin: 0 33px;
		}
	}
	.ant-card {
		background-image: url(${couBg});
		background-size: 100% 100%;
		background-repeat: no-repeat;
		height: 140px;
	}
	.ant-card-body {
		padding: 12px 24px;
	}
`
