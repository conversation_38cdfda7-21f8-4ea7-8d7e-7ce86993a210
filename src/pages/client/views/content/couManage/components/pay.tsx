import LayoutSlot from '@src/globalComponents/LayoutSlot'
import { commonModuleApi, transferModuleApi } from '@src/pages/client/api'
import { Button, Form, Input, Checkbox, Row, Col, Table, message, Spin } from 'antd'
import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import SmallTitle from '@src/globalComponents/SmallTitle'
import Feedback from '@src/pages/client/components/feedback'
import SelectCou from '@src/pages/client/components/SelectCou'
import SelectContract from '../../../../components/pagesComponents/couPay/selectContract'
import { renderAmountInCent, renderAmount } from '@src/pages/client/config/TableColumnsRender'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import { sumitConfirmModal, getReceiverCompany, inputPayAmount } from './createAndPayComponents'
import { FileOutlined } from '@ant-design/icons'
import { history } from '@src/utils/router'
import AttachmentTable from '@src/globalComponents/EditTable/AttachmentTable'
import PDFViewer from '@src/globalComponents/PDFViewer'
import { couDataOnChain } from '@src/pages/client/biz/bizIndex'
import CompanyNameChangTip from '@factor/components/companyNameChangTip'
import CreateContruct from '../couCreate/components/contruct'
import ProtocolViewer from '@src/globalComponents/protocol-pdf-viewer'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
const FormLayout = {
	labelCol: { span: 4 },
	wrapperCol: { span: 15 },
}

interface TDueDate {
	str: string
	date?: number
	days?: string | number
}

const Pay = (props: CouPayPropTypes) => {
	const ext: any = JSON.parse(localStorage.getItem('ext') || '{}')
	const company = ext?.user?.company
	let { amountSuffix, canUseAmount, getContractList, pay, validateMessage } = props
	const attachmentTable = React.useRef(null as any)
	const [form] = Form.useForm()
	const { uuid: companySellerUuid } = JSON.parse(localStorage.getItem('ext') || '{}')?.user?.company || {} //该用户的公司名称和类型
	const [successVisible, setSuccessVisible] = useState<boolean>(false) //提交成功后的页面
	const [dueDateObj, setDueDateObj] = useState<TDueDate>({ str: '' }) //兑付到期日的 数据  显示的 str  和提交的13位时间戳
	const [SelectCouVisible, setSelectCouVisible] = useState<boolean>(false) //选择融信弹窗
	const [couData, setCouData] = useState<any>([]) //已选择融信数据
	const [SelectContractVisible, setSelectContractVisible] = useState<boolean>(false) //选择合同弹窗
	const [contractData, setContractData] = useState<any>([]) //已选择合同数据
	const [hasBeneficiary, setHasBeneficiary] = useState<any>({}) //是否选择 收款方
	const [payAmount, setPayAmount] = useState<number>() //支付金额
	const [agreemented, setAgreemented] = useState<boolean>(false) //同意协议
	const [loading, setLoading] = useState(false)
	//协议处理部分
	const [protocolUrl, setProtocolUrl] = React.useState<string>('')
	const [protocolVisible, setProtocolVisible] = useState(false)
	const [goTransferId, setGoTransferId] = useState<string>()
	const [couSelectAmount, setCouSelectAmount] = useState<number>(0) //已经选融信金额
	const [isSamePayAmount, setIsSamePayAmount] = useState<boolean>(false) //是否匹配金额
	const [buyercompanyObj, setBuyerCompanyObj] = useState<any>({}) // 公司买方数组
	const [sellercompanyObj, setSellerCompanyObj] = useState<any>({}) // 公司卖方数组
	const [showCreate, setShowCreate] = useState(false)
	const [pdftitle, setPdftitle] = useState('"融信流转单"')
	const [aggreeAll, setAggreeAll] = useState(false)
	const [currentReadCou, setCurrentReadCou] = useState<any>()
	const [allCouPdfs, setAllCouPdfs] = useState<any>([])
	const [clickShowAll, setClickShowAll] = useState<boolean>(false)
	const [protocolLoading, setProtocolLoading] = useState<boolean>(false)
	const validateReg = /^(\d+)(.\d{0,2})?$/

	const aggreedProtocolFinish = () => {
		if (!clickShowAll) {
			const copyCou = JSON.parse(JSON.stringify(couData))
			const cur = JSON.parse(JSON.stringify(currentReadCou))
			copyCou.map(item => {
				if (item.couNo === currentReadCou?.couNo) {
					item.agreeProtocol = true
					cur['agreeProtocol'] = true
				}
			})

			let agree = true
			copyCou.map(item => {
				if (!item.hasOwnProperty('agreeProtocol') || (item.hasOwnProperty('agreeProtocol') && !item.agreeProtocol)) {
					agree = false
				}
			})
			setAgreemented(agree)
			setAggreeAll(agree)
			setCouData(copyCou)
			setCurrentReadCou(cur)
		} else {
			const copyCou = JSON.parse(JSON.stringify(couData))
			copyCou.map(item => {
				item.agreeProtocol = true
			})
			setCouData(copyCou)
			setAggreeAll(true)
		}
	}

	//选择合同的 ok 回调函数
	const selectContract = values => {
		setContractData([values])
		//关闭合同 弹窗
		setSelectContractVisible(false)
	}

	//选择融信的 ok 回调函数
	const selectCou = async (values, SelectAmount) => {
		if (!Array.isArray(values)) return
		if (Number(SelectAmount) === Number(payAmount)) {
			setIsSamePayAmount(true)
		} else {
			setIsSamePayAmount(false)
		}
		//保存已经选择的融信
		setCouSelectAmount(SelectAmount)

		//塞入融信编号
		const res = await commonModuleApi.getNo({ numberPrefix: 'COU', count: values.length * 2 }).catch(err => {
			console.log('err', err)
		})
		let i: number = 0
		let fiUuid: string = ''
		if (res && res.length) {
			values.forEach(cou => {
				cou['toCouNo'] = res[i++]
				cou['changeCouNo'] = res[i++]
				if (!fiUuid) {
					fiUuid = cou['creditUuid']
				}
			})
		}

		//保存融信数据
		setCouData(values)
		//关闭合同 弹窗
		setSelectCouVisible(false)
		// 121 中修复重新选择融信协议已读的问题
		setAggreeAll(false)
		setAgreemented(false)
	}

	//清除 已经选择的 合同   （基本的 信息发生改变 的时候 && 已经选）
	const clearSelectContract = () => {
		setContractData([])
	}
	//清除 已经选择的 融信  和已经选择的金额 （基本的 信息发生改变 的时候 && 已经选）
	const clearSelectCou = () => {
		setCouData([])
		setCouSelectAmount(0)
	}
	//输入框的之发生改变的 回调函数
	const recordFormItem = (name: 'payAmount' | 'receiverUuid' | 'dueDate', data: any) => {
		let isChangeBaseInfo = false
		if (name === 'payAmount') {
			if (payAmount && ((contractData && contractData.length > 0) || (couData && couData.length > 0))) {
				if (data !== payAmount) isChangeBaseInfo = true
			}
			setPayAmount(Number(data))
		} else if (name === 'dueDate') {
			if (dueDateObj && dueDateObj['str'] != '' && contractData && contractData.length > 0) {
				if (data['str'] !== dueDateObj['str']) isChangeBaseInfo = true
			}
			form.setFieldsValue({
				dueDatePay: data.str,
			})
			setDueDateObj(data)
		} else if (name === 'receiverUuid') {
			if (hasBeneficiary && hasBeneficiary['id'] != '' && ((contractData && contractData.length > 0) || (couData && couData.length > 0))) {
				if (data['id'] != hasBeneficiary['id']) isChangeBaseInfo = true
			}
			setHasBeneficiary(data)
		}
		if (isChangeBaseInfo) {
			message.warning('您已修改支付信息，需要重新选择合同与融信')
			clearSelectContract()
			clearSelectCou()
		}
	}

	//点击选择合同 ｜｜ 融信按钮时候的 检验基本的支付信息
	const selectBtnCheck = (type: 'COU' | 'CONTRACT') => {
		form
			.validateFields()
			.then(res => {
				if (type === 'CONTRACT') setSelectContractVisible(true)
				else setSelectCouVisible(true)
			})
			.catch(err => {
				message.warning('请先完善支付信息')
			})
	}

	//表单提交事件  支付Cou
	const onFinishForPayCou = async () => {
		if (isSamePayAmount && contractData && contractData.length === 1 && payAmount) {
			setLoading(true)
			if (couData && couData.length > 0) {
				const splitCouList = new Array<any>()
				couData.forEach(item => {
					const obj: any = {}
					obj.dueDate = item.dueDate
					obj.fromCouUuid = item.uuid
					obj.toCouNo = item.toCouNo
					obj.changeCouNo = item.changeCouNo
					obj.toCouAmountInCent = item.inputAmountInCent
					//循环调取接口获取每个融信的html转成pdf的url
					splitCouList.push(obj)
				})
				Promise.all(splitCouList).then(dataList => {
					const params = {
						contractUuid: contractData[0].uuid,
						payBase: {
							publishUuid: couData[0].publishUuid,
							creditUuid: couData[0].creditUuid,
							payCouAmountInCent: payAmount * 100,
							payRemark: form.getFieldValue('createRemark') || '',
							receiverUuid: form.getFieldValue('receiverUuid'),
						},
						splitCouList: dataList,
						attachment: JSON.stringify(attachmentTable?.current?.getValues()),
					}
					let modalTopCenterItem = (
						<div className="wx_Modal_top_center">
							<div className="wx_Modal_top_center_item">
								<p className="text">￥ {renderAmount(payAmount + '')}</p>
								<p className="label">支付金额</p>
							</div>
						</div>
					)
					let companyName = hasBeneficiary.orgName

					sumitConfirmModal(
						'支付信息确认',
						modalTopCenterItem,
						companyName,
						() => {
							console.log(params)

							pay(params)
								.then(res => {
									setGoTransferId(res)
									setSuccessVisible(true)
									setLoading(false)
									//融信数据存证上链
									let couNoList = []
									couNoList = couData.map(cou => {
										return cou['changeCouNo']
									})
									couDataOnChain(couNoList)
								})
								.catch(error => {
									console.log(error)
									setLoading(false)
								})
						},
						() => {
							setLoading(false)
						}
					)
				})
			}
		} else {
			form.validateFields()
			message.destroy()
			message.warning('请完善支付信息')
		}
	}

	//获取合同的表格列
	const getContractColumns = () => {
		const columnsDic = {}
		return getColumnsByPageName('couContractInform', columnsDic)
	}

	const getCouColumns = () => {
		const columnsDic = {
			inputAmountInCent: {
				dataIndex: 'inputAmountInCent',
				title: '融信金额(￥)',
				render: (text: any, record: any) => {
					return <span>{renderAmountInCent(text)}</span>
				},
			},
			// 融信流转单
			couFlowSheet: {
				dataIndex: 'couFlowSheet',
				render: (text: any, record: any) => {
					return (
						<span className="link" style={{ fontSize: '24px' }} onClick={() => showTransferProtocol(record)}>
							<FileOutlined />
						</span>
					)
				},
			},
		}

		let colns = getColumnsByPageName('selectCou', columnsDic)
		// 表格列长度的简写形式
		let columnIndexLengthMap = {
			1: 28,
		}
		colns = evenlyTableColunms(colns, columnIndexLengthMap)
		return colns
	}
	const showTransferProtocol = async record => {
		setClickShowAll(false)
		let data: any = {
			creditUuid: record.creditUuid,
			publishUuid: record.publishUuid,
			receiverUuid: hasBeneficiary['id'],
			splitDetail: {
				changeCouNo: record.changeCouNo,
				dueDate: record.dueDate,
				fromCouUuid: record.uuid,
				toCouAmountInCent: record.inputAmountInCent,
				toCouNo: record.toCouNo,
			},
		}
		setCurrentReadCou(record)
		console.log(record)
		const res = await transferModuleApi.transferProtocol4PayView(data)
		console.log(res)
		setProtocolUrl(res)
		setAllCouPdfs([res])
		setPdftitle('融信流转单')
		setProtocolVisible(true)
	}
	const showAllProtocols = () => {
		setProtocolLoading(true)
		const promiseArr = []
		for (let i = 0; i < couData.length; i++) {
			promiseArr.push(
				new Promise(async (resolve, reject) => {
					const record = couData[i]
					let data: any = {
						creditUuid: record.creditUuid,
						publishUuid: record.publishUuid,
						receiverUuid: hasBeneficiary['id'],
						splitDetail: {
							changeCouNo: record.changeCouNo,
							dueDate: record.dueDate,
							fromCouUuid: record.uuid,
							toCouAmountInCent: record.inputAmountInCent,
							toCouNo: record.toCouNo,
						},
					}
					console.log(record)
					const res = await transferModuleApi.transferProtocol4PayView(data).catch(err => {
						reject(err)
					})
					resolve(res)
				})
			)
		}
		Promise.all(promiseArr).then(res => {
			setAllCouPdfs(res)
			setProtocolVisible(true)
			setClickShowAll(true)
			setProtocolLoading(false)
		})
	}
	const showProtocol = async () => {
		message.warning('请在融信明细中查看《融信流转单》')
	}
	const getAmountSuffix = () => {
		return <div className="suffix">{amountSuffix + renderAmountInCent(Number(canUseAmount))}</div>
	}
	// 打开创建合同tab
	const handleGotoAddContract = () => {
		// const { receiverUuid } = form.getFieldsValue()
		// let qs = { add: 'true' }
		// if (receiverUuid && hasBeneficiary?.id === receiverUuid) {
		// 	qs['companySellerName'] = hasBeneficiary?.orgName
		// 	qs['companySellerId'] = receiverUuid
		// }
		// window.open(`${window.location.origin}/#/content/couAssets/contract?${new URLSearchParams(qs).toString()}`)

		setBuyerCompanyObj({ id: company?.id, orgName: company?.orgName }) // 默认买方为自身企业
		if (hasBeneficiary?.id) {
			setSellerCompanyObj({ id: hasBeneficiary.id, orgName: hasBeneficiary.orgName })
		}
		setShowCreate(true)
	}

	const handleProtocolCheckEvent = () => {
		if (!aggreeAll) {
			showAllProtocols()
			setAgreemented(false)
		} else {
			setAgreemented(!agreemented)
		}
	}

	useEffect(() => {
		if (aggreeAll) {
			setAgreemented(true)
		}
	}, [aggreeAll])

	return (
		<Box>
			<LayoutSlot>
				<Spin spinning={protocolLoading}>
					<div className="card-wrapper" style={{ paddingBottom: 0 }}>
						<div className="cardTitle">融信支付</div>
						<CompanyNameChangTip ishidden={company?.orgNameStatus == '1'}></CompanyNameChangTip>
						<Form {...FormLayout} layout="vertical" form={form}>
							<SmallTitle text={'支付信息'} />
							<div className="pay-info">
								<Row>
									{getReceiverCompany(transferModuleApi.getCompanyOfsupplier, value => {
										recordFormItem('receiverUuid', value)
									})}

									{inputPayAmount(validateMessage, validateReg, canUseAmount, getAmountSuffix, value => {
										if (Number(value.target.value) === 0 || value.target.value.match(/^0+$/)) {
											form.setFieldsValue({
												createCouAmountInCent: null,
											})
											form.validateFields(['createCouAmountInCent'])
										} else {
											recordFormItem('payAmount', value.target.value)
										}
									})}

									<Col span={12}>
										<Form.Item label="支付说明" name="createRemark">
											<Input placeholder="请输入支付说明" maxLength={60} showCount />
										</Form.Item>
									</Col>
								</Row>
							</div>
							<div className="finance-detail">
								<SmallTitle
									text={'融信明细'}
									slot={
										<span className="select-cou-info">
											<span className="selected-cou">已选融信：￥ {renderAmount(couSelectAmount + '')}</span>
											<span>
												还需选择：￥
												{renderAmount((Number(payAmount) * 100 - Number(couSelectAmount) * 100 || 0) / 100 + '')}
											</span>
										</span>
									}
								/>
								<div className="detail-info-container">
									<div className="btnCou">
										<Button type="primary" onClick={() => selectBtnCheck('COU')}>
											选择融信
										</Button>
										{hasBeneficiary['id'] && couData.length === 0 && <div>请选择融信</div>}
										{couSelectAmount > 0 && !isSamePayAmount && <div>融信金额与支付金额不匹配</div>}
									</div>
									<Table columns={getCouColumns()} dataSource={couData} pagination={false} className="tableBlock" />
								</div>
							</div>
							<SmallTitle text={'业务凭证'} />
							<div className="detail-info-container">
								<div className="btnCou">
									<Button type="primary" onClick={() => selectBtnCheck('CONTRACT')}>
										选择合同
									</Button>
									<Button onClick={handleGotoAddContract} style={{ marginLeft: 10 }}>
										新建合同
									</Button>
									{hasBeneficiary['id'] && contractData.length === 0 && <div>请选择合同</div>}
								</div>
								<Table columns={getContractColumns()} dataSource={contractData} pagination={false} className="tableBlock" />
							</div>

							<AttachmentTable ref={attachmentTable} />

							<div className="checkbox">
								<div>
									<Checkbox onChange={handleProtocolCheckEvent} checked={agreemented}></Checkbox>
									<span style={{ marginLeft: '8px' }}>
										同意并签署
										<span style={{ color: '#49A9EE', cursor: 'pointer' }} onClick={showProtocol}>
											《融信流转单》
										</span>
									</span>
								</div>
								<Button type="primary" disabled={!agreemented || !aggreeAll} loading={loading} onClick={onFinishForPayCou}>
									提交
								</Button>
							</div>
						</Form>
					</div>
				</Spin>
			</LayoutSlot>

			<SelectCou
				pageName="TRANSFER"
				searchPageName="paySelectAvibleCou"
				tablePageName="selectCouModel"
				dataInit={couData}
				visible={SelectCouVisible}
				onOk={selectCou}
				onCancel={() => setSelectCouVisible(false)}
				amountTabCallback={selectCouAmount => {
					return (
						<div className="title">
							<div className="selected" style={{ display: 'flex' }}>
								<div>已选融信：￥{renderAmount(selectCouAmount + '')}</div>
								<div>还需选择: ￥ {renderAmount((Number(payAmount) * 100 - Number(selectCouAmount) * 100) / 100 + '')}</div>
							</div>
						</div>
					)
				}}
			/>
			<SelectContract
				visible={SelectContractVisible}
				onOk={selectContract}
				payAmount={payAmount}
				availableAmountLimit={1}
				onCancel={() => setSelectContractVisible(false)}
				companyBuyerUuid={companySellerUuid}
				companySellerUuid={hasBeneficiary['id']}
				dataInit={contractData}
				getContractList={getContractList}
			/>
			<CreateContruct
				visible={showCreate}
				buyercompanyObj={buyercompanyObj}
				sellercompanyObj={sellercompanyObj}
				companySellerName={hasBeneficiary['id']}
				closeModal={value => {
					setShowCreate(value)
				}}
				showPdffn={(contructUrl, show) => {
					setProtocolUrl(contructUrl)
					setAllCouPdfs([contructUrl])
					setPdftitle('查看合同')
					setProtocolVisible(show)
				}}
			/>
			{/* <PDFViewer
				visible={protocolVisible}
				pdfUrl={protocolUrl}
				title={pdftitle}
				onCancel={() => {
					setProtocolUrl('')
					setProtocolVisible(false)
				}}
			/> */}
			{protocolVisible && (
				<ProtocolViewer
					visible={protocolVisible}
					pdfUrl={allCouPdfs}
					aggree={clickShowAll ? aggreeAll : currentReadCou?.agreeProtocol}
					onCancel={() => setProtocolVisible(false)}
					onFinish={() => {
						aggreedProtocolFinish()
					}}
				/>
			)}
			<Feedback
				onOk={() => {
					history.push('/content/couManage/myCou')
				}}
				onDetail={() => {
					if (goTransferId) {
						history.push('/content/couTransfer/payDetails', {
							DetailForTransfer: goTransferId,
						})
					}
				}}
				visible={successVisible}
				title="提交成功"
				subTitle=""
				btnTxts={['完成', '查看详情']}
			/>
		</Box>
	)
}

export default Pay
const Box = styled.div`
	.pay-info {
		border: 1px solid #e9e9e9;
		border-top: 0;
		padding: 20px 30px;
		margin-bottom: 10px;
		.ant-input-textarea {
			.ant-input {
				padding-right: 62px;
			}
		}
		.ant-input-textarea-show-count::after {
			position: absolute;
			right: 10px;
			bottom: 25px;
		}
		.ant-input-group-addon {
			background: #ffffff;
		}
		.suffix {
			color: rgba(0, 0, 0, 0.85) !important;
			line-height: 30px !important;
		}
		.ant-input-group-addon {
			background: none !important;
			padding: 0 10px 0 0 !important;
		}
	}
	.finance-detail {
		.select-cou-info {
			font-size: 14px;
			font-family: PingFangSC, PingFangSC-Medium;
			font-weight: 500;
			text-align: left;
			color: #1f7bf4;
			margin-left: 60px;
			.selected-cou {
				margin-right: 22px;
			}
		}
	}
	.detail-info-container {
		border: 1px solid #e9e9e9;
		border-top: 0;
		padding: 20px 10px;
		margin-bottom: 10px;
	}

	.boxContent {
		border: 1px solid #e9e9e9;
		border-top: 0;
		border-radius: 4px 4px 0px 0px;
		padding: 20px 40px 24px 40px;
		margin-bottom: 10px;
		.attachment-btn {
			padding: 0 !important;
		}
	}
	.ant-modal-confirm-content {
		margin-left: 0 !important;
	}
	.btnCou {
		display: flex;
		padding-left: 25px;
		padding-bottom: 20px;
		div {
			color: red;
			line-height: 33px;
			margin-left: 30px;
		}
	}
	.checkbox {
		text-align: center;
		div {
			margin: 20px 0;
		}
	}
	.cardTitle {
		width: 100%;
		height: 54px;
		line-height: 54px;
		border-bottom: 1px solid #e9e9e9;
		margin-bottom: 20px;
		box-sizing: border-box;
		font-size: 16px;
		font-weight: bold;
		text-align: left;
		color: rgba(0, 0, 0, 0.85);
	}
`
