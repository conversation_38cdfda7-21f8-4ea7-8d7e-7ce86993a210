import React from 'react'
import { Modal, Descriptions, Row, Col, Form, Input } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import GetCompanyName from '@src/globalComponents/SearchComByName'

export const sumitConfirmModal = (title, modalTopCenterItem, companyName, onOkCallback, onCancelCallback) => {
	Modal.confirm({
		title: title,
		icon: null,
		cancelText: '取消',
		okText: '确定',
		className: 'wx_Modal',
		content: (
			<div>
				<div>
					{modalTopCenterItem}
					<div className="wx_Modal_Main">
						<Descriptions column={1}>
							<Descriptions.Item label="收款方">{companyName}</Descriptions.Item>
							<Descriptions.Item
								className="wx_Modal_Warning_text"
								label={
									<>
										<span className="tips_icon">
											<ExclamationCircleOutlined />
										</span>
										<span>&nbsp;提示</span>
									</>
								}
							>
								请核对支付信息。收款方确认后，支付无法撤回！
							</Descriptions.Item>
						</Descriptions>
					</div>
				</div>
			</div>
		),
		onOk() {
			onOkCallback()
		},
		onCancel() {
			onCancelCallback()
		},
	})
}

//选择收款方企业
export const getReceiverCompany = (getCompanyApi, onChangeDataCallback) => {
	return (
		<Col span={12}>
			<GetCompanyName
				name="receiverUuid"
				label="收款方"
				placeholder="请选择收款方"
				getCompany={getCompanyApi}
				requireMsg="请选择收款方"
				extendMethod={value => onChangeDataCallback(value)}
				valuekey="id"
			/>
		</Col>
	)
}

//设置开立或支付金额
export const inputPayAmount = (validateMessage, validateReg, CanUseAmount, getAmountSufix, onBlurCallback) => {
	//比较最大可用额度
	const Tocompare = (value: number, tocompareCanUseAmount) => {
		let targetAmount = 0
		targetAmount = tocompareCanUseAmount
		return value <= targetAmount
	}
	return (
		<Col span={12}>
			<Row>
				<Col span={24}>
					<Form.Item
						labelCol={{ span: 7 }}
						wrapperCol={{ span: 15 }}
						className={'Payamount'}
						label="支付金额"
						name="createCouAmountInCent"
						validateFirst
						rules={[
							{ required: true, message: validateMessage, whitespace: true },
							{
								type: 'number',
								message: validateMessage,
								transform: value => {
									return Number(value)
								},
							},
							{
								pattern: validateReg,
								message: validateMessage,
							},
							{
								validator(_, value) {
									if (Tocompare(Number(value) * 100, CanUseAmount)) {
										return Promise.resolve()
									} else {
										return Promise.reject(new Error(validateMessage))
									}
								},
							},
						]}
						getValueFromEvent={event => {
							event.target.value = event.target.value.replace(/\s+/g, '')
							return event.target.value
						}}
					>
						<Input
							placeholder="请输入金额"
							onBlur={value => {
								onBlurCallback(value)
							}}
							addonAfter={<span style={{ marginLeft: 10, display: 'flex' }}>{getAmountSufix()}</span>}
						/>
						{/* <Form.Item noStyle>{getAmountSufix()}</Form.Item> */}
					</Form.Item>
				</Col>
				{/* <Col span={10}>{getAmountSufix()}</Col> */}
			</Row>
		</Col>
	)
}
