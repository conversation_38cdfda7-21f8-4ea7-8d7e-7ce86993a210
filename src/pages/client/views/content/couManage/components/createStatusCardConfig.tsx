import React from 'react'
import { renderAmountInCent } from '@src/pages/client/config/TableColumnsRender'
import { Button } from 'antd'
/**
 * @description 卡片信息展示部分（普通行业操作员）
 */
export const cardInfoForNormal = item => {
	return (
		<div className="item">
			<div>
				<p className="item_title">开立总额度</p>
				<p>¥ {item ? renderAmountInCent(Number(item.quotaInCent)) : '- -'}</p>
			</div>
			<div>
				<p className="item_title">可用额度</p>
				<p>¥ {item ? renderAmountInCent(Number(item.quotaInCent) - (Number(item.usedQuotaInCent) + Number(item.frozenQuotaInCent))) : '- -'}</p>
			</div>
			<div>
				<p className="item_title">已用额度</p>
				<p>¥ {item ? renderAmountInCent(Number(item.usedQuotaInCent) + Number(item.frozenQuotaInCent)) : '- -'}</p>
			</div>
		</div>
	)
}
/**
 * @description 卡片信息展示部分(建筑行业的-限额操作员)
 */
export const cardInfoForBuild = item => {
	return (
		<div className="item">
			<div>
				<p className="item_title">可用额度</p>
				<p style={{ color: '#006400' }}>
					¥{' '}
					{item
						? renderAmountInCent(
								Math.min(Number(item.quotaInCent) - (Number(item.usedQuotaInCent) + Number(item.frozenQuotaInCent)), Number(item.limitAmountInCent))
						  )
						: '_ _'}
				</p>
			</div>
		</div>
	)
}

/**
 * @description 卡片信息展示部分(项目操作员)
 */
export const cardInfoForTeam = item => {
	return (
		<div className="item">
			<div>
				<p className="item_title">可用额度</p>
				<p style={{ color: '#006400' }}>
					¥{' '}
					{item
						? renderAmountInCent(
								Math.min(Number(item.quotaInCent) - (Number(item.usedQuotaInCent) + Number(item.frozenQuotaInCent)), Number(item.teamAvailableQuotaInCent))
						  )
						: '_ _'}
				</p>
			</div>
		</div>
	)
}

/**
 * @description 点击去融资的按钮逻辑(普通行业操作员)
 */
export const toCreateCouButton = (item, canUse, goCreateCouCallback) => {
	return (
		<Button className="big-btn" type="primary" size="middle" onClick={() => goCreateCouCallback(item)} disabled={!canUse}>
			开立
		</Button>
	)
}

/**
 * @description 点击去融资的按钮逻辑(建筑行业的-限额操作员)
 */
export const toCreateCouButtonForBuild = (item, canUse, goCreateCouCallback) => {
	return (
		<Button
			type="primary"
			size="middle"
			className="big-btn"
			onClick={() => goCreateCouCallback(item)}
			disabled={
				!(item && Number(item.limitAmountInCent)) || Number(item.quotaInCent) - (Number(item.usedQuotaInCent) + Number(item.frozenQuotaInCent)) === 0 || !canUse
			}
		>
			开立
		</Button>
	)
}

/**
 * @description 点击去融资的按钮逻辑(项目操作员)
 */
export const toCreateCouButtonForTeam = (item, canUse, goCreateCouCallback) => {
	return (
		<Button
			type="primary"
			size="middle"
			className="big-btn"
			onClick={() => goCreateCouCallback(item)}
			disabled={
				!(item && Number(item.teamAvailableQuotaInCent)) ||
				Number(item.quotaInCent) - (Number(item.usedQuotaInCent) + Number(item.frozenQuotaInCent)) === 0 ||
				!canUse
			}
		>
			开立
		</Button>
	)
}
