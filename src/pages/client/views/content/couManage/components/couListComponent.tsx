import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import { commonModuleApi } from '@src/pages/client/api'
import { Button, message, Modal } from 'antd'
import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import OperatingArea from '@src/globalComponents/OperatingArea'
import { stampToTime, timeToStamp } from '@utils/timeFilter'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'

interface Props {
	/**
	 * @description 列表上部分的卡片
	 */
	pageCard?: any
	/**
	 * @description 列表上面的标题
	 */
	listTitle?: string
	/**
	 * @description tableColumns
	 */
	getColumns: any
	/**
	 * @description 点击搜索回调
	 */
	requireList: any
	/**
	 * @description 传入searchBar组件
	 */
	getSearchBar: any
	/**
	 * @description 依赖刷新
	 */
	refreshDev?: any
	defaultRefreshDev?: any
	isShowExport?: boolean
	initParams?: any
}

const getCouList = (props: Props) => {
	let { pageCard, listTitle, getColumns, requireList, getSearchBar, refreshDev, defaultRefreshDev, isShowExport = false, initParams = {} } = props
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 }) //表格 配置
	const [cardData, setCardData] = useState<any>() //卡片数据
	const [loading, setLoading] = useState(true) //表格加载
	const [dataSource, setDataSource] = useState<any[]>([]) //表格数据
	const [searchParams, setSearchParams] = useState<any>(initParams ?? {}) //searchbar 字段
	const [exportLoading, setExportLoading] = React.useState(false)
	const [modal, contextHolder] = Modal.useModal()

	useEffect(() => {
		//获取顶部卡片的数据
		getCardData()
	}, [])

	// 解决我的融信页面调用未Tab形式刷新多次的问题
	useEffect(() => {
		if (refreshDev && defaultRefreshDev) {
			if (refreshDev == defaultRefreshDev) {
				getListData()
			}
		} else {
			// 兼容BAU的调用
			getListData()
		}
	}, [pagination.current, pagination.pageSize, searchParams, refreshDev, defaultRefreshDev])

	const goSignProtocol = () => {
		modal.info({
			title: '提示',
			content: <div>请联系客户经理，签署平台合作协议</div>,
			okText: '已知悉',
		})
	}
	//获取顶部卡片的数据
	const getCardData = async () => {
		const res = await requireList.getCardData.requireApi(requireList.getCardData.params)
		// 目前只有一个授信银行
		if (res?.list?.[0]?.signStatus == '0') {
			goSignProtocol()
		}
		setCardData(res)
	}

	//获取表格数据
	const getListData = () => {
		setLoading(true)
		if (searchParams.maxDueDate) {
			searchParams.maxDueDate = timeToStamp(searchParams.maxDueDate, 'end')
		}
		requireList.getListData
			.requireApi({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...requireList.getListData.params,
				...searchParams,
			})
			.then(
				(res: any) => {
					pagination.total = res['total']
					if (res.list) {
						res.list.forEach((i: { key: any; transferUuid: string }) => {
							i.key = i.transferUuid
						})
						setDataSource([...res['list']])
					}
					setLoading(false)
				},
				err => setLoading(false)
			)
	}

	//导出融信信息
	const exportCouCsv = async () => {
		setExportLoading(true)
		let serverTime = await commonModuleApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`
		let type = 'application/vnd.ms-excel'
		requireList.exportCouCsv
			.requireApi({
				...searchParams,
				...requireList.exportCouCsv.params,
			})
			.then((response: any) => {
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}

	//search 提交 事件
	const onSubmit = (params: { toPubKey: string; couNo: [string]; due: any; maxDueDate: any; minDueDate: any }) => {
		const cashStatusList = params['cashStatusList']
			? Object.prototype.toString.call(params['cashStatusList']) === '[object Array]'
				? params['cashStatusList']
				: params['cashStatusList'].includes(',')
				? params['cashStatusList'].split(',')
				: [params['cashStatusList']]
			: null
		if (params.due && params.due.length === 2) {
			params.maxDueDate = params.due[1]
			params.minDueDate = params.due[0]
			delete params.due
		}
		pagination.current = 1
		setSearchParams({ ...params, cashStatusList })
	}
	//Search 重置
	const onClear = () => {
		pagination.current = 1
		setSearchParams({})
	}

	//表格改变 当前页面
	const handleChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	//表格改变 改变 size
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	return (
		<Box>
			<LayoutSlot>
				<div>
					<div>{pageCard && pageCard(cardData)}</div>
					<div>
						{listTitle && <div className="wx_List_title">{listTitle}</div>}
						<div className="operating-area-top-bottom-20">
							{getSearchBar(onClear, onSubmit)}
							{isShowExport && (
								<Button style={{ marginLeft: '20px' }} type="primary" onClick={exportCouCsv} loading={exportLoading}>
									导出
								</Button>
							)}
						</div>
						<BaseTable
							{...pagination}
							columns={getColumns}
							onPageChange={handleChange}
							onSizeChange={handleSizeChange}
							loading={loading}
							dataSource={dataSource}
						/>
					</div>
				</div>
				{/* modal.useModal 的配套context */}
				{contextHolder}
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.card-wrapper {
		border-radius: 0px;
		border: none;
	}
	.ant-card-bordered {
		border: none;
		border-bottom: 1px solid #f0f0f0;
	}
	.wx_List_title {
		font-size: 16px;
		font-weight: bold;
		text-align: left;
		color: rgba(0, 0, 0, 0.85);
		// line-height: 24px;
		background: none;
		border-bottom: 1px solid #e9e9e9;
		width: 100%;
	}
`
export default getCouList
