//这里是融信开立的状态 卡片组件
import React from 'react'
import { Card, Alert, Button, Modal } from 'antd'
import styled from 'styled-components'
import { history } from '@src/utils/router'
import {
	cardInfoForNormal,
	cardInfoForBuild,
	toCreateCouButton,
	toCreateCouButtonForBuild,
	cardInfoForTeam,
	toCreateCouButtonForTeam,
} from './createStatusCardConfig'
import { setStorage } from '@factor/biz/bizIndex'
const couBg = require('@src/assets/images/cou/cou_bg.png')
interface Props {
	/**
	 * @description 是否是限额操作员
	 */
	isCBuildOperator: boolean
	/**
	 * @description 是否是项目操作员
	 */
	isCTeamOperator: boolean
	/**
	 * @description 传入卡片的数据
	 */
	data: any
}

const messageTips = {
	noItemMessage: '您还没有获取授信，请联系金融机构进行授信。',
	activeFinanceServiceMessage: '您还没有激活该金融机构，请联系企业管理员登录系统激活该金融机构。',
	financeConfigIsFullMessage: '该金融机构信息不完善，请联系平台运营处理。',
}

const CreateStatusCard = (props: Props) => {
	const [modal, contextHolder] = Modal.useModal()
	const { isCBuildOperator = false, isCTeamOperator = false, data } = props
	//	去开立  存储  该融信的 信息 方便表单页面使用
	const goCreateCou = dataInfo => {
		setStorage({ createCouInfo: dataInfo })
		history.push('/content/couManage/create')
	}

	const goSignProtocol = dataInfo => {
		setStorage({ createCouInfo: dataInfo })
		// history.push('/content/couManage/protocolSign')
		modal.info({
			title: '提示',
			content: <div>请联系客户经理，签署平台合作协议</div>,
			okText: '已知悉',
		})
	}

	//生成 融信额度 卡片
	const pageCard = item => {
		/**
		 * 判断是否可用
		 * 1、没有授信信息的时候
		 * 2、没有激活该金融机构
		 * 3、该金融机构信息不完善
		 * canUse
		 * checkUseAbled()
		 */
		let message: string = ''
		const checkUseAbled = (initItem: any): boolean => {
			if (initItem) {
				// 134 融信开立
				// message = initItem.status !== 'confirmed' ? '该笔授信记录未审核通过，请联系平台运营处理' : ''
				// if (message) return false
				// if (initItem.activeFinanceService && initItem.financeConfigIsFull) {
				// 	return true
				// } else {
				// 	message = !initItem.activeFinanceService
				// 		? messageTips.activeFinanceServiceMessage
				// 		: !initItem.financeConfigIsFull
				// 			? messageTips.financeConfigIsFullMessage
				// 			: ''
				// }
				// 3/18删除以上 对于‘该笔授信记录未审核通过，请联系平台运营处理’ &&
				// '您还没有激活该金融机构，请联系企业管理员登录系统激活该金融机构。'的校验
				if (initItem.financeConfigIsFull) return true
				message = messageTips.financeConfigIsFullMessage
			} else {
				message = messageTips.noItemMessage
			}
			return false
		}
		let canUse = checkUseAbled(item)

		//不同角色展示的卡片信息
		const getCardInfo = item => {
			if (isCBuildOperator) {
				return cardInfoForBuild(item)
			} else if (isCTeamOperator) {
				return cardInfoForTeam(item)
			} else {
				return cardInfoForNormal(item)
			}
		}
		//不同角色开立按钮置灰判断
		const getDispalyedBtnStatus = item => {
			let dom = null
			// if (isCBuildOperator) {
			// 	dom = toCreateCouButtonForBuild(item, canUse, () => goCreateCou(item))
			// } else if (isCTeamOperator) {
			// 	dom = toCreateCouButtonForTeam(item, canUse, () => goCreateCou(item))
			// } else {
			// 	dom = toCreateCouButton(item, canUse, () => goCreateCou(item))
			// }
			// if (item?.signStatus == '0') {
			// 	// 进页面里面判断 1是否是江阴开户行 2是否在融信有效期内
			// 	dom = (
			// 		<Button className="big-btn" type="primary" size="middle" onClick={() => goSignProtocol(item)} disabled={!canUse}>
			// 			签署协议
			// 		</Button>
			// 	)
			// } else {
			// 	dom = toCreateCouButton(item, canUse, () => goCreateCou(item))
			// }
			dom = toCreateCouButton(item, canUse, () => (item?.signStatus == '0' ? goSignProtocol(item) : goCreateCou(item)))
			return <div className="btn">{dom}</div>
		}
		return (
			<div>
				<Card className="status" key={item ? item.id : 0}>
					<div className="content">
						<div className="leftImg">
							<img width={118} height={116} src={require('@src/assets/images/cou/cou_icon.png')} alt="" />
						</div>
						<div className="rightBox">
							<div className="line"></div>
							<div className="title">
								<img width={22} src={require('@src/assets/images/cou/time_icon.png')} alt="" />
								<span style={{ marginTop: '2px', color: '#6578A1' }}>到期日：{item ? item.creditDueDate : '- -'}</span>
							</div>
							<div className="detailContent" style={{ color: '#000000' }}>
								{getCardInfo(item)}
							</div>
							<div className="btnbox">{getDispalyedBtnStatus(item)}</div>
						</div>
					</div>
				</Card>
				{canUse ? null : <Alert message={message} type="error" style={{ width: '100%', margin: '20px 0 0 0', height: '40px' }} />}
				{/* modal.useModal 的配套context */}
				{contextHolder}
			</div>
		)
	}

	const showCreditCard = () => {
		let creditCards: any = []
		if (data && data.length > 0) {
			creditCards = data.map(item => {
				return pageCard(item)
			})
		} else {
			creditCards = pageCard(null)
		}
		return creditCards
	}
	return <Box>{showCreditCard()}</Box>
}

const Box = styled.div`
	margin-bottom: 10px;
	.status {
		margin-bottom: 20px;
		.detailContent {
		}
		.ant-card-body {
			font-size: 14px;
			color: #fff;
			padding: 12px 24px;
			.content {
				height: 116px;
				display: flex;
				.rightBox {
					width: 100%;
					margin-left: 32px;
					padding-left: 32px;
					box-sizing: border-box;
					position: relative;
					.line {
						position: absolute;
						top: 11px;
						left: 0px;
						width: 1px;
						height: 96px;
						border-left: 1px solid rgba(180, 190, 213, 1);
					}
					.title {
						display: flex;
						align-items: center;
						color: rgba(255, 255, 255, 0.85);
						img {
							margin-right: 5px;
						}
						margin-bottom: 20px;
					}
					.item {
						padding-left: 5px;
						// width: 480px;
						display: flex;
						div {
							margin-right: 80px;
							.item_title {
								color: rgba(0, 0, 0, 0.38);
								margin-bottom: 10px;
							}
							p:nth-child(2) {
								font-size: 24px;
								font-weight: 500;
							}
							p {
								margin-bottom: 0;
								color: rgba(23, 23, 23, 0.85);
							}
						}
					}
					.btnbox {
						position: absolute;
						top: 50%;
						right: 60px;
						transform: translateY(-50%);
					}
				}
			}
		}
	}
	.ant-card {
		width: 100%;
		background: url(${couBg});
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}
	.ant-card-bordered {
		border-bottom: none !important;
	}
`

export default CreateStatusCard
