import React, { useEffect, useState } from 'react'
import { Modal, Form, Input, Row, Col, Select, Calendar, Tooltip } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import styled from 'styled-components'
import { transferModuleApi, commonModuleApi } from '@src/pages/client/api'
import moment from '@src/utils/moment'

interface Props {
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 回显 日期数据
	 */
	dueDateObj: { str: string; date?: number; days?: string | number }
	/**
	 * @description 提交确定操作
	 */
	onOk: (any) => void
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
	/**
	 * @description 上级传过来的 信息 用于判断金额
	 */
	payAmount?: any
	/**
	 * @description 融信的 最大账期 限制
	 */
	dueDate: string
	/**
	 * @description 融信的 最大宽限日
	 */
	periodGraceCredit: string
	/**
	 * 开立最大账期(针对于银行总行设置)
	 */
	periodGraceCreate: string
	/**
	 * 开立最小账期(针对于银行总行设置)
	 */
	minPeriodGraceCreate: string
}

export default function CalendarModal(props: Props) {
	const { visible, onCancel, onOk, dueDate, periodGraceCredit, dueDateObj, periodGraceCreate, minPeriodGraceCreate } = props
	const [form] = Form.useForm()
	const [limitDays, setLimitDays] = useState<number>(0) //日期限制
	const [currentTime, setCurrentTime] = useState<any>(null)
	const [dateSpecialWorkDay, setDateSpecialWorkDay] = useState<string[]>([]) //工作日 1-5 的 特殊日  不可选
	const [dateSpecialWeekDay, setDateSpecialWeekDay] = useState<string[]>([]) //周末的 特殊日  可选
	const [dateValue, setDateValue] = useState<any>()
	useEffect(() => {
		if (visible) {
			window.moment = moment
			getCurrentTime()
		}
	}, [visible])

	useEffect(() => {
		getDateSpecial()
		showDataInit()
		getLimitDays()
	}, [currentTime])

	// 获取当前时间
	const getCurrentTime = () => {
		commonModuleApi.syncTime().then(res => {
			setCurrentTime(new Date(res))
		})
	}

	//获取账期
	const getLimitDays = () => {
		let curLimitDays = 0
		const dueDateDiffToday = moment(dueDate).endOf('day').diff(moment(currentTime).endOf('day'), 'days')
		//开立时，需要在授信到期日的前一天以及之前   距离今天可用日期最少1天 --> 授信到期日当天，距离今天可用日期最少0天
		if (dueDateDiffToday >= 0) {
			//最大账期限 《= 兑付到期日 + 授信宽限日期
			curLimitDays = dueDateDiffToday + Number(periodGraceCredit || 0)
			//最大账期 <=360天
			if (curLimitDays > Number(periodGraceCreate)) {
				curLimitDays = Number(periodGraceCreate)
			}
			setLimitDays(curLimitDays)
		}
	}

	//	获取年份列表 今年 - 10年
	const options: any = []
	for (let i = moment(currentTime).year(); i < moment(currentTime).year() + 10; i += 1) {
		options.push(
			<Select.Option key={i} value={i} className="year-item">
				{i}
			</Select.Option>
		)
	}

	//回显 日期
	const showDataInit = () => {
		const { date, days } = dueDateObj
		if (days) {
			form.setFieldsValue({
				days: days,
			})
		}
		if (date) {
			setDateValue(date)
		}
	}

	// 当前日期之前的日期不允许被选择(不包括当天)
	const disablebefore = date => {
		return date < moment(currentTime).subtract(1, 'days')
	}
	// 当前日期 limitDay 最大账期 后的 日期不能选
	const disableAfterYear = date => {
		return date > moment(currentTime).add(limitDays, 'day').endOf('day')
	}
	// 当天日期不许与被选择(包括当天)
	const disableDateToday = date => {
		return date < moment(currentTime).endOf('day')
	}

	// 最小账期前的日期不可选
	const disableDateMinDateOffset = date => {
		return (
			date <
			moment(currentTime)
				.add(Number(minPeriodGraceCreate) - 1, 'days')
				.endOf('day')
		)
	}

	// 双休日  并且不在 特殊日期中 才是 不可选
	const disableDateWeekDay = date => {
		const weekDay = date.day()
		//将date转化为YYYYMMDD的格式
		const dayTime = moment(date).add(0, 'day').format('YYYY-MM-DD')
		return (!dateSpecialWeekDay.includes(dayTime) && weekDay === 6) || (!dateSpecialWeekDay.includes(dayTime) && weekDay === 0)
	}

	// 工作日  在 特殊日期中  不可选
	const disableDateWorkDay = date => {
		//将date转化为YYYYMMDD的格式
		const dayTime = moment(date).add(0, 'day').format('YYYY-MM-DD')
		return dateSpecialWorkDay.includes(dayTime)
	}

	//工作日和 特殊节假日 不准选
	const disableDate = date => {
		return (
			disablebefore(date) ||
			disableAfterYear(date) ||
			disableDateToday(date) ||
			disableDateMinDateOffset(date) ||
			disableDateWeekDay(date) ||
			disableDateWorkDay(date)
		)
	}

	const getDateSpecial = () => {
		const IntervalStart = moment(currentTime).add(0, 'months').format('YYYY-MM')
		const IntervalEnd = moment(currentTime).add(12, 'months').format('YYYY-MM')
		//当前月 至 12月后
		transferModuleApi
			.getDateSpecial({ startMonth: IntervalStart, endMonth: IntervalEnd })
			.then(res => {
				let SpecialWeekDay = new Array()
				let SpecialWorkDay = new Array()
				res.map(item => {
					let strMonth = item.monthInYear
					item.specialDateJsonList.map(specialDate => {
						if (!specialDate.holiday) {
							if ((specialDate.dayNO + '').length === 1) specialDate.dayNO = '0' + specialDate.dayNO
							SpecialWeekDay.push(strMonth + '-' + specialDate.dayNO)
						} else {
							if ((specialDate.dayNO + '').length === 1) specialDate.dayNO = '0' + specialDate.dayNO
							SpecialWorkDay.push(strMonth + '-' + specialDate.dayNO)
						}
					})
				})
				setDateSpecialWeekDay(SpecialWeekDay)
				setDateSpecialWorkDay(SpecialWorkDay)
			})
			.catch(error => {
				console.log(error)
			})
	}

	const getDate = value => {
		if (value) {
			const data = moment(currentTime).add(value, 'day').format('YYYY-MM-DD') //value 对应的 YYYY-MM-DD
			const str = moment(currentTime).add(value, 'day') //value 对应的 monemnt 时间
			const weekendIsWork = (str.day() === 6 || str.day() === 0) && dateSpecialWeekDay.includes(data) // 判断周末是否需要工作
			const weekdayIsWork = !(str.day() === 6 || str.day() === 0) && !dateSpecialWorkDay.includes(data) // 判断周一到周五是否需要工作
			//首先判断 周末的 日子 是不是 特殊日
			if (weekendIsWork || weekdayIsWork) {
				setDateValue(str)
				return Promise.resolve()
			} else {
				setDateValue(str)
				return Promise.reject('请选择工作日')
			}
		} else {
			setDateValue(moment(currentTime).endOf('day'))
			return Promise.reject('请选择工作日')
		}
	}
	return (
		<Modal
			open={visible}
			title={'兑付到期日'}
			onCancel={() => {
				onCancel()
			}}
			destroyOnClose={true}
			onOk={() => {
				form
					.validateFields()
					.then(res => {
						const str = moment(dateValue).format('YYYY-MM-DD') //+ '(账期' + res.days + '天)'
						console.log(moment(dateValue).format('YYYY-MM-DD'))
						onOk({ str, date: moment(dateValue).valueOf(), days: res.days })
					})
					.catch(err => console.log(err))
			}}
		>
			<Box>
				<Form form={form}>
					<Row>
						<Col span={14}>
							<Form.Item
								name="days"
								label="账期"
								validateFirst
								rules={[
									{
										required: true,
										message: '请输入账期',
										whitespace: true,
									},
									{
										pattern: /^[0-9]*$/,
										message: '请输入正整数',
									},
									{
										validator(_, value) {
											return getDate(value)
										},
									},
									{
										validator(_, value) {
											if (value > limitDays) {
												return Promise.reject('不大于最大账期。')
											} else if (value < minPeriodGraceCreate) {
												return Promise.reject(`最小账期${minPeriodGraceCreate}天`)
											} else {
												return Promise.resolve()
											}
										},
									},
								]}
								getValueFromEvent={event => {
									event.target.value = event.target.value.replace(/^0/g, '')
									event.target.value = event.target.value.replace(/\s+/g, '')
									return event.target.value
								}}
							>
								<Input suffix="天" />
							</Form.Item>
						</Col>
						<Col span={10}>
							<div className="tip">
								最大账期：{limitDays}天 &nbsp;&nbsp;
								<Tooltip
									placement="top"
									title={
										limitDays === 0
											? '已过授信到期日，无法开立融信'
											: limitDays >= 1 && limitDays < Number(periodGraceCreate)
											? '最大账期以授信到期日为基准，按照银行要求设定'
											: limitDays === Number(periodGraceCreate)
											? `按照银行要求，最大账期不能超过${periodGraceCreate}天`
											: ''
									}
								>
									<ExclamationCircleOutlined style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer' }} />
								</Tooltip>
							</div>
						</Col>
					</Row>

					<div className="site-calendar-demo-card">
						{currentTime && (
							<Calendar
								fullscreen={false}
								onChange={value => {
									if (value) {
										setDateValue(value)
										console.log(value.diff(moment(currentTime).endOf('day'), 'day') + 1)

										form.setFieldsValue({
											days: (value.diff(moment(currentTime).endOf('day'), 'day') + 1).toString(),
										})
									}
								}}
								disabledDate={disableDate}
								value={moment(dateValue)}
								headerRender={({ value, type, onChange, onTypeChange }) => {
									const start = 0
									const end = 12
									const monthOptions: any = []
									const year = value.year()
									const months: any = moment.monthsShort()

									for (let index = start; index < end; index++) {
										monthOptions.push(
											<Select.Option className="month-item" value={`${index}`} key={`${index}`}>
												{months[index]}
											</Select.Option>
										)
									}
									const month = value.month()

									return (
										<div style={{ padding: 8 }}>
											<Row gutter={8} justify={'end'}>
												<Col>
													<Select
														size="small"
														dropdownMatchSelectWidth={false}
														className="my-year-select"
														onChange={newYear => {
															const now = value.clone().year(Number(newYear))
															onChange(now)
															form.validateFields(['days'])
														}}
														value={String(year)}
													>
														{options}
													</Select>
												</Col>
												<Col>
													<Select
														size="small"
														dropdownMatchSelectWidth={false}
														value={String(month)}
														onChange={selectedMonth => {
															const newValue = value.clone().month(parseInt(selectedMonth, 10))
															onChange(newValue)
															form.validateFields(['days'])
														}}
													>
														{monthOptions}
													</Select>
												</Col>
											</Row>
										</div>
									)
								}}
							/>
						)}
					</div>
				</Form>
			</Box>
		</Modal>
	)
}

const Box = styled.div`
	padding: 10px 24px;
	.ant-alert {
		margin-bottom: 10px;
	}
	.ant-input-affix-wrapper {
	}
	.tip {
		text-align: center;
		color: #7f7f7f;
		line-height: 30px;
	}
`
