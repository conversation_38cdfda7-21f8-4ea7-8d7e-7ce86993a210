import { couSignInfo, getSignId } from '@utils/sign'
function dataSign(dataList: Array<any>, serverTime: any, operationType: string) {
	let signData: any = {}
	const transfers: any = []
	const mapTradeVerifiableOperation = ['couCreat', 'couTransfer', 'reject'] //cou签名表
	console.log(dataList, serverTime, operationType, '7897897123')
	for (const data of dataList) {
		signData = {}
		const verifyObj: any = {}
		let tradeVerifiableSignData = ''
		let signId = null
		switch (operationType) {
			case 'couCreat': //cou创建
				signData['transferAmount'] = (parseFloat(data['sumTransferAmountInCent']) / 100).toFixed(2)
				signData['fromName'] = data['fromName']
				signData['toName'] = data['toName']
				signData['couInfo'] = data.contract.map((item: any) => ({
					invoiceCodeAndNumber: item.invoiceNumber + '_' + item.contractCode,
					publishName: item.publishName,
					amount: item.amount,
				}))
				signData['createTime'] = serverTime
				tradeVerifiableSignData = data['tradeVerifiableSignData']
				break
			case 'couTransfer': //cou流转
				signData['transferAmount'] = (parseFloat(data['sumTransferAmountInCent']) / 100).toFixed(2)
				signData['fromName'] = data['fromName']
				signData['toName'] = data['toName']
				signData['invoiceCodeAndNumberList'] = data.contract.map((item: any) => {
					return item.invoiceNumber + '_' + item.invoiceCode
				})
				signData['couInfo'] = data.transferCous.map((item: any) => ({
					parentUuid: item.parentUuid,
					originalUuid: item.originalUuid,
					publishName: item.publishName,
					amount: item.shouldprice,
				}))
				signData['createTime'] = serverTime
				tradeVerifiableSignData = data['tradeVerifiableSignData']
				break
			case 'receive':
				signData['sign'] = data.depositOriginalData ? JSON.parse(data.depositOriginalData).sign : ''
				signData['transferNo'] = data['transferNo']
				signData['confirmTime'] = serverTime
				if (data['fromType'] === 'C2') {
					signData['couInfo'] = data.transferCous.map((item: any) => ({
						uuid: item.uuid,
						publishName: item.publishName,
						amount: (parseFloat(item.amount) / 100).toFixed(2),
					}))
				} else if (data['fromType'] === 'S1') {
					signData['couInfo'] = data.transferCous.map((item: any) => ({
						uuid: item.uuid,
						parentUuid: item.parentUuid,
						originalUuid: item.originalUuid,
						publishName: item.publishName,
						amount: (parseFloat(item.amount) / 100).toFixed(2),
					}))
				}
				signId = getSignId()
				data['signId'] = signId
				tradeVerifiableSignData = signId + data['transferUuid']
				break
			case 'reject':
				signId = getSignId()
				data['signId'] = signId
				tradeVerifiableSignData = signId + data['transferUuid']
				break
		}

		signData = JSON.stringify(signData)
		if (mapTradeVerifiableOperation.includes(operationType)) {
			//cou签名
			verifyObj['tradeVerifiable'] = couSignInfo(data['signId'], tradeVerifiableSignData)
		}

		if (operationType === 'couCreat' || operationType === 'couTransfer') {
			transfers.push(verifyObj)
		} else {
			transfers.push({
				transferUuid: data['transferUuid'],
				...verifyObj,
			})
		}
	}
	return transfers
}

const couCashStatusList = [
	{ key: 'WAIT_CASH', name: '待兑付' },
	{ key: 'CASH_OK', name: '兑付成功' },
]

const pledgeStatusList = [
	{ key: '1', name: '质押中' },
	{ key: '0', name: '未质押' },
]

export { dataSign, couCashStatusList, pledgeStatusList }
