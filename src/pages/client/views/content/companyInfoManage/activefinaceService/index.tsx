import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import styled from 'styled-components'
import BaseTable from '@src/globalComponents/BaseTable'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import { companyInfoManageModuleApi } from '@src/pages/client/api'

const ActiveFinaceServiceList = () => {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [loading, setLoading] = useState(false)

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize])

	function getList() {
		setLoading(true)
		console.log(pagination)
		companyInfoManageModuleApi
			.getCompanyCircleList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
			})
			.then(
				res => {
					if (res) {
						setPagination({
							...pagination,
							total: res.total,
						})
						if (res.list && res.list.length > 0) {
							setDataSource([...res.list])
						} else {
							setDataSource([])
						}
						setLoading(false)
					}
				},
				err => {
					setLoading(false)
					pagination.total = 0
					setDataSource([])
				}
			)
	}

	//获取table的column
	const getColumns = () => {
		const pageName = 'ActiveFinaceService'
		return getColumnsByPageName(pageName)
	}

	const handlePageChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						rowKey="fiUuid"
					/>
				</div>
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div``

export default ActiveFinaceServiceList
