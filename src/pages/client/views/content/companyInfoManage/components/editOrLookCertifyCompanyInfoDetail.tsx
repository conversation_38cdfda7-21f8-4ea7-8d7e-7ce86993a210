/*
 * @Date: 2022-07-08 14:06:53
 * @LastEditors: jcl
 * @LastEditTime: 2022-09-30 16:30:39
 * @FilePath: \frontend-web\src\pages\client\views\content\companyInfoManage\components\editOrLookCertifyCompanyInfoDetail.tsx
 * @Description: file content
 */
import React, { useState, useEffect } from 'react'
import { Alert, Button, Form, message, Modal, Radio, Skeleton, Spin, Tag, Tooltip, Upload } from 'antd'
import styled from 'styled-components'
import { loginApis, companyInfoManageModuleApi } from '@src/pages/client/api'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import GetFormList from '@src/globalComponents/companyInfoForm/GetFormList'
import { history } from '@src/utils/router'
import { RadioChangeEvent } from 'antd/lib/radio'
import { ExclamationCircleOutlined, InfoCircleFilled } from '@ant-design/icons'
import { getStorage } from '@src/pages/client/biz/bizIndex'
import { useShowRejectImg } from '@src/global-hooks/useShowRejectImg'
import SmallTitle from '@src/globalComponents/small-title'
import { observer } from 'mobx-react-lite'
import fileUploading from '@src/globalStore/fileUploading'
import { validatePersonIdCode } from '@src/globalBiz/gBiz'
const layout = {
	labelCol: { span: 9 },
	wrapperCol: { span: 12 },
}
interface I {
	isLook?: boolean
}
const tagStyle = { width: '100px', lineHeight: '24px', 'text-align': 'center' }

const certificateText =
	'证件正面为带有人像的证件信息页,证件反面为带有发证机关国徽、区徽、有效日期或其他有效信息页，如证件人像面已包含姓名、证件号码、有效期等信息，证件反面照片可与证件正面照片相同'
const certificateValueLabelMap = {
	0: {
		text: '',
		labelArr: ['身份证（人像）', '身份证（国徽）'],
	},
	1: {
		text: certificateText,
		labelArr: ['港澳居民来往内地通行证(正面)', '港澳居民来往内地通行证(反面)'],
	},
	2: {
		text: certificateText,
		labelArr: ['台湾居民来往内地通行证(正面)', '台湾居民来往内地通行证(反面)'],
	},
	3: {
		text: certificateText,
		labelArr: ['护照(正面)', '护照(反面)'],
	},
}

function EditOrLookCertifyCompanyInfoDetail({ isLook = false }: I) {
	const [form] = Form.useForm()
	// 页面认证进度
	const [status, setStatus] = useState<any>(null)
	const [uuid, setUuid] = useState('')
	const [email, setEmail] = useState('')
	const [remark, setRemark] = useState('')
	const [spinning, setSpinning] = useState(false)
	const { showRejectImg } = useShowRejectImg(remark)
	// 企业类型
	const [companyType, setCompanyType] = useState<string>('CS')
	const [curCertificate, setCurCertificate] = useState(certificateValueLabelMap[0])
	const certificateTypeChange = value => {
		// value
		setCurCertificate({ ...certificateValueLabelMap[value] })
	}
	const [legalPersonFormItemList, setLegalPersonFormItemList] = useState([
		{ key: 'LegalPerson', span: 12, isLook, label: companyType === 'CS' ? '法定代表人' : '法定代表人/负责人' },
		{ key: 'CertificateType', span: 12, isLook, label: '证件类型', onchange: certificateTypeChange },
		{ key: 'LegalPersonIdCode', span: 12, isLook, label: '证件号码' },
		{ key: '_blank', span: 12, isLook, label: '-' },
		{ key: 'IDPortrait', span: 12, isLook, label: curCertificate.labelArr[0], showExampleImg: curCertificate.text ? false : true },
		{ key: 'IDNationalEmblem', span: 12, isLook, label: curCertificate.labelArr[1], showExampleImg: curCertificate.text ? false : true },
	])

	const [companyMsgFormItemList, setCompanyMsgFormItemList] = useState([
		{ key: 'CompanyName', span: 12, verifyExistence: true, isLook, label: '企业名称' },
		{ key: 'SocialCreditCode', span: 12, verifyExistence: true, isLook, label: '统一社会信用代码' },
		{ key: 'CorporationScale', span: 12, isLook, label: '企业规模' },
		{ key: 'Industry', span: 12, isLook, label: '所属行业' },
		{ key: 'ProvinceCity', span: 12, isLook, label: '所在地区' },
		{ key: 'Address', span: 12, isLook, label: '公司地址' },
		{ key: 'BusinessLicense', span: 12, isLook, label: '营业执照' },
		{ key: 'OpeningPermit', span: 12, isLook, label: companyType === 'CS' ? '其他附件' : '金融机构许可证' },
	])

	useEffect(() => {
		fileUploading.init()
		initPage()
	}, [])

	const initPage = () => {
		// add editCertifyCompanyInfoDetail
		// edit editCertifyCompanyInfoDetail status
		// look certifyCompanyInfoDetail
		// 1.3.3
		// 设置证件类型的默认值
		form.setFieldsValue({
			certificateType: 0,
		})
		setPageDetails()
	}

	//处理图像回显
	const _handlePreviewPic = (record: any) => {
		let fileContent: any[] = [],
			files = {}
		try {
			fileContent = JSON.parse(record.fileContent)
		} catch (error: any) {
			console.error(error)
			return files
		}
		if (Object.prototype.toString.call(fileContent) !== '[object Array]') return files
		fileContent.forEach((item, index) => {
			const newItem = {
				name: item.fileName,
				response: { data: item.fileUrl },
				status: 'done',
				uid: item.fileUid,
			}
			if (item.fileUid === 'openingPermit') {
				files['openingPermit'] = files['openingPermit'] || []
				newItem.uid += index + ''
				files['openingPermit'].push({ ...newItem })
				files['enterpriseCertificate'] = files['openingPermit']
			} else {
				files[item.fileUid] = files[item.fileUid] || []
				files[item.fileUid].push(newItem)
			}
		})
		return files
	}

	const setPageDetails = async () => {
		setSpinning(true)
		let record
		try {
			record = await companyInfoManageModuleApi.getCompanyInfo({})
			setSpinning(false)
		} catch (error: any) {
			return setSpinning(false)
		}
		// 未认证时
		if (!record?.uuid) {
			setStatus('')
		} else {
			setStatus(record.status)
			setUuid(record.uuid)
			setEmail(record.email)
			const res = await companyInfoManageModuleApi.getCompanyDetailByUuid({ uuid: record.uuid }).catch(err => {
				console.log('err', err)
			})
			setRemark(res?.remark)
			let files = _handlePreviewPic(record)
			//组装地址信息回显数据字段
			let provinceCity: any[] = []
			if (record.province && record.city) {
				const provinceCode = record.province.substring(1)
				const cityCode = record.city.substring(1)
				//将省份信息拼接好回显展示
				provinceCity = [provinceCode, cityCode]
			}

			// 如果fiCode为''，null，'--'，则为普通企业
			let type: string
			if (typeof record.fiCode === 'string') {
				const fiCode = record.fiCode.trim()
				if (fiCode === '' || fiCode === '--') {
					type = 'CS'
				} else {
					type = 'FI'
				}
			} else {
				type = 'CS'
			}

			setCompanyType(type)
			form.setFieldsValue({ ...record, provinceCity, companyType: type, ...files })
		}
	}
	const renderStatusTag = () => {
		if (status === null) return ''
		return {
			'': (
				<>
					<Tag color={'rgba(170, 170, 170, 1)'} style={tagStyle}>
						未认证
					</Tag>
				</>
			),
			INIT: (
				<>
					<Tag color={'rgba(245, 154, 35, 1)'} style={tagStyle}>
						审核中
					</Tag>
				</>
			),
			REJECT: (
				<>
					<Tag color={'rgba(217, 0, 27, 1)'} style={tagStyle}>
						不通过
					</Tag>
					<a onClick={() => history.push('/content/companyInfoManage/editCertifyCompanyInfoDetail')}>去重新认证</a>
				</>
			),
			CONFIRM: (
				<>
					<Tag color={'rgba(112, 182, 3, 1)'} style={tagStyle}>
						通过
					</Tag>
				</>
			),
		}[status]
	}

	// 格式化需要上传的文件列表
	const formatFileContent = () => {
		let normalFile = true
		let keyList = [
			'idPortraitoad',
			'idNationalEmblem',
			'businessLicense',
			'openingPermit',
			'CFCACertificateApplicationForm',
			'CFCACertificateOfAuthorization',
			'idCardOfHandler',
		]
		let targetArr = []
		keyList.forEach(keyName => {
			let itemArr = form.getFieldValue(keyName)
			if (Array.isArray(itemArr)) {
				let newItemArr = []
				itemArr.forEach(file => {
					if (file?.response?.data) {
						newItemArr.push({
							fileName: file?.name,
							fileUrl: file?.response?.data,
							fileUid: keyName,
						})
					} else {
						// 文件没有上传完成,不让提交
						normalFile = false
					}
				})
				targetArr = targetArr.concat(newItemArr)
			}
		})

		if (normalFile) {
			return JSON.stringify(targetArr)
		} else {
			return false
		}
	}

	// 校验身份证是否合法
	const checkIdIsLegal = (idVal, idType) => {
		let _IDRe18 = /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
		let _IDre15 = /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/
		let checkPassed: boolean = false

		if (idType === 0) {
			if (_IDRe18.test(idVal) || _IDre15.test(idVal)) {
				checkPassed = true
			}
		} else if (idType === 1) {
			if (idVal.length === 9) {
				checkPassed = true
			}
		} else if (idType === 2) {
			if (idVal.length === 8) {
				checkPassed = true
			}
		} else if (idType === 3) {
			if (idVal.length <= 20) {
				checkPassed = true
			}
		}

		if (!checkPassed) {
			message.error('证件号码不合法')
			return false
		} else {
			return true
		}
	}

	const registerCompany = async type => {
		let params = getRegisterParams()
		params.companyBasic['uuid'] = uuid
		if (!params.companyBasic.fileContent) {
			message.warning('提交失败，请检查所上传文件后重新提交')
			return
		}
		let res
		if (type === 'FI') {
			// 金融机构的默认值
			params.companyAddr.corporationScale = 'LARGE'
			params.companyAddr.industry = 'INDUSTRY_J'
			params.companyBasic.fiCode = form.getFieldValue('fiCode')
			if (status !== 'REJECT') {
				res = await loginApis.certifyCompany(params)
			} else {
				res = await loginApis.reCertifyCompany(params)
			}
		} else if (type === 'CS') {
			// 和sp 保持一致; 没有值时用--,保证类型是普通企业
			params.companyBasic.fiCode = '--'
			params.companyAddr.corporationScale = form.getFieldValue('corporationScale')
			params.companyAddr.industry = form.getFieldValue('industry')
			if (status !== 'REJECT') {
				res = await loginApis.certifyCompany(params)
			} else {
				res = await loginApis.reCertifyCompany(params)
			}
		}
		if (res) {
			if (res.returnCode % 1000 === 0) {
				Modal.info({
					title: '提交成功',
					icon: <InfoCircleFilled />,
					content: (
						<>
							<p>您的企业认证申请已成功提交。</p>
							<p>3-5个工作日审核通过后，您可以登录系统完善企业信息。</p>
						</>
					),
					onOk(..._args) {
						history.push('/content/companyInfoManage/certifyCompanyInfo')
					},
					okText: '确定',
				})
			} // 新增工商信息（企业三要素）校验 不通过
			else if (res.returnCode === 20118) {
				Modal.info({
					icon: <InfoCircleFilled />,
					title: '企业信息校验不通过',
					content: '请检查企业名称、统一社会信用代码、法定代表人是否输入错误；并重新提交认证',
					okText: '知道了',
				})
			} else {
				message.error(res.returnDesc)
			}
		}
	}

	// 注册
	const getRegisterParams = () => {
		// 统一 OoInfo获取方式 1.8.8
		const { operatingOrganizationUuid } = getStorage('OoInfo') || {}
		return {
			companyAddr: {
				address: form.getFieldValue('address'),
				city: form.getFieldValue('provinceCity')[1] !== 'undefined' ? 'P' + form.getFieldValue('provinceCity')[1] : null,
				corporationScale: '',
				industry: '',
				nationality: 'CHN',
				province: 'P' + form.getFieldValue('provinceCity')[0],
			},
			companyBasic: {
				companyName: form.getFieldValue('companyName'),
				fiCode: '',
				// companyShortName: form.getFieldValue('companyShortName'),
				fileContent: formatFileContent(),
				operatingOrganizationUuid,
				socialCreditCode: form.getFieldValue('socialCreditCode'),
				// uuid: 'string',
			},
			companyLegalPerson: {
				legalPerson: form.getFieldValue('legalPerson'),
				legalPersonIdCode: form.getFieldValue('legalPersonIdCode'),
				certificateType: form.getFieldValue('certificateType'),
			},
		}
	}
	// 检查公司是否存在
	const checkCompanyIsExist = async _values => {
		const params = {
			adminEmail: email,
			companyName: form.getFieldValue('companyName'),
			socialCreditCode: form.getFieldValue('socialCreditCode'),
			uuid: form.getFieldValue('uuid'),
		}
		const res = await loginApis.checkCompanyIsExist(params).catch(err => {
			return
		})
		if (res?.returnCode % 1000 === 0) {
			registerCompany(companyType)
		} else if (res?.returnCode) {
			message.error('企业或管理员信息已被注册使用')
		}
	}

	//表单校验成功
	const onFinish = (_values: any) => {
		form
			.validateFields()
			.then(values => {
				// 验证通过则，往下走
				let idVal: string = form.getFieldValue('legalPersonIdCode')
				let idType: string = form.getFieldValue('certificateType')
				// 点击提交后的第一个校验
				if (validatePersonIdCode(idType, idVal, message)) {
					checkCompanyIsExist(values)
					// registerCompany(companyType)
				}
			})
			.catch(errorInfo => {
				let errorFirstName = errorInfo?.errorFields?.[0]?.name?.[0]
				if (errorFirstName) {
					form.scrollToField(errorFirstName, { behavior: 'smooth', block: 'start' })
					if (
						errorFirstName === 'idPortraitoad' ||
						errorFirstName === 'idNationalEmblem' ||
						errorFirstName === 'enterpriseCertificate' ||
						errorFirstName === 'businessLicense' ||
						errorFirstName === 'openingPermit'
					) {
						scrollToAnchor(errorFirstName)
					}
				}
			})
	}
	const scrollToAnchor = id => {
		// 找到锚点
		let anchorElement = document.getElementById(id)
		// 如果对应id的锚点存在，就跳转到锚点
		if (anchorElement) {
			anchorElement.scrollIntoView({ block: 'start', behavior: 'smooth' })
		}
	}
	const onRadioChange = ({ target: { value } }: RadioChangeEvent) => {
		setCompanyType(value)
	}

	useEffect(() => {
		//legalPersonFormItemList
		let newLegalPersonFormItemList = [...legalPersonFormItemList]
		const iDPortraitItem = { key: 'IDPortrait', span: 12, isLook, label: curCertificate.labelArr[0], showExampleImg: curCertificate.text ? false : true }
		const iDNationalEmblemItem = {
			key: 'IDNationalEmblem',
			span: 12,
			isLook,
			label: curCertificate.labelArr[1],
			showExampleImg: curCertificate.text ? false : true,
		}

		newLegalPersonFormItemList = newLegalPersonFormItemList.slice(0, 4)
		if (curCertificate.text) {
			const span24Item = {
				key: '_blank',
				span: 24,
				isLook,
				label: '',
				content: (
					<div style={{ margin: '10px 50px 25px 50px' }}>
						<Alert message="" description={curCertificate.text} type="info" showIcon />
					</div>
				),
			}
			newLegalPersonFormItemList.push(span24Item)
		}
		newLegalPersonFormItemList.push(iDPortraitItem)
		newLegalPersonFormItemList.push(iDNationalEmblemItem)
		setLegalPersonFormItemList(newLegalPersonFormItemList)
	}, [curCertificate])

	useEffect(() => {
		let colArr = [
			{ key: 'CompanyName', span: 12, verifyExistence: true, isLook, label: '企业名称' },
			{ key: 'SocialCreditCode', span: 12, verifyExistence: true, isLook, label: '统一社会信用代码' },
			...(() => {
				let arr = [
					{ key: 'CorporationScale', span: 12, isLook, label: '企业规模' },
					{ key: 'Industry', span: 12, isLook, label: '所属行业' },
				]
				return companyType === 'CS' ? arr : []
			})(),
			{ key: 'ProvinceCity', span: 12, isLook, label: '所在地区' },
			{ key: 'Address', span: 12, isLook, label: '公司地址' },
			...(() => {
				let arr = [
					{ key: 'FiCode', span: 12, isLook, label: '金融机构编码', hiddenExtra: true },
					{ key: '_blank', span: 12, isLook, label: '-' },
				]
				return companyType === 'FI' ? arr : []
			})(),
			{ key: 'BusinessLicense', span: 12, isLook, label: '营业执照' },
			{ key: 'OpeningPermit', span: 12, isLook, label: companyType === 'CS' ? '其他附件' : '金融机构许可证' },
		]
		setCompanyMsgFormItemList(colArr)
	}, [companyType])

	const CFCAFormItemList = [
		{ key: 'CFCACertificateApplicationForm', span: 12, isLook, label: 'CFCA证书申请表' },
		{ key: 'CFCACertificateOfAuthorization', span: 12, isLook, label: '企业授权书' },
		{ key: 'IDCardOfHandler', span: 12, isLook, label: '经办人身份证复印件' },
	]

	return (
		<RegisterWrapper>
			<LayoutSlot>
				<div className="card-wrapper">
					<Spin spinning={spinning}>
						{status === null ? (
							<Skeleton active={spinning} />
						) : (
							<div className="editorlookcertifycompanyinfodetail">
								<div className="registerHeader">
									<div className="m-registerHeader">
										{isLook && <div className="title">认证进度:{renderStatusTag()}</div>}
										{status === 'REJECT' && (
											<Alert
												message={`审核意见`}
												description={<div id="reject-remark" dangerouslySetInnerHTML={{ __html: remark }}></div>}
												type="error"
												showIcon
												style={{ marginBottom: '20px' }}
											/>
										)}
										<Alert
											style={{ marginBottom: '20px' }}
											message="提示"
											description={
												<ul>
													<li>企业认证信息提交后，将会有运营人员进行企业信息审核，并需要您配合完成CFCA证书申请。</li>
													<li>企业认证通过后，您需要完善企业设置，如设定相关业务银行卡、签署相关协议。</li>
													<li>上传文件格式要求：支持pdf、jpg、jpeg、png格式，单个文件不超过20M。</li>
													<li>
														需要邮寄CFCA数字证书申请表、授权书;经办人身份证复印件纸质材料;收件人:运营管理部供应链融资平台审核专员-梅雨婷,收件地址:江阴市澄江中路1号，江苏江
														阴农村商业银行股份公司,联系电话:0510-86850365。
													</li>
												</ul>
											}
											type="info"
											showIcon
										/>
									</div>
									<div className="typeButton">
										<div className="type">
											<Tooltip
												placement="top"
												title={
													companyType === 'CS' ? (
														<p>
															您的注册类型为<b>普通企业</b>，您可以在平台作为核心企业开立融信，或作为供应商接收融信、融信融资。
														</p>
													) : (
														<p>
															您的注册类型为<b>金融机构</b>，您可以在平台为普通企业提供融信授信、融信融资等业务。
														</p>
													)
												}
											>
												<ExclamationCircleOutlined className="costConfirm" style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer' }} />
											</Tooltip>
											&nbsp;企业类型
										</div>
										{!isLook ? (
											<Radio.Group
												options={[
													{ label: '普通企业', value: 'CS' },
													{ label: '金融机构', value: 'FI' },
												]}
												onChange={onRadioChange}
												value={companyType}
												optionType="button"
												buttonStyle="solid"
											/>
										) : (
											{
												CS: '普通企业',
												FI: '金融机构',
											}[companyType]
										)}
									</div>
								</div>
								<div className="registerContent">
									<Form {...layout} form={form} name="register_form" className="registerForm" onFinish={onFinish}>
										<input name="uuid" hidden />
										<SmallTitle>企业信息</SmallTitle>
										<GetFormList form={form} configArr={companyMsgFormItemList} companyType={companyType}></GetFormList>

										<SmallTitle>法人信息</SmallTitle>
										<GetFormList form={form} configArr={legalPersonFormItemList} companyType={companyType}></GetFormList>

										<SmallTitle>CFCA申请信息</SmallTitle>
										<GetFormList form={form} configArr={CFCAFormItemList} companyType={companyType}></GetFormList>
									</Form>
								</div>

								{!isLook && (
									<div className="registerBottom">
										<Button loading={fileUploading.uploadingNum > 0} className="finish" type="primary" onClick={onFinish}>
											提交认证
										</Button>
									</div>
								)}
							</div>
						)}
					</Spin>
				</div>

				{showRejectImg()}
			</LayoutSlot>
		</RegisterWrapper>
	)
}

const RegisterWrapper = styled.div`
	height: 100%;
	#reject-remark img {
		cursor: pointer;
		width: 50%;
	}
	.editorlookcertifycompanyinfodetail {
		position: relative;
		.modal-mask {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			z-index: 1000;
			height: 100%;
		}
		.registerHeader {
			.m-registerHeader {
			}
			.title {
				font-size: 24px;
				text-align: left;
				display: flex;
				gap: 20px;
				margin-bottom: 20px;
				align-items: center;
				a {
					font-size: 16px;
				}
			}
			.remark {
				display: flex;
				gap: 20px;
				margin-bottom: 20px;
			}
			.typeButton {
				display: flex;
				align-items: center;
				margin-bottom: 24px;
				.type {
					width: 18.75%;
					text-align: right;
					&::after {
						content: ':';
						position: relative;
						top: -0.5px;
						margin: 0 8px 0 2px;
					}
				}
			}
			.message {
				margin-left: 18.75%;
				margin-top: 25px;
			}
		}
		.registerContent {
			// overflow-y: scroll;
			// ::-webkit-scrollbar-thumb {
			// 	// 改变滚动条颜色
			// 	/* background-color: rgb(36, 53, 195); */
			// 	background-color: rgba(255, 255, 255, 0.2);
			// }
			// ::-webkit-scrollbar-track-piece {
			// 	background-color: rgba(255, 255, 255, 0.1);
			// }
		}
		.registerForm {
		}
		.ant-alert-info {
			background-color: #e6f7ff;
			border: 1px solid #91d5ff;
		}
		ul {
			list-style: auto;
			margin: 0;
			padding: 0;
		}
		.registerBottom {
			height: 40px;
			font-size: 14px;
			margin-left: 18.75%;
			margin-top: 50px;
			.finish {
				width: 250px;
			}
			.goLogin {
				transform: translateX(386px);
				margin-top: 10px;
			}
		}
	}
	// 改变所有label字体颜色
	.ant-form-item-label > label {
	}
	// 改变所有提示字体的颜色
	.ant-form-item-extra {
	}
	// 上传组件的字体颜色
	.upload {
		.ant-upload-list,
		.ant-upload-list-item-card-actions .anticon {
		}
	}
	// 改变 input 的宽度
	.ant-form-item-control-input-content {
		width: 90%;
	}
	.registerForm {
		// 改变滚动条的颜色
		&::-webkit-scrollbar-thumb {
			background-color: rgb(18, 27, 63) !important;
		}
		&::-webkit-scrollbar-track-piece {
			background-color: transparent !important;
		}
	}

	// 对表单元素中的验证码进行偏移
	.ant-col-16 {
		margin-left: 4px;
	}
	.ant-col-6 {
		margin-left: -4px;
		// 获取验证码按钮宽度
		.captcha {
			width: 102px;
		}
	}

	.baseInfo {
		text-align: center;
		padding-bottom: 20px;
		font-size: 24px;
		text-align: right;
		width: 18.75%;
	}
	.bankAccounts {
		text-align: center;
		padding-bottom: 20px;
		font-size: 24px;
		text-align: left;
	}
	.corporateContact {
		text-align: center;
		padding-bottom: 20px;
		font-size: 24px;
		text-align: left;
	}
	.administratorAccount {
		text-align: center;
		padding-bottom: 20px;
		font-size: 24px;
		text-align: left;
	}
	.finish {
		position: relative;
		.ant-form-item-control {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
		}
	}
	b {
		font-weight: bold;
	}
	a {
		color: #1890ff;
		&:hover {
			color: #606eff;
		}
	}
`

export default observer(EditOrLookCertifyCompanyInfoDetail)
