/*
 * @Date: 2022-07-08 14:06:53
 * @LastEditors: jcl
 * @LastEditTime: 2022-07-18 17:19:00
 * @FilePath: \frontend-web\src\pages\client\views\content\companyInfoManage\certifyCompanyInfo\index.tsx
 * @Description: file content
 */
import React, { useEffect, useState } from 'react'
import { Descriptions, Tag, Alert, Spin } from 'antd'
import styled from 'styled-components'
import LayoutSlot from '@src/globalComponents/LayoutSlot'

import { companyInfoManageModuleApi } from '@src/pages/client/api'
import { history } from '@src/utils/router'
const DescriptionsItem = Descriptions.Item
const Styled = styled.div`
	height: 100%;
	.to-certified {
		width: 100px;
		lineheight: 14px;
		text-align: center;
	}
`
export default () => {
	const [user, setUser] = useState<any>({})
	const [status, setStatus] = useState<any>(null)
	const statusConfig = {
		'': (
			<>
				<Tag color={'rgba(170, 170, 170, 1)'} className="to-certified">
					未认证
				</Tag>
				<a onClick={() => history.push('/content/companyInfoManage/editCertifyCompanyInfoDetail')}>去认证</a>
			</>
		),
		INIT: (
			<>
				<Tag color={'rgba(245, 154, 35, 1)'} className="to-certified">
					审核中
				</Tag>
				<a onClick={() => history.push('/content/companyInfoManage/certifyCommpanyInfoDetail')}>查看认证信息</a>
			</>
		),
		REJECT: (
			<>
				<Tag color={'rgba(217, 0, 27, 1)'} className="to-certified">
					不通过
				</Tag>
				<a onClick={() => history.push('/content/companyInfoManage/certifyCommpanyInfoDetail')}>查看认证信息</a>
			</>
		),
		CONFIRM: (
			<>
				<Tag color={'rgba(112, 182, 3, 1)'} className="to-certified">
					通过
				</Tag>
				<a onClick={() => history.push('/content/companyInfoManage/certifyCommpanyInfoDetail')}>查看认证信息</a>
			</>
		),
	}
	useEffect(() => {
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		setUser(ext.user)
		getCompanyInfo()
	}, [])

	const getCompanyInfo = async () => {
		const { status = '' } = (await companyInfoManageModuleApi.getCompanyInfo({})) || {}
		setStatus(status)
	}

	return (
		<Styled>
			<LayoutSlot>
				<div className="card-wrapper">
					<Alert
						style={{ marginBottom: '20px' }}
						message="为了保证用户在平台上提交的所有内容均为合法合规，企业认证通过后才可使用平台功能。"
						type="info"
						showIcon
					/>
					<Descriptions column={1} labelStyle={{ width: '100px' }} contentStyle={{ width: '30%' }}>
						<DescriptionsItem label="用户名">{user?.username}</DescriptionsItem>
						<DescriptionsItem label="用户邮箱">{user?.email}</DescriptionsItem>
						<DescriptionsItem label="手机号">{user?.mobile}</DescriptionsItem>
						<DescriptionsItem label="企业认证">
							<Spin spinning={status === null}>{statusConfig[status]}</Spin>
						</DescriptionsItem>
					</Descriptions>
				</div>
			</LayoutSlot>
		</Styled>
	)
}
