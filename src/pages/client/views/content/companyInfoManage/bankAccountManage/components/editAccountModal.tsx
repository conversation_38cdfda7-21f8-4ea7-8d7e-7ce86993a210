import React, { ReactElement, useState, useEffect } from 'react'
import { Modal, Checkbox, Form, Input, message, Space, Button, Popover, Col, Row } from 'antd'
import { companyInfoManageModuleApi, contractModuleApi } from '@src/pages/client/api'
import GetCompanyName from '@src/globalComponents/SearchComByName'
import styled from 'styled-components'
import kunlun from '@src/utils/crypto'
import BankEnquiry from './bankEnquiry'
import { RSA_PUBLIC_KEY } from '@src/constants/constants'
import { NamePath } from 'antd/lib/form/interface'

const CheckboxGroup = Checkbox.Group
const layout = {
	labelCol: { span: 6 },
	wrapperCol: { span: 15 },
}
enum CompanyType {
	s = 'S',
	c = 'C',
}
interface Props {
	editData: any //编辑时带入数据
	visible: boolean
	onExport: () => void
	onCancel: () => void
}

function EditAccountInfoModal(props: Props): ReactElement {
	const [modalTitle, setModalTitle] = useState<string>('')
	const [previewData, setPreviewData] = useState<any>({}) // 设置回显数据
	const [confirmLoading, setConfirmLoading] = React.useState(false)
	const [fetchLoading, setFetchLoading] = React.useState(false)
	const [form] = Form.useForm()
	const { editData, visible, onExport, onCancel } = props
	const [openingbankVisible, setOpeningbankVisible] = React.useState(false)
	const checkBoxOptions = [
		{ label: '融信付款', value: 'PAY' },
		{ label: '融资收款', value: 'FINANCING_RECEIVE' },
		{ label: '融信收款', value: 'CASH_RECEIVE' },
	]
	const [oldAccountNum, setOldAccountNum] = React.useState<string>('')

	useEffect(() => {
		let title = ''
		if (editData) {
			setPreviewData({ uuid: editData.relationFi, companyName: editData.relationFiName })
			form.setFieldsValue({ ...editData, businessType: editData['businessType'].split(',') })
			setOldAccountNum(editData['accountNum'])
			if (editData['cardType'] === 'GENERAL') {
				title = '修改通用账户'
			} else {
				title = '修改专用账户'
			}
		} else {
			title = '添加专用账户'
		}
		if (visible) {
			setModalTitle(title)
		} else {
			form.resetFields()
		}
	}, [editData, visible])

	const checkBoxCfg = () => {
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let companyType = ext?.user?.company?.type?.split(',') || []
		let returnList: any = []
		let config = {
			[CompanyType.s]: ['FINANCING_RECEIVE', 'CASH_RECEIVE'],
			[CompanyType.c]: ['PAY'],
		}
		let selectedType = []
		if (editData) {
			//根据已选中类型显示选项
			selectedType = editData.businessType.split(',')
		}
		//根据公司类型显示选项 # 融资收款
		let orderTypeShowList = []
		if (companyType.includes(CompanyType.s)) {
			orderTypeShowList.push(...orderTypeShowList, ...config.S)
		}
		if (companyType.includes(CompanyType.c)) {
			orderTypeShowList.push(...orderTypeShowList, ...config.C)
		}
		//合并以及去重
		let optionList = [...orderTypeShowList, ...selectedType]
		optionList = Array.from(new Set(optionList))
		checkBoxOptions.forEach(item => {
			optionList.forEach(typeItem => {
				if (item.value === typeItem) {
					returnList.push(item)
				}
			})
		})
		return returnList
	}

	// 获取江阴开户行信息
	const handleFetchBank = () => {
		const acctno = form.getFieldValue('accountNum')
		if (acctno) {
			setFetchLoading(true)
			companyInfoManageModuleApi
				.queryJrBankInfoByNo({ acctno })
				.then((res: any) => {
					if (res) {
						form.setFieldsValue({
							accountBank: res?.brchna,
						})
					} else {
						message.error('未查询到开户行，请手动填写')
					}
				})
				.finally(() => {
					setFetchLoading(false)
				})
		} else {
			message.warn('请输入银行账号')
		}
	}

	const onFinish = () => {
		setConfirmLoading(true)
		const formAccountNum = form.getFieldValue('accountNum')
		let validateFields: NamePath[]
		if (editData?.accountNum === formAccountNum) {
			validateFields = ['accountName', 'accountBank']
		} else {
			validateFields = ['accountName', 'accountBank', 'accountNum']
		}

		form
			.validateFields(validateFields)
			.then(res => {
				res['cardType'] = editData ? editData['cardType'] : 'SPECIAL'
				//普通卡
				if (editData && editData['cardType'] === 'GENERAL') {
					res['businessType'] = 'FINANCING_RECEIVE,CASH_RECEIVE,PAY'
				} else {
					res['businessType'] = res['businessType'].join(',')
				}
				if (editData && editData['id']) res['id'] = editData['id']
				const ext = JSON.parse(localStorage.getItem('ext') || '{}')
				res['companyUuid'] = ext?.user?.company?.uuid
				kunlun.jsEncrypt.setPublicKey(RSA_PUBLIC_KEY)
				const encrypt_accountNum = kunlun.jsEncrypt.encrypt(res?.accountNum)
				const params = res
				if (editData?.accountNum === res?.accountNum || !res?.accountNum) {
					delete params?.accountNum
				} else {
					params.accountNum = encrypt_accountNum
				}
				companyInfoManageModuleApi
					.bankcardModify(params)
					.then(() => {
						message.success('操作成功')
						onCancel()
						onExport()
						setConfirmLoading(false)
					})
					.catch(error => {
						// onCancel()
						setConfirmLoading(false)
					})
			})
			.catch(error => {
				setConfirmLoading(false)
			})
	}

	const setAccountBank = item => {
		form.setFieldsValue({ accountBank: item.lname })
	}

	const checkBankAccountFormat = (key, value) => {
		if (value.startsWith('3022402') && value.length > 10) {
			let valueTemp = value.replaceAll('-', '')
			let valueResult = valueTemp.substring(0, 10) + '-' + valueTemp.substring(10)
			form.setFieldsValue({ accountNum: valueResult })
		}
	}

	const editAccountForm = () => {
		return (
			<>
				<Form.Item
					name="accountName"
					label="户名"
					rules={[
						{
							required: true,
							message: '请输入户名',
						},
					]}
				>
					<Input placeholder="请输入户名" maxLength={64} disabled />
				</Form.Item>
				<Form.Item label="账号" required>
					<Space>
						<Form.Item
							name="accountNum"
							noStyle
							validateTrigger={['onBlur', 'onChange']}
							rules={[
								// {
								// 	required: true,
								// 	message: '请输入账号，由数字或横线组成',
								// 	pattern: /^[0-9-]*$/,
								// 	whitespace: true,
								// },
								({ getFieldValue }) => ({
									validateTrigger: ['onBlur', 'onChange'],
									validator(_, value) {
										if (oldAccountNum !== value) {
											const regexp = /^[0-9-]*$/
											if (regexp.test(value)) {
												checkBankAccountFormat('accountNum', value)
												return Promise.resolve()
											}
											return Promise.reject(new Error('请输入账号，由数字或横线组成'))
										}
										return Promise.resolve()
									},
								}),
							]}
						>
							<Input placeholder="请输入账号" style={{ width: 200 }} maxLength={32} />
						</Form.Item>
						<Popover content={'非江阴农商银行开户，请手动填写开户行'} title={null}>
							<Button onClick={handleFetchBank} loading={fetchLoading}>
								获取开户行
							</Button>
						</Popover>
					</Space>
				</Form.Item>
				<Form.Item
					name="accountBank"
					label="开户行"
					rules={[
						{
							required: true,
							message: '请输入开户行',
						},
					]}
				>
					<Input placeholder="非江阴农商银行开户，请手动填写" maxLength={64} />
				</Form.Item>
				<Row>
					<Col span={6}></Col>
					<Col span={15}>
						<div className="link" onClick={() => setOpeningbankVisible(true)} style={{ textDecoration: 'underline' }}>
							查询开户行
						</div>
					</Col>
				</Row>
				{editData && editData['cardType'] === 'GENERAL' ? null : specialCardEdit()}
			</>
		)
	}

	const specialCardEdit = () => {
		return (
			<>
				<GetCompanyName
					name="relationFi"
					label="金融机构"
					placeholder={'请选择金融机构'}
					getCompany={contractModuleApi.modsearchByName}
					companyType="FI"
					requireMsg="请选择金融机构"
					valuekey="uuid"
					previewData={previewData}
				/>
				<Form.Item
					name="businessType"
					label="业务类型"
					rules={[
						{
							required: true,
							message: '请选择业务类型',
						},
					]}
				>
					<CheckboxGroup options={checkBoxCfg()} />
				</Form.Item>
			</>
		)
	}

	return (
		<Modal width="600px" title={modalTitle} open={visible} onOk={onFinish} onCancel={onCancel} confirmLoading={confirmLoading}>
			<div className="card-wrapper" style={{ padding: '15px 30px' }}>
				<Form {...layout} form={form} name="add_form">
					{editAccountForm()}
				</Form>
			</div>
			<BankEnquiry visible={openingbankVisible} onCancel={() => setOpeningbankVisible(false)} getAccountBank={setAccountBank}></BankEnquiry>
		</Modal>
	)
}

const EditWrapper = styled.div``

export default React.memo(EditAccountInfoModal)
