import React, { ReactElement, useState, useEffect } from 'react'
import { Modal, Form, Input, Button } from 'antd'
import { companyInfoManageModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import BaseTable from '@src/globalComponents/BaseTable'
import styled from 'styled-components'

interface Props {
	visible: boolean
	onCancel: () => void
	getAccountBank: (params: any) => void
}

function EditAccountInfoModal(props: Props): ReactElement {
	const [dataSource, setDataSource] = useState([])
	const [form] = Form.useForm()
	const { visible, onCancel, getAccountBank } = props

	const onButtonSubmit = () => {
		let values = form.getFieldsValue(['bank', 'province', 'city', 'key'])
		getInfo(values)
	}

	const getInfo = async params => {
		let res = await companyInfoManageModuleApi.getOpeningBank(params).catch(err => console.log(err))
		if (res) {
			setDataSource(res)
		}
	}

	const setEditData = item => {
		onClear()
		getAccountBank(item)
		onCancel()
	}

	const onClear = () => {
		setDataSource([])
		form?.resetFields()
	}
	const textWarp = text => {
		return text ? <TextWrapper>{text}</TextWrapper> : '--'
	}
	const getColumns = () => {
		const colns = {
			operation: {
				render: (text, recode) => {
					return (
						<span className="link" onClick={() => setEditData(recode)}>
							设为开户行
						</span>
					)
				},
			},
			addr: {
				render: textWarp,
			},
			lname: {
				render: textWarp,
			},
		}

		const evenlyColns = evenlyTableColunms(getColumnsByPageName('openingBank', colns), {
			0: 20,
			1: 20,
			2: 6,
		})
		return evenlyColns
	}

	return (
		<Modal width="800px" title="查询开户行" open={visible} footer={null} onCancel={onCancel}>
			<div className="card-wrapper">
				<Form name="bankEnquiry" layout="inline" form={form}>
					<div style={{ display: 'flex', flexWrap: 'wrap', flexDirection: 'column' }}>
						<div style={{ flex: 1, display: 'flex', marginBottom: '15px' }}>
							<Form.Item label="银行名称" name="bank" labelCol={{ span: 6 }}>
								<Input placeholder="如：中国农业银行" style={{ width: 200 }} />
							</Form.Item>
							<Form.Item label="省" name="province">
								<Input placeholder="如：江苏" style={{ width: 200 }} />
							</Form.Item>
							<Form.Item label="市" name="city">
								<Input placeholder="如：无锡" style={{ width: 200 }} />
							</Form.Item>
						</div>
						<div style={{ display: 'flex', justifyContent: 'space-between' }}>
							<Form.Item label="关键字" name="key" labelCol={{ span: 3 }} style={{ flex: 1, marginLeft: '-8px' }}>
								<Input placeholder="如：峭岐" style={{ width: 200 }} />
							</Form.Item>
							<div>
								<Button type="primary" style={{ marginRight: '10px' }} onClick={onButtonSubmit}>
									查询
								</Button>
								<Button className="clear" onClick={onClear}>
									重置
								</Button>
							</div>
						</div>
					</div>
				</Form>
				<BaseTable
					style={{ marginTop: '20px', height: '300px', overflowY: 'auto' }}
					dataSource={dataSource}
					columns={getColumns()}
					havePagination={false}
				></BaseTable>
			</div>
		</Modal>
	)
}

const TextWrapper = styled.div`
	text-align: left;
	white-space: normal;
	word-break: break-all;
`

export default React.memo(EditAccountInfoModal)
