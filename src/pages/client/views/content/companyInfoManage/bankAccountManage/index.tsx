import React, { useEffect, useState } from 'react'
import { But<PERSON>, Row, Col, Popconfirm, Spin, message, Tag, Divider } from 'antd'
import styled from 'styled-components'
import { companyInfoManageModuleApi } from '@src/pages/client/api'
import EditAccountModal from './components/editAccountModal'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
const cardBg = require('@src/assets/images/company-center/card-bg.png')
const cardLeftIcon = require('@src/assets/images/company-center/card-left-icon.png')
const cardTypeOptions = {
	GENERAL: '结算账户',
	SPECIAL: '金融机构专用卡',
}
function BankAccount() {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [editAccountVisible, setEditAccountVisible] = useState<boolean>(false)
	const [cardDataLoading, setCardDataLoading] = useState<boolean>(false)
	const [getCardData, setCardData] = useState<any>(null)
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')
	useEffect(() => {
		getBankcardList()
	}, [])

	const getBankcardList = async () => {
		setCardDataLoading(true)
		let bankcardList = await companyInfoManageModuleApi.getBankcard({ companyUuid: ext?.user?.company?.id }).catch(err => {
			console.log(err)
		})
		bankcardList.length > 0 &&
			bankcardList.map(item => {
				item.accountName = ext?.user?.company?.orgName
			})
		setDataSource(bankcardList)
		setCardDataLoading(false)
	}

	const submitAccountInfo = () => {
		getBankcardList()
	}
	const deleteCard = data => {
		companyInfoManageModuleApi.deleteCard({ companyUuid: data.companyUuid, id: data.id }).then(() => {
			message.success('删除成功')
			getBankcardList()
		})
	}
	const modifyAccount = data => {
		setCardData(data)
		setEditAccountVisible(true)
	}

	const cardInfo = cardData => {
		let businessTypeList = cardData.businessType.split(',')
		let filterBusinessType: Array<any> = []
		let config = {
			FINANCING_RECEIVE: '融资收款',
			CASH_RECEIVE: '融信收款',
			PAY: '融信付款',
		}
		businessTypeList.forEach(typeItem => {
			if (config[typeItem]) filterBusinessType.push(config[typeItem])
		})
		let color = cardData.cardType === 'SPECIAL' ? 'red' : 'blue'
		return (
			<React.Fragment key={cardData.id}>
				<div className="card">
					<div style={{ position: 'absolute', top: '14px', right: '32px' }}>
						<Tag color="#F27A00">{cardTypeOptions[cardData.cardType]}</Tag>
					</div>
					<img src={cardLeftIcon} className="card-left-icon" />
					<div className="content-right">
						<p>账号： {cardData.accountNum}</p>
						<p>户名： {cardData.accountName}</p>
						<p>开户行： {cardData.accountBank}</p>
						{cardData.cardType === 'SPECIAL' && <p>金融机构： {cardData.relationFiName}</p>}
						<p>业务类型： {filterBusinessType.join('、')}</p>
					</div>
				</div>
				<div className="buttom">
					<Button type="primary" className="big-btn" onClick={() => modifyAccount(cardData)}>
						修改
					</Button>
					{cardData.cardType === 'GENERAL' ? null : (
						<Popconfirm title="您确认删除此金融机构专用卡？" onConfirm={() => deleteCard(cardData)}>
							<Button danger type="text">
								删除
							</Button>
						</Popconfirm>
					)}
				</div>
			</React.Fragment>
		)
	}
	const showCards = () => {
		if (dataSource && dataSource.length > 0) {
			let result: any = []
			let index = 0
			dataSource.forEach(cardData => {
				let col = (
					<div key={cardData}>
						<div className="card-out-container">{cardInfo(cardData)}</div>
						<Divider />
					</div>
				)
				result.push(col)
			})
			return result
		} else {
			return null
		}
	}
	return (
		<BankAccountBox>
			<LayoutSlot>
				<div className="card-wrapper">
					{/* <div className="description">
						<div>账户说明：</div>
						<div>1、默认使用“通用卡”的银行账户，进行核心企业融信付款、供应商融信收款、供应商融资收款业务。</div>
						<div>2、您也可以添加“金融机构专用账户”，当与指定金融机构发生特定的业务往来时，将优先使用专用卡的账户。</div>
						<div>3、同一个金融机构同一种业务类型只能存在一张专用卡。</div>
					</div> */}
					<Spin spinning={cardDataLoading} delay={500}>
						<div className="card-container"> {showCards()}</div>
					</Spin>
				</div>
			</LayoutSlot>
			<EditAccountModal editData={getCardData} visible={editAccountVisible} onCancel={() => setEditAccountVisible(false)} onExport={submitAccountInfo} />
		</BankAccountBox>
	)
}

const BankAccountBox = styled.div`
	height: 100%;

	.card-container {
		width: 100%;
		margin-top: 40px;
		display: flex;
		justify-content: center;
		flex-direction: column;
		align-items: center;

		.card-out-container {
			text-align: center;
			width: 665px;
		}

		.card {
			width: 665px;
			height: 256px;
			background: url(${cardBg}) no-repeat;
			display: flex;
			justify-content: flex-start;
			position: relative;
			.card-left-icon {
				width: 146px;
				height: 168px;
				margin: 44px 32px 0 40px;
			}

			.ant-tag {
				width: 64px;
				height: 26px;
				line-height: 26px;
				text-align: center;
				border-radius: 12px;
			}
			.rowName {
				text-align: right;
			}
			.rowValue {
				text-align: left;
				word-break: break-all;
			}
			.content-right {
				margin-top: 50px;
				p {
					text-align: left;
					margin: 18px 0;
					font-size: 14px;
				}
			}
		}
		.cardRow {
			text-align: center;
			margin: 0 auto 30px 0;
		}
	}
	.firstBox {
		.title {
		}
		.ant-btn {
			float: right;
		}
	}
	.description {
		background-color: #f2f2f2;
		margin: 30px 0;
		padding: 15px;
		font-size: 14px;
		line-height: 25px;
	}

	.buttom {
		text-align: center;
		margin: 30px auto;
	}
	.cardTypeStyle {
		width: 150px;
		display: inline-block;
		margin-right: 20px;
		text-align: center;
		border-radius: 12px;
		color: #f2f2f2;
	}
	.blue {
		background-color: #39a7f0;
	}
	.red {
		background-color: #f67c83;
	}
`

export default BankAccount
