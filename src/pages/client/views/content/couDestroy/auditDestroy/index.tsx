/*
 * @Date: 2022-09-13 13:59:40
 * @LastEditors: jcl
 * @FilePath: \frontend-web\src\pages\client\views\content\couDestroy\auditDestroy\index.tsx
 * @Description: 待核销
 */

import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import BaseTable from '@globalComponents/BaseTable'
import LayoutSlot from '@globalComponents/LayoutSlot'
import { commonModuleApi, destroyModuleApi } from '@src/pages/client/api'
import { history } from '@src/utils/router'
import { formatTableData } from '@utils/format'
import { Checkbox, Form, Input, message, Modal, Tooltip } from 'antd'
import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import { InfoCircleFilled } from '@ant-design/icons'
import PDFViewer from '@src/globalComponents/PDFViewer'
import { getStorage } from '@src/pages/client/biz/bizIndex'
export default () => {
	//table相关
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [protocolVisible, setProtocolVisible] = useState<boolean>(false)
	const [protocolHtmlStr, setProtocolHtmlStr] = useState<string>('')
	const [dataSource, setDataSource] = useState<any[]>([])
	const [tableLoading, setTableLoading] = useState<boolean>(false)
	let modal
	const [form] = Form.useForm()
	useEffect(() => {
		getAuditDestroyList()
	}, [pagination.current, pagination.pageSize])

	const onOperaCallback = (data: any) => {
		history.push('/content/couDestroy/detail', {
			uuid: data.uuid,
		})
	}
	const getColumns = () => {
		const columnsDic = {
			holderCompanyName: {
				title: '融信持有方',
			},
			publishCompanyName: {
				title: '融信开立方',
			},
			destroyNo: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									onOperaCallback(record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			operation: { render: renderOperation, width: 120 },
		}
		return getColumnsByPageName('auditDestroy', columnsDic)
	}

	//获取企业信息列表
	const getAuditDestroyList = async (initValues?: any) => {
		let params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			processDefinitionKeyList: ['couFinanceDestroy', 'couNonFinanceDestroy'],
			roleNameList: getStorage('roleList') || [],
		}
		if (initValues) {
			params = Object.assign(params, initValues)
		}
		setTableLoading(true)
		const res = await destroyModuleApi.getAuditDestroyList(params).catch(() => {
			console.log('getAuditDestroyList', res)
			setDataSource([])
			setTableLoading(false)
			pagination.total = 0
			setPagination({ ...pagination })
		})
		if (res) {
			if (res.data && res.data.length > 0) {
				setTableLoading(false)
				const list = res.data.map(item => {
					return { businessKey: item.businessKey, taskId: item.id, ...item.bizMap?.destroyBasic4List, ...item.bizMap?.destroyCou4List }
				})
				setDataSource(formatTableData.addKey(list))
				console.log('formatTableData.addKey(list)', formatTableData.addKey(list))
				pagination.total = res.count
				setPagination({ ...pagination })
			} else {
				setDataSource([])
				setTableLoading(false)
				pagination.total = 0
				setPagination({ ...pagination })
			}
		}
	}

	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}

	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}

	const submitData = (initValues: any) => {
		return commonModuleApi.doTask(initValues).then(item => {
			if (item.returnCode === 17000) {
				modal.destroy()
				message.success('处理成功')
				getAuditDestroyList({
					pageNum: 1,
					pageSize: 10,
				})
			} else {
				message.error(item.returnDesc)
			}
		})
	}
	const submit = () => {
		form.validateFields().then(values => {
			const { agreement, ...reset } = values
			submitData(reset)
		})
	}
	const handleChangeCheckBox = e => {
		console.log('e', form.getFieldValue('agreement'))
		modal?.update({ okButtonProps: { disabled: !form.getFieldValue('agreement') } })
	}
	const handleConfirm = async record => {
		let roleList = getStorage('roleList') || []
		const ext = JSON.parse(localStorage.getItem('ext') || '{}')
		const companyUuid: string = ext.user && ext.user.company ? ext.user.company.uuid : ''
		let isLastAudit = await commonModuleApi
			.isLastFlowPerson({
				companyUuid,
				role: roleList.length > 0 ? roleList[0] : '',
				taskId: record?.taskId,
			})
			.catch(err => {
				console.log(err)
			})

		form.setFieldsValue({
			businessKey: record.businessKey,
			taskId: record.taskId,
			outcome: 'agree',
		})
		modal = Modal.confirm({
			icon: <InfoCircleFilled style={{ color: '#1890ff' }} />,
			title: '确定通过？',
			okText: '确定',
			cancelText: '取消',
			okButtonProps: { disabled: isLastAudit && !form.getFieldValue('agreement') },
			content: (
				<Form form={form}>
					<p>确定通过后无法撤销</p>
					<Form.Item name="businessKey" hidden></Form.Item>
					<Form.Item name="taskId" hidden></Form.Item>
					<Form.Item name="outcome" hidden></Form.Item>
					{isLastAudit ? (
						<Form.Item
							name="agreement"
							valuePropName="checked"
							rules={[
								{
									validator: (_, value) => (value ? Promise.resolve() : Promise.reject(new Error('请签署协议!'))),
								},
							]}
						>
							<Checkbox onChange={handleChangeCheckBox}>
								<div style={{ marginLeft: '5px', fontSize: '13px', display: 'inline-block' }}>
									同意并签署
									<a
										onClick={() => {
											if (record.cashPdfUrl) {
												setProtocolHtmlStr(record.cashPdfUrl)
												setProtocolVisible(true)
											} else {
												message.error('获取协议失败')
											}
										}}
									>
										《线下兑付知悉书》
									</a>
								</div>
							</Checkbox>
						</Form.Item>
					) : null}
				</Form>
			),
			closable: true,
			onOk: () => {
				submit()
			},
			onCancel: () => form.resetFields(),
		})
	}
	const handleReject = record => {
		console.log('handleReject', record)
		form.setFieldsValue({
			businessKey: record.businessKey,
			taskId: record.taskId,
			outcome: 'reject',
		})
		modal = Modal.confirm({
			icon: null,
			title: '确定拒绝？',
			closable: true,
			onOk: () => {
				submit()
			},
			onCancel: () => form.resetFields(),
			content: (
				<Form form={form}>
					<Form.Item name="businessKey" hidden></Form.Item>
					<Form.Item name="taskId" hidden></Form.Item>
					<Form.Item name="outcome" hidden></Form.Item>
					<Form.Item name="comment">
						<Input.TextArea showCount maxLength={50} rows={4} placeholder="请描述拒绝理由，最多输入50个字符" />
					</Form.Item>
				</Form>
			),
		})
	}

	//渲染操作列
	const renderOperation = (text: string, record: any) => {
		return (
			<div>
				<span className="link left-btn" onClick={() => handleConfirm(record)}>
					通过
				</span>
				<span className="link red" onClick={() => handleReject(record)}>
					拒绝
				</span>
			</div>
		)
	}

	return (
		<LayoutSlot>
			<SearchCompanyStyle className="card-wrapper">
				<BaseTable
					className="card-table"
					loading={tableLoading}
					dataSource={dataSource}
					columns={getColumns()}
					total={pagination.total}
					current={pagination.current}
					onPageChange={handlePageChange}
					onSizeChange={handleSizeChange}
				/>
			</SearchCompanyStyle>
			<PDFViewer
				title="线下兑付知悉书"
				visible={protocolVisible}
				pdfUrl={protocolHtmlStr}
				onCancel={() => {
					setProtocolVisible(false)
					setProtocolHtmlStr('')
				}}
			/>
		</LayoutSlot>
	)
}
const SearchCompanyStyle = styled.div`
	.left-btn {
		margin-right: 5px;
	}
`
