import BaseTable from '@src/globalComponents/BaseTable'
import SmallTitle from '@src/globalComponents/SmallTitle'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import { Descriptions } from 'antd'
import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { history } from '@src/utils/router'
import { destroyModuleApi } from '@src/pages/client/api'
import { DestroyStatus } from '@src/globalConfig/codeMaps'
import PDFViewer from '@src/globalComponents/PDFViewer'

const index = () => {
	const { uuid } = history.location.state
	const [destroyBasic4Detail, setDestroyBasic4Detail] = useState<any>(null)
	const [destroyCou4Detail, setDestroyCou4Detail] = useState<Array<any>>([])
	const [pdfVisible, setPdfVisible] = useState<boolean>(false)
	const [pdfUrl, setPdfUrl] = useState<string>('')

	useEffect(() => {
		getDetail()
	}, [])
	//获取合同的表格列
	const getColumns = () => {
		const columnsDic = {
			couAmountInYuan: {
				width: 200,
			},
		}
		return getColumnsByPageName('destroyCou', columnsDic)
	}

	const getDetail = async () => {
		const data: any = await destroyModuleApi.getDetail({ uuid: uuid })
		setDestroyBasic4Detail(data.destroyBasic4Detail)
		let arr: any = []
		arr.push(data.destroyCou4Detail)
		setDestroyCou4Detail(arr)
	}

	const handleViewPdf = (visible: boolean, url: string) => {
		setPdfVisible(visible)
		setPdfUrl(url)
	}

	return (
		<Box>
			{destroyBasic4Detail && (
				<div className="card-wrapper">
					<SmallTitle text="核销详情" />
					<Descriptions bordered column={2}>
						<Descriptions.Item label="核销申请编号">{destroyBasic4Detail.destroyNo}</Descriptions.Item>
						<Descriptions.Item label="状态">
							{(destroyBasic4Detail && DestroyStatus.filter(item => item.code === destroyBasic4Detail.status)[0].name) || '- -'}
						</Descriptions.Item>
						<Descriptions.Item label="创建日期">{destroyBasic4Detail.createTime}</Descriptions.Item>
						<Descriptions.Item label="处理意见">{destroyBasic4Detail.auditReason || '- -'}</Descriptions.Item>
					</Descriptions>
					<div className="smallTitle">
						<SmallTitle text="核销融信" />
					</div>
					<BaseTable columns={getColumns()} dataSource={destroyCou4Detail} havePagination={false} />
					<div className="smallTitle">
						<SmallTitle text="付款凭证" />
					</div>
					<div className="link" onClick={() => handleViewPdf(true, destroyBasic4Detail.payPdfUrl)}>
						付款凭证.pdf
					</div>

					{destroyBasic4Detail.status === 'CONFIRMED' && (
						<>
							<div className="smallTitle">
								<SmallTitle text="线下兑付知悉书" />
							</div>
							<div className="link" onClick={() => handleViewPdf(true, destroyBasic4Detail.cashPdfUrl)}>
								线下兑付知悉书.pdf
							</div>
						</>
					)}
				</div>
			)}

			<PDFViewer title="" visible={pdfVisible} pdfUrl={pdfUrl} onCancel={() => setPdfVisible(false)} />
		</Box>
	)
}

const Box = styled.div`
	.smallTitle {
		margin-top: 30px;
	}
`

export default index
