import React, { useState, useEffect } from 'react'
import { Button, message, Table, Alert, Form, Upload } from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import { destroyModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { history } from '@src/utils/router'
import { formatUrl } from '@utils/format'
import SmallTitle from '@src/globalComponents/SmallTitle'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import SelectCou from './components/selectCou'
import DestroyInfoConfirm from './components/destroyInfoConfirm'
import Feedback from '@src/pages/client/components/feedback'
import PDFViewer from '@src/globalComponents/PDFViewer'
import styled from 'styled-components'
import { observer } from 'mobx-react-lite'
import fileUploading from '@src/globalStore/fileUploading'
import { getUploadStatusValidator } from '@src/globalBiz/gBiz'
import { customRequest } from '@src/globalBiz/gBiz'

function index() {
	const [form] = Form.useForm()
	const [loading, setLoading] = useState<boolean>(false)
	const [selectCouVisible, setSelectCouVisible] = useState<boolean>(false)
	const [destroyInfoConfirmVisible, setDestroyInfoConfirmVisible] = useState<boolean>(false)
	const [feedbackVisible, setFeedbackVisible] = useState<boolean>(false)
	const [couDataSource, setCouDataSource] = useState<any[]>([]) // 融信信息表格中的数据
	// 核销相关信息
	const [destroyUuid, setDestroyUuid] = useState<string>('') // 核销申请详情的uuid
	const [destroyInfoObj, setDestroyInfoObj] = useState<any>({}) // 核销信息确认对象
	// pdf预览弹框控制
	const [pdfVisible, setPdfVisible] = React.useState<boolean>(false)
	const [pdfUrl, setPdfUrl] = React.useState<string>('')
	const [pdfTitle, setPdfTitle] = React.useState<string>('')
	const [feedbackInfoObj, setFeedbackInfoObj] = useState<any>({
		status: 'success',
		title: '提交成功',
	})

	// 点击核销信息确认 OK
	const handleDestroyInfoConfirmOk = () => {
		setLoading(true)
		console.log('form.getFieldValue', form.getFieldValue('payVoucher'))
		const couUuid = couDataSource[0].couUuid
		const payPdfUrl = form.getFieldValue('payVoucher')[0]?.response?.data
		destroyModuleApi.createDestroy({ couUuid, payPdfUrl }).then(
			res => {
				console.log('创建退款申请', res)
				setDestroyUuid(res)
				setLoading(false)
				setFeedbackVisible(true)
			},
			reason => {
				setLoading(false)
			}
		)
		setDestroyInfoConfirmVisible(false)
	}

	// 点击提交按钮
	const submitData = () => {
		if (!couDataSource?.length) {
			return message.warning('请选择需要核销的融信')
		}

		form
			.validateFields()
			.then(values => {
				// 验证通过，核销信息确认弹窗显示
				setDestroyInfoConfirmVisible(true)
			})
			.catch(errorInfo => {
				const errorFirstName = errorInfo['errorFields'][0]['name'][0]
				if (errorFirstName === 'payVoucher') {
					return message.warning('请上传付款凭证')
				}
			})
	}

	// 点击选择融信 OK
	const handleSelectCouOk = async value => {
		const couInfoObj = value[0]
		// 设置核销信息对象
		setDestroyInfoObj({
			couNo: couInfoObj.couNo,
			couAmountInYuan: couInfoObj.couAmountInYuan,
			holderCompanyName: couInfoObj.holderCompanyName,
		})
		setCouDataSource(value)
		setSelectCouVisible(false)
	}

	// 选择融信
	const handleSelectCou = () => {
		setSelectCouVisible(true)
	}
	const getColumns = type => {
		return getColumnsByPageName(type)
	}

	const upLoadProps = {
		data: { type: 'destroy' },
		maxCount: 1,
		action: formatUrl('/pledge-config/common/uploadFile'),
		accept: '.pdf',
		beforeUpload: file => {
			const ext = file.name.split('.').pop().toLowerCase()
			const isPdf = ext === 'pdf'
			if (!isPdf) {
				message.error('仅支持格式PDF')
				return Upload.LIST_IGNORE
			}
			const isLtMaxSize = file.size / 1024 / 1024 <= 5
			if (!isLtMaxSize) {
				message.error('文件大小不能超过5M')
				return Upload.LIST_IGNORE
			}
			return Promise.resolve(file)
		},
		onChange: info => {
			if (info?.file?.status) {
				const { file } = info
				const { status, uid } = file
				if (uid) {
					if (status === 'uploading') {
						fileUploading.addUploadingFile(uid)
					} else {
						fileUploading.removeUploadingFile(uid)
					}
				}
			}
		},
		onPreview: fileObj => {
			const fileTitle = fileObj.name
			// 得到url的相对路径
			try {
				let fileUrl = fileObj.response.data
				setPdfTitle(fileTitle)
				setPdfUrl(fileUrl)
				setPdfVisible(true)
			} catch (err) {
				console.log('err', err)
			}
		},
		customRequest,
	}

	useEffect(() => {
		fileUploading.init()
	}, [])

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<div className="couDestroy">
						<div className="title">
							<SmallTitle text="核销融信" />
							<Button type="primary" onClick={handleSelectCou}>
								选择核销融信
							</Button>
						</div>
						<div className="alert">
							<Alert message="请选择需要核销的融信" type="warning" showIcon style={{ marginTop: '10px', marginBottom: '10px' }} />
						</div>
						<div className="table">
							<Table dataSource={couDataSource} columns={getColumns('selectCouDrawerD')} pagination={false} />
						</div>
					</div>
					<div className="payVoucher">
						<div className="title">
							<SmallTitle text="上传付款凭证" />
						</div>
						<div className="alert">
							<Alert
								message="请上传线下兑付的打款记录或文字说明（格式要求PDF），提交后系统将会对您上传的附件进行电子签章"
								type="warning"
								showIcon
								style={{ marginTop: '10px', marginBottom: '10px' }}
							/>
						</div>
						<div className="form">
							<Form form={form}>
								<Form.Item
									label="付款凭证"
									name="payVoucher"
									rules={[
										{ required: true, message: '请上传文件' },
										{
											validator: getUploadStatusValidator('文件上传失败'),
										},
									]}
									valuePropName="fileList"
									getValueFromEvent={(e: any) => {
										if (Array.isArray(e)) {
											return e
										}
										return e && e.fileList
									}}
								>
									<Upload name="file" {...upLoadProps}>
										<Button id="upload" icon={<UploadOutlined />}>
											上传
										</Button>
									</Upload>
								</Form.Item>
							</Form>
							<div className="submit">
								<Button loading={loading || fileUploading.uploadingNum > 0} type="primary" htmlType="submit" onClick={submitData}>
									提交
								</Button>
							</div>
						</div>
					</div>
				</div>
				<SelectCou
					visible={selectCouVisible}
					dataInit={couDataSource}
					onOk={value => {
						handleSelectCouOk(value)
					}}
					onCancel={() => {
						setSelectCouVisible(false)
					}}
				/>
				<DestroyInfoConfirm
					visible={destroyInfoConfirmVisible}
					destroyInfoObj={destroyInfoObj}
					onOk={() => {
						handleDestroyInfoConfirmOk()
					}}
					onCancel={() => setDestroyInfoConfirmVisible(false)}
				/>
				<Feedback
					onOk={() => {
						history.push('/content/couDestroy/destroyList')
					}}
					onDetail={() => {
						// 存储返回的uuid，供在融资申请详情中使用
						history.push('/content/couDestroy/detail', {
							uuid: destroyUuid,
						})
					}}
					visible={feedbackVisible}
					status={feedbackInfoObj.status}
					title={feedbackInfoObj.title}
					subTitle={feedbackInfoObj.subTitle}
					btnTxts={['完成', '查看详情']}
				/>
			</LayoutSlot>
			<PDFViewer title={pdfTitle} visible={pdfVisible} pdfUrl={pdfUrl} onCancel={() => setPdfVisible(false)} />
		</Box>
	)
}

const Box = styled.div`
	height: 100%;
	width: 100%;
	.payVoucher {
		margin-top: 20px;
	}
	.submit {
		display: flex;
		justify-content: center;
	}
	.form {
		.ant-upload-list-item-info .ant-upload-text-icon .anticon,
		.ant-upload-list-item-card-actions .anticon {
			color: #1890ff;
		}

		.ant-upload-list-item-name {
			color: #1890ff;
			cursor: pointer;
		}
		.ant-upload-list-item-card-actions-btn  {
			opacity: 1;
		}
		.ant-upload-text-icon {
			a {
				color: #606eff;
			}
		}

		.ant-upload-list-item-error .ant-upload-list-item-name,
		.ant-upload-list-item-error .ant-upload-text-icon .anticon,
		.ant-upload-list-item-error .ant-upload-list-item-card-actions .anticon {
			color: #ff4d4f;
		}
	}
`

export default observer(index)
