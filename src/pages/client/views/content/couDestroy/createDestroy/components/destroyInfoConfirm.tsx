import React from 'react'
import { Modal, Alert } from 'antd'
import { renderAmount } from '@src/pages/client/config/TableColumnsRender'
import styled from 'styled-components'

interface Props {
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 退款信息对象
	 */
	destroyInfoObj: any
	/**
	 * @description 提交确定操作
	 */
	onOk: () => void
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
}

const DestroyInfoConfirm = (props: Props) => {
	const { visible, destroyInfoObj, onOk, onCancel } = props

	return (
		<Modal
			open={visible}
			title="核销信息确认"
			width="700px"
			onCancel={() => {
				onCancel()
			}}
			onOk={() => {
				onOk()
			}}
		>
			<Box>
				<div className="alert">
					<Alert message="请核对需要核销的融信信息。持有方确认后，核销无法撤回！" type="warning" showIcon style={{ marginBottom: '10px' }} />
				</div>
				<div className="title">
					<div className="couNo">融信编号：{destroyInfoObj.couNo}</div>
					<div className="couAmountInYuan">融信金额：￥{renderAmount(destroyInfoObj.couAmountInYuan)}</div>
					<div className="holderCompanyName">融信持有方：{destroyInfoObj.holderCompanyName}</div>
				</div>
			</Box>
		</Modal>
	)
}
const Box = styled.div`
	padding: 10px 24px;
	.title {
		padding: 5px 10px;
		background: #ececec;
		border-radius: 3px;
		margin-bottom: 10px;
	}
`

export default DestroyInfoConfirm
