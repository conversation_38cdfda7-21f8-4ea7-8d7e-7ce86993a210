import React, { useState, useEffect } from 'react'
import { message } from 'antd'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { destroyModuleApi } from '@src/pages/client/api'
import BaseTable from '@src/globalComponents/BaseTable'
import BaseDrawer from '@src/globalComponents/BaseDrawer'
import SearchBar from '@src/pages/client/components/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import styled from 'styled-components'

interface Props {
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 用于 回显
	 */
	dataInit: any[]
	/**
	 * @description 提交确定操作
	 */
	onOk: (value: any) => void
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
}

const SelectCou = (props: Props) => {
	const [loading, setLoading] = useState(false)
	const [btnLoading, setBtnLoading] = useState(false)
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 }) // current: 4, pageSize: 20
	const [searchParams, setSearchParams] = useState({})
	const [dataSource, setDataSource] = useState<any[]>([])
	const [selectedDataSource, setSelectedDataSource] = useState<any[]>([]) // 表格中选中的数据源
	const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]) // 表格中选中的row的key
	const { visible, dataInit, onOk, onCancel } = props

	useEffect(() => {
		if (visible) {
			// 设置回显
			if (dataInit.length) {
				setSelectedRowKeys([dataInit[0].couNo])
				setSelectedDataSource(dataInit)
			} else {
				setSelectedRowKeys([])
				setSelectedDataSource([])
			}
			getList()
		}
	}, [visible])

	useEffect(() => {
		if (visible) getList()
	}, [searchParams, pagination.current, pagination.pageSize])

	// 得到核销融信列表
	const getList = () => {
		setLoading(true)
		destroyModuleApi
			.getCouList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...searchParams,
			})
			.then(
				(res: any) => {
					pagination.total = res['total']
					setPagination({ ...pagination })
					if (res.list && res.list.length) {
						setDataSource([...res['list']])
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => setLoading(false)
			)
	}
	// 校验融信是否满足条件
	const checkSelectCou = async () => {
		setBtnLoading(true)
		const couUuid = selectedDataSource[0]?.couUuid
		const res = await destroyModuleApi
			.checkSelectCou({
				couUuid,
			})
			.catch(err => {
				console.log('err', err)
			})
		setBtnLoading(false)
		if (res !== undefined) {
			// 校验成功
			onOk(selectedDataSource)
		}
	}
	const getColumns = () => {
		const columnsDic = {}
		return getColumnsByPageName('selectCouDrawerD', columnsDic)
	}
	const handleOnChange = (onSelectedRowKeys, onSelectedRows) => {
		setSelectedRowKeys(onSelectedRowKeys)
		setSelectedDataSource(onSelectedRows)
	}
	const onSearchCou = (params: { transferNo: string }) => {
		pagination.current = 1
		setSearchParams({
			...params,
		})
	}
	const onResetCou = () => {
		pagination.current = 1
		setSearchParams({})
	}
	const handlePageNumChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const handlePageSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	return (
		<BaseDrawer
			visible={visible}
			title={'选择核销融信'}
			width="880px"
			loading={btnLoading}
			onClose={onCancel}
			onOk={() => {
				if (selectedDataSource && selectedDataSource.length) {
					// 去校验cou是否满足条件
					checkSelectCou()
				} else {
					message.warning('请选择要核销的融信')
				}
			}}
		>
			<Box>
				<OperatingArea>
					<SearchBar pageName="selectCouDrawerD" onSubmit={onSearchCou} onClear={onResetCou} />
				</OperatingArea>
				<BaseTable
					rowKey="couNo"
					rowSelection={{
						type: 'radio',
						onChange: handleOnChange,
						selectedRowKeys: selectedRowKeys,
					}}
					columns={getColumns()}
					onPageChange={handlePageNumChange}
					onSizeChange={handlePageSizeChange}
					{...pagination}
					dataSource={dataSource}
					loading={loading}
				/>
			</Box>
		</BaseDrawer>
	)
}

const Box = styled.div`
	.ant-alert {
		margin-bottom: 10px;
	}
	.title {
		padding: 5px 10px;
		display: flex;
		background: #ececec;
		border-radius: 3px;
		margin-bottom: 10px;
		div {
			flex: 1;
		}
	}
	.ant-table-body {
		height: 150px;
	}
`

export default SelectCou
