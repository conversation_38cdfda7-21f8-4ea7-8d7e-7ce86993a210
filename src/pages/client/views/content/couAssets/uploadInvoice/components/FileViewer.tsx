import React from 'react'
import styled, { createGlobalStyle } from 'styled-components'
import { Empty, Modal, Image } from 'antd'
import { If, Then, Else } from 'react-if'
export interface PropsStruct {
	/**
	 * @description 标题
	 */
	title: string
	/**
	 * @description url地址
	 */
	url: string
	/**
	 * @description 显示/隐藏
	 */
	visible: boolean
	/**
	 * @description 点击关闭事件
	 */
	onCancel: () => void
	/**
	 * @description 内容的类型
	 */
	fileType?: string
}

function FileViewer(props: PropsStruct) {
	const [blobUrl, setBlobUrl] = React.useState<string | undefined>(undefined)

	React.useEffect(() => {
		if (props.visible && props.url) {
			const blob: Blob = b64toBlob(props.url)
			setBlobUrl(URL.createObjectURL(blob) + '##scrollbars=0&view=FitH')
		}
	}, [props.visible])

	const cancelDlg = () => {
		setBlobUrl(undefined)
		props.onCancel()
	}

	//base64转Blob方法
	const b64toBlob = b64Data => {
		const sliceSize: number = 512
		const byteCharacters: string = atob(b64Data)
		const byteArrays: any[] = []

		for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
			const slice: string = byteCharacters.slice(offset, offset + sliceSize)
			const byteNumbers: any[] = new Array(slice.length)
			for (let i = 0; i < slice.length; i++) {
				byteNumbers[i] = slice.charCodeAt(i)
			}
			const byteArray: Uint8Array = new Uint8Array(byteNumbers)
			byteArrays.push(byteArray)
		}
		const fileType: string = props.fileType || 'application/pdf'
		const blob: Blob = new Blob(byteArrays, { type: fileType })
		return blob
	}

	return (
		<div>
			{props.fileType === 'application/pdf' ? (
				<Modal className="file-viewer" title={`预览: ${props.title}`} open={props.visible} footer={null} width="1000px" onCancel={props.onCancel} zIndex={2000}>
					<If condition={blobUrl}>
						<Then>
							<Wrapper>
								<iframe id="file" src={blobUrl} frameBorder="0" scrolling="no" />
							</Wrapper>
						</Then>
						<Else>
							<Empty style={{ paddingTop: 300 }} description={<span>预览失败</span>} />
						</Else>
					</If>
					<GlobalStyle />
				</Modal>
			) : (
				<ShowImg>
					<Image
						src={blobUrl}
						preview={{
							visible: props.visible,
							onVisibleChange: (visible, prevVisible) => {
								cancelDlg()
							},
						}}
					/>
				</ShowImg>
			)}
		</div>
	)
}

const GlobalStyle = createGlobalStyle`

  	.file-viewer {
    top: 5vh;
    height: 85vh;
    .ant-modal-header{
      padding: 8px 24px;
    }
    .ant-modal-close-x{
      line-height: 40px;
    }
    .ant-modal-body{
      padding: 0;
      padding-bottom: 0 !important;
      font-size: 0;
      height: calc(90vh - 55px);
      overflow: auto;
      .ant-spin-nested-loading{
        height: 100%;
        .ant-spin-container{
          height: 100%
        }
      }
      .img{
          width: 100%;
          height: 100%;
      }
	}
	}
`
const Wrapper = styled.div`
	height: 100%;
	#file {
		width: 100%;
		height: 100%;
	}
`

const ShowImg = styled.div`
	.ant-image {
		display: none;
	}
`

export default React.memo(FileViewer)
