import React, { useState, useRef } from 'react'
import { userAuthModuleApi } from '@src/pages/client/api'
import SearchTable from '@globalComponents/new-widget/wx-table/searchTable'
import { getTabTit } from '@globalBiz/gBiz'
import { Button } from 'antd'
import styled from 'styled-components'
import CreateTeamModal from './components/createTeamModal'
import { history } from '@src/utils/router'
import dayjs from 'dayjs'

const teamManage = () => {
	const createTeamRef = useRef(null)
	const tableRef = useRef(null)
	const columns = [
		getTabTit(['项目组编号', 'teamNo', null]),
		getTabTit(['项目组名称', 'name', null]),
		getTabTit(['用户数', 'teamUsersCount']),
		getTabTit(['创建时间', 'createTime'], text => {
			return dayjs(text).format('YYYY/MM/DD HH:mm:ss')
		}),
		getTabTit(['操作', 'operation'], (_, record) => {
			return (
				<div>
					<a className="mr-10" onClick={gotoDetailsPage.bind(null, record)}>
						管理
					</a>
				</div>
			)
		}),
	]
	const tableConfig = {
		table: {
			columns,
			requestUrl: userAuthModuleApi.getTeamList,
		},
	}

	const showCreateModal = () => {
		createTeamRef?.current?.show({
			updateTable: tableRef?.current?.updateTable,
		})
	}

	const gotoDetailsPage = record => {
		history.push('/content/userAuth/teamManageDetails', {
			record,
		})
	}

	return (
		<PageWrapper className="card-wrapper">
			<CreateTeamModal ref={createTeamRef} />

			<div className="mb10">
				<Button type="primary" onClick={showCreateModal}>
					新建项目组
				</Button>
			</div>
			<SearchTable ref={tableRef} {...tableConfig}></SearchTable>
		</PageWrapper>
	)
}
const PageWrapper = styled.div``

export default teamManage

/*
// 确认删除项目组
const showDelModal = () => {
    Modal.confirm({
        title: '确认删除项目组',
        content: <div style={{ margin: '20px 0;' }}>删除项目组后,项目组的用户、额度将全部被删除</div>,
        onOk: (record: {}) => {
            //do del action
        },
    })
}

*/
