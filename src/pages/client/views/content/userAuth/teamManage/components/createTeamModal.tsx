import React, { forwardRef, useState, useEffect } from 'react'
import { WxModal, WxModalProps, WxBaseForm, WxBaseFormProps, useModalParams } from '@cnpm/wx-rc/components/index'
import { Input, Form, message } from 'antd'
import styled from 'styled-components'
import { forbidWhiteSpace } from '@src/globalBiz/gBiz'
import { userAuthModuleApi } from '@src/pages/client/api'

const CreateTeamModal = forwardRef((props, modalRef: any) => {
	const [form] = Form.useForm()
	const [loading, setLoading] = useState(false)
	const { updateTable, open } = useModalParams(modalRef)
	const handleSubmit = values => {
		let params = {
			name: values.name,
		}
		setLoading(true)
		userAuthModuleApi
			.addNewTeam(params)
			.then(res => {
				message.success('项目组创建成功')
				modalRef?.current?.hide()
				if (updateTable) {
					updateTable()
				}
			})
			.finally(() => {
				setLoading(false)
			})
	}
	const modalProps: WxModalProps = {
		modalProps: {
			title: '创建项目组',
			width: 600,
			confirmLoading: loading,
			onOk: () => {
				form.submit()
			},
		},
	}
	const fromProps: WxBaseFormProps = {
		layoutType: 'modal',
		formItemList: [
			{
				formItemProps: {
					label: '项目组名称',
					name: 'name',
					rules: [
						{
							required: true,
							message: '请输入项目组名称',
						},
						{
							validator: () => {
								return Promise.resolve()
							},
							message: '项目组名称重复',
						},
					],
				},
				control: <Input maxLength={64} onKeyDown={forbidWhiteSpace} />,
			},
		],
		formProps: {
			form,
			onFinish: handleSubmit,
		},
		needFooter: false,
	}

	useEffect(() => {
		if (open) {
			form.resetFields()
		}
	}, [open])

	return (
		<WxModal {...modalProps} ref={modalRef}>
			<ModalWrapper>
				<WxBaseForm {...fromProps} />
			</ModalWrapper>
		</WxModal>
	)
})

const ModalWrapper = styled.div`
	margin: 40px 0;
`

export default CreateTeamModal
