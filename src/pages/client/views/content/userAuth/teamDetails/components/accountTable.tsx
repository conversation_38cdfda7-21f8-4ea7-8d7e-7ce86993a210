import React, { forwardRef, useState, useEffect, useRef } from 'react'
import type { ForwardedRef } from 'react'
import { WxModal, WxModalProps, WxBaseForm, useModalParams } from '@cnpm/wx-rc/components/index'
import type { WxBaseFormProps } from '@cnpm/wx-rc/components/index'
import { Tooltip, Form, DatePicker, Button, Input, message } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { forbidWhiteSpace } from '@src/globalBiz/gBiz'
import { userAuthModuleApi } from '@src/pages/client/api'
import SearchTable from '@globalComponents/new-widget/wx-table/searchTable'
import { getTabTit, invalidVal } from '@globalBiz/gBiz'
import { stampToTime, timeToStamp } from '@utils/timeFilter'
import { formatNumber } from '@utils/format'
import FormItem from 'antd/es/form/FormItem'
import styled from 'styled-components'
import dayjs from 'dayjs'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
const RangePicker = DatePicker.RangePicker
const { round2Thousands } = formatNumber
interface IAccountTableProps {
	modalTitle: string
	transferType: 'PAY' | 'CREATE'
	teamNo: string
	usedTip: string
	id: string
}

interface IModalParams {
	open: boolean
	type: 'add' | 'edit'
	modalTitle: string
	transferType: 'PAY' | 'CREATE'
	teamNo: string
	id: string
	quotaNo: string
	quotaInYuan?: string
	getQuota: () => void
	quotaTableRef: any
}

type refInstance = {
	show: (params: IModalParams) => void
	hide: () => VoidFunction
}

const AddOrEditAccount = forwardRef((props, modalRef: ForwardedRef<refInstance>) => {
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')
	const user = ext.user || {}
	const { open, type, transferType, teamNo, id, modalTitle, getQuota, quotaInYuan, quotaNo, quotaTableRef } = useModalParams(modalRef) as IModalParams
	const titleTxt = `${type === 'add' ? '设置' : '修改'}${modalTitle}`
	const [form] = Form.useForm()
	const [loading, setLoading] = useState(false)

	const accountModalProps: WxModalProps = {
		modalProps: {
			title: titleTxt,
			width: 600,
			onOk: () => {
				form.submit()
			},
			confirmLoading: loading,
		},
	}

	const getControlList = () => {
		let itemsList = []
		if (type === 'edit') {
			itemsList.push({
				control: <div>{quotaNo || invalidVal}</div>,
				formItemProps: {
					name: 'quotaNo',
					label: '额度编号',
					rules: [{ required: true }],
				},
			})
		}
		itemsList.push({
			control: <Input maxLength={50} placeholder="请输入" onKeyDown={forbidWhiteSpace} suffix="元" />,
			formItemProps: {
				name: 'account',
				label: '额度',
				rules: [
					{ required: true, message: '请输入正数，整数位最多10位，小数最多2位' },
					{
						pattern: /^[1-9]\d{0,9}$|^\d{1,10}\.\d{1,2}$/,
						message: '请输入正数，整数位最多10位，小数最多2位',
					},
				],
			},
		})
		return itemsList
	}

	const accountFormProps: WxBaseFormProps = {
		formProps: {
			form,
			onFinish: values => {
				let params = {
					teamNo: teamNo,
					transferType: transferType,
					quotaInYuan: values.account,
					id: undefined,
				}
				setLoading(true)

				if (type === 'add') {
					userAuthModuleApi
						.addTeamQuota(params)
						.then(res => {
							getQuota()
							quotaTableRef?.current?.updateTable()
							// @ts-ignore
							modalRef?.current?.hide()
						})
						.finally(() => {
							setLoading(false)
						})
				} else {
					params.id = id
					userAuthModuleApi
						.editTeamQuota(params)
						.then(res => {
							getQuota()
							quotaTableRef?.current?.updateTable()
							// @ts-ignore
							modalRef?.current?.hide()
						})
						.finally(() => {
							setLoading(false)
						})
				}
			},
		},
		formItemList: getControlList(),
		needFooter: false,
	}

	useEffect(() => {
		if (open) {
			if (type === 'add') {
				form.resetFields()
			} else {
				//初始化
				form.setFieldsValue({
					account: (quotaInYuan || '').slice(0, -3),
					quotaNo,
				})
			}
		}
	}, [open])

	return (
		<WxModal {...accountModalProps} ref={modalRef}>
			<div style={{ padding: '40px 0' }}>
				<WxBaseForm {...accountFormProps} />
			</div>
		</WxModal>
	)
})

const AccountTable = (props: IAccountTableProps) => {
	// addTeamQuota
	const { transferType, modalTitle, teamNo, id, usedTip } = props
	const accountModal = useRef(null)
	const quotaTableRef = useRef(null)
	const [quota, setQuata] = useState({
		availableQuotaInYuan: '0',
		totalQuotaInYuan: '0',
		usedQuotaInYuan: '0',
	})
	const columns = [
		getTabTit(['额度编号', 'quotaNo']),
		getTabTit(['额度(¥)', 'quotaInYuan'], text => {
			return round2Thousands(Number(text) || 0)
		}),
		getTabTit(['创建时间', 'createTime'], text => {
			return text ? dayjs(text).format('YYYY/MM/DD HH:mm:ss') : invalidVal
		}),
		getTabTit(['修改时间', 'updateTime'], text => {
			return text ? dayjs(text).format('YYYY/MM/DD HH:mm:ss') : invalidVal
		}),
		getTabTit(['操作', 'operation'], (_, record) => {
			return (
				<a
					onClick={() => {
						accountModal?.current?.show({
							modalTitle: modalTitle,
							type: 'edit',
							teamNo,
							transferType,
							quotaNo: record.quotaNo,
							quotaInYuan: record.quotaInYuan,
							id: record.id,
							getQuota,
							quotaTableRef,
						})
					}}
				>
					修改
				</a>
			)
		}),
	]
	const exportQuotaData = () => {
		let params = quotaTableRef?.current?.getValues()
		let { pageNum, pageSize } = quotaTableRef?.current?.getTableDataDetail()

		params = { ...params, transferType, teamNo, pageNum, pageSize }
		userAuthModuleApi
			.teamQuotaExport(params)
			.then((response: any) => {
				console.log(`modalTitle = ${modalTitle}`)
				downloadFileByFileFlow(response, 'application/vnd.ms-excel', `${modalTitle}.xlsx`, () => {
					message.success(`${modalTitle}下载成功`)
				})
			})
			.catch(e => {
				message.error('下载失败')
			})
	}
	const tableConfig = {
		table: {
			requestParam: {
				transferType,
				teamNo,
			},
			columns,
			requestUrl: userAuthModuleApi.getTeamQuotaList,
		},
		searchBar: {
			btnSlot: (
				<Button type="primary" onClick={exportQuotaData}>
					导出
				</Button>
			),
			transform: param => {
				if (param.beginEndDate && param.beginEndDate.length > 1) {
					param.beginDate = stampToTime(param.beginEndDate[0], 6)
					param.endDate = stampToTime(param.beginEndDate[1], 6)
					delete param.beginEndDate
				}
				return param
			},
			formItems: (
				<>
					<FormItem name="beginEndDate" label="" className="s-range-picker">
						<RangePicker
							getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
							placeholder={['创建时间(开始)', '创建时间(结束)']}
							style={{ width: 290 }}
							format="YYYY-MM-DD"
						/>
					</FormItem>
				</>
			),
		},
	}

	const showAddModal = () => {
		accountModal?.current?.show({
			modalTitle: modalTitle,
			type: 'add',
			teamNo,
			transferType,
			id,
			getQuota,
			quotaTableRef,
		})
	}

	const getQuota = () => {
		const params = {
			transferType: transferType,
			teamNo: teamNo,
		}
		userAuthModuleApi.teamQuotaQuery(params).then(res => {
			let { availableQuotaInYuan, totalQuotaInYuan, usedQuotaInYuan } = res

			availableQuotaInYuan = round2Thousands(Number(availableQuotaInYuan))
			totalQuotaInYuan = round2Thousands(Number(totalQuotaInYuan))
			usedQuotaInYuan = round2Thousands(Number(usedQuotaInYuan))

			setQuata({
				availableQuotaInYuan,
				totalQuotaInYuan,
				usedQuotaInYuan,
			})
		})
	}

	useEffect(() => {
		getQuota()
	}, [])

	return (
		<Wrapper>
			<div className="tip-container">
				<div>
					<span>
						总额度
						<Tooltip placement="top" title={'所有创建的额度记录的额度之和'}>
							<QuestionCircleOutlined className="icon" />
						</Tooltip>
					</span>
					<span className="money">¥ {quota.totalQuotaInYuan}</span>
				</div>
				<div>
					<span>
						已用额度
						<Tooltip placement="top" title={usedTip}>
							<QuestionCircleOutlined className="icon" />
						</Tooltip>
					</span>
					<span className="money red">¥ {quota.usedQuotaInYuan}</span>
				</div>
				<div>
					<span>
						可用额度
						<Tooltip placement="top" title={'可用额度=总额度-已用额度'}>
							<QuestionCircleOutlined className="icon" />
						</Tooltip>
					</span>
					<span className="money green">¥ {quota.availableQuotaInYuan}</span>
				</div>
			</div>

			<div className="table-wrap">
				<Button className="add-account" type="primary" onClick={showAddModal}>
					添加额度
				</Button>
				<SearchTable {...tableConfig} ref={quotaTableRef} />
			</div>

			<AddOrEditAccount ref={accountModal} />
		</Wrapper>
	)
}

const Wrapper = styled.div`
	.tip-container {
		display: flex;
		margin: 50px 0 20px 30px;

		> div {
			width: 175px;
			> span {
				display: block;
				.icon {
					color: #036bbb;
					cursor: pointer;
					margin-left: 5px;
				}
			}
			.money {
				font-size: 18px;
				margin-top: 10px;
				font-weight: bold;
			}
			.green {
				color: green;
			}
		}
	}
	.table-wrap {
		position: relative;
		.add-account {
			position: absolute;
			top: 3px;
		}
		.s-range-picker {
			width: 300px;
		}
		.ant-btn {
			margin-right: 10px;
		}
		.ant-btn:last-child {
			margin-right: 0;
		}
	}
`

export default AccountTable
