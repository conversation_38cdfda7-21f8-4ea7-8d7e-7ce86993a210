import React, { forwardRef, useState, useEffect } from 'react'
import { WxModal, WxModalProps, WxBaseForm, useModalParams } from '@cnpm/wx-rc/components/index'
import type { WxBaseFormProps } from '@cnpm/wx-rc/components/index'
import { Input, Form, Checkbox, message } from 'antd'
import styled from 'styled-components'
import { forbidWhiteSpace } from '@src/globalBiz/gBiz'
import { userAuthModuleApi } from '@src/pages/client/api'

const AddOrEditUser = forwardRef((props, modalRef: any) => {
	const { type, teamNo, tableRef, open, record } = useModalParams(modalRef)
	const [form] = Form.useForm()
	const [loading, setLoading] = useState(false)
	const [roleList, setRoleList] = useState([])
	const [curCheckedList, setCurCheckedList] = useState([])
	const [idDisabledMap, setIdDisabledMap] = useState({})

	const handleSubmit = values => {
		setLoading(true)
		let params = {
			...values,
			teamNo,
			projectCode: 'factor',
		}
		if (type === 'add') {
			userAuthModuleApi
				.addTeamUser(params)
				.then(res => {
					message.success('新建用户成功')
					modalRef?.current?.hide()
					tableRef?.current?.updateTable()
				})
				.finally(() => {
					setLoading(false)
				})
		} else {
			params.lob = 'factor'
			params.uuid = record.uuid
			userAuthModuleApi
				.updateTeamUser(params)
				.then(res => {
					message.success('修改用户成功')
					modalRef?.current?.hide()
					tableRef?.current?.updateTable()
				})
				.finally(() => {
					setLoading(false)
				})
		}
	}

	const modalProps: WxModalProps = {
		modalProps: {
			title: type === 'add' ? '新建用户' : '修改用户',
			width: 700,
			confirmLoading: loading,
			onOk: () => {
				form.submit()
			},
		},
	}

	/*
	只支持单角色；
	如何双身份的话，以roleName.slice(-6)相等视为同角色
	*/
	const handleCheckBox = checkedList => {
		let roleSuffix = ''
		let roleNameLen = 6
		let newDisableMap = {}
		for (let i = 0, len = roleList.length; i < len; i++) {
			let item = roleList[i]
			if (checkedList.includes(item.id)) {
				roleSuffix = item.roleName.slice(roleNameLen * -1)
				break
			}
		}
		roleList.forEach(item => {
			if (item.roleName.slice(roleNameLen * -1) === roleSuffix || roleSuffix === '') {
				newDisableMap[item.id] = false
			} else {
				newDisableMap[item.id] = true
			}
		})

		setIdDisabledMap(newDisableMap)
	}

	const fromProps: WxBaseFormProps = {
		layoutType: 'modal',
		formItemList: [
			{
				formItemProps: {
					label: '用户名',
					name: 'username',
					rules: [
						{
							required: true,
							message: '请输入用户名',
						},
					],
				},
				control: <Input maxLength={20} placeholder="请输入用户名" onKeyDown={forbidWhiteSpace} />,
			},
			{
				formItemProps: {
					label: '用户邮箱',
					name: 'email',
					rules: [
						{
							required: true,
							type: 'email',
							message: '请输入邮箱,并确认格式正确',
						},
					],
				},
				control: <Input maxLength={64} placeholder="请输入用户邮箱" onKeyDown={forbidWhiteSpace} />,
			},
			{
				formItemProps: {
					label: '手机号',
					name: 'mobile',
					rules: [
						{
							required: true,
							pattern: /^1\d{10}$/,
							message: '请输入手机号,并确认格式正确',
						},
					],
				},
				control: <Input maxLength={11} placeholder="请输入手机号" onKeyDown={forbidWhiteSpace} />,
			},
			{
				control: (
					<div className="roleIdsWrap">
						<Form.Item label="角色权限" name="roleIds" rules={[{ required: true, message: '请选择角色权限' }]}>
							<Checkbox.Group onChange={handleCheckBox}>
								{roleList.map(item => {
									return (
										<Checkbox key={item.id} value={item.id} disabled={idDisabledMap[item.id]}>
											{item.remark}
										</Checkbox>
									)
								})}
							</Checkbox.Group>
						</Form.Item>
					</div>
				),
			},
		],
		formProps: {
			form,
			onFinish: handleSubmit,
		},
		needFooter: false,
	}

	useEffect(() => {
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let companyType = ext.user && ext.user.company ? ext.user.company.type : ''
		// 点击修改或新建获取的角色列表
		userAuthModuleApi
			.searchRoleList({ companyType })
			.then(res => {
				if (Array.isArray(res)) {
					const targetRoleList = res.filter(item => item.roleType === 3) || []
					setRoleList(targetRoleList)
				}
			})
			.catch(err => {
				console.log('err', err)
			})
	}, [])

	useEffect(() => {
		if (open) {
			if (type === 'add') {
				//清空原状态
				form.resetFields()
				setCurCheckedList([])
				setIdDisabledMap({})
			} else {
				//清空原状态
				let roleIds = (record.roleList || []).map(item => item.id)
				form.setFieldsValue({ ...record, roleIds })
				setCurCheckedList(roleIds)
				handleCheckBox(roleIds)
				//setIdDisabledMap({});
			}
		}
	}, [open])

	return (
		<WxModal {...modalProps} ref={modalRef}>
			<ModalWrapper>
				<WxBaseForm {...fromProps} />
			</ModalWrapper>
		</WxModal>
	)
})

const ModalWrapper = styled.div`
	margin: 40px 0;
	.ant-checkbox-group {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}
	.ant-checkbox-wrapper + .ant-checkbox-wrapper {
		margin-left: 0;
	}
`

export default AddOrEditUser
