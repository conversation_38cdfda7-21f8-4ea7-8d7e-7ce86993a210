import React, { useState, useRef } from 'react'
import { userAuthModuleA<PERSON> } from '@src/pages/client/api'
import SearchTable, { type SearchTableProps } from '@globalComponents/new-widget/wx-table/searchTable'
import { getTabTit } from '@globalBiz/gBiz'
import { Button, Modal, Descriptions, Tabs, List, message } from 'antd'
import styled from 'styled-components'
import SmallTitle from '@globalComponents/SmallTitle'
import AddOrEditUser from './components/addOrEditUser'
import AccountTable from './components/accountTable'
import { history } from '@src/utils/router'
import dayjs from 'dayjs'

const teamManage = () => {
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')
	const loginCompanyType = ext?.user?.loginCompanyType || ''
	//console.log(`loginCompanyType = ${loginCompanyType}`);
	const { record } = history.location.state
	const { teamNo } = record

	const addOrEditUserRef = useRef(null)
	const tableRef = useRef(null)
	const showAddUserModal = options => {
		addOrEditUserRef?.current?.show(options)
	}
	const [loading, setLoading] = useState(false)
	const descItems = [
		{
			label: '项目组编号',
			children: teamNo,
		},
		{
			label: '项目组名称',
			children: record.name,
		},
		{
			label: '创建时间',
			children: dayjs(record.createTime).format('YYYY/MM/DD HH:mm:ss'),
		},
	]

	const showDelModal = record => {
		Modal.confirm({
			title: '确认删除用户',
			content: <div style={{ height: '40px' }}></div>,
			onOk: () => {
				const params = {
					teamNo,
					userId: record.id,
				}
				console.log(`params = ${JSON.stringify(params)}`)
				userAuthModuleApi.delTeamUserList(params).then(res => {
					message.success('删除用户成功')
					tableRef?.current?.updateTable()
				})
			},
		})
	}

	const tabColumns = [
		getTabTit(['用户名', 'username']),
		getTabTit(['用户邮箱', 'email', null]),
		getTabTit(['手机号', 'mobile']),
		getTabTit(['角色权限', 'roleList'], (text, record) => {
			let list = record.roleList
			let targetArr = list.map(item => {
				return item.remark
			})
			return targetArr.join('、')
		}),
		getTabTit(['操作', 'operation'], (text, record) => {
			return (
				<div>
					<a
						className="mr10"
						onClick={showAddUserModal.bind(null, {
							record,
							type: 'edit',
							teamNo,
							tableRef,
						})}
					>
						修改
					</a>
					<a className="red" onClick={showDelModal.bind(null, record)}>
						删除
					</a>
				</div>
			)
		}),
	]
	const tableConfig: SearchTableProps = {
		table: {
			showPagination: false,
			transform: (res = []) => {
				let newObj = { list: res, pageNum: 1, total: res.length }
				return newObj
			},
			requestParam: { teamNo },
			columns: tabColumns,
			requestUrl: userAuthModuleApi.getTeamUserList,
		},
	}

	return (
		<PageWrapper className="card-wrapper">
			<AddOrEditUser ref={addOrEditUserRef} />
			<SmallTitle>基本信息</SmallTitle>
			<Descriptions title="" bordered column={2}>
				{descItems.map(item => {
					return <Descriptions.Item label={<div style={{ whiteSpace: 'nowrap' }}>{item.label}</div>}>{item.children}</Descriptions.Item>
				})}
			</Descriptions>
			<Tabs
				style={{ marginTop: '40px' }}
				defaultActiveKey="1"
				items={[
					{
						label: '项目组用户管理',
						key: '1',
						children: (
							<div>
								<div className="mb10">
									<Button type="primary" onClick={showAddUserModal.bind(null, { type: 'add', teamNo, tableRef: tableRef })}>
										添加用户
									</Button>
								</div>
								<SearchTable {...tableConfig} ref={tableRef}></SearchTable>
							</div>
						),
					},
					...(() => {
						let itemsArr = []

						if (loginCompanyType.indexOf('S') > -1) {
							itemsArr.push({
								label: '支付/融资额度管理',
								key: '2',
								children: (
									<AccountTable
										modalTitle="支付/融资额度"
										usedTip="已用额度包含支付/融资审核中的和支付/融资成功的融信金额"
										transferType="PAY"
										teamNo={teamNo}
										id={record.id}
									/>
								),
							})
						}

						if (loginCompanyType.indexOf('C') > -1) {
							itemsArr.push({
								label: '开立额度管理',
								key: '3',
								children: (
									<AccountTable
										modalTitle="开立额度"
										usedTip="已用额度包含开立审核中的和开立成功的融信金额"
										transferType="CREATE"
										teamNo={teamNo}
										id={record.id}
									/>
								),
							})
						}

						return itemsArr
					})(),
				]}
			/>
		</PageWrapper>
	)
}
const PageWrapper = styled.div`
	.ant-tabs-tab-btn {
		padding: 0 20px;
	}
`

export default teamManage
