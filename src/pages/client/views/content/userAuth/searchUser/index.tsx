import React, { ReactElement, useEffect, useState } from 'react'
import styled from 'styled-components'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import OperatingArea from '@src/globalComponents/OperatingArea'
import BaseTable from '@src/globalComponents/BaseTable'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { Button, Modal, Space, Alert } from 'antd'
import { userAuthModuleA<PERSON> } from '@src/pages/client/api'
import { formatTableData } from '@utils/format'
import AddOrEditUserInfoModal from './components/addOrEditUserInfoModal'
import UserDetailModal from './components/userDetailModal'
import { onOk, TableOptions } from '@src/utils/util'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'

type FormModalType = 'add' | 'edit'
export default function SearchUser(): ReactElement {
	const [formModalType, setFormModalType] = useState<FormModalType>('add')
	const [userInfo, setUserInfo] = useState({})
	const [modalVisible, setModalVisible] = useState(false)
	const [addAndEditModalVisible, setAddAndEditModalVisible] = useState(false)
	const [tableLoading, setTableLoading] = useState(false)
	const [dataSource, setDataSource] = useState([])
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const { confirm } = Modal
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')

	useEffect(() => {
		getInfoList()
	}, [pagination.current, pagination.pageSize])

	const getInfoList = async () => {
		const params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
		}
		setTableLoading(true)
		const res = await userAuthModuleApi.getNonTeamList(params)
		setTableLoading(false)
		if (res) {
			const list = formatTableData.addKey(res.list)
			setDataSource(list)
			pagination.total = res.total
			setPagination({ ...pagination })
		}
	}

	const handleEditFinish = types => {
		setAddAndEditModalVisible(false)
		onOk(types, getInfoList)
	}

	const handleAddOrEditUserInfo = (type: FormModalType, record?: any) => {
		setFormModalType(type)
		if (record) {
			setUserInfo(record)
		} else setUserInfo({})
		setAddAndEditModalVisible(true)
	}

	// 解除关联
	const handleDisassociate = record => {
		Modal.confirm({
			title: '确定解除关联',
			content: '解除关联后，该用户将不关联本企业',
			onOk() {
				userAuthModuleApi.removeAccountForOrg({ accountId: record?.accountId }).then(res => {
					if (res) {
						onOk('解除', getInfoList)
					}
				})
			},
		})
	}

	const getColumns = () => {
		const columnsDic = {
			accountId: {
				unit: 23,
			},
			roleList: {
				render: record => {
					let newRoles = []
					record?.map(item => {
						return newRoles.push(item?.vname)
					})
					return newRoles.join('、')
				},
			},
			operation: {
				width: 140,
				render: (text: string, record: any) => {
					const loginedAccountId = ext?.user?.accountId
					// 当前用户是管理员
					const isAdmin = record?.accountId === loginedAccountId ? true : false

					return (
						<Space size="small">
							<a className={'edit_btn'} onClick={() => handleAddOrEditUserInfo('edit', record)}>
								修改
							</a>
							{!isAdmin && (
								<a className="delete_btn" type="text" onClick={() => handleDisassociate(record)}>
									解除关联
								</a>
							)}
						</Space>
					)
				},
			},
		}
		return evenlyTableColunms(getColumnsByPageName('searchUser', columnsDic))
	}

	function handleChange(current: number) {
		pagination.current = current
		setPagination({ ...pagination })
	}
	function handleSizeChange(size: number) {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	return (
		<LayoutSlot>
			<Wrapper className="card-wrapper">
				<Alert message="说明：添加用户需要为已经在平台注册的用户，且完成实名认证" type="info" showIcon style={{ marginBottom: '20px' }} />
				<OperatingArea>
					<Button onClick={() => handleAddOrEditUserInfo('add')} type="primary" className="big-btn">
						添加用户
					</Button>
				</OperatingArea>
				<BaseTable
					loading={tableLoading}
					columns={getColumns()}
					dataSource={dataSource}
					{...pagination}
					onPageChange={handleChange}
					onSizeChange={handleSizeChange}
				/>
				<UserDetailModal userInfo={userInfo} visible={modalVisible} onCancel={() => setModalVisible(false)} />
				<AddOrEditUserInfoModal
					type={formModalType}
					userInfo={userInfo}
					visible={addAndEditModalVisible}
					onOk={handleEditFinish}
					onCancel={() => setAddAndEditModalVisible(false)}
					options={TableOptions}
				/>
			</Wrapper>
		</LayoutSlot>
	)
}

const Wrapper = styled.div`
	.edit_btn {
		color: #1890ff;
		margin-right: 10px;
	}
	.delete_btn {
		color: #ff4d4f;
	}
	.username {
		color: #606eff;
		cursor: pointer;
	}
`
