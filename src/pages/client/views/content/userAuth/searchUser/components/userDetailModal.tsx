/*
 * @Author: chenyan
 * @Date: 2023-01-30 16:19:41
 * @LastEditTime: 2023-02-02 11:51:25
 * @FilePath: /frontend-web/src/pages/client/views/content/userAuth/searchUser/components/userDetailModal.tsx
 * @Description:
 */
import React, { ReactElement } from 'react'
import { Modal, Descriptions } from 'antd'
import styled from 'styled-components'
interface Props {
	userInfo: any
	visible: boolean
	onCancel: () => any
}

function UserDetailModal({ visible, onCancel, userInfo }: Props): ReactElement {
	let roleArr: string[] = []
	if (userInfo.roleList && userInfo.roleList.length > 0) {
		for (let item of userInfo.roleList) {
			roleArr.push(item.remark)
		}
	}
	let returnData = ''
	if (roleArr.length > 0) {
		returnData = roleArr.join('，')
	} else {
		returnData = '- -'
	}

	return (
		<Modal open={visible} title="查看用户详情" onCancel={onCancel} footer={null} width={600}>
			<Wrapper className="main-wrapper">
				<Descriptions layout="horizontal" column={{ xs: 1, sm: 1, md: 1 }}>
					<Descriptions.Item label="用户名">{userInfo.username}</Descriptions.Item>
					<Descriptions.Item label="用户邮箱">{userInfo.email}</Descriptions.Item>
					<Descriptions.Item label="手机号">{userInfo.mobile}</Descriptions.Item>
					<Descriptions.Item label="角色权限">{returnData}</Descriptions.Item>
				</Descriptions>
			</Wrapper>
		</Modal>
	)
}

const Wrapper = styled.div`
	padding: 10px;
	.ant-descriptions-item-label {
		width: 50%;
		display: block;
		text-align: right;
	}
`

export default UserDetailModal
