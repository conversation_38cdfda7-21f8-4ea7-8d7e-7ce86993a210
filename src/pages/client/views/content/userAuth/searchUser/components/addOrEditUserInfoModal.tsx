import styled from 'styled-components'
import React, { ReactElement, useEffect, useState } from 'react'
import { Modal, Form, Input, Checkbox, message } from 'antd'
import { userAuthModuleApi } from '@src/pages/client/api'
import { formatLoginUrl } from '@src/utils/jy-helper'

interface Props {
	type: 'add' | 'edit'
	userInfo: any
	visible: boolean
	onOk: (types: string) => any
	onCancel: () => any
	options: any
}

/*
vcode 角色对应vcode 映射关系 ，2、3是互斥关系
1: '管理员'
2: "操作员"
3: "审核员"
*/

function AddOrEditUserInfoModal({ type, visible, onCancel, onOk, userInfo, options }: Props): ReactElement {
	const [form] = Form.useForm()
	const [roleIdList, setroleIdList] = useState<[{ [key: string]: string }]>([{}])
	const [roleListOptions, setRoleListOptions] = useState<any[]>([])
	const [loading, setLoading] = useState<boolean>(false)
	const isEdit = type === 'edit'
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')
	const loginedAccountId = ext?.user?.accountId
	const isAdmin = userInfo?.accountId === loginedAccountId ? true : false

	useEffect(() => {
		if (visible) {
			getroleList()
		}
	}, [visible])

	//查询现有配置角色列表
	const getroleList = async () => {
		// 点击修改或新建获取的角色列表
		const roleList =
			(await userAuthModuleApi
				.getSupportOrgTypeRole({
					supportOrgType: ext?.user?.company?.type,
					roleType: 0,
				})
				.catch(err => {
					console.log('err', err)
				})) || []

		let newRoleListOptions = roleList?.map(item => ({ label: item?.vname || '', value: item?.vcode || '' }))
		setRoleListOptions(newRoleListOptions)
		setroleIdList(roleList)
	}

	useEffect(() => {
		// 取到接口返回的角色id
		let roleIds: string[] = []
		roleIdList.forEach(item => {
			roleIds.push(item.id)
		})
		if (type === 'add') {
			form.resetFields()
		} else if (type === 'edit') {
			const roleIdList = userInfo?.roleList?.map(i => i?.vcode)
			form.setFieldsValue({ ...userInfo, roleIdList })
			// 禁用逻辑初始化
			selectRoleChange(roleIdList)
		}
		// roleListOption 发生了改变
	}, [roleIdList])

	//提交表单（新增 && 编辑）前置操作
	const handleSubmit = () => {
		form.validateFields().then(
			res => {
				console.log('表单验证后的res: ', res)
				handleAddOrEditUserInfo(res)
			},
			err => {
				console.log(err, 'err')
			}
		)
	}
	//提交表单（新增 && 编辑）提交数据
	const handleAddOrEditUserInfo = async (values: any) => {
		setLoading(true)
		const previousIds = userInfo?.roleList?.map(item => item.vcode)
		const accountName = userInfo?.accountName
		const loginedAccountId = ext?.user?.accountId
		const params = {
			accountName: values?.accountName,
			mobile: values?.mobile,
			orgId: ext?.user?.orgId,
			// 改参数名了
			//roleIdList: values?.roleIdList,
			roleCodeList: values?.roleIdList,
		}

		if (type === 'add') {
			userAuthModuleApi
				.addAccountForOrg(params)
				.then(res => {
					if (res) {
						onOk(options.ADD)
						setLoading(false)
					}
				})
				.catch(error => {
					console.log(error)
					setLoading(false)
				})
				.finally(() => {
					setLoading(false)
				})
		} else if (isEdit) {
			params['accountId'] = userInfo?.accountId
			params['userId'] = userInfo?.id
			params['supportOrgType'] = ext?.user?.company?.type
			if (isAdmin && !params['roleCodeList'].includes(1)) {
				// 管理员vcode 默认添加 1
				params['roleCodeList'].push(1)
			}

			const tempA = previousIds?.sort((a, b) => a - b)
			const tempB = params.roleCodeList?.sort((a, b) => a - b)
			if (JSON.stringify(tempA) == JSON.stringify(tempB) && accountName == values.accountName) {
				message.error('信息未修改，请修改后提交或取消修改')
				setLoading(false)
			} else {
				userAuthModuleApi
					.updateAccountForOrg(params)
					.then(res => {
						if (res) {
							onOk(options.EDIT)
							setLoading(false)
							if (isAdmin && JSON.stringify(tempA) !== JSON.stringify(tempB)) {
								Modal.info({
									title: <div style={{ fontWeight: 'normal', fontSize: 15 }}>您的权限有变更，请重新企业登录</div>,
									closable: false,
									maskClosable: false,
									wrapClassName: 'modal-btn-center',
									style: { marginTop: 30 },
									onOk() {
										const returnUrl = encodeURIComponent(`${window.location.origin}${window.location.pathname}`)
										window.location.href = formatLoginUrl({ returnUrl, token: localStorage.getItem('token') })
									},
									okText: '重新企业登录',
								})
							}
						}
					})
					.finally(() => {
						setLoading(false)
					})
			}
		}
	}

	//let roleListOptions = roleIdList?.map(item => ({ label: item?.vname || '', value: item?.vcode || '' }))
	const selectRoleChange = checkedValue => {
		let newRoleListOptions = [...roleListOptions]
		const setDisabledForRole = (vcodeArr: number[]) => {
			let selectedRoleType = vcodeArr[0]
			let disabledRoleType = vcodeArr[1]
			// 操作员 与 审核员的角色是互斥的
			if (checkedValue?.includes(selectedRoleType)) {
				newRoleListOptions = newRoleListOptions.map(item => {
					if (item.value === disabledRoleType) {
						item.disabled = true
					} else {
						item.disabled = false
					}
					return item
				})
			}

			if (!checkedValue || checkedValue.length === 0) {
				newRoleListOptions = newRoleListOptions.map(item => {
					item.disabled = false
					return item
				})
			}
		}

		setDisabledForRole([2, 3])
		setDisabledForRole([3, 2])
		setRoleListOptions(newRoleListOptions)
	}

	return (
		<Modal
			forceRender
			open={visible}
			title={type === 'add' ? '新建用户' : '修改用户'}
			onCancel={() => {
				setLoading(false)
				onCancel()
			}}
			width={600}
			onOk={handleSubmit}
			getContainer={false}
			okButtonProps={{
				loading: loading,
			}}
			maskClosable={false}
		>
			<div className="card-wrapper modal-content-padding">
				<Form form={form} name="addUserForm" onFinish={handleSubmit} labelCol={{ span: 5 }} wrapperCol={{ span: 17 }}>
					{isEdit ? (
						<Form.Item name="accountId" label="用户ID">
							<Input disabled />
						</Form.Item>
					) : null}
					<Form.Item
						name="mobile"
						className="mobile"
						label={'手机号'}
						rules={[
							{
								required: true,
								message: '请输入手机号',
								whitespace: true,
							},
							{
								pattern: /^1\d{10}$/,
								message: '请输入正确的手机号',
							},
						]}
					>
						<Input type="string" disabled={isEdit} placeholder="请输入手机号" maxLength={11} />
					</Form.Item>
					<Form.Item className="username" label="真实姓名" name="accountName" rules={[{ required: true, message: '请输入真实姓名', whitespace: true }]}>
						<Input placeholder="请输入真实姓名" maxLength={20} disabled={isEdit} />
					</Form.Item>
					<CheckboxStyle>
						<Form.Item className="roleIds" label="角色权限" name="roleIdList" rules={!isAdmin ? [{ required: true, message: '请选择角色权限' }] : []}>
							<Checkbox.Group options={roleListOptions} onChange={selectRoleChange} />
						</Form.Item>
					</CheckboxStyle>
				</Form>
			</div>
		</Modal>
	)
}
const CheckboxStyle = styled.div`
	.roleIds {
		.ant-checkbox-wrapper {
			margin: 0 15px 0 0;
		}
		.explain {
			color: rgb(249, 163, 20);
			margin-left: 5px;
			cursor: pointer;
		}
	}
`

export default AddOrEditUserInfoModal
