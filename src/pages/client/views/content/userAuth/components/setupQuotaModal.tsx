import React, { ReactElement, useEffect } from 'react'
import { Modal, Form, Input, Row, Col, Tooltip, message } from 'antd'
import { userAuthModuleApi } from '@src/pages/client/api'
import styled from 'styled-components'
import { numberToThousands } from '@utils/timeFilter'
import { QuestionCircleFilled } from '@ant-design/icons'

const layout = {
	labelCol: { span: 8 },
	wrapperCol: { span: 14 },
}

interface Props {
	dataInfo: any
	visible: boolean
	onCancel: () => any
	onOk: () => any
	//可用额度
	availableAmount: number
	//弹窗需要的文案配置
	data: {
		// 弹窗内操作员类型
		operatorName: string
		//已用额度提示文案
		usedTips: string
		//关键词 用于拼接文案
		keyWord: string
	}
}

function SetupQuotaModal({ visible, onCancel, dataInfo, onOk, availableAmount, data }: Props): ReactElement {
	const [form] = Form.useForm()
	const [confirmLoading, setConfirmLoading] = React.useState(false)
	useEffect(() => {
		if (dataInfo && dataInfo.quotaInCent) {
			form.setFieldsValue({ quotaInCent: Number(dataInfo.quotaInCent) / 100 })
		}
		if (!visible) form.resetFields()
	}, [visible])
	const submitData = () => {
		setConfirmLoading(true)
		form
			.validateFields()
			.then(res => {
				const params = {
					uuid: dataInfo.uuid,
					quotaInCent: Number(res['quotaInCent']) * 100,
				}
				userAuthModuleApi
					.setQuota(params)
					.then(item => {
						message.success('设置成功')
						onCancel()
						onOk()
						setConfirmLoading(false)
					})
					.catch(err => {
						setConfirmLoading(false)
					})
			})
			.catch(error => {
				setConfirmLoading(false)
				return
			})
	}
	return (
		<Modal open={visible} title="设置额度" onCancel={onCancel} onOk={submitData} okText="确定" width={600} confirmLoading={confirmLoading}>
			<Wrapper className="main-wrapper">
				<Row style={{ marginBottom: '15px' }}>
					<Col className="rowName" span={8}>
						{data.operatorName + '限额操作员'}：
					</Col>
					<Col span={14}>{dataInfo['username']}</Col>
				</Row>
				<Row style={{ marginBottom: '15px' }}>
					<Col className="rowName" span={8}>
						已用额度：
					</Col>
					<Col span={14}>
						{numberToThousands(Number(availableAmount) / 100)} &nbsp;&nbsp;
						<Tooltip placement="top" title={data.usedTips}>
							<QuestionCircleFilled className="costConfirm" style={{ color: 'rgb(249, 163, 20)', cursor: 'pointer' }} />
						</Tooltip>
					</Col>
				</Row>
				<Form {...layout} form={form}>
					<Form.Item
						name="quotaInCent"
						className="quotaInCent"
						label={data.keyWord + '额度'}
						extra={data.keyWord + '额度不可低于历史已用额度'}
						rules={[
							{
								required: true,
								pattern: /^(?!-1+(?:\.0+)?$)(?:[1-9]\d{0,9}|0)(?:\.\d{1,2})?$/,
								max: 12,
								message: '请输入数值，整数最多10位，小数最多2位',
								whitespace: true,
							},
							{
								validator(_, value) {
									let limitAmount = Number(availableAmount) / 100
									if (limitAmount <= Number(value)) {
										return Promise.resolve()
									} else {
										if (value && !isNaN(value)) {
											return Promise.reject(new Error(data.keyWord + '额度不可低于历史已用额度'))
										} else {
											return Promise.resolve()
										}
									}
								},
							},
						]}
					>
						<Input placeholder="0.00" />
					</Form.Item>
				</Form>
			</Wrapper>
		</Modal>
	)
}

const Wrapper = styled.div`
	padding: 30px;
	.rowName {
		text-align: right;
	}
	.quotaInCent {
		margin-bottom: 20px;
		.ant-form-item-explain-error {
			position: absolute;
			top: 52px;
		}
	}
`

export default SetupQuotaModal
