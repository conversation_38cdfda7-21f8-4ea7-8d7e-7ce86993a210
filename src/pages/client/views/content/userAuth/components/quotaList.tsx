import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import styled from 'styled-components'
import BaseTable from '@src/globalComponents/BaseTable'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import SetupQuotaModal from './setupQuotaModal'
import { userAuthModuleApi } from '@src/pages/client/api'
import { formatTableData } from '@utils/format'

type pageName = 'PAY' | 'CREATE'
type operatorNameType = '供应商' | '核心企业'

interface Props {
	//页面类型（涉及本页面有： 支付/融资额度 || 开立额度 两个页面）
	pageName: pageName
	//getlistAPi
	getListApi: ({ pageNum, pageSize }) => any
	//列表匹配的标识
	columnsPageName: string
	//弹窗需要的文案配置
	data: {
		// 弹窗内操作员类型
		operatorName: operatorNameType
		//已用额度提示文案
		usedTips: string
		//关键词 用于拼接文案
		keyWord: string
	}
}

function QuotaLIst(props: Props) {
	const { getListApi, columnsPageName, data } = props
	const [tableLoading, setTableLoading] = useState(false)
	const [dataSource, setDataSource] = useState<any>([])
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [dataInfo, setDataInfo] = useState<any>({})
	const [availableAmount, setAvailableAmount] = useState<any>(0)
	const [modalVisible, setModalVisible] = useState(false)
	useEffect(() => {
		buildOperatorList()
	}, [pagination.current, pagination.pageSize])

	const buildOperatorList = async () => {
		const params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
		}
		setTableLoading(true)
		const res = await getListApi(params).catch(err => {
			console.log('err', err)
		})
		setTableLoading(false)
		if (res) {
			const list = formatTableData.addKey(res.list)
			setDataSource(list)
			pagination.total = res.total
			setPagination({ ...pagination })
		}
	}
	const getColumns = () => {
		const columnsDic = {
			operation: {
				render: (text: string, record: any) => {
					return (
						<div className="link" onClick={() => setupAmount(record)}>
							设置额度
						</div>
					)
				},
			},
		}
		return getColumnsByPageName(columnsPageName, columnsDic)
	}
	const setupAmount = async record => {
		let getAvailableAmount = await userAuthModuleApi.usedAmount({ uuid: record.uuid }).catch(err => {
			console.log('err', err)
		})
		setAvailableAmount(getAvailableAmount)
		setDataInfo(record)
		setModalVisible(true)
	}
	const handleChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	return (
		<CouManageWrapper>
			<LayoutSlot>
				<div className="card-wrapper">
					<BaseTable
						loading={tableLoading}
						columns={getColumns()}
						dataSource={dataSource}
						{...pagination}
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
					/>
					{dataSource.length > 0 ? null : <div className="noData">请先创建限额操作员用户。</div>}
				</div>
			</LayoutSlot>
			<SetupQuotaModal
				dataInfo={dataInfo}
				visible={modalVisible}
				onOk={() => {
					setModalVisible(false)
					buildOperatorList()
				}}
				onCancel={() => setModalVisible(false)}
				availableAmount={availableAmount}
				data={data}
			/>
		</CouManageWrapper>
	)
}

const CouManageWrapper = styled.div`
	height: 100%;
	.noData {
		color: #f41314;
		font-size: 12px;
		margin-top: 20px;
	}
`

export default QuotaLIst
