import React, { useState, useEffect } from 'react'
import { message, Alert } from 'antd'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { refundModuleApi } from '@src/pages/client/api'
import BaseTable from '@src/globalComponents/BaseTable'
import BaseDrawer from '@src/globalComponents/BaseDrawer'
import SearchBar from '@src/pages/client/components/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import styled from 'styled-components'
import { getCurrentRole } from '@factor/biz/bizIndex'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'

interface Props {
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 用于 回显
	 */
	dataInit: any[]
	/**
	 * @description 提交确定操作
	 */
	onOk: (value: any) => void
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
}

const SelectPay = (props: Props) => {
	const [loading, setLoading] = useState(false)
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 }) // current: 4, pageSize: 20
	const [searchParams, setSearchParams] = useState({})
	const [dataSource, setDataSource] = useState<any[]>([])
	const [selectedDataSource, setSelectedDataSource] = useState<any[]>([]) // 表格中选中的数据源
	const { pubKey } = JSON.parse(localStorage.getItem('ext') || '{}')?.user?.company || {} //该用户的公司名称和类型
	const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]) // 表格中选中的row的key
	const { visible, dataInit, onOk, onCancel } = props

	useEffect(() => {
		if (visible) {
			// 设置回显
			if (dataInit.length) {
				setSelectedRowKeys([dataInit[0].transferUuid])
				setSelectedDataSource(dataInit)
			} else {
				setSelectedRowKeys([])
				setSelectedDataSource([])
			}
			getList()
		}
	}, [props.visible])

	useEffect(() => {
		if (visible) getList()
	}, [searchParams, pagination.current, pagination.pageSize])

	// 得到支付记录列表
	const getList = () => {
		setLoading(true)
		refundModuleApi
			.getPayList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				accrualPrinciple: 'RECEIVEABLE', // 接收
				status: 'CONFIRMED', // 支付成功
				limitOperator: false,
				transferType: 'CREATE',
				// Pledge-529
				orgType: (getCurrentRole() || {}).supportOrgType,
				...searchParams,
			})
			.then(
				(res: any) => {
					pagination.total = res['total']
					setPagination({ ...pagination })
					if (res.list && res.list.length) {
						setDataSource([...res['list']])
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => setLoading(false)
			)
	}
	const getColumns = () => {
		const columnsDic = {
			transferNo: {
				width: 200,
			},
			transferFor: {
				render: (text: any, record: any) => {
					return <span>{record.fromCouPubKey === pubKey ? '付款' : '收款'}</span>
				},
			},
		}
		return evenlyTableColunms(getColumnsByPageName('selectPayModel', columnsDic))
	}
	const handleOnChange = (onSelectedRowKeys, onSelectedRows) => {
		console.log('+++++++++++', onSelectedRowKeys, onSelectedRows)
		setSelectedRowKeys(onSelectedRowKeys)
		setSelectedDataSource(onSelectedRows)
	}
	const onSearchPay = (params: { transferNo: string }) => {
		pagination.current = 1
		setSearchParams({
			...params,
		})
	}
	const onResetPay = () => {
		pagination.current = 1
		setSearchParams({})
	}
	const handlePageNumChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const handlePageSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	return (
		<BaseDrawer
			visible={visible}
			title={'选择支付记录'}
			width="800px"
			onClose={onCancel}
			onOk={() => {
				if (selectedDataSource && selectedDataSource.length) {
					onOk(selectedDataSource)
				} else {
					message.warning('请选择支付记录')
				}
			}}
		>
			<Box>
				<Alert
					message="说明："
					type="warning"
					description={
						<>
							<p style={{ margin: '0' }}>1、仅支持核心企业直接开立的支付记录退款，不支持通过供应商流转的融信支付记录退款；</p>
							<p style={{ margin: '0' }}>2、仅支持全额退款，不支持部分退款；支付记录的融信必须全额持有，且未兑付、未质押；</p>
						</>
					}
				></Alert>
				<OperatingArea>
					<SearchBar pageName="selectPayModal" showLabel={true} onSubmit={onSearchPay} onClear={onResetPay} />
				</OperatingArea>
				<BaseTable
					rowKey="transferUuid"
					rowSelection={{
						type: 'radio',
						onChange: handleOnChange,
						selectedRowKeys: selectedRowKeys,
					}}
					columns={getColumns()}
					onPageChange={handlePageNumChange}
					onSizeChange={handlePageSizeChange}
					{...pagination}
					dataSource={dataSource}
					loading={loading}
				/>
			</Box>
		</BaseDrawer>
	)
}

const Box = styled.div`
	/* padding: 10px 24px; */
	.ant-alert {
		margin-bottom: 10px;
	}
	.title {
		padding: 5px 10px;
		display: flex;
		background: #ececec;
		border-radius: 3px;
		margin-bottom: 10px;
		div {
			flex: 1;
		}
	}
	.ant-table-body {
		height: 150px;
	}
`

export default SelectPay
