import React from 'react'
import { Modal, Alert } from 'antd'
import { renderAmountInCent } from '@src/pages/client/config/TableColumnsRender'
import styled from 'styled-components'

interface Props {
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 退款信息对象
	 */
	refundInfoObj: any
	/**
	 * @description 提交确定操作
	 */
	onOk: () => void
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
}

const RefundInfoConfirm = (props: Props) => {
	const { visible, refundInfoObj, onOk, onCancel } = props

	return (
		<Modal
			open={visible}
			title="退款信息确认"
			width="700px"
			onCancel={() => {
				onCancel()
			}}
			onOk={() => {
				onOk()
			}}
		>
			<Box>
				<div className="alert">
					<Alert message="请核对退款信息。收款方确认后，退款无法撤回！" type="warning" showIcon style={{ marginBottom: '10px' }} />
				</div>
				<div className="title">
					<div className="receiveName">收款方：{refundInfoObj.receiveName}</div>
					<div className="refundAmount">退款金额：￥{renderAmountInCent(refundInfoObj.refundAmount)}</div>
				</div>
			</Box>
		</Modal>
	)
}
const Box = styled.div`
	padding: 10px 24px;
	.title {
		padding: 5px 10px;
		background: #ececec;
		border-radius: 3px;
		margin-bottom: 10px;
	}
`

export default RefundInfoConfirm
