import React, { useState, useEffect } from 'react'
import { Button, message, Checkbox, Table, Alert, Form, Input } from 'antd'
import { commonModuleApi, transferModuleApi, refundModuleApi } from '@src/pages/client/api'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { renderAmount, renderAmountInCent } from '@src/pages/client/config/TableColumnsRender'
import SmallTitle from '@src/globalComponents/SmallTitle'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import SelectPay from './components/selectPay'
import SelectCou from '@src/pages/client/components/SelectCou'
import SelectCouCheckbox from '@src/pages/client/components/SelectCouCheckbox'
import RefundInfoConfirm from './components/refundInfoConfirm'
import Feedback from '@src/pages/client/components/feedback'
import PDFViewer from '@src/globalComponents/PDFViewer'
import styled from 'styled-components'
import { history } from '@src/utils/router'
import { couDataOnChain } from '@src/pages/client/biz/bizIndex'
import CompanyNameChangTip from '@src/pages/client/components/companyNameChangTip'

const { TextArea } = Input

function index() {
	const [form] = Form.useForm()
	const [loading, setLoading] = useState(false)
	const [selectPayVisible, setSelectPayVisible] = useState<boolean>(false)
	const [refundInfoConfirmVisible, setRefundInfoConfirmVisible] = useState<boolean>(false)
	const [feedbackVisible, setFeedbackVisible] = useState<boolean>(false)

	const [couDataSource, setCouDataSource] = useState<any[]>([]) // 融信信息表格中的数据
	const [payDataSource, setPayDataSource] = useState<any[]>([]) // 支付记录表格中的数据

	// 退款相关信息
	const [refundUuid, setRefundUuid] = useState<string>('') // 退款申请详情的uuid
	const [refundInfoObj, setRefundInfoObj] = useState<any>({}) // 退款信息确认对象
	const [refundType, setRefundType] = React.useState<'PAYREFUND' | 'CREATEREFUND'>('CREATEREFUND') // 开立还是支付对应的支付记录
	const [refundAmountInCent, setRefundAmountInCent] = useState<number>(0) //退款金额（支付记录中的金额）
	const [createRefundParamsObj, setCreateRefundParamsObj] = useState<any>({}) // 创建退款参数对象
	const { pubKey, orgNameStatus } = JSON.parse(localStorage.getItem('ext') || '{}')?.user?.company || {} //该用户的公司的pubKey
	const [feedbackInfoObj, setFeedbackInfoObj] = useState<any>({
		status: 'success',
		title: '提交成功',
	})
	const [checkRongXinMoney, setCheckRongXinMoney] = useState<boolean>(false)

	useEffect(() => {
		console.log('refundType', refundType)
	}, [refundType])
	// 点击退款信息确认 OK
	const handleRefundInfoConfirmOk = () => {
		// 二次校验金额是否满足关系
		if (refundInfoObj.refundAmount > refundAmountInCent) {
			message.error('已选融信金额不可大于支付记录可退金额')
			return
		}

		setLoading(true)
		if (refundType === 'CREATEREFUND') {
			refundModuleApi.createRefundToC(createRefundParamsObj).then(
				res => {
					console.log('创建退款申请', res)
					setRefundUuid(res)
					setLoading(false)
					setFeedbackVisible(true)
				},
				reason => {
					setLoading(false)
				}
			)
		} else if (refundType === 'PAYREFUND') {
			refundModuleApi.createRefundToS(createRefundParamsObj).then(
				res => {
					console.log('创建退款申请', res)
					setRefundUuid(res)
					setLoading(false)
					setFeedbackVisible(true)
					//融信数据存证上链
					let couNoList = []
					couNoList = couDataSource.map(cou => {
						return cou['changeCouNo']
					})
					couDataOnChain(couNoList)
				},
				reason => {
					setLoading(false)
				}
			)
		}
		setRefundInfoConfirmVisible(false)
	}

	// 点击选择支付记录 OK
	const handleSelectPayOk = async value => {
		console.log('选择支付记录', value)
		let transferCou = value[0].transferCous[0]
		let relCouList = value[0].relCouList
		let refundDetails = { receiveName: value[0]?.fromName }

		if (transferCou.originalNo === transferCou.couNo) {
			console.log('CREATEREFUND')
			setRefundType('CREATEREFUND')
		} else {
			console.log('PAYREFUND')
			setRefundType('PAYREFUND')
			// 获取融信浏转单协议的模版
		}
		const useAmount = await refundModuleApi.getRefoundAmount({ transferUuid: value[0].transferUuid })
		form.resetFields()
		setRefundAmountInCent(Number(value[0].sumTransferAmountInCent - useAmount * 100))
		setPayDataSource(value)
		setSelectPayVisible(false)
		setCouDataSource([])
		setCheckRongXinMoney(false)
		if (relCouList && relCouList.length) {
			relCouList = relCouList.map(item => {
				return { key: item.uuid, ...item }
			})
			refundDetails['refundAmount'] = relCouList.reduce((a, b) => a + b.amount, 0)
			setCheckRongXinMoney(refundDetails['refundAmount'] != transferCou?.amount)
			setCouDataSource(relCouList)
		}
		setRefundInfoObj(refundDetails)
	}

	// const checkRongXinMoney = () => {
	// 	if (!refundInfoObj?.refundAmount) {
	// 		return false
	// 	}
	// 	return refundInfoObj?.refundAmount <= payDataSource[0].transferCous[0]?.amount
	// }

	// 选择支付记录
	const handleSelectPay = () => {
		setSelectPayVisible(true)
	}

	// 处理上传协议
	const handleCommitParams = async () => {
		const resRef = await commonModuleApi.getNo({ numberPrefix: 'FRA' }).catch(err => {
			console.log('err', err)
		})

		const dataList = new Array<any>()
		payDataSource[0].transferCous.forEach(item => {
			const obj: any = {}
			obj.changeCouNo = item.originalNo
			obj.dueDate = item.dueDate
			obj.receiveCouNo = item.couNo
			obj.refundCouAmountInCent = item.amount
			obj.refundCouUuid = item.uuid

			dataList.push(obj)
		})

		const params = {
			creditUuid: couDataSource[0].creditUuid,
			oldTransferUuid: payDataSource[0].transferUuid,
			publishUuid: payDataSource[0]?.transferCous[0]?.publishCouPubKey,
			receiveCompanyPubKey: payDataSource[0].fromCouPubKey,
			refundAmountInCent: refundInfoObj.refundAmount,
			refundCouApplyParaList: dataList,
			refundNo: resRef[0],
			refundReason: form.getFieldValue('comment') || '',
		}

		setCreateRefundParamsObj(params)
		setRefundInfoConfirmVisible(true)
	}

	const getColumns = type => {
		const columnsDic = {
			transferFor: {
				render: (text: any, record: any) => {
					return <span>{record.fromCouPubKey === pubKey ? '付款' : '收款'}</span>
				},
			},
		}
		return evenlyTableColunms(getColumnsByPageName(type, columnsDic))
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<Alert message="请仔细确认所填写信息，本申请通过审核并退还融信后不可再进行回退和修改！" type="warning" showIcon style={{ marginBottom: '10px' }} />
					<CompanyNameChangTip ishidden={orgNameStatus === '1'}></CompanyNameChangTip>
					<div className="payRecord">
						<SmallTitle text="支付记录" />
						<div className="payDetail">
							<div className="title">
								<Button type="primary" onClick={handleSelectPay}>
									选择支付记录
								</Button>
							</div>
							<div className="table">
								<Table rowKey="transferUuid" dataSource={payDataSource} columns={getColumns('selectPay')} pagination={false} />
							</div>
						</div>
					</div>
					<div className="couInfo">
						<SmallTitle text="融信信息" />
						<div className="couDetail">
							<div className="table">
								<Table dataSource={couDataSource} columns={getColumns('selectCouRC')} pagination={false} />
							</div>
						</div>
						{checkRongXinMoney && (
							<Alert
								message="原支付记录的可用融信不足，无法退款；原支付记录的融信必须全额持有，且未兑付、未质押；"
								type="error"
								showIcon
								style={{ marginBottom: '10px' }}
							/>
						)}
					</div>
					<div className="refundInfo">
						<SmallTitle text="退款详情" />
						<div className="refundDetail">
							<div className="title">退款理由:&nbsp;&nbsp;&nbsp;</div>
							<div className="content">
								<Form form={form}>
									<Form.Item name="comment">
										<TextArea showCount maxLength={50} rows={4} placeholder="请输入退款理由，最多输入50个字符" />
									</Form.Item>
								</Form>
							</div>
						</div>
					</div>
					<div className="finally">
						<div className="submit">
							<Button
								type="primary"
								disabled={checkRongXinMoney}
								onClick={() => {
									// 退款信息确认弹窗显示
									if (couDataSource && couDataSource.length) {
										handleCommitParams()
									} else {
										message.warning('请完善信息')
									}
								}}
								loading={loading}
							>
								提交
							</Button>
						</div>
					</div>
				</div>
				<SelectPay
					visible={selectPayVisible}
					dataInit={payDataSource}
					onOk={value => {
						handleSelectPayOk(value)
					}}
					onCancel={() => {
						setSelectPayVisible(false)
					}}
				/>
				<RefundInfoConfirm
					visible={refundInfoConfirmVisible}
					refundInfoObj={refundInfoObj}
					onOk={() => {
						handleRefundInfoConfirmOk()
					}}
					onCancel={() => setRefundInfoConfirmVisible(false)}
				/>
				<Feedback
					onOk={() => {
						history.push('/content/refundManage/sOperatorRefundList')
					}}
					onDetail={() => {
						// 存储返回的uuid，供在融资申请详情中使用
						history.push('/content/refundManage/refundDetail', {
							refundUuid: refundUuid,
						})
					}}
					visible={feedbackVisible}
					status={feedbackInfoObj.status}
					title={feedbackInfoObj.title}
					subTitle={feedbackInfoObj.subTitle}
					btnTxts={['完成', '查看详情']}
				/>
			</LayoutSlot>
		</Box>
	)
}

const Box = styled.div`
	height: 100%;
	.card-wrapper {
		.payRecord {
			.payDetail {
				.title {
					display: flex;
					align-items: center;
					margin: 20px 0 20px 0;
					.detail {
						width: 112px;
					}
				}
				.table {
					margin-bottom: 20px;
				}
			}
		}
		.couInfo {
			.couDetail {
				.title {
					display: flex;
					align-items: center;
					margin: 20px 0 20px 0;
				}
				.table {
					margin: 20px 0 20px 0;
				}
			}
		}
		.refundInfo {
			.refundDetail {
				display: flex;
				margin-left: 12px;
				margin-top: 20px;
				.content {
					width: 800px;
				}
			}
		}
		.finally {
			.agreement {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-bottom: 20px;
			}
			.submit {
				display: flex;
				justify-content: center;
			}
		}
	}
`

export default index
