import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import styled from 'styled-components'
import SmallTitle from '@src/globalComponents/SmallTitle'
import Baseinfo from './components/Baseinfo'
import AuditModal from '../auditRefund/components/auditModal'
import { Table, message, Button } from 'antd'
import { refundModuleApi, commonModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import PDFViewer from '@globalComponents/PDFViewer'
import { history } from '@src/utils/router'
import { businessProtocolMap } from '@src/globalConfig/protocolConfig'
import { couDataOnChain, getCurrentRole } from '@src/pages/client/biz/bizIndex'
import ChainInfoModal from '@src/globalComponents/chainInfoComponents/ChainInfoModal'

function detail() {
	const refundUuid = history.location.state?.refundUuid
	const auditData = history.location.state?.auditData


	const [detailInfo, setDetailInfo] = useState<any>()
	const [visible, setVisible] = useState<boolean>(false)
	const [pdfPath, setPdfPath] = useState<string>('')

	const [oldTransfer, setOldTransfer] = useState<[{ [key: string]: any }]>([{}])
	const [transferCous, setTransferCous] = useState<[{ [key: string]: any }]>([{}])
	//判断是否是 开立退款  or 支付退款
	const [iSCreateRefound, setiSCreateRefound] = useState<boolean>(false)
	const [chainInfoModalVisible, setChainInfoModalVisible] = useState<boolean>(false)
	const [onChainData, setOnChainData] = useState({})
	const [auditVisible, setAuditVisible] = useState(false)
	const [auditType, setAuditType] = useState<any>('agree')
	const [auditTitle, setAuditTitle] = useState<any>('确定通过？')
	const [confirmLoading, setConfirmLoading] = React.useState(false)
	const [isLastAuditorPerson, setIsLastAuditor] = React.useState(false)

	useEffect(() => {
		if (refundUuid) getDetail(refundUuid)
	}, [])

	const getDetail = (paramsRefundUuid: string) => {
		refundModuleApi
			.getRefundDetail({
				refundUuid: paramsRefundUuid,
			})
			.then(
				(res: any) => {
					console.log(res.refundType)

					if (res.refundType === 'CREATE_REFUND') setiSCreateRefound(true)
					else setiSCreateRefound(false)
					//将详情返回数据 拆分到 页面 对应的三个部分对应的数据
					//原支付记录
					const { oldTransferAmountInCent, oldTransferNo, oldTransferPayName, oldTransferReceiveName, oldTransferUuid } = res
					let initTransferCous: any = []
					//融信信息
					res.refundCouList.forEach(item => {
						let data: any = {}
						data.couNo = item.refundCouNo
						data.amount = item.refundCouAmountInCent
						data.publishName = item.refundCouPublishName
						data.creditCompanyName = item.refundCouCreditName
						data.dueDate = item.refundCouDueDate
						data.fileInfo = item.refundCouTransferUrl
						initTransferCous.push(data)
					})
					setTransferCous(initTransferCous)
					setOldTransfer([
						{
							oldTransferAmountInCent,
							oldTransferNo,
							oldTransferPayName,
							oldTransferReceiveName,
							oldTransferUuid,
						},
					])
					setDetailInfo(res)
				},
				error => console.log(error)
			)
	}

	//查看pdf  承诺函
	const viewPdf = (url: string) => {
		if (url) {
			setPdfPath(url)
			setVisible(true)
		}
	}

	const auditTransfer = async (title, type) => {
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let companyUuid: string = ext.user && ext.user.company ? ext.user.company.uuid : ''
		let isLastAuditor: any = await commonModuleApi
			.isLastFlowPerson({
				companyUuid,
				role: getCurrentRole()?.roleCode || '',
				taskId: auditData?.id || '',
			})
			.catch(err => {
				console.log('err', err)
			})
		setIsLastAuditor(isLastAuditor)
		setAuditVisible(true)
		setAuditType(type)
		setAuditTitle(title)
	}

	const forCouDataOnChain = () => {
		refundModuleApi
			.getRefundDetail({
				refundUuid: detailInfo.refundUuid,
			})
			.then(
				(res: any) => {
					if (res && Array.isArray(res.refundCouList)) {
						let couNoList = []
						couNoList = res.refundCouList.map(cou => {
							return cou['refundCouNo']
						})
						couDataOnChain(couNoList)
					}
				},
				error => console.log(error)
			)
	}

	const submitData = async data => {
		setConfirmLoading(true)

		let outcome = auditType === 'reject' ? 'reject' : 'agree'
		let param: any = {
			businessKey: auditData?.businessKey,
			taskId: auditData?.id,
			comment: data.comment,
			outcome,
		}

		if (auditType === 'agreeButContract') {
			param.jsonParamStr = JSON.stringify({
				contractUuid: data.contractUuid,
			})
		}

		commonModuleApi
			.doTask(param)
			.then(item => {
				setAuditVisible(false)
				setConfirmLoading(false)
				if (item.returnCode === 17000) {
					message.success('处理成功')
					let ext = JSON.parse(localStorage.getItem('ext') || '{}')
					let companyType = ext?.user?.loginCompanyType
					//如果是最后一个审核者,cou数据要存证上链
					if (isLastAuditorPerson) {
						forCouDataOnChain()
					}
					history.push('/content/refundManage/auditRefund')
				} else {
					message.error(item.returnDesc)
				}
			})
			.catch(err => {
				setConfirmLoading(false)
			})
	}



	const getTransferColumns = type => {
		const columnsDic: any = {
			fromName: {
				dataIndex: 'oldTransferPayName',
			},
			toCompanyName: {
				dataIndex: 'oldTransferReceiveName',
			},
		}
		return getColumnsByPageName(type, columnsDic)
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<SmallTitle text="退款详情" />
					{detailInfo && <Baseinfo data={detailInfo} iSCreateRefound={iSCreateRefound} isDetail={false} />}
					<SmallTitle text="原支付记录" />
					{oldTransfer.length > 0 && (
						<Table rowKey="uuid" style={{ marginBottom: '20px' }} columns={getTransferColumns('oldTransfer')} dataSource={oldTransfer} pagination={false} />
					)}
					<SmallTitle text="融信信息" />
					{transferCous.length > 0 && (
						<Table
							rowKey="couNo"
							style={{ marginBottom: '20px' }}
							columns={getTransferColumns('couTransferInDetail')}
							dataSource={transferCous}
							pagination={false}
						/>
					)}
					<PDFViewer title="" visible={visible} onCancel={() => setVisible(false)} pdfUrl={pdfPath} />
				</div>
			</LayoutSlot>
			<ChainInfoModal visible={chainInfoModalVisible} onCancel={() => setChainInfoModalVisible(false)} chainData={onChainData} />
			<AuditModal
				title={auditTitle}
				visible={auditVisible}
				onCancel={() => setAuditVisible(false)}
				onSubmit={submitData}
				setConfirmLoading={setConfirmLoading}
				confirmLoading={confirmLoading}
				type={auditType}
			/>
			{auditData && <div className="options_btn">
				<Button type="primary" danger onClick={() => auditTransfer('确定拒绝？', 'reject')} style={{ marginRight: 10 }}>
					拒绝
				</Button>
				<Button type="primary" onClick={() => auditTransfer('确定通过？', auditData.name === 'SOperator' ? 'agreeButContract' : 'agree')}>
					通过
				</Button>
			</div>}
		</Box>
	)
}

const Box = styled.div`
	.options_btn {
		text-align: center;
		span {
			display: inline-block;
			margin: 0 20px;
		}
	}
`
export default detail
