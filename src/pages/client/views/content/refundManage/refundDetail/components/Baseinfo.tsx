import React, { useState } from 'react'
import { Descriptions, Tooltip } from 'antd'
import BloackInfo from './BlockInfo'
import { getRefoundStatus, renderAmountInCent, renderDate_, renderTooltip } from '@src/pages/client/config/TableColumnsRender'
import { transferModuleApi, refundModule<PERSON>pi } from '@src/pages/client/api'

const Baseinfo = (props: { data: any; iSCreateRefound: boolean; isDetail?: boolean }) => {
	const { data, iSCreateRefound, isDetail = true } = props
	const [BlockVisible, setBlockVisible] = useState<boolean>(false)
	const [blockData, setBlockData] = useState<any>()

	const getCheckChainInfoApi = () => {
		if (iSCreateRefound) return refundModuleApi.getCreateRefoundChainInfo({ refundUuid: data.refundUuid })
		else return transferModuleApi.getChainInfo({ transferUuid: data.refundTransferUuid })
	}

	const getBlockChainInfo = () => {
		getCheckChainInfoApi().then(res => {
			let getData: any = {}
			//上述接口因为开立与支付的退款链上明细接口不一样，所以我在这里再次梳理一下数据结构
			if (iSCreateRefound) {
				getData.para = res.refundAmountInCent
				getData.transactionHash = res.transactionHash
				getData.blockNumber = res.blockNumber
				getData.createTime = res.chainSuccessTime

				getData.fromPubKey = res.fromPubKey
			} else {
				getData.para = res.onProcess.para
				getData.transactionHash = res.onProcess.transactionHash
				getData.blockNumber = res.onProcess.blockNumber
				getData.createTime = res.onProcess.createTime

				getData.fromPubKey = res.fromPubKey
				getData.toPubKey = res.toPubKey
			}
			setBlockData(getData)
			setBlockVisible(true)
		})
	}

	return (
		<div style={{ marginBottom: '20px' }}>
			<Descriptions bordered column={2}>
				<Descriptions.Item label="退款申请编号">
					<Tooltip title={data.transferNo}>{data.refundNo}</Tooltip>
				</Descriptions.Item>
				<Descriptions.Item label="状态">{getRefoundStatus(data.status)}</Descriptions.Item>
				<Descriptions.Item label="退款金额(¥)">{renderAmountInCent(data.amountInCent)}</Descriptions.Item>
				<Descriptions.Item label="退款方" contentStyle={{ maxWidth: '220px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
					{renderTooltip(data.refundCompanyFullName)}
				</Descriptions.Item>
				<Descriptions.Item label="收款方" contentStyle={{ maxWidth: '220px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
					{renderTooltip(data.receiveCompanyFullName)}
				</Descriptions.Item>
				<Descriptions.Item label="创建日期">{renderDate_(data.createTime)}</Descriptions.Item>
				{isDetail ? (
					<Descriptions.Item label="链上明细">
						<span className="link" onClick={getBlockChainInfo}>
							查看链上明细
						</span>
					</Descriptions.Item>
				) : (
					''
				)}
				<Descriptions.Item label="处理意见" contentStyle={{ maxWidth: '220px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
					{renderTooltip(data.auditReason ? data.auditReason : '- -')}
				</Descriptions.Item>
				<Descriptions.Item label="退款理由">{data.refundReason ? data.refundReason : '- -'}</Descriptions.Item>
			</Descriptions>
			<BloackInfo
				visible={BlockVisible}
				data={blockData}
				iSCreateRefound={iSCreateRefound}
				onOk={() => {
					return
				}}
				onCancel={() => setBlockVisible(false)}
			/>
		</div>
	)
}

export default Baseinfo
