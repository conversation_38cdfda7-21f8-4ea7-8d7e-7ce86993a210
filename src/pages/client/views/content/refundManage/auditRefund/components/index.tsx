import React, { useEffect, useState } from 'react'
import { message, <PERSON><PERSON><PERSON>, Modal, Button, Row, Col } from 'antd'
import { ExclamationCircleFilled } from '@ant-design/icons'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { deepExtend } from '@utils/util'
import { commonModuleApi, refundModuleApi } from '@src/pages/client/api'
import AuditModal from './auditModal'
import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import styled from 'styled-components'
import { history } from '@src/utils/router'
import { getStorage, couDataOnChain, getCurrentRole } from '@src/pages/client/biz/bizIndex'
function AuditList() {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [loading, setLoading] = useState(false)
	const [auditVisible, setAuditVisible] = useState(false)
	const [alertVisible, setAlertVisible] = useState(false)
	const [auditType, setAuditType] = useState<any>('agree')
	const [auditTitle, setAuditTitle] = useState<any>('确定通过？')
	const [confirmLoading, setConfirmLoading] = React.useState(false)
	const [refundRowData, setRefundRowData] = React.useState<any>({})
	//判断是否是最后一个审核者
	const [isLastAuditorPerson, setIsLastAuditor] = React.useState(false)

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize])

	// 得到退款申请列表
	const getList = () => {
		setLoading(true)
		const storageRoleId = getStorage('roleId')
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		const params = {
			pageNum: pagination.current,
			pageSize: pagination.pageSize,
			processDefinitionKeyList: ['pledgeRefundCreate'],
			roleNameList: getCurrentRole()?.roleCode ? [getCurrentRole()?.roleCode] : [],
		}

		commonModuleApi.loadCurrentUserTask(params).then(
			(value: any) => {
				let res = deepExtend({}, value)
				pagination.total = res['count']
				if (res && res.data) {
					let filterData: Array<any> = []
					filterData = res['data'].map(item => {
						//数据结构调整
						let auditData = {
							assignee: item.assignee,
							businessKey: item.businessKey,
							createTime: item.createTime,
							id: item.id,
							name: item.name,
						}
						return { ...item.bizMap, ...{ auditData } }
					})
					console.log('filterData', filterData)
					setDataSource(filterData)
					setLoading(false)
				} else {
					setDataSource([])
					setLoading(false)
				}
			},
			err => {
				pagination.total = 0
				setDataSource([])
				setLoading(false)
			}
		)
	}
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}
	const handlePageChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const onOperCallback = async (str: string, data: any, type?: string, title?: string) => {
		if (str === 'refundNo') {
			// 存储退款申请详情，防止刷新数据丢失
			history.push('/content/refundManage/refundDetail', {
				refundUuid: data.refundUuid,
				auditData: data.auditData
			})
		} else if (str === 'auditRefund') {
			let roleList = getStorage('roleList') || []
			let ext = JSON.parse(localStorage.getItem('ext') || '{}')
			let companyUuid: string = ext.user && ext.user.company ? ext.user.company.uuid : ''
			let isLastAuditor: any = await commonModuleApi
				.isLastFlowPerson({
					companyUuid,
					role: getCurrentRole()?.roleCode || '',
					taskId: data.auditData.id,
				})
				.catch(err => {
					console.log('err', err)
				})
			setIsLastAuditor(isLastAuditor)
			setRefundRowData(data)
			setAuditVisible(true)
			setAuditType(type)
			setAuditTitle(title)
		}
	}
	const getColumns = () => {
		const pageName = 'auditRefundList'
		const columnsDic = {
			oldPayNo: {
				width: 200,
			},
			refundNo: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									onOperCallback('refundNo', record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			operation: {
				render: (text: any, record: any) => {
					// console.log('退款申请记录中的record', record)
					// 判断是否是供应商审核员，若是则需要选择合同
					let selectContract = record.auditData.name === 'SOperator'
					return (
						<div>
							<span className="link" onClick={() => onOperCallback('auditRefund', record, selectContract ? 'agreeButContract' : 'agree', '确定通过？')}>
								通过
							</span>
							<span className="red" onClick={() => onOperCallback('auditRefund', record, 'reject', '确定拒绝？')}>
								拒绝
							</span>
						</div>
					)
				},
			},
		}
		return getColumnsByPageName(pageName, columnsDic)
	}

	const submitData = async data => {
		setConfirmLoading(true)
		console.log('refundRowData', refundRowData)
		// 处理 agreeButContract 类型
		let outcome = auditType === 'reject' ? 'reject' : 'agree'
		let param: any = {
			businessKey: refundRowData.auditData.businessKey,
			taskId: refundRowData.auditData.id,
			comment: data.comment,
			outcome,
		}

		if (auditType === 'agreeButContract') {
			param.jsonParamStr = JSON.stringify({
				contractUuid: data.contractUuid,
			})
		}
		commonModuleApi
			.doTask(param)
			.then(item => {
				getList()
				setAuditVisible(false)
				setConfirmLoading(false)
				if (item.returnCode === 17000) {
					message.success('处理成功')
					let ext = JSON.parse(localStorage.getItem('ext') || '{}')
					let companyType = ext?.user?.loginCompanyType
					//如果是最后一个审核者,cou数据要存证上链
					if (isLastAuditorPerson) {
						forCouDataOnChain()
					}
				} else {
					message.error(item.returnDesc)
				}
			})
			.catch(err => {
				setConfirmLoading(false)
			})
	}

	const forCouDataOnChain = () => {
		refundModuleApi
			.getRefundDetail({
				refundUuid: refundRowData.refundUuid,
			})
			.then(
				(res: any) => {
					if (res && Array.isArray(res.refundCouList)) {
						let couNoList = []
						couNoList = res.refundCouList.map(cou => {
							return cou['refundCouNo']
						})
						couDataOnChain(couNoList)
					}
				},
				error => console.log(error)
			)
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<BaseTable
						onPageChange={handlePageChange}
						onSizeChange={handleSizeChange}
						columns={getColumns()}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						rowKey="id"
					/>
				</div>
			</LayoutSlot>
			<AuditModal
				title={auditTitle}
				visible={auditVisible}
				onCancel={() => setAuditVisible(false)}
				onSubmit={submitData}
				setConfirmLoading={setConfirmLoading}
				confirmLoading={confirmLoading}
				type={auditType}
			/>

			<Modal className="warnmodal" open={alertVisible} footer={null} closable={false}>
				<Row>
					<Col span={2}>
						<ExclamationCircleFilled className="warnicon" style={{ fontSize: '26px', color: '#EFB041' }} />
					</Col>
					<Col span={22}>
						<h4 className="headtxt">您需要先激活此金融机构，才可以进行业务操作</h4>
						<p>请联系企业管理员登录系统，激活此金融机构</p>
					</Col>
				</Row>

				<Row style={{ justifyContent: 'center' }}>
					<Button
						type="primary"
						onClick={() => {
							setAlertVisible(false)
						}}
					>
						我知道了
					</Button>
				</Row>
			</Modal>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
`

export default AuditList
