import React, { useState, useEffect } from 'react'
import { Modal, Form, Input, message, Alert } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { refundModuleApi } from '@src/pages/client/api'
import BaseTable from '@src/globalComponents/BaseTable'
import BaseDrawer from '@src/globalComponents/BaseDrawer'
import styled from 'styled-components'

const { TextArea } = Input
export interface PropsStruct {
	/**
	 * @description 标题
	 */
	title: string
	/**
	 * @description 显示/隐藏
	 */
	visible: boolean
	/**
	 * @description 点击关闭事件
	 */
	onCancel: () => void
	/**
	 * @description 提交事件
	 */
	onSubmit: (values) => void
	/**
	 * @description 提交时加载
	 */
	confirmLoading: boolean

	setConfirmLoading: (loading: boolean) => void
	/**
	 * @description 通过还是拒绝
	 */
	type: string
}
export default function AuditModalPage(props: PropsStruct) {
	const [loading, setLoading] = useState(false)
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const { visible, onCancel, title, onSubmit, confirmLoading, setConfirmLoading, type } = props
	const [selectedData, setSelectedData] = useState<any>()
	const [form] = Form.useForm()

	useEffect(() => {
		if (visible && type === 'agreeButContract') {
			console.log('type', type)
			getList()
		}
	}, [visible, pagination, type])

	useEffect(() => {
		if (!confirmLoading) {
			form.resetFields()
		}
	}, [confirmLoading])

	// 得到合同列表
	const getList = () => {
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let uuid = ext?.user?.company?.uuid
		setLoading(true)
		refundModuleApi
			.getList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				companySellerUuid: uuid,
			})
			.then(
				(res: any) => {
					pagination.total = res['total']
					if (res.list && res.list.length > 0) {
						setDataSource([...res['list']])
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => {
					message.error(err)
					setLoading(false)
				}
			)
	}

	const submitData = () => {
		setConfirmLoading(true)
		form.validateFields().then(
			res => {
				if (type === 'agreeButContract') {
					if (selectedData && selectedData.uuid) {
						res.contractUuid = selectedData.uuid
						onSubmit(res)
					} else {
						message.warning('请选择合同')
					}
				} else {
					console.log('res', res)
					onSubmit(res)
				}
			},
			err => {
				console.log(err)
			}
		)
	}

	const handlePageChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	const getColumns = () => {
		const pageName = 'auditRefundContractList'
		const columnsDic = {}
		return getColumnsByPageName(pageName, columnsDic)
	}
	//选择合同 回调
	const handleSelected = record => {
		console.log('record', record)
		setSelectedData(record)
	}

	return (
		<Box>
			{type === 'agreeButContract' ? (
				<BaseDrawer
					title={title}
					open={visible}
					onClose={() => {
						onCancel()
						form.resetFields()
					}}
					onOk={submitData}
					width={'700px'}
					className={'wx_Modal wx_Modal_confirm wx_contract'}
				>
					<div>
						<Alert message="因业务需求，请先选择一份合同关联您的退回融信，确定通过后无法撤销。" type="warning" showIcon style={{ marginBottom: '10px' }} />
						<BaseTable
							rowKey="id"
							onPageChange={handlePageChange}
							onSizeChange={handleSizeChange}
							columns={getColumns()}
							loading={loading}
							dataSource={dataSource}
							{...pagination}
							rowSelection={{
								type: 'radio',
								onSelect: handleSelected,
							}}
							locale={{
								emptyText: '暂无数据，请先上传一份您为卖方的合同',
							}}
						/>
					</div>
				</BaseDrawer>
			) : (
				<Modal
					title={title}
					open={visible}
					onCancel={() => {
						onCancel()
						form.resetFields()
					}}
					onOk={submitData}
					width={type === 'agreeButContract' ? '680px' : '520px'}
					confirmLoading={confirmLoading}
					className={type === 'agreeButContract' ? 'wx_Modal wx_Modal_confirm wx_contract' : 'wx_Modal wx_Modal_confirm'}
				>
					<div className="content wx_Modal_Main">
						{type === 'reject' ? (
							<Form form={form}>
								<Form.Item name="comment">
									<TextArea showCount maxLength={50} rows={4} placeholder="请描述拒绝理由，最多输入50个字符" />
								</Form.Item>
							</Form>
						) : type === 'agree' ? (
							<p
								className="wx_Modal_Main_tips"
								style={{
									margin: '24px 50px 52px 50px',
									textAlign: 'center',
									fontSize: '16px',
								}}
							>
								<ExclamationCircleOutlined style={{ color: '#1785ff', marginRight: '5px' }} /> 确定通过后无法撤销
							</p>
						) : null}
					</div>
				</Modal>
			)}
		</Box>
	)
}

const Box = styled.div``
