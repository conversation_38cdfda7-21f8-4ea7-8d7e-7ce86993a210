import React, { useState } from 'react'
import { invoicingManageModuleApi } from '@src/pages/client/api'
import SearchTable from '@globalComponents/new-widget/wx-table/searchTable'
import { getTabTit, invalidVal } from '@globalBiz/gBiz'
import { DatePicker, Tooltip } from 'antd'
import { renderAmount, renderData } from '@src/pages/client/config/TableColumnsRender'
import FormItem from 'antd/es/form/FormItem'
import styled from 'styled-components'
const RangePicker = DatePicker.RangePicker
import DetailModal from '@clientComponents/detailModal'
import { stampToTime } from '@src/utils/timeFilter'

const MyInvoiceData = () => {
	const [detailVisible, setDetailVisible] = useState<boolean>(false)
	const [invoiceData, setInvoiceData] = useState<any>({})
	const columns = [
		getTabTit(['发票号码', 'invoiceNumber'], (text, record) => {
			if (text === undefined || text === null) {
				return invalidVal
			}
			return (
				<Tooltip title={text}>
					<span
						className="link"
						title={text}
						onClick={() => {
							setInvoiceData(record)
							setDetailVisible(true)
						}}
					>
						{text}
					</span>
				</Tooltip>
			)
		}),
		getTabTit(['买方名称', 'buyerName', null, true]),
		getTabTit(['卖方名称', 'supplierName', null, true]),
		getTabTit(['含税金额（￥）', 'pretaxAmount'], renderAmount),
		getTabTit(['开票日期', 'invoiceDate'], renderData),
		getTabTit(['开票业务', 'linkBiz']),
	]
	const tableConfig = {
		table: {
			columns,
			requestUrl: invoicingManageModuleApi.queryMyInvoice,
		},
		searchBar: {
			transform: param => {
				if (param.invoiceDate && param.invoiceDate.length > 1) {
					param.invoiceDateFrom = stampToTime(param.invoiceDate[0], 6)
					param.invoiceDateTo = stampToTime(param.invoiceDate[1], 6)
					delete param.invoiceDate
				}
				return param
			},
			formItems: (
				<>
					<FormItem name="invoiceDate" label="" className="invoiceDate">
						<RangePicker
							getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
							placeholder={['开票日期（开始)', '开票日期（结束)']}
							style={{ width: 290 }}
							format="YYYY-MM-DD"
						/>
					</FormItem>
				</>
			),
		},
	}
	return (
		<Box>
			<div className="card-wrapper">
				<SearchTable {...tableConfig}></SearchTable>
			</div>
			<DetailModal title="查看发票" pageName="invoicing" onCancel={() => setDetailVisible(false)} visible={detailVisible} dataSource={invoiceData} />
		</Box>
	)
}
const Box = styled.div`
	.form .ant-form-item.invoiceDate {
		width: 300px;
	}
`

export default MyInvoiceData
