import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import { Alert, Button, Descriptions } from 'antd'
import SmallTitle from '@src/globalComponents/SmallTitle'
import styled from 'styled-components'
import { useForm } from 'antd/lib/form/Form'
import EditInvoiceHeaderModal from './components/editInvoiceHeaderModal'
import { InvoiceHeaderInfoIP } from '@src/@types/invoice'
import { invoicingManageModuleApi } from '@factor/api'

const InvoiceHeader = () => {
	const [invoiceHeaderInfo, setInvoiceHeaderInfo] = useState<InvoiceHeaderInfoIP | null>()
	const [editModalVisible, setEditModalVisible] = useState(false)
	const [form] = useForm()
	useEffect(() => {
		getInvoiceHeaderInfo()
	}, [])

	/**
	 * 获取发票抬头信息
	 */
	const getInvoiceHeaderInfo = async () => {
		let res = await invoicingManageModuleApi.queryInvoicingInfo().catch(err => {
			console.log(err)
		})
		if (res) {
			setInvoiceHeaderInfo(res)
		}
	}

	/**
	 * 打开编辑弹窗,回显值
	 */
	const onOpenEditModal = () => {
		form.setFieldsValue(invoiceHeaderInfo)
		setEditModalVisible(true)
	}

	/**
	 * 关闭编辑弹窗
	 */
	const onCloseInvoiceHeaderModal = (isUpdate: boolean = false) => {
		if (isUpdate) {
			getInvoiceHeaderInfo()
		}
		setEditModalVisible(false)
	}

	return (
		<Styled>
			<LayoutSlot>
				<div className="card-wrapper">
					<Alert
						style={{ marginBottom: '20px' }}
						message={<div style={{ fontSize: '14px' }}>说明：请维护您的开票信息，并确保与税务局数据一致，若开票信息有变更，请及时更新</div>}
						description={
							<div>
								<div style={{ fontSize: '14px' }}>1、此发票信息用于平台运营方为您开具基于平台服务费等相关的发票</div>
								<div style={{ fontSize: '14px' }}>2、邮寄信息用于平台为您寄送纸质发票、协议文件等</div>
							</div>
						}
						type="info"
						showIcon
					/>
					<div className="operate">
						<Button type="primary" onClick={onOpenEditModal}>
							编辑
						</Button>
					</div>
					<div>
						<SmallTitle>开票内容</SmallTitle>
						<Descriptions column={2} labelStyle={{ width: '100px' }} bordered={true} contentStyle={{ width: '30%' }}>
							<Descriptions.Item label="发票抬头">{invoiceHeaderInfo?.companyName}</Descriptions.Item>
							<Descriptions.Item label="纳税人识别码">{invoiceHeaderInfo?.socialCreditCode}</Descriptions.Item>
							<Descriptions.Item label="公司地址">{invoiceHeaderInfo?.address}</Descriptions.Item>
							<Descriptions.Item label="公司电话">{invoiceHeaderInfo?.phone}</Descriptions.Item>
							<Descriptions.Item label="开户银行">{invoiceHeaderInfo?.bankName}</Descriptions.Item>
							<Descriptions.Item label="开户银行账号">{invoiceHeaderInfo?.bankNo}</Descriptions.Item>
						</Descriptions>
						<SmallTitle>邮寄信息</SmallTitle>
						<Descriptions column={2} labelStyle={{ width: '100px' }} bordered={true} contentStyle={{ width: '30%' }}>
							<Descriptions.Item label="收件人姓名">{invoiceHeaderInfo?.recipientName}</Descriptions.Item>
							<Descriptions.Item label="收件人电话">{invoiceHeaderInfo?.recipientPhone}</Descriptions.Item>
							<Descriptions.Item label="收件人地址">{invoiceHeaderInfo?.recipientAddress}</Descriptions.Item>
							<Descriptions.Item label="">{}</Descriptions.Item>
						</Descriptions>
						<SmallTitle>其他</SmallTitle>
						<Descriptions column={2} labelStyle={{ width: '100px' }} bordered={true} contentStyle={{ width: '30%' }}>
							<Descriptions.Item label="接收邮箱">{invoiceHeaderInfo?.recipientEmail}</Descriptions.Item>
							<Descriptions.Item label="">{}</Descriptions.Item>
						</Descriptions>
					</div>
					<EditInvoiceHeaderModal visible={editModalVisible} data={invoiceHeaderInfo} onCancel={onCloseInvoiceHeaderModal}></EditInvoiceHeaderModal>
				</div>
			</LayoutSlot>
		</Styled>
	)
}

const Styled = styled.div`
	.operate {
		margin-bottom: 20px;
	}
	.ant-descriptions {
		padding: 20px;
	}
`
export default InvoiceHeader
