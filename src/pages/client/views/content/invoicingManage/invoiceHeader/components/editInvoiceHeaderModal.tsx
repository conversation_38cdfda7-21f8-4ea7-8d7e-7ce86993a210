import styled from 'styled-components'
import React, { ReactElement, useEffect } from 'react'
import { Modal, Form, Input, message } from 'antd'
import SmallTitle from '@src/globalComponents/SmallTitle'
import { InvoiceHeaderInfoIP } from '@src/@types/invoice'
import { invoicingManageModuleApi } from '@factor/api'

interface Props {
	visible: boolean
	onCancel: (isUpdate: boolean) => void
	data: InvoiceHeaderInfoIP | Object
}

function EditInvoiceHeaderModal({ visible, data, onCancel }: Props): ReactElement {
	const [form] = Form.useForm()
	const [loading, setLoading] = React.useState<boolean>(false)
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')

	useEffect(() => {
		if (visible) {
			let invoiceHeaderInfo = Object.assign({}, { companyName: ext?.user?.company?.companyName, socialCreditCode: ext?.user?.company?.socialCreditCode }, data)
			form.setFieldsValue(invoiceHeaderInfo)
		}
	}, [visible])

	//提交表单（新增 && 编辑）前置操作
	const handleSubmit = () => {
		setLoading(true)
		form.validateFields().then(
			async values => {
				let res = await invoicingManageModuleApi.modifyInvoiceHeader(values).catch(err => {
					console.log(err)
				})
				if (res) {
					message.success('提交成功')
				}
				setLoading(false)
				if (res) {
					onCancel(true)
				}
			},
			err => {
				console.log(err, 'err')
				setLoading(false)
			}
		)
	}
	/**
	 * 弹窗关闭事件
	 */
	const onCancelModal = () => {
		onCancel && onCancel(false)
	}

	return (
		<Modal
			onOk={handleSubmit}
			bodyStyle={{ paddingBottom: 0 }}
			style={{ top: 30 }}
			width={860}
			open={visible}
			title="编辑开票信息"
			onCancel={onCancelModal}
			confirmLoading={loading}
		>
			<ModalBox>
				<Form form={form} className="form">
					<SmallTitle text="开票内容"></SmallTitle>
					<ul className="form-list">
						<Form.Item className="form-item" label="发票抬头" name="companyName" rules={[{ required: true, message: '企业全称不能为空', whitespace: true }]}>
							<Input disabled={true} />
						</Form.Item>
						<Form.Item
							className="form-item"
							label="纳税人识别码"
							name="socialCreditCode"
							rules={[{ required: true, message: '纳税人识别码不能为空', whitespace: true }]}
						>
							<Input disabled={true} />
						</Form.Item>
						<Form.Item className="form-item" label="公司地址" name="address" rules={[{ required: true, message: '请输入公司地址', whitespace: true }]}>
							<Input placeholder="请输入" maxLength={64} />
						</Form.Item>
						<Form.Item className="form-item" label="公司电话" name="phone" rules={[{ required: true, message: '请输入公司电话', whitespace: true }]}>
							<Input placeholder="请输入" maxLength={20} />
						</Form.Item>
						<Form.Item
							className="form-item"
							label="开户银行"
							name="bankName"
							rules={[
								{
									required: true,
									message: '请输入开户行',
									whitespace: true,
								},
							]}
						>
							<Input placeholder="请输入" maxLength={64} />
						</Form.Item>
						<Form.Item
							className="form-item"
							label="银行账号"
							name="bankNo"
							rules={[
								{
									required: true,
									message: '请输入账号，由数字组成',
									pattern: /^[0-9]*$/,
									whitespace: true,
								},
							]}
						>
							<Input placeholder="请输入" maxLength={32} />
						</Form.Item>
					</ul>
					<SmallTitle text="邮寄信息"></SmallTitle>
					<ul className="form-list">
						<Form.Item
							className="form-item"
							label="收件人姓名"
							name="recipientName"
							rules={[{ required: true, message: '请输入收件人姓名', whitespace: true }]}
						>
							<Input placeholder="请输入" maxLength={20} />
						</Form.Item>
						<Form.Item
							className="form-item"
							label="收件人电话"
							name="recipientPhone"
							rules={[{ required: true, message: '请输入收件人电话', whitespace: true }]}
						>
							<Input placeholder="请输入" maxLength={20} />
						</Form.Item>
						<Form.Item
							className="form-item"
							label="收件人地址"
							name="recipientAddress"
							extra="该地址用于邮寄发票等信息，请准确填写包含省、市的详细地址"
							rules={[{ required: true, message: '请输入收件人地址', whitespace: true }]}
						>
							<Input placeholder="请输入详细地址" maxLength={64} />
						</Form.Item>
					</ul>
					<SmallTitle text="其他"></SmallTitle>
					<ul className="form-list">
						<Form.Item
							className="form-item"
							label="接收邮箱"
							name="recipientEmail"
							extra="该邮箱用于接收电子发票，请填写正确的邮箱"
							rules={[
								{
									required: true,
									message: '请输入电子邮箱',
									whitespace: true,
								},
								{
									type: 'email',
									message: '请输入电子邮箱，并确认格式正确',
									whitespace: true,
								},
							]}
						>
							<Input placeholder="请输入" maxLength={64} />
						</Form.Item>
					</ul>
				</Form>
			</ModalBox>
		</Modal>
	)
}

const ModalBox = styled.div`
	padding: 20px;
	.form-list .form-item:last-child {
		width: 50%;
	}
	.ant-form-item-extra {
		white-space: nowrap;
	}
`

export default EditInvoiceHeaderModal
