import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import styled from 'styled-components'
import SearchBar from '@src/pages/client/components/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import { Button, message, Tooltip } from 'antd'
import { transferModuleApi, commonModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import { formatTableData } from '@utils/format'
import { cashCouStatusList, pledgeStatusList } from '@factor/config/cou/couAssetsConfig'
import { stampToTime, timeToStamp } from '@utils/timeFilter'
import CouDeatail from './detail/CouDetailModal'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import ChainInfoModal from '@src/globalComponents/chainInfoComponents/ChainInfoModal'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import { useLocation } from 'react-router-dom'

const options = {
	cashStatusList: cashCouStatusList,
	pledgeStatusList,
}

interface HolderDetailProps {
	limitOperator: boolean
}

const HolderDetail = (props: HolderDetailProps) => {
	let { limitOperator } = props
	const [holderPagination, setHolderPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [holderTableDataSource, setHolderTableDataSource] = useState<any[]>([])
	const [holderTableLoading, setHolderTableLoading] = useState(false)
	const [exportLoading, setExportLoading] = React.useState(false)
	const publishPubKey = JSON.parse(localStorage.getItem('ext') || '{}')?.user?.company?.pubKey
	const [CouDeatailVisible, setCouDeatailVisible] = React.useState<boolean>(false)
	const [detailData, setDetilaData] = React.useState<any>(null)
	const [chainInfoModalVisible, setChainInfoModalVisible] = useState<boolean>(false)
	const [onChainData, setOnChainData] = useState({})
	const location = useLocation()
	const state = location?.state as any
	const cashStatusList = state?.cashStatusList
	const pledgeStatus = state?.pledgeStatus
	const [searchParams, setSearchParams] = useState<any>({ cashStatusList, pledgeStatus })

	useEffect(() => {
		getOriginanlList()
	}, [holderPagination.current, holderPagination.pageSize, searchParams])
	const getOriginanlList = () => {
		setHolderTableLoading(true)
		transferModuleApi
			.getHolderDetailList({
				pageNum: holderPagination.current,
				pageSize: holderPagination.pageSize,
				publishPubKey,
				...searchParams,
				limitOperator: limitOperator,
			})
			.then(result => {
				if (result) {
					holderPagination.total = result['total']
					if (result.list) {
						setHolderTableDataSource(formatTableData.addKey(result['list']))
					}
				}
				setHolderTableLoading(false)
			})
			.catch(() => {
				setHolderTableLoading(false)
			})
	}

	//search 提交 事件
	const onSubmit = (params: { catchDate: any; cashStatusList: any; couNo: [string] }) => {
		holderPagination.current = 1
		if (params['cashStatusList']) {
			params['cashStatusList'] =
				Object.prototype.toString.call(params['cashStatusList']) === '[object Array]'
					? params['cashStatusList']
					: params['cashStatusList'].includes(',')
					? params['cashStatusList'].split(',')
					: [params['cashStatusList']]
		}
		let endDate, startDate
		if (params && params.catchDate && params.catchDate.length === 2) {
			endDate = timeToStamp(params.catchDate[1], 'end')
			startDate = params.catchDate[0]
			delete params.catchDate
		}
		setSearchParams({
			...params,
			startDate,
			endDate,
		})
	}
	//Search 重置
	const onClear = () => {
		holderPagination.current = 1
		setSearchParams({})
	}
	const onOperCallback = value => {
		setDetilaData(value)
		setCouDeatailVisible(true)
	}
	const getHolderColumns = () => {
		const columnsDic = {
			couNo: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text} className="link">
							<span
								onClick={() => {
									onOperCallback(record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			originalCouNo: {
				dataIndex: 'originalCouNo',
				title: '原始融信编号',
				width: 200,
				ellipsis: {
					showTitle: false,
				},
				render: (text: any, record: any) => {
					return <Tooltip title={text}>{text}</Tooltip>
				},
			},
			couAmountInCent: {
				title: '金额(￥)',
			},
			onChainCertificate: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => showCertificate(record)}>
							查看证书
						</span>
					)
				},
			},
		}

		let colns = getColumnsByPageName('holderDetail', columnsDic)
		// 表格列长度的简写形式
		let columnIndexLengthMap = {
			0: 10,
			1: 8,
			2: 14,
			3: 14,
			4: 9,
			5: 8,
			6: 8,
			7: 10,
			8: 8,
			9: 5,
		}
		colns = evenlyTableColunms(colns, columnIndexLengthMap)
		return colns
	}

	const showCertificate = record => {
		commonModuleApi
			.queryCouProof({ couNo: record.couNo })
			.then(data => {
				if (data && data.status === 'DONE') {
					setOnChainData(data)
					setChainInfoModalVisible(true)
				} else {
					message.warning('证书未生成，完成审批且上链后生成证书')
				}
			})
			.catch(err => {
				console.log(err)
			})
	}

	function handleHolderTableChange(current: number) {
		holderPagination.current = current
		setHolderPagination({ ...holderPagination })
	}
	function handleHolderTableSizeChange(size: number) {
		holderPagination.current = 1
		holderPagination.pageSize = size
		setHolderPagination({ ...holderPagination })
	}
	//导出融信信息
	const holderExportCouCsv = async () => {
		setExportLoading(true)
		let serverTime = await commonModuleApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`
		transferModuleApi
			.exportHolderDetailList({
				pageSize: 90000000,
				publishPubKey,
				limitOperator: limitOperator,
				pageNum: 1,
				...searchParams,
			})
			.then((response: any) => {
				let type = 'application/vnd.ms-excel'
				downloadFileByFileFlow(response, type, fileName, () => {
					message.success('导出成功')
					setExportLoading(false)
				})
			})
			.catch(() => {
				setExportLoading(false)
				message.error('导出失败')
			})
	}

	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar
							showLabel={false}
							pageName={'holderDetailSearch'}
							optionData={options}
							onClear={onClear}
							onSubmit={onSubmit}
							initValues={{ cashStatusList, pledgeStatus, needClear: true }}
						/>
						{/* <Button style={{ marginLeft: '20px' }} type="primary" onClick={holderExportCouCsv} loading={exportLoading}>
							导出
						</Button> */}
					</OperatingArea>
					<BaseTable
						columns={getHolderColumns()}
						loading={holderTableLoading}
						dataSource={holderTableDataSource}
						{...holderPagination}
						onPageChange={handleHolderTableChange}
						onSizeChange={handleHolderTableSizeChange}
					/>
					<CouDeatail
						data={detailData}
						visible={CouDeatailVisible}
						onOk={() => {
							return
						}}
						onCancel={() => {
							setCouDeatailVisible(false)
							setDetilaData(null)
						}}
					/>
				</div>
			</LayoutSlot>
			<ChainInfoModal visible={chainInfoModalVisible} onCancel={() => setChainInfoModalVisible(false)} chainData={onChainData} />
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.options_btn {
		span {
			display: inline-block;
			margin: 0 20px;
		}
	}
`

export default HolderDetail
