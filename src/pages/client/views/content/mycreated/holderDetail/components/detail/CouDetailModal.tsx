import React from 'react'
import { Descriptions, Modal } from 'antd'
import styled from 'styled-components'
import { renderDate_, renderAmountInCent, renderOriginalCouStatus } from '@src/pages/client/config/TableColumnsRender'

interface Props {
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 提交确定操作
	 */
	onOk: () => void
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
	/**
	 * @description 弹窗展示的数据
	 */
	data: any
}

const status = {
	WAIT: '等待上链',
	GOING: '上链中',
	DONE: '上链成功',
	FAIL: '上链失败',
}
const CouDeatail = (props: Props) => {
	const { visible, onOk, onCancel, data } = props

	return (
		<div>
			<Modal width={700} open={visible} title={'详情'} onCancel={onCancel} onOk={onOk} footer={null}>
				<Box>
					{data && (
						<div>
							<div className="wx_Modal_Main">
								<Descriptions column={2}>
									<Descriptions.Item label="融信编号">{data.couNo}</Descriptions.Item>
									<Descriptions.Item label="金额（￥）">{renderAmountInCent(data.couAmountInCent)}</Descriptions.Item>
									<Descriptions.Item label="开立方">{data.publishName}</Descriptions.Item>
									<Descriptions.Item label="持有方">{data.holderName}</Descriptions.Item>
									<Descriptions.Item label="授信方">{data.creditName}</Descriptions.Item>
									<Descriptions.Item label="兑付到期日">{renderDate_(data.dueDate)}</Descriptions.Item>
									<Descriptions.Item label="兑付状态">{renderOriginalCouStatus(data.cashStatus)}</Descriptions.Item>
								</Descriptions>
							</div>
						</div>
					)}
				</Box>
			</Modal>
		</div>
	)
}

const Box = styled.div`
	padding: 0 30px;
`
export default CouDeatail
