import React, { useEffect, useState } from 'react'
import SearchBar from '@clientComponents/SearchBar'
import styled from 'styled-components'
import { creditModuleApi, financeModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { renderAmountInCent } from '@src/pages/client/config/TableColumnsRender'
import { Tooltip, Button, message } from 'antd'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import OperatingArea from '@src/globalComponents/OperatingArea'
import AddOrEditUserInfoModal from '../components/addOrEditInfoModal'
import { TableOptions } from '@src/utils/util'
import CreditInfoModal from '../components/creditInfoModal'

type FormModalType = 'add' | 'edit'

//搜索工具栏的付息方式下拉菜单 S供应商付 C核心企业付
const options = {
	interestPayWay: [
		{ key: 'supplier', name: '供应商付息' },
		{ key: 'center', name: '核心企业付息' },
	],
	status: [
		{ name: '审核中', key: 'checking' },
		{ name: '审核拒绝', key: 'rejected' },
		{ name: '审核通过', key: 'confirmed' },
	],
}
function Index() {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [searchParams, setSearchParams] = useState({})
	const [loading, setLoading] = useState(false)
	const [companyInfo, setCompanyInfo] = useState<any>({})
	const [type, setType] = useState<FormModalType>('add')
	const [visible, setVisible] = useState(false)
	//获取业务模式 是否为非直连模式
	const [isDisDirectConnection, setIsDisDirectConnection] = useState<boolean>(false)
	const [creditInfoData, setCreditInfoData] = React.useState<any>({})
	const [creditInfoVisible, setCreditInfoVisible] = React.useState<boolean>(false)

	useEffect(() => {
		getBusinessSchema()
	}, [])

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	//	获取业务类型
	const getBusinessSchema = () => {
		const { uuid } = JSON.parse(localStorage.getItem('ext') || '{}').user?.company || {}
		financeModuleApi
			.getFiConfigDetail({ fiUuid: uuid })
			.then(res => {
				//去配置逻辑 金融机构和母公司，没有则取母公司
				let fiCongfi = res.financeBusinessConfig || res.parentFinanceBusinessConfig || null
				if (!fiCongfi) message.error('金融机构配置信息不完善')
				if (1 == fiCongfi.financeRateFlag) {
					setIsDisDirectConnection(true)
				}
			})
			.catch(err => {
				console.log('err', err)
			})
	}

	function getList() {
		setLoading(true)
		creditModuleApi
			.getList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...searchParams,
			})
			.then(
				(res: any) => {
					// pagination.total = res['total']
					if (res && res.list) {
						res.list.forEach((i: { key: any; id: any }) => {
							i.key = i.id
						})
						pagination.total = res['total']
						setDataSource([...res['list']])
					} else {
						pagination.total = 0
						setDataSource([])
					}
					setLoading(false)
				},
				err => setLoading(false)
			)
	}
	const onSubmit = (params: { interestPayWay: string; companyName: string }) => {
		pagination.current = 1
		setSearchParams({ ...params })
	}

	const onClear = () => {
		setSearchParams({})
	}
	const handleChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	const handleSizeChange = (size: number) => {
		console.log(size)
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	//点击新增融信企业
	const handleAdd = (t: FormModalType) => {
		setType(t)
		setVisible(true)
	}
	//点击修改
	const handleEdit = (t: FormModalType, record) => {
		setCompanyInfo(record)
		setType(t)
		setVisible(true)
	}
	//取消表单操作
	const handOnCancel = () => {
		setVisible(false)
	}

	const getColumns = () => {
		const columnsDic = {
			creditCompany: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									setCreditInfoVisible(true)
									setCreditInfoData(record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			quotaCanUse: {
				title: '可用开立(￥)',
				render: (text: any, record: any) => {
					const { quotaInCent, usedQuotaInCent, frozenQuotaInCent } = record
					let filterQuotaInCent = quotaInCent
					let filterUsedQuotaInCent = usedQuotaInCent
					let filterFrozenQuotaInCent = frozenQuotaInCent
					if (typeof quotaInCent === 'string') {
						filterQuotaInCent = Number(quotaInCent)
					}
					if (typeof usedQuotaInCent === 'string') {
						filterUsedQuotaInCent = Number(usedQuotaInCent)
					}
					if (typeof frozenQuotaInCent === 'string') {
						filterFrozenQuotaInCent = Number(frozenQuotaInCent)
					}
					return <span>{renderAmountInCent(filterQuotaInCent - (filterUsedQuotaInCent + filterFrozenQuotaInCent))}</span>
				},
			},
			operation: {
				dataIndex: 'operation',
				title: '操作',
				render: (text: any, record: any) => {
					return (
						<span className="link" onClick={() => handleEdit('edit', record)}>
							修改
						</span>
					)
				},
			},
		}
		return getColumnsByPageName('credit', columnsDic)
	}

	//表单 finish 后的操作
	const handleEditFinish = types => {
		setVisible(false)
		message.success('提交成功，请联系运营人员审核')
		getList()
		// onOk(types, getList)
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<Button onClick={() => handleAdd('add')} type="primary">
							添加授信企业
						</Button>
						<SearchBar onClear={onClear} onSubmit={onSubmit} pageName="credit" optionData={options} />
					</OperatingArea>
					<BaseTable
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						columns={getColumns()}
					/>
					<AddOrEditUserInfoModal
						options={TableOptions}
						type={type}
						onCancel={handOnCancel}
						onOk={handleEditFinish}
						visible={visible}
						creditCompanyInfo={companyInfo}
						isDisDirect={isDisDirectConnection}
					/>
				</div>
			</LayoutSlot>
			<CreditInfoModal
				visible={creditInfoVisible}
				creditInfo={creditInfoData}
				onCancel={() => {
					setCreditInfoVisible(false)
				}}
			></CreditInfoModal>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
`

export default Index
