/*
 * @Date: 2022-09-08 14:27:57
 * @LastEditors: jcl
 * @FilePath: \frontend-web\src\pages\client\views\content\companyCreditManage\ratesSetting\index.tsx
 * @Description: In User Settings Edit
 */

import { InfoCircleFilled } from '@ant-design/icons'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import SearchBar from '@clientComponents/SearchBar'
import BaseTable from '@src/globalComponents/BaseTable'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import OperatingArea from '@src/globalComponents/OperatingArea'
import { rateApi } from '@src/pages/client/api'
import { TableOptions } from '@src/utils/util'
import { Alert, Button, message, Modal } from 'antd'
import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import AddOrEditUserInfoModal from './components/addOrEditInfoModal'

type FormModalType = 'add' | 'edit'

export default () => {
	const [dataSource, setDataSource] = useState<any[]>([])
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
	const [searchParams, setSearchParams] = useState({})
	const [loading, setLoading] = useState(false)
	const [rateInfo, setRateInfo] = useState<any>({})
	const [type, setType] = useState<FormModalType>('add')
	const [visible, setVisible] = useState(false)

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	function getList() {
		setLoading(true)
		rateApi
			.getList({
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				...searchParams,
			})
			.then(
				(res: any) => {
					if (res && res.list) {
						res.list.forEach((i: { key: any; id: any }) => {
							i.key = i.id
						})
						setDataSource([...res['list']])
						pagination.total = res['total']
					} else {
						setDataSource([])
						pagination.total = 0
					}
					setLoading(false)
				},
				() => setLoading(false)
			)
	}
	const onSubmit = (params: { interestPayWay: string; companyName: string }) => {
		pagination.current = 1
		setSearchParams({ ...params })
	}

	const handleSizeChange = (size: number) => {
		pagination.pageSize = size
		pagination.current = 1

		setPagination({ ...pagination })
	}

	const handleChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}

	//点击新增融信企业
	const handleAdd = (t: FormModalType) => {
		setVisible(true)
		setType(t)
	}
	//点击修改
	const handleEdit = (t: FormModalType, record) => {
		setRateInfo(record)
		setType(t)
		setVisible(true)
	}
	const onClear = () => {
		setSearchParams({})
	}
	//删除
	const handleDel = ({ id }) => {
		Modal.confirm({
			title: '确定删除该条记录?',
			icon: <InfoCircleFilled />,
			onOk() {
				rateApi.del({ id }).then(
					(res: any) => {
						message.success('删除成功')
						setSearchParams({ ...searchParams, current: 1 })
						setLoading(false)
					},
					() => setLoading(false)
				)
			},
		})
	}
	//取消表单操作
	const handOnCancel = () => {
		setVisible(false)
	}

	const getColumns = () => {
		const columnsDic = {
			operation: {
				dataIndex: 'operation',
				title: '操作',
				render: (text: any, record: any) => {
					return (
						<>
							<span className="link" onClick={() => handleEdit('edit', record)}>
								修改
							</span>
							<span className="red" onClick={() => handleDel(record)}>
								删除
							</span>
						</>
					)
				},
			},
		}
		return getColumnsByPageName('ratesSetting', columnsDic)
	}

	//表单 finish 后的操作
	const handleEditFinish = () => {
		setVisible(false)
		message.success(type === 'add' ? '添加成功' : '修改成功')
		getList()
	}
	return (
		<Styled>
			<LayoutSlot>
				<div className="card-wrapper">
					<Alert
						style={{ marginBottom: '10px' }}
						message="关于特殊利率的说明"
						description={
							<ol style={{ listStyleType: 'decimal' }}>
								<li>您可在本页面增加供应商的特殊利率和保理手续费率</li>
								<li>
									若供应商申请融资时，付息方式为供应商付息，且您为该供应商设置了特殊利率与保理手续费率，则供应商融资时取您为其设置的特殊利率与保理手续费率
								</li>
							</ol>
						}
						type="info"
						showIcon
					/>
					<OperatingArea>
						<Button onClick={() => handleAdd('add')} type="primary">
							添加
						</Button>
						<SearchBar onClear={onClear} onSubmit={onSubmit} pageName="ratesSetting" />
					</OperatingArea>
					<BaseTable
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
						loading={loading}
						dataSource={dataSource}
						{...pagination}
						columns={getColumns()}
					/>
					<AddOrEditUserInfoModal options={TableOptions} type={type} onCancel={handOnCancel} onOk={handleEditFinish} visible={visible} rateInfo={rateInfo} />
				</div>
			</LayoutSlot>
		</Styled>
	)
}

const Styled = styled.div`
	width: 100%;
	height: 100%;
`
