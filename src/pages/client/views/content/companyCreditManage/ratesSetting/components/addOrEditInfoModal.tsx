import styled from 'styled-components'
import React, { ReactElement, useEffect } from 'react'
import { Modal, Form, Input } from 'antd'
import { rateApi } from '@src/pages/client/api'
import GetFormList from '@src/globalComponents/companyInfoForm/GetFormList'
interface Props {
	type: 'add' | 'edit'
	rateInfo: {
		id: number
		companyName: string
	}
	visible: boolean
	onOk: (types: string) => any
	onCancel: () => any
	options: any
}
const layout = {
	labelCol: { span: 9 },
	wrapperCol: { span: 8 },
}
function AddOrEditUserInfoModal({ type, visible, onCancel, onOk, rateInfo, options }: Props): ReactElement {
	const [form] = Form.useForm()
	const [actionsType, setActionsType] = React.useState(options.ADD)
	const [loading, setLoading] = React.useState<boolean>(false)

	useEffect(() => {
		if (type === 'add') {
			setActionsType(options.ADD)
			form.resetFields()
		}
		if (type === 'edit') {
			setActionsType(options.EDIT)
			form.setFieldsValue({ ...rateInfo })
		}
	}, [visible])

	//提交表单（新增 && 编辑）前置操作
	const handlerOk = () => {
		form.submit()
	}
	const onFinish = values => {
		setLoading(true)
		values.factoringRate = Number(values.factoringRate)
		values.interestRate = Number(values.interestRate)
		if (type === 'edit') {
			values['companyName'] = rateInfo.companyName
			values['id'] = rateInfo.id
			handleEditRateInfo(values)
		} else {
			handleAddRateInfo(values)
		}
	}
	//提交表单（ 编辑）提交数据
	const handleEditRateInfo = async (values: any) => {
		await rateApi.modify(values).then(
			res => {
				setLoading(false)
				onOk(actionsType)
				form.resetFields()
			},
			err => {
				setLoading(false)
			}
		)
	}
	//提交表单（新增）提交数据
	const handleAddRateInfo = async (values: any) => {
		await rateApi.add(values).then(
			res => {
				setLoading(false)
				onOk(actionsType)
				form.resetFields()
			},
			err => {
				setLoading(false)
			}
		)
	}

	return (
		<Modal
			onOk={handlerOk}
			bodyStyle={{ paddingBottom: 0 }}
			style={{ top: 30 }}
			width={500}
			open={visible}
			title={type === 'add' ? '添加' : '修改'}
			onCancel={onCancel}
			confirmLoading={loading}
		>
			<ModalBox>
				<Form form={form} {...layout} className="form" onFinish={onFinish}>
					<Form.Item name="id" hidden>
						<input type="hidden" />
					</Form.Item>
					<Form.Item className="companyName" label="供应商" name="companyName" rules={[{ required: true, message: '请输入供应商全称', whitespace: true }]}>
						<Input placeholder="请输入供应商全称" maxLength={64} disabled={type === 'edit' ? true : false} />
					</Form.Item>
					<GetFormList
						configArr={[
							{ key: 'InterestRate', span: 24 },
							{ key: 'FactoringRate', span: 24 },
						]}
						pageType="ratesSetting"
					></GetFormList>
				</Form>
			</ModalBox>
		</Modal>
	)
}

const ModalBox = styled.div`
	.fileBox {
		display: flex;
		line-height: 32px;
		p {
			margin-left: 5px;
		}
	}
	.form {
		padding-top: 20px;
	}
	.titleText {
		.titleTag {
			font-size: 16px;
			margin: 10px 0 0 80px;
		}
		.titleStatus {
			width: 550px;
			margin: 15px auto;
		}
		.auditAlert {
			width: 550px;
			margin: 0 auto;
		}
	}
`

export default AddOrEditUserInfoModal
