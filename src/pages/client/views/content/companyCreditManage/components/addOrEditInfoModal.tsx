import styled from 'styled-components'
import React, { ReactElement, useEffect } from 'react'
import { Modal, Form, Input, Alert, Tag, Select } from 'antd'
import { creditModuleApi } from '@src/pages/client/api'
import { FormConfig } from './formConfig'
import GetFormList from '@src/globalComponents/companyInfoForm/GetFormList'
import moment from 'moment'
import { deepExtend } from '@utils/util'
import { stampToTime } from '@utils/timeFilter'
import SmallTitle from '@src/globalComponents/SmallTitle'
import { creditStatusList } from '@factor/config/companyCreditManage'
interface Props {
	type: 'add' | 'edit'
	creditCompanyInfo: any
	visible: boolean
	onOk: (types: string) => any
	onCancel: () => any
	options: any
	isDisDirect: boolean
}
const layout = {
	labelCol: { span: 9 },
	wrapperCol: { span: 8 },
}
function AddOrEditUserInfoModal({ type, visible, onCancel, onOk, creditCompanyInfo, options, isDisDirect }: Props): ReactElement {
	const [form] = Form.useForm()
	const [actionsType, setActionsType] = React.useState(options.ADD)
	const [loading, setLoading] = React.useState<boolean>(false)

	useEffect(() => {
		if (type === 'add') {
			setActionsType(options.ADD)
			form.resetFields()
		}
		if (type === 'edit') {
			setActionsType(options.EDIT)
			let copyCompanyInfo = deepExtend({}, creditCompanyInfo)

			if (!copyCompanyInfo.creditDueDate) {
				copyCompanyInfo = { ...copyCompanyInfo, ...{ creditDueDate: stampToTime(new Date(), 6) } }
			}
			copyCompanyInfo.creditDueDate = moment(copyCompanyInfo.creditDueDate, 'YYYY-MM-DD')
			copyCompanyInfo.quotaInCent /= 100
			copyCompanyInfo.financeDiscount = Number(copyCompanyInfo.financeDiscount) * 100
			form.setFieldsValue(copyCompanyInfo)
		}
	}, [visible])

	//提交表单（新增 && 编辑）前置操作
	const handleSubmit = () => {
		setLoading(true)
		form.validateFields().then(
			res => {
				res.creditDueDate = moment(res.creditDueDate).format('YYYY-MM-DD')
				res.quotaInCent *= 100
				res.factoringRate = Number(res.factoringRate)
				res.interestRate = Number(res.interestRate)
				res.financeDiscount = isDisDirect ? Number(res.financeDiscount) / 100 : 1
				if (type === 'edit') {
					res['companyUuid'] = creditCompanyInfo.companyUuid
				}
				handleAddOrEditUserInfo(res)
			},
			err => {
				console.log(err, 'err')
				setLoading(false)
			}
		)
	}
	//提交表单（新增 && 编辑）提交数据
	const handleAddOrEditUserInfo = async (values: any) => {
		await creditModuleApi.modify(values).then(
			res => {
				setLoading(false)
				onOk(actionsType)
				form.resetFields()
			},
			err => {
				setLoading(false)
			}
		)
	}
	const verifyStatus = (text: string) => {
		if (text === 'checking') {
			return <Tag color="#F59A23">审核中</Tag>
		}
		if (text === 'confirmed') {
			return <Tag color="#87d068">审核通过</Tag>
		}
		if (text === 'rejected') {
			return <Tag color="#f50">审核拒绝</Tag>
		}
	}

	const titleAndAlert = () => {
		console.log('ffffff', creditCompanyInfo)
		return (
			<div className="titleText">
				<SmallTitle text="审核信息" />
				<div>
					<div className="titleStatus">
						<span>审核状态：</span>
						<span>{verifyStatus(creditCompanyInfo.status)}</span>
					</div>
					{creditCompanyInfo.status === 'rejected' ? (
						<Alert className="auditAlert" message="审核意见" description={creditCompanyInfo.auditReason} type="error" showIcon />
					) : null}
				</div>
				<SmallTitle text="基本信息" />
			</div>
		)
	}

	return (
		<Modal
			onOk={handleSubmit}
			bodyStyle={{ paddingBottom: 0 }}
			style={{ top: 30 }}
			width={860}
			open={visible}
			title={type === 'add' ? '添加授信企业' : '修改授信信息'}
			onCancel={onCancel}
			confirmLoading={loading}
			okText={'提交审核'}
		>
			<ModalBox>
				{type === 'edit' ? titleAndAlert() : null}
				<Form form={form} {...layout} className="form">
					<Form.Item className="companyName" label="授信企业" name="companyName" rules={[{ required: true, message: '请输入企业全称', whitespace: true }]}>
						<Input placeholder="请输入企业全称" maxLength={64} disabled={type === 'edit' ? true : false} />
					</Form.Item>
					<GetFormList configArr={isDisDirect ? FormConfig['credit1'] : FormConfig['credit']} pageType="forget"></GetFormList>
					{type === 'add' ? (
						''
					) : (
						<Form.Item label="授信状态" className="creditStatus" name="enable">
							<Select placeholder="选择授信状态" getPopupContainer={(triggerNode: any) => triggerNode.parentNode}>
								{creditStatusList.map(item => {
									return (
										<Select.Option value={item.key} key={item.key}>
											{item.value}
										</Select.Option>
									)
								})}
							</Select>
						</Form.Item>
					)}
				</Form>
			</ModalBox>
		</Modal>
	)
}

const ModalBox = styled.div`
	.fileBox {
		display: flex;
		line-height: 32px;
		p {
			margin-left: 5px;
		}
	}
	.form {
		padding-top: 20px;
	}
	.titleText {
		.titleTag {
			font-size: 16px;
			margin: 10px 0 0 80px;
		}
		.titleStatus {
			width: 550px;
			margin: 15px auto;
		}
		.auditAlert {
			width: 550px;
			margin: 0 auto;
		}
	}
`

export default AddOrEditUserInfoModal
