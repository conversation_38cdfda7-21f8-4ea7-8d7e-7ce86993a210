import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Alert, Tag } from 'antd'
import { formatNumber } from '@utils/format'
import SmallTitle from '@src/globalComponents/SmallTitle'
import styled from 'styled-components'
import { renderCreditStatus } from '@src/pages/client/config/TableColumnsRender'

interface PropsStruct {
	/**
	 * @description 授信信息
	 */
	creditInfo: any
	/**
	 * @description 是否开启
	 */
	visible: boolean
	/**
	 * @description 取消操作
	 */
	onCancel: () => void
}

function CreditInfoModal(props: PropsStruct) {
	const { visible, creditInfo, onCancel } = props

	const formatListData = {
		checkValue(text: any) {
			if (!text) return '- -'
			return text
		},
		quota(quotaInCent: number) {
			const quotaInYuan = quotaInCent / 100
			return formatNumber.addThousandsSign(quotaInYuan)
		},
		calcCanUseQuote() {
			const { quotaInCent, usedQuotaInCent, frozenQuotaInCent } = creditInfo
			let filterQuotaInCent = quotaInCent
			let filterUsedQuotaInCent = usedQuotaInCent
			let filterFrozenQuotaInCent = frozenQuotaInCent
			if (typeof quotaInCent === 'string') {
				filterQuotaInCent = Number(quotaInCent)
			}
			if (typeof usedQuotaInCent === 'string') {
				filterUsedQuotaInCent = Number(usedQuotaInCent)
			}
			if (typeof frozenQuotaInCent === 'string') {
				filterFrozenQuotaInCent = Number(frozenQuotaInCent)
			}
			const calcCanUseQuoteInYuan = (filterQuotaInCent - filterUsedQuotaInCent - filterFrozenQuotaInCent) / 100
			return formatNumber.addThousandsSign(calcCanUseQuoteInYuan)
		},
		interestPayWay(type: string) {
			switch (type) {
				case 'supplier':
					return '供应商付息'
				case 'center':
					return '核心企业付息'
			}
		},
		verifyStatus(text: string) {
			if (text === 'checking') {
				return <Tag color="#F59A23">审核中</Tag>
			}
			if (text === 'confirmed') {
				return <Tag color="#87d068">审核通过</Tag>
			}
			if (text === 'rejected') {
				return <Tag color="#f50">审核拒绝</Tag>
			}
		},
	}
	const getItemTemp = (type, val?: any) => {
		let titleTxt = ''
		let contentTxt = formatListData.checkValue(val)
		switch (type) {
			case 'companyName':
				titleTxt = '授信企业:'
				break
			case 'relationFiName':
				titleTxt = '授信方:'
				break
			case 'quotaInCent':
				titleTxt = '开立额度:'
				contentTxt = formatListData.quota(val) + ' 元'
				break
			case 'canUseQuoteInCent':
				titleTxt = '可用开立:'
				contentTxt = formatListData.calcCanUseQuote() + ' 元'
				break
			case 'creditDueDate':
				titleTxt = '授信到期日:'
				break
			case 'interestPayWay':
				contentTxt = formatListData.interestPayWay(val)
				titleTxt = '付息方式:'
				break
			case 'interestRate':
				titleTxt = '年化融资利率:'
				contentTxt = val + ' %'
				break
			case 'factoringRate':
				titleTxt = '保理手续费率:'
				contentTxt = val + ' %'
				break
			case 'financeDiscount':
				titleTxt = '融资比例:'
				contentTxt = val * 100 + ' %'
				break
			case 'customerManagerMobile':
				titleTxt = '客户经理手机号:'
				break
			case 'enable':
				contentTxt = renderCreditStatus(val)
				titleTxt = '授信状态:'
				break
			case 'status':
				titleTxt = '审核状态:'
				contentTxt = formatListData.verifyStatus(val)
				break
		}
		return (
			<>
				<span className="left">{titleTxt}</span>
				<Tooltip title={contentTxt} placement="topLeft">
					<span className="right">{contentTxt}</span>
				</Tooltip>
			</>
		)
	}
	const getInfoList = infoList => {
		const infoItems: any = {
			companyName: function (val: any) {
				return getItemTemp('companyName', val)
			},
			relationFiName: function (val: any) {
				return getItemTemp('relationFiName', val)
			},
			quotaInCent: function (val: any) {
				return getItemTemp('quotaInCent', val)
			},
			canUseQuoteInCent: function () {
				return getItemTemp('canUseQuoteInCent')
			},
			creditDueDate: function (val: any) {
				return getItemTemp('creditDueDate', val)
			},
			interestPayWay: function (val: any) {
				return getItemTemp('interestPayWay', val)
			},
			interestRate: function (val: any) {
				return getItemTemp('interestRate', val)
			},
			factoringRate: function (val: any) {
				return getItemTemp('factoringRate', val)
			},
			financeDiscount: function (val: any) {
				return getItemTemp('financeDiscount', val)
			},
			customerManagerMobile: function (val: any) {
				return getItemTemp('customerManagerMobile', val)
			},
			enable: function (val: any) {
				return getItemTemp('enable', val)
			},
			status: function (val: any) {
				return getItemTemp('status', val)
			},
		}
		return (
			<ul className="card-list">
				{infoList.map((item: any) => {
					const itemRender: Function = infoItems[item]
					if (itemRender) {
						return (
							<li key={item} className="card-item">
								{itemRender(creditInfo[item])}
							</li>
						)
					} else return null
				})}
			</ul>
		)
	}
	//审核信息
	const VerifyInfo = () => {
		const verifyInfoList: string[] = ['status']
		const { status } = creditInfo
		return (
			<CreditInfoWrapper className="card-wrapper">
				<SmallTitle text="审核信息" />
				{getInfoList(verifyInfoList)}
				{status === 'rejected' ? <Alert message="审核意见" description={creditInfo.auditReason} type="error" showIcon /> : ''}
			</CreditInfoWrapper>
		)
	}
	//基本信息
	const BaseInfo = () => {
		const baseInfoList: string[] = [
			'companyName',
			'relationFiName',
			'quotaInCent',
			'canUseQuoteInCent',
			'creditDueDate',
			'interestPayWay',
			'interestRate',
			'factoringRate',
			'financeDiscount',
			'customerManagerMobile',
			'enable',
		]
		return (
			<CreditInfoWrapper className="card-wrapper">
				<SmallTitle text="基本信息" />
				{getInfoList(baseInfoList)}
			</CreditInfoWrapper>
		)
	}

	return (
		<Modal title="查看授信信息" open={visible} onCancel={onCancel} footer={null} width="860px">
			<div className="main-wrapper">
				<VerifyInfo />
				<BaseInfo />
			</div>
		</Modal>
	)
}

const CreditInfoWrapper = styled.div``

export default CreditInfoModal
