import React from 'react'
import { Tooltip } from 'antd'
import { creditModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import { numberToThousands } from '@utils/timeFilter'
import LayoutSlot from '@globalComponents/LayoutSlot'
import BaseTable from '@globalComponents/BaseTable'
import SearchBar from '@src/pages/client/components/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import CreditInfoModal from '../components/creditInfoModal'
import styled from 'styled-components'

function CreditList() {
	//table相关
	const [searchParams, setSearchParams] = React.useState({})
	const [pagination, setPagination] = React.useState({
		current: 1,
		pageSize: 10,
		total: 0,
	})
	const [dataSource, setDataSource] = React.useState([])
	const [tableLoading, setTableLoading] = React.useState<boolean>(false)

	const [creditInfoVisible, setCreditInfoVisible] = React.useState<boolean>(false)
	const [creditInfoData, setCreditInfoData] = React.useState<any>({})

	React.useEffect(() => {
		getFinanceCreditList()
	}, [pagination.current, pagination.pageSize, searchParams])

	// 获取融资授信列表
	const getFinanceCreditList = () => {
		setTableLoading(true)
		creditModuleApi.getCreditChildren({ pageNum: pagination.current, pageSize: pagination.pageSize, ...searchParams }).then(
			res => {
				pagination.total = res.total
				setPagination({ ...pagination })

				setDataSource(res.list)
				setTableLoading(false)
			},
			reason => {
				setTableLoading(false)
			}
		)
	}

	//处理搜索
	const handleSearch = (values: any) => {
		//点击查询后，带入参数页码置为1, 记录当前查询参数
		pagination.current = 1
		setSearchParams(values)
		setPagination({ ...pagination })
	}
	const handlePageChange = pageNum => {
		pagination.current = pageNum
		setPagination({ ...pagination })
	}
	const handleSizeChange = pageSize => {
		pagination.pageSize = pageSize
		setPagination({ ...pagination })
	}
	const handleReset = () => {
		pagination.current = 1
		setSearchParams({})
		setPagination({ ...pagination })
	}

	const getColumns = () => {
		const columnsDic = {
			availableCredit: {
				dataIndex: 'availableCredit',
				render: (text: any, record: any) => {
					const availableCredit = (record.quotaInCent - record.usedQuotaInCent - record.frozenQuotaInCent) / 100
					if (!Number.isNaN(availableCredit)) {
						return numberToThousands(availableCredit)
					} else {
						return ''
					}
				},
			},
			creditCompany: {
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text}>
							<span
								className="link"
								title={text}
								onClick={() => {
									setCreditInfoVisible(true)
									setCreditInfoData(record)
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
		}
		return getColumnsByPageName('creditList', columnsDic)
	}

	return (
		<LayoutSlot>
			<CreditListStyle className="card-wrapper">
				<OperatingArea>
					<SearchBar
						pageName="creditList"
						optionData={{
							companyTypes: [
								{ name: '授信方', key: 'relationFiName' },
								{ name: '授信企业', key: 'companyName' },
							],
							interestPayWay: [
								{ name: '供应商付息', key: 'supplier' },
								{ name: '核心企业付息', key: 'center' },
							],
							status: [
								{ name: '审核中', key: 'checking' },
								{ name: '审核拒绝', key: 'rejected' },
								{ name: '审核通过', key: 'confirmed' },
							],
						}}
						initValues={{
							companyType: { name: '授信方', key: 'relationFiName' },
						}}
						onSubmit={handleSearch}
						onClear={handleReset}
					/>
				</OperatingArea>
				<BaseTable
					className="card-table"
					rowKey="id"
					loading={tableLoading}
					dataSource={dataSource}
					columns={getColumns()}
					total={pagination.total}
					current={pagination.current}
					onPageChange={handlePageChange}
					onSizeChange={handleSizeChange}
				/>
			</CreditListStyle>
			<CreditInfoModal
				visible={creditInfoVisible}
				creditInfo={creditInfoData}
				onCancel={() => {
					setCreditInfoVisible(false)
				}}
			></CreditInfoModal>
		</LayoutSlot>
	)
}

const CreditListStyle = styled.div`
	.ant-table-cell {
		display: table-cell;
		vertical-align: middle;
	}
`

export default CreditList
