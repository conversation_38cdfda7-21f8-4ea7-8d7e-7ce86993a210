import React from 'react'
import { invoiceModuleApi } from '@src/pages/client/api'
import InvoiceWarning from '@src/globalComponents/cross-platform-page/invoiceWarning/invoiceWarning'
import { hasAuth } from '@src/pages/client/biz/bizIndex'
const ClientInvoiceWarning = () => {
	let requestList = {
		getListData: {
			requestApi: invoiceModuleApi.getInvoiceAlarmList,
		},
		exportCouCsv: {
			requestApi: invoiceModuleApi.exportInvoiceAlarm,
		},
	}
	return <InvoiceWarning excelBizType="invoiceAlarm" hasFiInputAuth={hasAuth('bl_afterLoanManage:invoiceWarning_with_fi_search')} requestList={requestList} />
}

export default ClientInvoiceWarning
