import React from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import styled from 'styled-components'
import FinanceCheckListComponents from '@src/globalComponents/cross-platform-page/financeCheckManage/financeCheckList'
import { financeCheckManageModuleApi } from '@src/pages/client/api'

const FinanceCheckList: React.FC = () => {
	const bl_Api = {
		listApi: financeCheckManageModuleApi.financeCheckFiParentList,
		viewApi: financeCheckManageModuleApi.getCheckTaskListByFinanceUuid,
		aginApi: financeCheckManageModuleApi.financeCheckRelaunch,
		exportApi: financeCheckManageModuleApi.financeCheckTaskExport,
		exportParams: {
			excelBizType: 'zdwDuplicateFinanceFiParent',
		},
	}
	const goPage = '/content/financeCheckManage/pfiFinanceCheckDetail'
	return (
		<BoxStyle>
			<LayoutSlot>
				<div className="card-wrapper">
					<FinanceCheckListComponents api={bl_Api} goPage={goPage} />
				</div>
			</LayoutSlot>
		</BoxStyle>
	)
}

const BoxStyle = styled.div`
	width: 100%;
	height: 100%;
`

export default FinanceCheckList
