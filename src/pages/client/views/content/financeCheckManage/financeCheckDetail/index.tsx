import React from 'react'
import { financeCheckManageModuleApi, registerManageModuleApi } from '@factor/api'
import CommonFinanceCheckDetail from '@globalComponents/cross-platform-page/financeCheckManage/financeCheckDetail'

interface ApiModuleProps {
	getCheckTaskCount: Function
	getCheckTaskDetail: Function
	getCheckTaskItemById: Function
	getCheckTaskIdListByType: Function
	downloadFiles: Function
	getObjectionList: Function
	downloadRegisterFileZip: Function
	downloadReport: Function
	pdfViewer: Function
}

const FinanceCheckDetail = () => {
	const apiModule: ApiModuleProps = {
		getCheckTaskCount: financeCheckManageModuleApi.getCheckTaskCount,
		getCheckTaskDetail: financeCheckManageModuleApi.getCheckTaskDetail,
		getCheckTaskItemById: financeCheckManageModuleApi.getCheckTaskItemById,
		getCheckTaskIdListByType: financeCheckManageModuleApi.getCheckTaskIdListByType,
		downloadFiles: financeCheckManageModuleApi.downloadFiles,
		getObjectionList: financeCheckManageModuleApi.getObjectionList,
		downloadRegisterFileZip: financeCheckManageModuleApi.downloadRegisterFileZip,
		downloadReport: financeCheckManageModuleApi.downloadReport,
		pdfViewer: financeCheckManageModuleApi.pdfViewer,
	}
	return <CommonFinanceCheckDetail apiModule={apiModule}></CommonFinanceCheckDetail>
}
export default FinanceCheckDetail
