import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import styled from 'styled-components'
import AuditModal from '@src/globalComponents/AuditModal'
import ukeyAlert from '@src/pages/client/components/ukeyAlert'
import { checkUkey, handleUkey, clearTimeout, isCheckUkey, getCheckUkeyMsg } from '@src/pages/client/components/checkUkey'
import { Tooltip, message, Modal, Button, Row, Col, Alert, Spin, Table } from 'antd'
import { ExclamationCircleFilled } from '@ant-design/icons'
import { commonModuleApi, financeModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import { deepExtend } from '@utils/util'
import { history } from '@src/utils/router'
import { getStorage, couDataOn<PERSON>hain } from '@src/pages/client/biz/bizIndex'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
const receiveIcon = require('@src/assets/images/transfer/receive-icon.png')
const payIcon = require('@src/assets/images/transfer/pay-icon.png')

let hasOpened = false

const WaitAuditList = () => {
	const [auditTransferPagination, setAuditTransferPagination] = useState({ current: 1, pageSize: 10, total: 0 }) //表格 配置
	const [loading, setLoading] = useState(true) //表格加载
	const [dataSource, setDataSource] = useState<any[]>([]) //表格数据
	const [transferData, setTransferData] = useState<any>({}) //表格数据
	const [auditAlertVisible, setAuditAlertVisible] = useState(false)
	const [transferAuditVisible, setTransferAuditVisible] = useState(false)
	const [transferAuditType, setTransferAuditType] = useState('agree')
	const [transferAuditTitle, setTransferAuditTitle] = useState('确定通过？')
	const [auditConfirmLoading, setAuditConfirmLoading] = React.useState(false)
	const [ukeyCheckResult, setUkeyCheckResult] = useState<any>({})
	const [ukeyCountersign, setUkeyCountersign] = useState(null)
	const [ukeyLoading, setUkeyLoading] = useState(false)
	const [isUkeyAlert, setIsUkeyAlert] = useState(false)
	//判断是否是最后一个审核者
	const [isLastAuditorPerson, setIsLastAuditor] = React.useState(false)
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')
	const paperSubmitMark = JSON.parse(localStorage.getItem('ext') || '{}').user?.company?.paperSubmitMark

	const [errorUkeyArray, setErrorUkeyArray] = useState<any[]>([])
	const [errorUkeyModal, setErrorUkeyModal] = useState<boolean>(false)
	const [ukeyChooseModal, setUkeyChooseModal] = useState<boolean>(false)
	const [validity, setValidity] = useState<string>('')
	const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([])
	const [chooseKeyData, setChooseKeyData] = useState<string>('')
	const [ukeyHaveList, setUkeyHaveList] = useState<any[]>([])
	const [createUkeyNo, setCreateUkeyNo] = useState<string>('')

	const [auditValue, setAuditValue] = useState<any>({})
	const [auditTitle, setAuditTitle] = useState<string>('')
	const [auditType, setAuditType] = useState<string>('')

	useEffect(() => {
		getList()
	}, [auditTransferPagination.current, auditTransferPagination.pageSize])

	useEffect(() => {
		getUkeyAlert()
		return () => {
			clearTimeout()
		}
	}, [])

	const getCurrentRole = () => {
		const currentRoleId = getStorage('roleId')
		// const currentRoleId = '12';
		const ext = JSON.parse(localStorage.getItem('ext') || '{}')
		return ext?.loginPerm?.roleList?.find(i => i?.roleId === currentRoleId)
	}

	const getList = (current?: any) => {
		setLoading(true)
		commonModuleApi
			.loadCurrentUserTask({
				pageNum: auditTransferPagination.current,
				pageSize: auditTransferPagination.pageSize,
				processDefinitionKeyList: ['create', 'pay'],
				roleNameList: [getCurrentRole()?.roleCode || ''],
			})
			.then(
				(res: any) => {
					let copyTransferData = deepExtend({}, res)
					let _auditTransferPagination = { ...auditTransferPagination }
					_auditTransferPagination.total = copyTransferData['count']
					setAuditTransferPagination({ ..._auditTransferPagination })
					if (!copyTransferData.data.length && auditTransferPagination.current !== 1) {
						_auditTransferPagination.current -= 1
						// getList()
						setAuditTransferPagination({ ..._auditTransferPagination })
					}
					if (copyTransferData.data.length > 0) {
						let filterTransferData: Array<any> = []
						filterTransferData = copyTransferData['data'].map(item => {
							//数据结构调整
							let auditTransferData = {
								assignee: item.assignee,
								businessKey: item.businessKey,
								createTime: item.createTime,
								id: item.id,
								name: item.name,
							}
							return { ...item.bizMap, ...{ auditTransferData } }
						})
						setDataSource(filterTransferData)
					} else {
						setDataSource([])
					}
					setLoading(false)
				},
				err => setLoading(false)
			)
	}

	const handleUkeyAlert = async (params: any) => {
		if (params.type === 'result') {
			setUkeyCheckResult(params)
		} else {
			setValidity(params.validity)
			setErrorUkeyArray(params.ukeyArray)
			if (!hasOpened) {
				if (params.validity === 'invalid') {
					setErrorUkeyModal(true)
					setSelectedRowKeys([])
					setChooseKeyData('')
				} else {
					setUkeyChooseModal(true)
					setSelectedRowKeys([])
					setChooseKeyData('')
				}
			} else {
				hasOpened = true
			}
		}
	}

	const getUkeyAlert = async () => {
		let checkUkeyMsg = await getCheckUkeyMsg()
		setIsUkeyAlert(checkUkeyMsg)
	}

	const resetUkeyInfo = () => {
		clearTimeout()
		setUkeyCheckResult({ checkUkeyresult: false, checkUkeyErrorResult: '' })
	}

	//审核操作
	const auditTransfer = async (value, title, type, chooseKeyData) => {
		setAuditValue(value)
		setAuditTitle(title)
		setAuditType(type)
		let roleList = getStorage('roleList') || []
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		try {
			setUkeyLoading(true)
			resetUkeyInfo()
			let callUkeyParams = await checkUkey(value)
			if (callUkeyParams) {
				const ukeylist = callUkeyParams['ukeylist']
				const checkData = callUkeyParams['checkData']
				let tmpUkeyList = []
				ukeylist.forEach(item => {
					tmpUkeyList.push(item.mediaNbr)
				})
				setUkeyHaveList(tmpUkeyList)
				setCreateUkeyNo(checkData.ukeyNo)
				let signInfo = await handleUkey({ ...callUkeyParams, value, chooseKeyData, handleUkeyAlert })
				if (signInfo?.info) {
					if (!signInfo.signedText || signInfo.signedText === 'E:1000') {
						resetUkeyInfo()
						setTransferAuditVisible(false)
						setUkeyLoading(false)
						return
					}
					if (!signInfo.signedText || signInfo.signedText === 'E:1000') {
						resetUkeyInfo()
						setTransferAuditVisible(false)
						setUkeyLoading(false)
						return
					}
				}
				setUkeyCountersign({ ...signInfo })
			}
			let companyUuid: string = ext.user && ext.user.company ? ext.user.company.uuid : ''
			let isLastAuditor: any = await commonModuleApi
				.isLastFlowPerson({
					companyUuid,
					role: getCurrentRole()?.roleCode || '',
					taskId: value.auditTransferData.id,
				})
				.catch(err => {
					console.log('err', err)
				})
			// if (isLastAuditor && !value.financeActiveStatus) {
			// 	setAuditAlertVisible(true)
			// } else if (![undefined].includes(isLastAuditor)) {
			// }

			// const isPublishCou = value?.transferCous?.length !== 0 && value?.transferCous?.[0]?.uuid === value?.transferCous?.[0]?.originalUuid
			// 开立供应商操作员同意时判断纸质材料是否提交
			if (type == 'agree' && getCurrentRole()?.roleCode == 'SOperator' && paperSubmitMark == 0) {
				message.error('您未提交纸质材料，无法接收融信，请联系银行客户经理')
			} else {
				setIsLastAuditor(isLastAuditor)
				setTransferAuditType(type)
				setTransferAuditTitle(title)
				setTransferData(value)
				setTransferAuditVisible(true)
			}
			setUkeyLoading(false)
		} catch (e) {
			setUkeyLoading(false)
			console.error(e)
		}
	}

	const submitAuditData = async submitData => {
		if (isCheckUkey && ukeyCheckResult?.checkUkeyresult) {
			return
		}
		setAuditConfirmLoading(true)
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let companyType = ext.user && ext.user.company ? ext.user.company.type : ''
		let companyPubKey = ext.user && ext.user.company ? ext.user.company.pubKey : ''
		let isAgree = true
		if (transferAuditType !== 'agree') {
			isAgree = false
		}
		if (companyType && companyPubKey) {
			//判断是否从缓存中获取到了必要字段
			let queryData = {
				isAgree: isAgree, //是否同意
				payCompanyType: companyType,
				toPubKey: companyPubKey,
				transferUuid: transferData.transferUuid,
				agreeReason: submitData.comment,
				lastNodeFlag: isLastAuditorPerson,
				// skipExpression2: !haveAuditor,
			}
			if (isCheckUkey) {
				console.log({ ukeyCountersign })
				queryData['ukeySign'] = ukeyCountersign?.signedText || null
				queryData['ukeyPublicKey'] = ukeyCountersign?.certificate || null
			}
			let param = {}
			param = {
				businessKey: transferData.auditTransferData.businessKey, // auditTransferData
				taskId: transferData.auditTransferData.id,
				outcome: transferAuditType,
				jsonParamStr: JSON.stringify(queryData),
			}

			commonModuleApi
				.doTask(param)
				.then(item => {
					setTransferAuditVisible(false)
					setAuditConfirmLoading(false)
					clearTimeout()
					if (item.returnCode === 17000) {
						message.success('处理成功')
						//判断是否是开立过来的cou
						let isPublishCou = false
						if (transferData.transferCous.length === 1 && transferData.transferCous[0].uuid === transferData.transferCous[0].originalUuid) {
							isPublishCou = true
						}
						// if (isLastAuditorPerson) {
						//融信数据存证上链
						let couNoList = []
						couNoList = transferData.transferCous.map(cou => {
							return cou['couNo']
						})
						if (transferAuditType === 'agree') {
							//如果是同意，开立或者支付都要存证上链
							if (isLastAuditorPerson) {
								couDataOnChain(couNoList)
							}
						} else {
							//如果是拒绝，只有支付存证上链，开立不存证上链
							if (!isPublishCou) {
								couDataOnChain(couNoList)
							}
						}
						// }
					} else if (item.returnCode === 30015) {
						message.error('重复操作，该项支付已完成处理')
					} else {
						// 删除关键字（银行接口异常）
						let returnDesc = item.returnDesc.replace('银行接口异常', '')
						message.error(returnDesc)
					}
					getList()
				})
				.catch(err => {
					setAuditConfirmLoading(false)
				})
		} else {
			message.error('操作失败，请联系运营处理')
			setAuditConfirmLoading(false)
		}
	}

	//查看数据详情
	const checkdetail = values => {
		const { transferUuid } = values
		history.push('/content/couTransfer/payDetails', {
			DetailForTransfer: transferUuid,
			isFromAudit: true,
			auditData: values,
			isUkey: true,
		})
	}

	//表格改变 当前页面
	const handleChange = (current: number) => {
		auditTransferPagination.current = current
		setAuditTransferPagination({ ...auditTransferPagination })
	}
	//表格改变 改变 size
	const handleSizeChange = (size: number) => {
		auditTransferPagination.current = 1
		auditTransferPagination.pageSize = size
		setAuditTransferPagination({ ...auditTransferPagination })
	}

	//获取合同的表格列
	const getColumns = () => {
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let companyPubKey = ext.user && ext.user.company ? ext.user.company.pubKey : ''
		const columnsDic = {
			transferNo: {
				width: 240,
				render: (text, record) => {
					return (
						<Tooltip title={text}>
							<div style={{ display: 'flex', alignItems: 'center', height: '20px' }}>
								<div style={{ verticalAlign: 'middle', height: '20px' }}>
									{record.fromCouPubKey === companyPubKey ? <img src={payIcon} className="pay-icon" /> : <img src={receiveIcon} className="receive-icon" />}
								</div>
								&nbsp;
								<div onClick={() => checkdetail(record)} style={{ color: '#02A7F0', cursor: 'pointer', height: '20px', lineHeight: '20px' }}>
									{text}
								</div>
							</div>
						</Tooltip>
					)
				},
			},
			transferFor: {
				render: (text, record) => {
					let typeText = ''
					if (record.fromCouPubKey === companyPubKey) {
						typeText = '付款'
					} else {
						typeText = '收款'
					}
					return typeText
				},
			},
			operator: {
				render: (_, record) => {
					if (record.fromCouPubKey === companyPubKey) {
						return <Tooltip title={record.operator}>{record.operator ? record.operator : '- -'}</Tooltip>
					} else {
						return '- -'
					}
				},
			},
			operation: {
				render: (text, record) => {
					return (
						<div className="options_btn">
							<span className="link" onClick={() => auditTransfer(record, '确定通过？', 'agree', '')}>
								通过
							</span>
							<span className="red" onClick={() => auditTransfer(record, '确定拒绝？', 'reject', '')}>
								拒绝
							</span>
						</div>
					)
				},
			},
		}

		let colns = getColumnsByPageName('AuditList', columnsDic)
		// 表格列长度的简写形式
		let columnIndexLengthMap = {
			0: 22,
			1: 13,
			2: 18,
			3: 18,
		}
		colns = evenlyTableColunms(colns, columnIndexLengthMap)
		return colns
	}

	const getSingleUkeyNo = str => {
		if (str) {
			const pattern = /CN=(\w+)/
			const match = str.match(pattern)
			return match ? match[1] : ''
		}
		return ''
	}

	const getCheckBoxState = record => {
		return {
			disabled:
				(!record.mediaNbr.startsWith('CN=JRCBHB') && !record.mediaNbr.startsWith('CN=JRCBFTG')) || !ukeyHaveList.includes(getSingleUkeyNo(record['mediaNbr'])),
		}
	}

	const handleOnChange = (onSelectedRowKeys, selectedRows) => {
		setSelectedRowKeys(onSelectedRowKeys)
		setChooseKeyData(selectedRows[0].mediaNbr)
	}

	const ukeyChooseColumn = () => {
		return [
			{
				dataIndex: 'mediaNbr',
				title: 'UKEY序列号',
				render: (text, record) => {
					return (
						<span
							style={
								validity === 'valid' &&
								((!record.mediaNbr.startsWith('CN=JRCBHB') && !record.mediaNbr.startsWith('CN=JRCBFTG')) ||
									!ukeyHaveList.includes(getSingleUkeyNo(record.mediaNbr)))
									? { opacity: 0.5 }
									: { opacity: 1 }
							}
						>
							{text}
						</span>
					)
				},
			},
			{
				dataIndex: 'action',
				title: validity === 'valid' ? '有效说明' : '无效说明',
				render: (text, record) => {
					const mediaNbr = record['mediaNbr']
					let content = ''
					if (!mediaNbr.startsWith('CN=JRCBHB') && !mediaNbr.startsWith('CN=JRCBFTG')) {
						content = '版本错误'
					} else if (!ukeyHaveList.includes(getSingleUkeyNo(mediaNbr))) {
						content = '非本企业UKEY'
					} else if (createUkeyNo === getSingleUkeyNo(mediaNbr)) {
						content = '与开立UKEY重复'
					} else {
						content = '有效'
					}
					return (
						<span
							style={
								validity === 'valid' &&
								((!mediaNbr.startsWith('CN=JRCBHB') && !mediaNbr.startsWith('CN=JRCBFTG')) || !ukeyHaveList.includes(getSingleUkeyNo(mediaNbr)))
									? { opacity: 0.5 }
									: { opacity: 1 }
							}
						>
							{content}
						</span>
					)
				},
			},
		]
	}

	const sumitUkeyForAuditCou = () => {
		if (chooseKeyData) {
			setUkeyChooseModal(false)
			auditTransfer(auditValue, auditTitle, auditType, chooseKeyData)
		} else {
			message.error('请选择需要使用的UKEY')
		}
	}

	return (
		<Box>
			<Spin tip="" spinning={ukeyLoading}>
				<LayoutSlot>
					<div className="card-wrapper">
						{isUkeyAlert && dataSource && dataSource.length ? (
							<Alert
								message="提示:开立审核请插入本企业UKEY并确认UKEY背部序列号是JRCBHB或JRCBFTG开头，且与操作员发起开立时用的不是同一把UKEY"
								type="warning"
								description=" "
								style={{ margin: '0', paddingLeft: '50px' }}
							></Alert>
						) : (
							''
						)}
						<BaseTable
							{...auditTransferPagination}
							columns={getColumns()}
							onPageChange={handleChange}
							onSizeChange={handleSizeChange}
							loading={loading}
							dataSource={dataSource}
							rowKey="transferUuid"
						/>
					</div>
				</LayoutSlot>
				<AuditModal
					title={transferAuditTitle}
					onCancel={() => {
						setTransferAuditVisible(false)
						clearTimeout()
					}}
					onSubmit={submitAuditData}
					visible={transferAuditVisible}
					type={transferAuditType}
					confirmLoading={auditConfirmLoading}
					customMode={() => {
						return isCheckUkey && ukeyCheckResult?.checkUkeyresult ? ukeyAlert({ checkUkeyErrorResult: ukeyCheckResult.checkUkeyErrorResult }) : null
					}}
					isBtnDisabled={isCheckUkey && ukeyCheckResult?.checkUkeyresult}
				/>
				<Modal className="warnmodal" open={auditAlertVisible} footer={null} closable={false}>
					<Row>
						<Col span={2}>
							<ExclamationCircleFilled className="warnicon" style={{ fontSize: '26px', color: '#EFB041' }} />
						</Col>
						<Col span={22}>
							<h4 className="headtxt">您需要先激活此金融机构，才可以进行业务操作</h4>
							<p>请联系企业管理员登录系统，激活此金融机构</p>
						</Col>
					</Row>

					<Row style={{ justifyContent: 'center' }}>
						<Button
							type="primary"
							onClick={() => {
								setAuditAlertVisible(false)
							}}
						>
							我知道了
						</Button>
					</Row>
				</Modal>
				<Modal
					title="提示"
					open={errorUkeyModal}
					maskClosable={false}
					footer={null}
					onCancel={() => {
						setErrorUkeyModal(false)
						setUkeyLoading(false)
					}}
				>
					<div>
						<div>
							<div>未检测到有效的企业UKEY，请插入UKEY并确认UKEY背部序列号是JRCBHB或JRCBFTG开头;</div>
							<div style={{ marginTop: '20px' }}>UKEY检测：</div>
							<div style={{ marginTop: '20px' }}>
								<Table
									bordered
									locale={{ emptyText: '未插入UKEY' }}
									columns={ukeyChooseColumn()}
									dataSource={errorUkeyArray}
									pagination={false}
									className="tableBlock"
								/>
							</div>
						</div>
					</div>
				</Modal>
				<Modal
					title="选择UKEY"
					open={ukeyChooseModal}
					maskClosable={false}
					footer={
						validity === 'valid' ? (
							<Button type={'primary'} onClick={sumitUkeyForAuditCou}>
								确定
							</Button>
						) : null
					}
					onCancel={() => {
						setUkeyChooseModal(false)
						setUkeyLoading(false)
					}}
				>
					<div>
						<div>
							<div>检测您插入了多把UKEY，请选择需要使用的UKEY</div>
							<div style={{ marginTop: '20px' }}>选择UKEY：</div>
							<div style={{ marginTop: '20px' }}>
								<BaseTable
									columns={ukeyChooseColumn()}
									dataSource={errorUkeyArray}
									havePagination={false}
									rowSelection={
										validity === 'valid'
											? {
													type: 'radio',
													getCheckboxProps: getCheckBoxState,
													onChange: handleOnChange,
													selectedRowKeys: selectedRowKeys,
											  }
											: {}
									}
								/>
							</div>
						</div>
					</div>
				</Modal>
			</Spin>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.options_btn {
		span {
			display: inline-block;
		}
		span:nth-child(1) {
			margin-right: 16px;
		}
	}
	.warnalert {
		position: fixed;
		top: 50px;
	}
	.auditTableInfoTest {
		width: 20px;
		height: 20px;
		background-color: rgb(64, 197, 133);
		color: white;
		line-height: 20px;
		text-align: center;
		border-radius: 4px;
	}

	.card-wrapper .ant-table-wrapper .ant-table-container table > thead > tr:first-child th:first-child {
		padding-left: 37px !important;
	}
	.ant-table-container table > tbody > tr td:first-child {
		padding-left: 0px !important;
	}

	.pay-icon,
	.receive-icon {
		width: 28px;
		height: 28px;
		margin: -5px 4px 0 15px;
	}
`

export default WaitAuditList
