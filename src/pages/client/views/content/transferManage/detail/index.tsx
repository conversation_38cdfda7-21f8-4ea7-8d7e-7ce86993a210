import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import styled from 'styled-components'
import SmallTitle from '@src/globalComponents/SmallTitle'
import Baseinfo from '../components/Baseinfo'
import ukeyAlert from '@src/pages/client/components/ukeyAlert'
import { checkUkey, handleUkey, clearTimeout, isCheckUkey, getCheckUkeyMsg } from '@src/pages/client/components/checkUkey'
import { Table, Tooltip, message, Button, Spin, Alert, Modal } from 'antd'
import { transferModuleApi, commonModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import DetailModal from '@clientComponents/detailModal'
import PDFViewer from '@globalComponents/PDFViewer'
import { history } from '@src/utils/router'
import AttachmentTable from '@src/globalComponents/EditTable/AttachmentTable'
import { businessProtocolMap } from '@src/globalConfig/protocolConfig'
import ChainInfoModal from '@src/globalComponents/chainInfoComponents/ChainInfoModal'
import { couDataOnChain, getCurrentRole, getStorage } from '@src/pages/client/biz/bizIndex'
import AuditModal from '@src/globalComponents/AuditModal'
import { useLocation, useNavigate } from 'react-router-dom'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import BaseTable from '@globalComponents/BaseTable'

let hasOpened = false

function detail() {
	const attachmentTable = React.useRef(null as any)
	// const transferUuid = history.location.state?.DetailForTransfer
	const { state } = useLocation() as any
	const transferUuid = state?.DetailForTransfer
	const isFromAudit = state?.isFromAudit || false
	const auditData = state?.auditData || {}
	const isUkey = state?.isUkey || false
	const [detailInfo, setDetailInfo] = useState<any>()
	const [visible, setVisible] = useState<boolean>(false)
	const [pdfPath, setPdfPath] = useState<string>('')
	const [attachmentInfoList, setAttachmentInfoList] = useState<object[]>([])
	const [detailVisible, setDetailVisible] = useState(false)
	const [chainInfoModalVisible, setChainInfoModalVisible] = useState<boolean>(false)
	const [onChainData, setOnChainData] = useState({})
	const [invoiceList, setInvoiceList] = useState([])
	const [invoiceData, setInvoiceData] = useState<any>({})
	const [DetailTitle, setDetailTitle] = useState<string>('')
	const [DetailType, setDetailType] = useState<string>('')
	const [contractData, setContractData] = useState<any>({
		contractCode: '',
		name: '', // 合同名称
		companySellerName: '',
		companyBuyerName: '',
		productName: '', // 产品名称
		amount: '', // 合同金额
		startDate: [], // 开始时间
		endDate: [], // 结束时间
		signDate: '', // 签订日期
	})
	const [transferAuditVisible, setTransferAuditVisible] = useState(false)
	const [transferAuditType, setTransferAuditType] = useState('agree')
	const [transferAuditTitle, setTransferAuditTitle] = useState('确定通过？')
	const [auditConfirmLoading, setAuditConfirmLoading] = React.useState(false)
	const [ukeyCheckResult, setUkeyCheckResult] = useState<any>({})
	const [ukeyCountersign, setUkeyCountersign] = useState(null)
	const [ukeyLoading, setUkeyLoading] = useState(false)
	const [isUkeyAlert, setIsUkeyAlert] = useState(false)
	//判断是否是最后一个审核者
	const [isLastAuditorPerson, setIsLastAuditor] = React.useState(false)
	const navigate = useNavigate()

	const [errorUkeyArray, setErrorUkeyArray] = useState<any[]>([])
	const [errorUkeyModal, setErrorUkeyModal] = useState<boolean>(false)
	const [ukeyChooseModal, setUkeyChooseModal] = useState<boolean>(false)
	const [validity, setValidity] = useState<string>('')
	const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([])
	const [chooseKeyData, setChooseKeyData] = useState<string>('')
	const [ukeyHaveList, setUkeyHaveList] = useState<any[]>([])
	const [createUkeyNo, setCreateUkeyNo] = useState<string>('')

	const [auditTitle, setAuditTitle] = useState<string>('')
	const [auditType, setAuditType] = useState<string>('')

	useEffect(() => {
		if (transferUuid) {
			getDetail(transferUuid)
			getInvoiceList(transferUuid)
		}
		getUkeyAlert()
		return () => {
			clearTimeout()
		}
	}, [])

	useEffect(() => {
		if (!detailInfo) return
		const { attachment: attachmentStr } = detailInfo
		setAttachmentInfoList(JSON.parse(attachmentStr || '[]'))
	}, [JSON.stringify(detailInfo)])

	const getUkeyAlert = async () => {
		let checkUkeyMsg = await getCheckUkeyMsg()
		setIsUkeyAlert(checkUkeyMsg)
	}
	const getInvoiceList = (paramsTransferUuid: string) => {
		transferModuleApi
			.getTransferInvoices({
				id: paramsTransferUuid,
			})
			.then(
				res => {
					setInvoiceList(res.list)
				},
				error => console.log(error)
			)
	}
	const getDetail = (paramsTransferUuid: string) => {
		transferModuleApi
			.getTransferDetail({
				transferUuid: paramsTransferUuid,
			})
			.then(
				res => {
					setDetailInfo(res)
				},
				error => console.log(error)
			)
	}

	//查看pdf  承诺函
	const viewPdf = (url: string) => {
		if (url) {
			setPdfPath(url)
			setVisible(true)
		}
	}

	// 点击合同编号
	const clickContractCode = (record, type, title) => {
		console.log('record', record)
		setDetailVisible(true)
		setContractData(record)
		setDetailTitle(title)
		setDetailType(type)
	}

	const getTransferColumns = type => {
		const columnsDic = {
			invoiceNumber: {
				unit: 18,
				dataIndex: 'invoiceNumber',
				render: (text: any, record: any) => {
					return (
						<Tooltip title={text} className="link">
							<span
								onClick={() => {
									clickContractCode(record, 'invoice', '查看发票')
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			contractCode: {
				render: (text: any, record: any) => {
					return (
						<Tooltip placement="topLeft" className="link" title={text}>
							<span
								onClick={() => {
									clickContractCode(record, 'contract', '查看合同')
								}}
							>
								{text}
							</span>
						</Tooltip>
					)
				},
			},
			fileInfo: {
				dataIndex: 'fileInfo',
				title: '查看',
				render: (text: any, record: any) => {
					let getProtocol: any = {}
					if (text) {
						getProtocol = JSON.parse(text).protocolList[0]
					}
					console.log(getProtocol.type)
					const protocolName = businessProtocolMap.filter(item => item.type === getProtocol.type)[0]?.protocolName || ''
					return (
						<span className="link" onClick={() => viewPdf(getProtocol.url ? getProtocol.url : null)}>
							{protocolName}
						</span>
					)
				},
			},
			onChainCertificate: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => showCertificate(record)}>
							查看证书
						</span>
					)
				},
			},
		}
		let colns = getColumnsByPageName(type, columnsDic)
		// 表格列长度的简写形式,不写，即均分长度
		let columnIndexLengthMap = {}
		colns = evenlyTableColunms(colns, columnIndexLengthMap)
		return colns
	}
	const showCertificate = record => {
		commonModuleApi
			.queryCouProof({ couNo: record.couNo })
			.then(data => {
				if (data && data.status === 'DONE') {
					setOnChainData(data)
					setChainInfoModalVisible(true)
				} else {
					message.warning('证书未生成，完成审批且上链后生成证书')
				}
			})
			.catch(err => {
				console.log(err)
			})
	}

	const handleUkeyAlert = async (params: any) => {
		if (params.type === 'result') {
			setUkeyCheckResult(params)
		} else {
			setValidity(params.validity)
			setErrorUkeyArray(params.ukeyArray)
			if (!hasOpened) {
				if (params.validity === 'invalid') {
					setErrorUkeyModal(true)
					setSelectedRowKeys([])
					setChooseKeyData('')
				} else {
					setUkeyChooseModal(true)
					setSelectedRowKeys([])
					setChooseKeyData('')
				}
			} else {
				hasOpened = true
			}
		}
	}

	const resetUkeyInfo = () => {
		clearTimeout()
		setUkeyCheckResult({ checkUkeyresult: false, checkUkeyErrorResult: '' })
	}
	const auditTransfer = async (title, type, chooseKeyData) => {
		setAuditTitle(title)
		setAuditType(type)
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let companyUuid: string = ext.user && ext.user.company ? ext.user.company.uuid : ''
		try {
			if (isUkey) {
				setUkeyLoading(true)
				resetUkeyInfo()
				let callUkeyParams = await checkUkey(auditData)
				if (callUkeyParams) {
					const ukeylist = callUkeyParams['ukeylist']
					const checkData = callUkeyParams['checkData']
					let tmpUkeyList = []
					ukeylist.forEach(item => {
						tmpUkeyList.push(item.mediaNbr)
					})
					setUkeyHaveList(tmpUkeyList)
					setCreateUkeyNo(checkData.ukeyNo)
					let signInfo = await handleUkey({ ...callUkeyParams, value: auditData, chooseKeyData, handleUkeyAlert })
					if (signInfo?.info) {
						if (!signInfo.signedText || signInfo.signedText === 'E:1000') {
							resetUkeyInfo()
							setTransferAuditVisible(false)
							setUkeyLoading(false)
							return
						}
						if (!signInfo.signedText || signInfo.signedText === 'E:1000') {
							resetUkeyInfo()
							setTransferAuditVisible(false)
							setUkeyLoading(false)
							return
						}
						setUkeyCountersign({ ...signInfo })
					}
				}
			}
			let isLastAuditor: any = await commonModuleApi
				.isLastFlowPerson({
					companyUuid,
					role: getCurrentRole()?.roleCode || '',
					taskId: auditData.auditTransferData.id,
				})
				.catch(err => {
					console.log('err', err)
				})

			setIsLastAuditor(isLastAuditor)
			setTransferAuditType(type)
			setTransferAuditTitle(title)
			setTransferAuditVisible(true)
			setUkeyLoading(false)
		} catch (e) {
			setUkeyLoading(false)
		}
	}

	const submitAuditData = async submitData => {
		if (isUkey && isCheckUkey && ukeyCheckResult?.checkUkeyresult) {
			return
		}
		setAuditConfirmLoading(true)
		let ext = JSON.parse(localStorage.getItem('ext') || '{}')
		let companyType = ext.user && ext.user.company ? ext.user.company.type : ''
		let companyPubKey = ext.user && ext.user.company ? ext.user.company.pubKey : ''
		let isAgree = true
		if (transferAuditType !== 'agree') {
			isAgree = false
		}
		if (companyType && companyPubKey) {
			//判断是否从缓存中获取到了必要字段
			let queryData = {
				isAgree: isAgree, //是否同意
				payCompanyType: companyType,
				toPubKey: companyPubKey,
				transferUuid: transferUuid,
				agreeReason: submitData.comment,
				// skipExpression2: !haveAuditor,
			}
			if (isUkey && isCheckUkey) {
				queryData['ukeySign'] = ukeyCountersign?.signedText || null
				queryData['ukeyPublicKey'] = ukeyCountersign?.certificate || null
			}
			let param = {}
			param = {
				businessKey: auditData.auditTransferData.businessKey, // auditTransferData
				taskId: auditData.auditTransferData.id,
				outcome: transferAuditType,
				jsonParamStr: JSON.stringify(queryData),
			}
			commonModuleApi
				.doTask(param)
				.then(item => {
					clearTimeout()
					setTransferAuditVisible(false)
					setAuditConfirmLoading(false)
					if (item.returnCode === 17000) {
						message.success('处理成功')
						//判断是否是开立过来的cou
						// 更新该条数据的审核状态
						let isPublishCou = false
						if (auditData.transferCous.length === 1 && auditData.transferCous[0].uuid === auditData.transferCous[0].originalUuid) {
							isPublishCou = true
						}
						// if (isLastAuditorPerson) {
						//融信数据存证上链
						let couNoList = []
						couNoList = auditData.transferCous.map(cou => {
							return cou['couNo']
						})
						if (transferAuditType === 'agree') {
							//如果是同意，开立或者支付都要存证上链
							if (isLastAuditorPerson) {
								couDataOnChain(couNoList)
							}
						} else {
							//如果是拒绝，只有支付存证上链，开立不存证上链
							if (!isPublishCou) {
								couDataOnChain(couNoList)
							}
						}
						// }
						navigate('/content/couTransfer/waitAuditorList')
					} else if (item.returnCode === 30015) {
						message.error('重复操作，该项支付已完成处理')
					} else {
						// 删除关键字（银行接口异常）
						let returnDesc = item.returnDesc.replace('银行接口异常', '')
						message.error(returnDesc)
					}
				})
				.catch(err => {
					setAuditConfirmLoading(false)
				})
		} else {
			message.error('操作失败，请联系运营处理')
			setAuditConfirmLoading(false)
		}
	}

	const getSingleUkeyNo = str => {
		if (str) {
			const pattern = /CN=(\w+)/
			const match = str.match(pattern)
			return match ? match[1] : ''
		}
		return ''
	}

	const getCheckBoxState = record => {
		return {
			disabled:
				(!record.mediaNbr.startsWith('CN=JRCBHB') && !record.mediaNbr.startsWith('CN=JRCBFTG')) || !ukeyHaveList.includes(getSingleUkeyNo(record['mediaNbr'])),
		}
	}

	const handleOnChange = (onSelectedRowKeys, selectedRows) => {
		setSelectedRowKeys(onSelectedRowKeys)
		setChooseKeyData(selectedRows[0].mediaNbr)
	}

	const ukeyChooseColumn = () => {
		return [
			{
				dataIndex: 'mediaNbr',
				title: 'UKEY序列号',
				render: (text, record) => {
					return (
						<span
							style={
								validity === 'valid' &&
								((!record.mediaNbr.startsWith('CN=JRCBHB') && !record.mediaNbr.startsWith('CN=JRCBFTG')) ||
									!ukeyHaveList.includes(getSingleUkeyNo(record.mediaNbr)))
									? { opacity: 0.5 }
									: { opacity: 1 }
							}
						>
							{text}
						</span>
					)
				},
			},
			{
				dataIndex: 'action',
				title: validity === 'valid' ? '有效说明' : '无效说明',
				render: (text, record) => {
					const mediaNbr = record['mediaNbr']
					let content = ''
					if (!mediaNbr.startsWith('CN=JRCBHB') && !mediaNbr.startsWith('CN=JRCBFTG')) {
						content = '版本错误'
					} else if (!ukeyHaveList.includes(getSingleUkeyNo(mediaNbr))) {
						content = '非本企业UKEY'
					} else if (createUkeyNo === getSingleUkeyNo(mediaNbr)) {
						content = '与开立UKEY重复'
					} else {
						content = '有效'
					}
					return (
						<span
							style={
								validity === 'valid' &&
								((!mediaNbr.startsWith('CN=JRCBHB') && !mediaNbr.startsWith('CN=JRCBFTG')) || !ukeyHaveList.includes(getSingleUkeyNo(mediaNbr)))
									? { opacity: 0.5 }
									: { opacity: 1 }
							}
						>
							{content}
						</span>
					)
				},
			},
		]
	}

	const sumitUkeyForAuditCou = () => {
		if (chooseKeyData) {
			setUkeyChooseModal(false)
			auditTransfer(auditTitle, auditType, chooseKeyData)
		} else {
			message.error('请选择需要使用的UKEY')
		}
	}

	return (
		<Box>
			<Spin tip="" spinning={ukeyLoading}>
				<LayoutSlot>
					{isUkey && isUkeyAlert ? (
						<Alert
							message="提示:开立审核请插入本企业UKEY并确认UKEY背部序列号是JRCBHB或JRCBFTG开头，且与操作员发起开立时用的不是同一把UKEY"
							type="warning"
							description=" "
							style={{ paddingLeft: '50px' }}
						></Alert>
					) : (
						''
					)}
					<div className="card-wrapper">
						<SmallTitle text="基本信息" />
						{detailInfo && <Baseinfo data={detailInfo} />}
						<SmallTitle text="合同" />
						{detailInfo && (
							<Table
								rowKey="uuid"
								style={{ marginBottom: '20px' }}
								className="table-border-exclude-top mb20"
								columns={getTransferColumns('contractInDetail')}
								dataSource={detailInfo.contracts}
								pagination={false}
								bordered={false}
							/>
						)}
						{detailInfo?.transferType == 'CREATE' ? (
							<>
								<SmallTitle text="发票" />
								<Table
									rowKey="uuid"
									className="table-border-exclude-top mb20"
									columns={getTransferColumns('invoiceDetail')}
									dataSource={invoiceList}
									pagination={false}
								/>
							</>
						) : null}
						{}
						<SmallTitle text="融信明细" />
						{detailInfo && (
							<Table
								rowKey="couNo"
								className="table-border-exclude-top mb20"
								columns={getTransferColumns('payDetailsCouTransferInDetail')}
								dataSource={detailInfo.transferCous}
								pagination={false}
							/>
						)}
						<AttachmentTable ref={attachmentTable} isEdit={false} initialValues={attachmentInfoList} />
						<PDFViewer title="" visible={visible} onCancel={() => setVisible(false)} pdfUrl={pdfPath} />
						<DetailModal title={DetailTitle} pageName={DetailType} onCancel={() => setDetailVisible(false)} visible={detailVisible} dataSource={contractData} />
						{/* <DetailModal title="查看发票" pageName="invoice" onCancel={() => setDetailVisible(false)} visible={detailVisible} dataSource={invoiceData} /> */}
						{isFromAudit && (
							<div className="options_btn">
								<Button type="primary" danger onClick={() => auditTransfer('确定拒绝？', 'reject', '')} style={{ marginRight: 10 }}>
									拒绝
								</Button>
								<Button type="primary" onClick={() => auditTransfer('确定通过？', 'agree', '')}>
									通过
								</Button>
							</div>
						)}
					</div>
				</LayoutSlot>
				<ChainInfoModal visible={chainInfoModalVisible} onCancel={() => setChainInfoModalVisible(false)} chainData={onChainData} />
				<AuditModal
					title={transferAuditTitle}
					onCancel={() => {
						clearTimeout()
						setTransferAuditVisible(false)
					}}
					onSubmit={submitAuditData}
					visible={transferAuditVisible}
					type={transferAuditType}
					confirmLoading={auditConfirmLoading}
					customMode={() => {
						return isUkey && isCheckUkey && ukeyCheckResult?.checkUkeyresult ? ukeyAlert({ checkUkeyErrorResult: ukeyCheckResult.checkUkeyErrorResult }) : null
					}}
					isBtnDisabled={isUkey && isCheckUkey && ukeyCheckResult?.checkUkeyresult}
				/>
				<Modal
					title="提示"
					open={errorUkeyModal}
					maskClosable={false}
					footer={null}
					onCancel={() => {
						setErrorUkeyModal(false)
						setUkeyLoading(false)
					}}
				>
					<div>
						<div>
							<div>未检测到有效的企业UKEY，请插入UKEY并确认UKEY背部序列号是JRCBHB或JRCBFTG开头;</div>
							<div style={{ marginTop: '20px' }}>UKEY检测：</div>
							<div style={{ marginTop: '20px' }}>
								<Table
									bordered
									locale={{ emptyText: '未插入UKEY' }}
									columns={ukeyChooseColumn()}
									dataSource={errorUkeyArray}
									pagination={false}
									className="tableBlock"
								/>
							</div>
						</div>
					</div>
				</Modal>
				<Modal
					title="选择UKEY"
					open={ukeyChooseModal}
					maskClosable={false}
					footer={
						validity === 'valid' ? (
							<Button type={'primary'} onClick={sumitUkeyForAuditCou}>
								确定
							</Button>
						) : null
					}
					onCancel={() => {
						setUkeyChooseModal(false)
						setUkeyLoading(false)
					}}
				>
					<div>
						<div>
							<div>检测您插入了多把UKEY，请选择需要使用的UKEY</div>
							<div style={{ marginTop: '20px' }}>选择UKEY：</div>
							<div style={{ marginTop: '20px' }}>
								<BaseTable
									columns={ukeyChooseColumn()}
									dataSource={errorUkeyArray}
									havePagination={false}
									rowSelection={
										validity === 'valid'
											? {
													type: 'radio',
													getCheckboxProps: getCheckBoxState,
													onChange: handleOnChange,
													selectedRowKeys: selectedRowKeys,
											  }
											: {}
									}
								/>
							</div>
						</div>
					</div>
				</Modal>
			</Spin>
		</Box>
	)
}

const Box = styled.div`
	.options_btn {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 40px;
		span {
			padding: 0 10px;
		}
	}
	.titleTag {
		border-bottom: 0;
	}
`
export default detail
