import React, { useEffect, useState } from 'react'
import LayoutSlot from '@src/globalComponents/LayoutSlot'
import BaseTable from '@src/globalComponents/BaseTable'
import styled from 'styled-components'
import SearchBar from '@src/pages/client/components/SearchBar'
import OperatingArea from '@src/globalComponents/OperatingArea'
import { Tooltip, message, Button } from 'antd'
import { transferModuleApi, commonModuleApi } from '@src/pages/client/api'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import { deepExtend } from '@utils/util'
import { searchCouPageList } from '@factor/config/cou/couAssetsConfig'
import { getFlowElementStatus } from '@utils/dataClean'
import FlowChartModal from '@src/globalComponents/FlowChartModal'
import { stampToTime } from '@src/utils/timeFilter'
import { history } from '@src/utils/router'
import { downloadFileByFileFlow } from '@src/utils/exportBlob'
import { getCurrentRole } from '@factor/biz/bizIndex'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import { useLocation } from 'react-router-dom'
const receiveIcon = require('@src/assets/images/transfer/receive-icon.png')
const payIcon = require('@src/assets/images/transfer/pay-icon.png')

const optionsDate = {
	accrualPrinciple: [
		{ key: 'RECEIVEABLE', name: '收款' },
		{ key: 'PAY', name: '付款' },
	],
	status: searchCouPageList,
}

const initValues = {
	accrualPrinciple: 'RECEIVEABLE',
}

interface FlowManageProps {
	limitOperator: boolean
	searchBarPageName: string
}

const FlowManage = (props: FlowManageProps) => {
	const { limitOperator, searchBarPageName } = props
	const user = JSON.parse(localStorage.getItem('ext') || '{}')?.user
	const { pubKey } = user?.company || {} //该用户的公司名称和类型
	const { loginCompanyType } = user || {}
	const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 }) //表格 配置
	const [loading, setLoading] = useState(true) //表格加载
	const [dataSource, setDataSource] = useState<any[]>([]) //表格数据
	const [exportLoading, setExportLoading] = React.useState(false)
	const [flowChartVisible, setFlowChartVisible] = useState(false) // 审核流传图的弹窗
	const [flowChartData, setFlowChartData] = useState<any[]>([]) // 流转图数据
	const [flowChartType, setFlowChartType] = useState<string>('pay') // 流转图类型
	const location = useLocation()
	const state = location?.state as any
	const status = state?.status
	const accrualPrinciple = state?.accrualPrinciple
	const [searchParams, setSearchParams] = useState<any>({ status: status, accrualPrinciple: accrualPrinciple }) //searchbar 字段

	useEffect(() => {
		getList()
	}, [pagination.current, pagination.pageSize, searchParams])

	const getList = () => {
		setLoading(true)
		transferModuleApi
			.getTransferList({
				...searchParams,
				pageNum: pagination.current,
				pageSize: pagination.pageSize,
				// Pledge-529
				orgType: (getCurrentRole() || {}).supportOrgType,
				limitOperator: limitOperator,
			})
			.then(
				(res: any) => {
					let data = deepExtend({}, res)
					pagination.total = data['total']
					if (data.list) {
						data.list.forEach((i: { key: any; transferUuid: any; fromCouPubKey: any; peeOrPay: any }) => {
							i.key = i.transferUuid
							if (i.fromCouPubKey === pubKey) {
								i.peeOrPay = '付款'
							} else {
								i.peeOrPay = '收款'
							}
						})
						setDataSource([...data['list']])
					}
					setLoading(false)
				},
				err => setLoading(false)
			)
	}

	//查看数据详情
	const checkdetail = values => {
		const { transferUuid } = values
		history.push('/content/couTransfer/payDetails', {
			DetailForTransfer: transferUuid,
		})
	}
	// 获取融信流转的流程图数据
	const getFlowChartData = record => {
		let businessKey = record.transferUuid
		commonModuleApi.getTransferChartData({ businessKey }).then(
			res => {
				if (res.returnCode == 17000) {
					setFlowChartVisible(true)
					// 设置审核流程图的类型
					setFlowChartType(res.data.processDefKey)
					let result = getFlowElementStatus(res.data)
					setFlowChartData(result)
				} else {
					message.error('此项数据暂不支持查看流程图')
				}
			},
			reason => {
				console.log('reason', reason)
			}
		)
	}

	//search 提交 事件
	const onSubmit = (params: { accrualPrinciple: string; status: string; transferNo: string }) => {
		pagination.current = 1
		// setPagination({ ...pagination })
		setSearchParams({ ...params })
	}
	//Search 重置
	const onClear = () => {
		pagination.current = 1
		setSearchParams({})
	}

	//表格改变 当前页面
	const handleChange = (current: number) => {
		pagination.current = current
		setPagination({ ...pagination })
	}
	//表格改变 改变 size
	const handleSizeChange = (size: number) => {
		pagination.current = 1
		pagination.pageSize = size
		setPagination({ ...pagination })
	}

	//获取合同的表格列
	const getColumns = () => {
		const columnsDic = {
			transferNo: {
				width: 240,
				render: (text, record) => {
					return (
						<Tooltip title={text}>
							<div style={{ display: 'flex', alignItems: 'center', height: '20px' }}>
								<div style={{ verticalAlign: 'middle', height: '20px' }}>
									{record.fromCouPubKey === pubKey ? <img src={payIcon} className="pay-icon" /> : <img src={receiveIcon} className="receive-icon" />}
								</div>
								&nbsp;
								<div onClick={() => checkdetail(record)} className="transferNoStyle">
									{text}
								</div>
							</div>
						</Tooltip>
					)
				},
			},

			transferListStatus: {
				width: 110,
			},
			transferListAmount: {
				width: 130,
			},

			operator: {
				render: (_, record) => {
					if (record.peeOrPay === '付款') {
						return <Tooltip title={record.operator}>{record.operator ? record.operator : '- -'}</Tooltip>
					} else {
						return '- -'
					}
				},
			},
			operation: {
				render: (text, record) => {
					return (
						<span className="link" onClick={() => getFlowChartData(record)}>
							审核流程
						</span>
					)
				},
			},
		}
		let colns = getColumnsByPageName('Transferlist', columnsDic)
		// 表格列长度的简写形式
		let columnIndexLengthMap = {
			0: 20,
			2: 11,
			1: 9,
			3: 17,
			4: 17,
		}
		colns = evenlyTableColunms(colns, columnIndexLengthMap)
		return colns
	}

	//导出流转管理信息
	const exportCouCsv = async () => {
		setExportLoading(true)
		let params = {
			...searchParams,
			pageNum: 1,
			pageSize: 2147483646,
			limitOperator: limitOperator,
		}
		CsvFile(params, [])
	}
	const CsvFile = async (params: any, dataList: any) => {
		const serverTime = await commonModuleApi.syncTime().catch(err => {
			console.log('err', err)
		})
		const fileName = `Download_${stampToTime(serverTime, 9)}.xlsx`
		transferModuleApi
			.transferExport(params)
			.then((response: any) => {
				let type = 'application/vnd.ms-excel'
				downloadFileByFileFlow(response, type, fileName, () => {
					setExportLoading(false)
					message.success('导出成功')
				})
			})
			.catch(() => {
				message.error('导出失败')
				setExportLoading(false)
			})
	}
	return (
		<Box>
			<LayoutSlot>
				<div className="card-wrapper">
					<OperatingArea>
						<SearchBar
							showLabel={false}
							pageName={searchBarPageName}
							optionData={optionsDate}
							onClear={onClear}
							onSubmit={onSubmit}
							initValues={{ ...searchParams, needClear: true }}
						/>
						{/* <Button style={{ marginLeft: '20px' }} type="primary" onClick={exportCouCsv} loading={exportLoading}>
							导出
						</Button> */}
					</OperatingArea>
					<BaseTable
						{...pagination}
						columns={getColumns()}
						onPageChange={handleChange}
						onSizeChange={handleSizeChange}
						loading={loading}
						dataSource={dataSource}
					/>
				</div>
			</LayoutSlot>
			<FlowChartModal
				type={flowChartType}
				data={flowChartData}
				visible={flowChartVisible}
				onCancel={() => {
					setFlowChartVisible(false)
				}}
			></FlowChartModal>
		</Box>
	)
}

const Box = styled.div`
	width: 100%;
	height: 100%;
	.options_btn {
		span {
			display: inline-block;
			margin-right: 15px;
		}
	}
	.listTableInfoTest {
		width: 20px;
		height: 20px;
		background-color: rgb(64, 197, 133);
		color: white;
		line-height: 20px;
		text-align: center;
		border-radius: 4px;
	}
	.payTextColor {
		background-color: rgb(64, 197, 133);
	}
	.textColor {
		background-color: rgb(86, 166, 254);
	}
	.transferNoStyle {
		color: #02a7f0;
		cursor: pointer;
		height: 20px;
		line-height: 20px;
	}

	.card-wrapper .ant-table-wrapper .ant-table-container table > thead > tr:first-child th:first-child {
		padding-left: 37px !important;
	}
	.ant-table-container table > tbody > tr td:first-child {
		padding-left: 0px !important;
	}

	.pay-icon,
	.receive-icon {
		width: 28px;
		height: 28px;
		margin: -5px 4px 0 15px;
	}
`

export default FlowManage
