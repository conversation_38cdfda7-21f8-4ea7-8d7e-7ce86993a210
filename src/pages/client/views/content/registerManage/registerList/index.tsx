/*
 * @Descripttion: 登记列表
 * @Version: 1.0
 * @Author: chenyan
 * @Date: 2023-03-23 16:07:14
 * @LastEditors: chenyan
 * @LastEditTime: 2023-04-04 17:29:54
 */
import React from 'react'
import { getColumnsByPageName } from '@src/pages/client/config/TableColumnsConfig'
import { registerManageModuleApi } from '@src/pages/client/api'
import SearchBar from '@src/pages/client/components/SearchBar'
import CommonRegisterList from '@globalComponents/cross-platform-page/registerManage/registerList'

const RegisterList: React.FC = () => {
	const apiModule = {
		getRegisterList: registerManageModuleApi.getRegisterList,
		downloadRegisterAttachment: registerManageModuleApi.downloadRegisterAttachment,
	}
	return (
		<CommonRegisterList
			apiModule={apiModule}
			getColumnsByPageName={getColumnsByPageName}
			SearchBar={SearchBar}
			detailUrl={'/content/registerManage/registerDetail'}
			changRecordDetailUrl={'/content/registerManage/changeDetail'}
		></CommonRegisterList>
	)
}
export default RegisterList
