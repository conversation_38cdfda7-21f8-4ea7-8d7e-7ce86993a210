import React from 'react'
import { registerManageModuleApi } from '@src/pages/client/api'
import CommonRegisterDetail from '@globalComponents/cross-platform-page/registerManage/registerDetail'

const RegisterDetail: React.FC = () => {
	const apiModule = {
		getRegisterDetail: registerManageModuleApi.getRegisterDetail,
		downloadPledgeAttachment: registerManageModuleApi.downloadPledgeAttachment,
	}
	return <CommonRegisterDetail apiModule={apiModule}></CommonRegisterDetail>
}
export default RegisterDetail
