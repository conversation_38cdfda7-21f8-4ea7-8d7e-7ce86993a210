import React, { useEffect } from 'react'
import { Layout, message, Modal, Alert, Table } from 'antd'
import styled from 'styled-components'
import { Outlet, useNavigate } from 'react-router-dom'
//引入子组件
import NavLayout from '@src/globalComponents/NavLayout'
import BreadcrumbLayout from '@src/globalComponents/BreadcrumbLayout'
import UserInfo from '@src/globalComponents/UserInfo'
import loginApis from '@src/pages/client/api/login'
import { history } from '@src/utils/router'
import { uiData } from '@src/pages/client/store'
import { observer } from 'mobx-react-lite'
import { getStorage, setStorage, initInnerPagesName, hasAuth } from '@src/pages/client/biz/bizIndex'
import { getColumnsByPageName } from '@clientComponents/../config/TableColumnsConfig'
import BaseTable from '@src/globalComponents/BaseTable'
import { getFrontCodeListForAdmin, getRealMenuList, getFirstPagePath } from '@src/pages/client/biz/bizIndex'
import { financeModuleApi } from '@src/pages/client/api'
import { evenlyTableColunms } from '@src/globalBiz/gBiz'
import PDFViewer from '@src/globalComponents/PDFViewer'
import { toJS } from 'mobx'
const { Content } = Layout

function MainContent() {
	// const userNavList = getStorage('menuList')
	const [modal, contextHolder] = Modal.useModal()
	const navigator = useNavigate()
	const ext = JSON.parse(localStorage.getItem('ext') || '{}')
	const userNavList = toJS(uiData?.menuList)
	const [getUserInfo, setUserInfo] = React.useState<any>(null)
	const [visible, setVisible] = React.useState<boolean>(false)
	const [pdfUrl, setPdfUrl] = React.useState<string>('')
	const handleLogout = async (cb: () => void) => {
		await loginApis.logout().catch(error => {
			console.log(error)
		})
		message.success('退出成功')
		history.push('/home')
		cb()
	}

	useEffect(() => {
		setTimeout(() => {
			const ext = JSON.parse(localStorage.getItem('ext') || '{}')
			const userInfo = ext.user || null
			if (!userInfo) {
				message.warning('未登录')
				// history.push('/home')
			} else {
				setUserInfo(userInfo)
			}

			//调用接口判读该用户是否有未处理的兑付信息(1.7.6需求)
			showCouReminder()

			// 150 增加应收账款质押通知
			if (hasAuth('pledge_web:login:pledgeNotice')) {
				showPledgeReminder()
			}
		}, 200)
	}, [])

	const showPledgeReminder = () => {
		let res = JSON.parse(localStorage.getItem('pledgeRemindData') || '[]')
		console.log('$$$$$$', res)
		if (res && Array.isArray(res) && res.length > 0) {
			const getCashTableColumns = () => {
				const columnsDic = {
					createTime: {
						title: '发送日期',
					},
					operation: {
						unit: 10,
						render: (text, record) => {
							return (
								<span className="link" onClick={() => openFile(record)}>
									查看
								</span>
							)
						},
					},
				}
				return evenlyTableColunms(getColumnsByPageName('pledgeRemindData', columnsDic))
			}
			modal.warning({
				title: '应收账款质押通知',
				width: 900,
				content: (
					<div>
						<Alert
							message={
								<div>
									您的企业新增了<span style={{ fontSize: '18px', fontWeight: 'bold' }}> {res.length} </span>笔应收账款质押通知。
								</div>
							}
							type="warning"
							showIcon
							style={{ marginBottom: '10px' }}
						/>
						<Table size="small" columns={getCashTableColumns()} dataSource={res} pagination={false} scroll={{ y: 260 }} />
					</div>
				),
				okText: '已知悉',
				onOk() {
					financeModuleApi
						.updatePledgeReminderList({ idList: res.map(item => item?.id) })
						.then(() => {
							localStorage.removeItem('pledgeRemindData')
						})
						.catch(() => {})
				},
			})
		}
	}

	const openFile = record => {
		setPdfUrl(record?.fileInfo)
		setVisible(true)
	}
	const showCouReminder = () => {
		let couCashRemindData = JSON.parse(localStorage.getItem('couCashRemindData') || '[]')
		if (couCashRemindData && Array.isArray(couCashRemindData) && couCashRemindData.length > 0) {
			let remindDataTable = (
				<BaseTable columns={getColumnsByPageName('couCashRemindData')} dataSource={couCashRemindData} havePagination={false} scroll={{ y: 260 }} />
			)
			let alertDescription = (
				<div>
					<div>若走银行清分渠道还款，请联系运营人员处理</div>
					<div>若为线下还款（二次支付货款），请在融信核销页面，发起融信核销</div>
				</div>
			)
			let remindAlert = (
				<Alert
					message={`您有${couCashRemindData.length}笔融信，已过兑付日期，仍未兑付成功，请及时关注并处理`}
					description={alertDescription}
					type="warning"
					showIcon
					style={{ marginBottom: '10px' }}
				/>
			)
			warning(remindAlert, remindDataTable)
		}
	}

	const warning = (remindAlert, remindDataTable) => {
		modal.warning({
			title: '融信兑付提醒',
			width: 900,
			content: (
				<div>
					{remindAlert}
					{remindDataTable}
				</div>
			),
			onOk() {
				localStorage.setItem('couCashRemindData', '[]')
			},
		})
	}

	/**
	 * 切换角色回调：重新处理新角色权限
	 * @param newRole
	 */
	const handleRoleSwitchCallback = newRole => {
		let { menuList: originMenuList, frontCodeList = [], roleList = [], roleId } = newRole || {}
		if (ext?.user?.adminFlag) {
			frontCodeList = getFrontCodeListForAdmin()
		}
		originMenuList = originMenuList || []
		frontCodeList = frontCodeList || []
		let realMenuList = getRealMenuList(frontCodeList, originMenuList)
		// 存储登录的一般信息
		setStorage({ frontCodeList, originMenuList, menuList: realMenuList, roleList, roleId })
		// 更新路由
		uiData.updateFrontCodeList(frontCodeList)
		uiData.updateMenuList(realMenuList)
		let menuList = getStorage('menuList')
		// 跳转到第一个页面
		let targetPath = getFirstPagePath(menuList)
		if (targetPath) {
			history.push(targetPath)
		}
	}
	// 检查路由是否带token，有的话检查token并进行登录
	const handleCheckToken = (nowToken?: any, preToken?: any) => {
		// 检查是否有token
		if (nowToken) {
			// 直接跳转base页面走baseInfo的登录
			navigator('/home/<USER>')
		}
	}

	useEffect(() => {
		window.onstorage = function (event) {
			if (event.key === 'token' && event.newValue !== event.oldValue) {
				// Token has been updated, refresh the page
				/**
				 * 当界面停留在正常菜单界面的时候
				 * 监听token变化的时候 需要重新获取用户信息
				 * 决定是否停留在当前界面还是去异常界面
				 */
				console.log('onstorage content page')
				handleCheckToken(event.newValue, event.oldValue)
			}
		}
	}, [])
	return (
		<MainContentWrapper>
			<Layout style={{ height: '100%' }}>
				<NavLayout userNavList={userNavList} title="江阴农商银行" uiData={uiData} {...{ setStorage, getStorage, initInnerPagesName }} showBusiChange={true} />
				<Layout>
					<UserInfo
						roleSwitchCallback={handleRoleSwitchCallback}
						user={getUserInfo}
						account={getUserInfo && getUserInfo.email ? getUserInfo.email : ''}
						onLogout={handleLogout}
					/>
					<div className="top-bar">
						<BreadcrumbLayout userNavList={userNavList} uiData={uiData} />
					</div>
					<Content className="content-box client_content">
						<Outlet />
					</Content>
				</Layout>
			</Layout>
			<PDFViewer title="" visible={visible} onCancel={() => setVisible(false)} pdfUrl={pdfUrl} />
			{/* modal.useModal 的配套context */}
			{contextHolder}
		</MainContentWrapper>
	)
}

const MainContentWrapper = styled.div`
	position: relative;
	height: 100vh;
	overflow: hidden;
	.top-bar {
		min-height: 54px;
		line-height: 54px;
		box-shadow: 0px 2px 3px 0px rgba(0, 21, 41, 0.03) inset;
		background: #ffffff;
		text-align: right;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 24px;
		-webkit-app-region: drag;
		.ant-breadcrumb li:last-child {
			color: rgba(0, 0, 0, 0.65);
			font-weight: 400;
			font-size: 14px;
		}
	}
	.content-box {
		margin: 24px;
		overflow-y: auto;
		background: #fff;
		padding: 24px 20px;
		border-radius: 2px;
		position: relative; /*个别页面 比如融信开立的顶部 有全宽度的border 需要定位*/
	}
	/* dashboard页面的背景要是灰色 */
	.client_content:has(.dashboard) {
		background: #f1f1f1;
		padding: 24px;
		margin: 0;
	}
`

export default observer(MainContent)
