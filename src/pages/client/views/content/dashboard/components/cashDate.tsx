import React from 'react'
import styled from 'styled-components'
interface Props {
	title: string
	amount: string
	count: string | number
	date?: string
	action: () => void
	isDue?: boolean
}
const CashDateCom = (props: Props) => {
	const { title, amount, count, date, action, isDue = false } = props
	const clickFun = () => {
		action?.()
	}
	return (
		<Wrapper>
			<div className={isDue ? 'due cash-date-box' : 'cash-date-box'}>
				<div className={isDue ? 'due cash-header' : 'cash-header'}>
					{isDue ? (
						<img src={require('@src/assets/images/dashboard/cash-date-due.png')} />
					) : (
						<img src={require('@src/assets/images/dashboard/cash-date.png')} />
					)}
					{title}
				</div>
				<div className="cash-body">
					<div>
						<div>总额</div>
						<div>
							<span className="amount">{amount}</span>元
						</div>
					</div>
					<div>
						<span>笔数：</span>{' '}
						<span className="clickCount" onClick={clickFun}>
							{count}
						</span>
					</div>
					{date && <div> {date}</div>}
				</div>
			</div>
		</Wrapper>
	)
}
const Wrapper = styled.div`
	display: inline-block;
	width: 100%;
	height: 100%;
	font-size: 14px;
	font-family: PingFangSC, PingFangSC-Regular;
	font-weight: 400;
	color: rgba(0, 0, 0, 0.65);

	.cash-date-box {
		background: #f6faff;
		border-radius: 16px;
		height: 100%;
	}
	.cash-date-box.due {
		background: #fffaf6;
		border-radius: 16px;
	}
	.cash-header {
		background: rgba(33, 45, 66, 7%);
		border-radius: 16px 16px 0 0;
		height: 46px;
		display: flex;
		align-items: center;

		> img {
			width: 10px;
			height: 10px;
			margin: 10px 6px 10px 10px;
		}
	}
	.cash-header.due {
		background: #f1ebe5;
	}
	.cash-body {
		padding: 10px 20px;
		> div {
			margin-bottom: 12px;
		}
	}
	.amount {
		font-size: 20px;
		font-family: PingFangSC, PingFangSC-Medium;
		font-weight: 500;
		color: rgba(0, 0, 0, 0.85);
		padding-right: 5px;
	}
	.clickCount {
		color: #1890ff;
		cursor: pointer;
		position: relative;
		text-decoration: underline;
	}
	/* .clickCount::after {
		content: '';
		width: 100%;
		height: 2px;
		position: absolute;
		bottom: 1px;
		background: #1890ff;
		right: 0px;
	} */
`
export default CashDateCom
