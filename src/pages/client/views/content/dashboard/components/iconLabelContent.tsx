import React, { ReactNode } from 'react'
import styled from 'styled-components'
interface Props {
	icon: ReactNode
	label: string
	content: string
	action?: () => void
}
const IconLabelContent = (props: Props) => {
	const { label, icon, content, action } = props

	const clickFun = () => {
		action?.()
	}
	return (
		<Wrapper>
			{icon}
			<span className="label">{label}</span>:
			<span className="num">
				<span className="clickU" onClick={clickFun}>
					{content}
				</span>
				<span className="label">笔</span>
			</span>
		</Wrapper>
	)
}
const Wrapper = styled.div`
	display: flex;
	align-items: center;
	padding: 14px 24px;
	height: 38px;
	background: #fbfdff;
	border-radius: 6px;
	box-sizing: border-box;
	margin: 15px 10px;
	> span {
		margin-left: 10px;
	}
	.label {
		font-size: 14px;
		font-weight: 400;
		text-align: left;
		color: rgba(0, 0, 0, 0.65);
		text-align-last: justify;
		margin-right: 3px;
		min-width: 62px;
	}
	.num {
		font-size: 18px;
		text-align: left;
		font-weight: bold;
		color: rgba(0, 0, 0, 0.85);
	}
	.clickU {
		color: #1890ff;
		cursor: pointer;
		position: relative;
		margin-right: 8px;
		text-decoration: underline;
	}
	/* .clickU::after {
		content: '';
		width: 100%;
		height: 2px;
		position: absolute;
		bottom: 3px;
		background: #1890ff;
		right: -1px;
	} */
`
export default IconLabelContent
