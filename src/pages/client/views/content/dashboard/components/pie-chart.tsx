import React, { CSSProperties, useEffect, useState } from 'react'
import * as echarts from 'echarts'
import { numberToThousands } from '@src/utils/timeFilter'
type EChartsOption = echarts.EChartsOption

interface PieChartProps {
	id: string
	style?: CSSProperties
	option?: EChartsOption
	data?: any
	text?: string
	isLegend?: boolean
	colorList?: string[]
	radius?: string[]
}

const PieChart = React.forwardRef((props: PieChartProps, ref: any) => {
	const [myChart, setMyChart] = useState<echarts.EChartsType>()

	React.useImperativeHandle(ref, () => ({
		onResize: () => myChart?.resize(),
	}))
	const { id, style, data, text, isLegend = false, colorList, radius } = props
	const defaultOption = {
		title: {
			show: false,
		},
		tooltip: {
			trigger: 'item',
			show: true,
			valueFormatter: value => '¥ ' + numberToThousands(value),
		},
		legend: {
			orient: 'vertical',
			left: 'left',
		},
		series: [
			{
				name: '开立额度',
				type: 'pie',
				radius: ['0%', '55%'],
				center: ['50%', '50%'],
				label: {
					formatter: params => {
						if (params?.data?.action) {
							// {zz${params.dataIndex}|}
							return `{a|${params.name}}\n{b${params.dataIndex}|¥ ${numberToThousands(params.value)}}\n{s${params.dataIndex}|}`
						} else {
							return `{a|${params.name}}\n{x|¥ ${numberToThousands(params.value)}}`
						}
					},
					minMargin: 5,
					edgeDistance: 40,
					lineHeight: 15,
					rich: {
						a: {
							color: 'rgba(0,0,0,0.65)',
							padding: [0, 0, 10, 0],
							fontSize: 13,
						},
						b0: {
							color: colorList[0],
							shadowBlur: 'true',
							fontSize: 15,
						},
						b1: {
							color: colorList[1],
							shadowBlur: 'true',
							fontSize: 15,
						},
						b2: {
							color: colorList[2],
							shadowBlur: 'true',
							fontSize: 15,
						},
						b3: {
							color: colorList[3],
							shadowBlur: 'true',
							fontSize: 15,
						},
						x: {
							color: 'rgba(0,0,0,0.65)',
							fontSize: 15,
						},
						zz0: {
							width: 8,
							height: 8,
							borderRadius: 4,
							backgroundColor: colorList[0],
						},
						zz1: {
							width: 8,
							height: 8,
							borderRadius: 4,
							backgroundColor: colorList[1],
						},
						zz2: {
							width: 8,
							height: 8,
							borderRadius: 4,
							backgroundColor: colorList[2],
						},
						s0: {
							borderColor: colorList[0],
							width: '100%',
							borderWidth: 1,
							height: 0,
							verticalAlign: 'top',
						},
						s1: {
							borderColor: colorList[0],
							width: '100%',
							borderWidth: 1,
							height: 0,
							verticalAlign: 'top',
						},
						s2: {
							borderColor: colorList[2],
							width: '100%',
							borderWidth: 1,
							height: 0,
							verticalAlign: 'top',
						},
						s3: {
							borderColor: colorList[3],
							width: '100%',
							borderWidth: 1,
							height: 0,
							verticalAlign: 'top',
						},
					},
				},
				labelLine: {
					show: true,
					// length: 10,
					// length2: 20,
					// minTurnAngle: 20,
				},
				labelLayout(params) {
					const radiusWidth = (myChart?.getHeight()! * 0.55) / 2
					const top = myChart?.getHeight()! / 2 - radiusWidth
					const bottom = top + myChart?.getHeight()! * 0.55
					const left = myChart?.getWidth()! / 2 - radiusWidth
					const right = myChart?.getWidth()! * 0.55 + left
					const cX = left + radiusWidth
					const cY = top + radiusWidth
					const isPointInCircle = (pointX: number, pointY: number, circleX: number = cX, circleY: number = cY, radius: number = radiusWidth) => {
						// 计算点到圆心的距离
						const distance = Math.sqrt((pointX - circleX) ** 2 + (pointY - circleY) ** 2)
						// 判断距离是否小于等于半径
						return distance <= radius
					}
					// console.log(params)
					// console.log(left, top, '(', cX, cY, ')', radiusWidth, isPointInCircle(params?.labelLinePoints?.[2]?.[0], params?.labelLinePoints?.[2]?.[1]))
					if (isPointInCircle(params?.labelLinePoints?.[2]?.[0], params?.labelLinePoints?.[2]?.[1])) {
						if (params?.align == 'right') {
							params.labelLinePoints[2][0] = left
						} else {
							params.labelLinePoints[2][0] = right
						}
						params.labelLinePoints[2][1] = bottom
					}
					return {
						...params,
						x: params?.labelLinePoints?.[2]?.[0],
						y: params?.labelLinePoints?.[2]?.[1],
					}
				},
				emphasis: {
					itemStyle: {
						shadowBlur: 10,
						shadowOffsetX: 0,
						shadowColor: 'rgba(0, 0, 0, 0.5)',
					},
					scaleSize: 1.1,
				},
				itemStyle: {
					borderRadius: 1,
					borderColor: '#fff',
					borderWidth: 1,
				},
				data: [],
			},
		],
	}
	const { option = defaultOption } = props

	if (text) option.title['text'] = text
	if (data) option.series[0].data = data
	if (colorList && colorList.length > 0) {
		option.series[0].color = colorList
	}
	option.legend['show'] = isLegend
	if (radius) option.series[0].radius = radius

	useEffect(() => {
		initPieChart(option)
	}, [data, option])

	const initPieChart = option => {
		const chartDom = document.getElementById(id)!
		const myChart = echarts.init(chartDom)
		setMyChart(myChart)
		option && myChart.setOption(option)
		myChart.on('click', function (params: any) {
			if (params?.event?.topTarget?.parent?.style?.rich) {
				params?.data?.action?.()
				console.log(params)
			}
		})
	}
	return <div id={id} style={style}></div>
})

export default PieChart
