import React, { ReactNode } from 'react'
import styled from 'styled-components'
interface Props {
	icon: ReactNode
	title: string
	action?: ReactNode
}
const TitleCom = (props: Props) => {
	const { title, icon, action } = props
	return (
		<Wrapper>
			<div className="title">
				{icon}
				<span className="txt">{title}</span>
			</div>
			<div className="action">{action}</div>
		</Wrapper>
	)
}
const Wrapper = styled.div`
	display: flex;
	align-items: center;
	padding: 14px 24px;
	border-bottom: 1px solid #cbd5e3;
	justify-content: space-between;

	.title {
		display: flex;
		align-items: center;

		.txt {
			margin-left: 12px;
			height: 24px;
			font-size: 16px;
			font-family: PingFangSC, PingFangSC-Medium;
			font-weight: bold;
			text-align: left;
			color: rgba(0, 0, 0, 0.85);
			line-height: 24px;
		}
	}
	.action {
	}
`
export default TitleCom
