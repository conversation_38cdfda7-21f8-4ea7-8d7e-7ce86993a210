import React, { useEffect, useRef } from 'react'
import { Button, Card, Col, Descriptions, Divider, Row, Space, Statistic } from 'antd'
import '@src/pages/client/assets/styles/common.less'
import { FileDoneOutlined } from '@ant-design/icons'
// import * as echarts from "echarts"

const backgroudStyle = {
	backgroundColor: '#f6faff',
}

const IconTitle = (title: string, action?: any) => {
	return (
		<Row justify={'space-between'}>
			<Col span={4}>
				<span>
					<FileDoneOutlined style={{ paddingRight: '5px' }} />
					{title}
				</span>
			</Col>
			<Col span={2}>{action}</Col>
		</Row>
	)
}

const NeedDeal = () => {
	return (
		<Card title={IconTitle('待办')} className="card-border" style={backgroudStyle}>
			<div style={{ display: 'flex', alignItems: 'center', height: '170px' }}>
				<Card.Grid className="card-grid-divider-rborder card-grid-divider-unshadow" hoverable={false}>
					<Statistic className="statistic-container" title={'融信接收/审核'} valueStyle={{ order: -1 }} value={8} />
				</Card.Grid>
				<Card.Grid className="card-grid-divider-rborder card-grid-divider-unshadow" hoverable={false}>
					<Statistic className="statistic-container" title={'融信支付审核'} valueStyle={{ order: -1 }} value={2} />
				</Card.Grid>
				<Card.Grid className="card-grid-divider-unshadow" hoverable={false}>
					<Statistic className="statistic-container" title={'融信审核'} valueStyle={{ order: -1 }} value={3} />
				</Card.Grid>
			</div>
		</Card>
	)
}

const BalanceAccount = () => {
	return (
		<Card title={IconTitle('结算账户')} className="card-border" style={backgroudStyle}>
			<div style={{ display: 'flex', alignItems: 'center', height: '170px' }}>
				<Card.Grid className="card-grid-divider-nborder card-grid-divider-unshadow" hoverable={false}>
					<img src="https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png" style={{ width: '100%', height: '100%' }} />
				</Card.Grid>
				<Card.Grid className="card-grid-divider-nborder card-grid-divider-unshadow" hoverable={false}>
					<Descriptions column={1}>
						<Descriptions.Item label="账号">6222********317</Descriptions.Item>
						<Descriptions.Item label="户名">6222********317</Descriptions.Item>
						<Descriptions.Item label="开户行">6222********317</Descriptions.Item>
					</Descriptions>
				</Card.Grid>
			</div>
		</Card>
	)
}

const FastEntry = () => {
	return (
		<Card title={IconTitle('快捷入口')} className="card-border">
			<div style={{ height: '170px' }}>
				<Row gutter={[15, 15]} justify={'space-around'} style={{ height: '100%' }}>
					<Col span={4} style={{ height: '100%' }}>
						<Button style={{ width: '100%', height: '100%' }}>
							<p>
								<FileDoneOutlined />
							</p>
							<p>融信支付</p>
						</Button>
					</Col>
					<Col span={4} style={{ height: '100%' }}>
						<Button style={{ width: '100%', height: '100%' }}>
							<p>
								<FileDoneOutlined />
							</p>
							<p>融信融资</p>
						</Button>
					</Col>
					<Col span={4} style={{ height: '100%' }}>
						<Button style={{ width: '100%', height: '100%' }}>
							<p>
								<FileDoneOutlined />
							</p>
							<p>发票上传</p>
						</Button>
					</Col>
					<Col span={4} style={{ height: '100%' }}>
						<Button style={{ width: '100%', height: '100%' }}>
							<p>
								<FileDoneOutlined />
							</p>
							<p>合同上传</p>
						</Button>
					</Col>
					<Col span={4} style={{ height: '100%' }}>
						<Button style={{ width: '100%', height: '100%' }}>
							<p>
								<FileDoneOutlined />
							</p>
							<p>我的融信</p>
						</Button>
					</Col>
				</Row>
			</div>
		</Card>
	)
}

const MyCredit = () => {
	// const myCreditRef = useRef(null)
	//
	// useEffect(() => {
	// 	let myCreditInstance = echarts.init(myCreditRef.current)
	// 	const myCreditOption = {}
	// 	myCreditInstance.setOption(myCreditOption)
	// }, [])

	return (
		<Card title={IconTitle('我的授信')} className="card-border">
			<div style={{ height: '170px' }}>
				<Row gutter={[15, 15]} justify={'space-around'} style={{ height: '100%' }}>
					<Col span={8}>
						<Descriptions column={1}>
							<Descriptions.Item label={'授信额度'}>126560.09</Descriptions.Item>
							<Descriptions.Item label={'到期日'}>126560.09</Descriptions.Item>
							<Descriptions.Item label={'利率'}>126560.09</Descriptions.Item>
						</Descriptions>
					</Col>
					<Col span={8}>{/*<div ref={myCreditRef} style={{height: '100%'}}></div>*/}</Col>
					<Col span={8}>
						<Descriptions column={1}>
							<Descriptions.Item label={'合计融资'}>126560.09</Descriptions.Item>
							<Descriptions.Item label={'已还款'}>126560.09</Descriptions.Item>
							<Descriptions.Item label={'未还款'}>126560.09</Descriptions.Item>
						</Descriptions>
					</Col>
				</Row>
			</div>
		</Card>
	)
}

const MyFuseCredit = () => {
	return (
		<Card title={IconTitle('我的融信统计')} className="card-border">
			<Row gutter={[15, 15]} justify={'space-between'}>
				<Col span={4}>
					<Statistic title={'累计接收融信'} value={126126560.09} precision={2} />
				</Col>
				<Col span={4}>
					<Statistic title={'累计质押融信'} value={126126560.09} precision={2} />
				</Col>
				<Col span={4}>
					<Statistic title={'可用融信'} value={126126560.09} precision={2} />
				</Col>
				<Col span={4}>
					<Statistic title={'累计支付融信'} value={126126560.09} precision={2} />
				</Col>
				<Col span={4}>
					<Statistic title={'累计已兑付'} value={126126560.09} precision={2} />
				</Col>
				<Col span={12}>a</Col>
				<Col span={12}>a</Col>
			</Row>
		</Card>
	)
}

const PaymentRecord = () => {
	const paymentButton = () => {
		console.log('paymentButton')
	}

	const infoButton = <Button type={'link'} onClick={paymentButton}>{`详情 >`}</Button>

	return (
		<Card title={IconTitle('支付记录', infoButton)} className="card-border">
			<div style={{ height: '170px' }}>
				<Descriptions column={1}>
					<Descriptions.Item label={'审核中'}>10笔 &nbsp;&nbsp;&nbsp;&nbsp;126560.09</Descriptions.Item>
					<Descriptions.Item label={'已接收'}>10笔 &nbsp;&nbsp;&nbsp;&nbsp;126560.09</Descriptions.Item>
					<Descriptions.Item label={'已拒绝'}>10笔 &nbsp;&nbsp;&nbsp;&nbsp;126560.09</Descriptions.Item>
				</Descriptions>
			</div>
		</Card>
	)
}

const FinanceRecord = () => {
	const financeButton = () => {
		console.log('financeButton')
	}

	const infoButton = <Button type={'link'} onClick={financeButton}>{`详情 >`}</Button>

	return (
		<Card title={IconTitle('融资记录', infoButton)} className="card-border">
			<div style={{ height: '170px' }}>
				<Descriptions column={1}>
					<Descriptions.Item label={'审核中'}>10笔 &nbsp;&nbsp;&nbsp;&nbsp;126560.09</Descriptions.Item>
					<Descriptions.Item label={'已放款'}>10笔 &nbsp;&nbsp;&nbsp;&nbsp;126560.09</Descriptions.Item>
					<Descriptions.Item label={'已拒绝'}>10笔 &nbsp;&nbsp;&nbsp;&nbsp;126560.09</Descriptions.Item>
				</Descriptions>
			</div>
		</Card>
	)
}

const CashingFuseCredit = () => {
	return (
		<Card title={IconTitle('近7天会兑付的融信')} className="card-border">
			<div style={{ display: 'flex', alignItems: 'center', height: '170px' }}>
				<Card.Grid className="card-grid-divider-unshadow" hoverable={false}>
					<Statistic className="statistic-container" title={'总笔数'} valueStyle={{ order: -1 }} value={6} />
				</Card.Grid>
				<Card.Grid className="card-grid-divider-unshadow" hoverable={false}>
					<Statistic className="statistic-container" title={'总金额'} valueStyle={{ order: -1 }} value={126560} />
				</Card.Grid>
				<Card.Grid className="card-grid-divider-unshadow" hoverable={false}>
					<Statistic className="statistic-container" title={'持有的金额'} valueStyle={{ order: -1 }} value={126560} />
				</Card.Grid>
				<Card.Grid className="card-grid-divider-unshadow" hoverable={false}>
					<Statistic className="statistic-container" title={'质押中的金额'} valueStyle={{ order: -1 }} value={6560560} />
				</Card.Grid>
			</div>
		</Card>
	)
}

const MonthFinance = () => {
	return <Card title={IconTitle('融资月统计')} className="card-border"></Card>
}

const SDashboard = () => {
	return (
		<div>
			<Row gutter={[15, 15]}>
				<Col span={12}>
					<NeedDeal />
				</Col>
				<Col span={12}>
					<BalanceAccount />
				</Col>
				<Col span={24}>
					<FastEntry />
				</Col>
				<Col span={24}>
					<MyCredit />
				</Col>
				<Col span={24}>
					<MyFuseCredit />
				</Col>
				<Col span={12}>
					<PaymentRecord />
				</Col>
				<Col span={12}>
					<FinanceRecord />
				</Col>
				<Col span={24}>
					<CashingFuseCredit />
				</Col>
				<Col span={24}>
					<MonthFinance />
				</Col>
			</Row>
		</div>
	)
}
export default SDashboard
