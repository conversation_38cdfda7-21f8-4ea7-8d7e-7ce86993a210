import React from 'react'
import styled from 'styled-components'
interface Props {
	label: string
	content: string
	clickable?: boolean
	action?: () => void
	className?: string
}
const LabelContent = (props: Props) => {
	const { label, content, clickable = false, action, className } = props
	const clickFun = () => {
		action?.()
	}
	return (
		<Wrapper className={className}>
			<span>{label}</span>：
			{clickable ? (
				<span className="click" onClick={clickFun}>
					{content}
				</span>
			) : (
				<span>{content}</span>
			)}
		</Wrapper>
	)
}
const Wrapper = styled.div`
	display: flex;
	align-items: center;
	padding: 0px 16px 0px 24px;
	height: 38px;
	background: #f8f9fb;
	border-radius: 6px;
	box-sizing: border-box;

	> span:first-child {
		font-size: 14px;
		font-weight: 400;
		color: rgba(0, 0, 0, 0.65);
		min-width: 62px;
		text-align-last: justify;
		margin-right: 3px;
	}
	> span:last-child {
		font-size: 17px;
		color: rgba(0, 0, 0, 0.85);
		font-family: PingFangSC, PingFangSC-Regular;
		font-weight: 400;
	}
	.click {
		color: #1890ff !important;
		cursor: pointer;
		text-decoration: underline;
	}
`
export default LabelContent
