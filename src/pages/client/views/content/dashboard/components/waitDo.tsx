import React from 'react'
import styled from 'styled-components'
interface Props {
	data: ToDoItem[]
}
export type ToDoItem = {
	number: string | number
	label: string
	action?: () => void
}
const WaitDo = (props: Props) => {
	const { data } = props
	const clickFun = callback => {
		callback?.()
	}
	return (
		<Wrapper>
			{data.map(item => {
				return (
					<div
						className="waitdo-item"
						style={item?.action ? { cursor: 'pointer', width: `calc(100% / ${data.length})` } : { cursor: 'not-allowed', width: `calc(100% / ${data.length})` }}
						onClick={() => clickFun(item?.action)}
					>
						<div className="number">{item.number}</div>
						<div className="label">{item.label}</div>
					</div>
				)
			})}
		</Wrapper>
	)
}
const Wrapper = styled.div`
	display: flex;
	width: max-content;
	width: 100%;

	.waitdo-item {
		/* min-width: 150px; */
		text-align: center;
		padding: 0;
		margin: 50px;
		position: relative;
		.number {
			font-family: PingFangSC, PingFangSC-Medium;
			font-size: 46px;
			font-weight: 500;
			text-align: center;
			color: #1f7bf4;
		}
		.label {
			font-size: 14px;
			font-family: PingFangSC, PingFangSC-Regular;
			color: rgba(0, 0, 0, 0.65);
		}
	}
	.waitdo-item::after {
		content: '';
		width: 1px;
		height: 100%;
		position: absolute;
		background: #c5cedb;
		top: 0;
		right: -50px;
	}
	.waitdo-item:last-child::after {
		background: transparent;
	}
`
export default WaitDo
