import React from 'react'
import styled from 'styled-components'
interface Props {
	type: 'pay' | 'open' | 'contract' | 'invoice' | 'mycou' | 'finance' | 'cash'
	action: () => void
}
const EntranceCom = (props: Props) => {
	const { type, action } = props
	const clickFun = () => {
		action?.()
	}
	return (
		<Wrapper onClick={clickFun} className="entrance-box">
			{type === 'open' && (
				<div className="entrance-item">
					{action ? (
						<>
							<img src={require('@src/assets/images/dashboard/open.png')} />
							<img src={require('@src/assets/images/dashboard/open-txt.png')} />
						</>
					) : (
						<>
							<img src={require('@src/assets/images/dashboard/open-lose.png')} />
							<img src={require('@src/assets/images/dashboard/open-txt-lose.png')} />
						</>
					)}
				</div>
			)}

			{type === 'pay' && (
				<div className="entrance-item">
					{action ? (
						<>
							<img src={require('@src/assets/images/dashboard/pay.png')} />
							<img src={require('@src/assets/images/dashboard/pay-txt.png')} />
						</>
					) : (
						<>
							<img src={require('@src/assets/images/dashboard/pay-lose.png')} />
							<img src={require('@src/assets/images/dashboard/pay-txt-lose.png')} />
						</>
					)}
				</div>
			)}

			{type === 'contract' && (
				<div className="entrance-item">
					{action ? (
						<>
							<img src={require('@src/assets/images/dashboard/contract.png')} />
							<img src={require('@src/assets/images/dashboard/contract-txt.png')} />
						</>
					) : (
						<>
							<img src={require('@src/assets/images/dashboard/contract-lose.png')} />
							<img src={require('@src/assets/images/dashboard/contract-txt-lose.png')} />
						</>
					)}
				</div>
			)}

			{type === 'invoice' && (
				<div className="entrance-item">
					{action ? (
						<>
							<img src={require('@src/assets/images/dashboard/invoice.png')} />
							<img src={require('@src/assets/images/dashboard/invoice-txt.png')} />
						</>
					) : (
						<>
							<img src={require('@src/assets/images/dashboard/invoice-lose.png')} />
							<img src={require('@src/assets/images/dashboard/invoice-txt-lose.png')} />
						</>
					)}
				</div>
			)}

			{type === 'cash' && (
				<div className="entrance-item">
					{action ? (
						<>
							<img src={require('@src/assets/images/dashboard/cash.png')} />
							<img src={require('@src/assets/images/dashboard/cash-txt.png')} />
						</>
					) : (
						<>
							<img src={require('@src/assets/images/dashboard/cash-lose.png')} />
							<img src={require('@src/assets/images/dashboard/cash-txt-lose.png')} />
						</>
					)}
				</div>
			)}
			{type === 'mycou' && (
				<div className="entrance-item">
					{action ? (
						<>
							<img src={require('@src/assets/images/dashboard/my-cou.png')} />
							<img src={require('@src/assets/images/dashboard/my-cou-txt.png')} />
						</>
					) : (
						<>
							<img src={require('@src/assets/images/dashboard/my-cou-lose.png')} />
							<img src={require('@src/assets/images/dashboard/my-cou-txt-lose.png')} />
						</>
					)}
				</div>
			)}

			{type === 'finance' && (
				<div className="entrance-item">
					{action ? (
						<>
							<img src={require('@src/assets/images/dashboard/finance.png')} />
							<img src={require('@src/assets/images/dashboard/finance-txt.png')} />
						</>
					) : (
						<>
							<img src={require('@src/assets/images/dashboard/finance-lose.png')} />
							<img src={require('@src/assets/images/dashboard/finance-txt-lose.png')} />
						</>
					)}
				</div>
			)}
		</Wrapper>
	)
}
const Wrapper = styled.div`
	display: inline-block;
	width: 100%;
	height: 100%;

	.entrance-item {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		padding: 10px;
		background: #f6faff;
		border-radius: 14px;
		box-sizing: border-box;
		flex-direction: column;
		cursor: pointer;
		> img:first-child {
			width: 56px;
			height: 56px;
			margin: 14px;
		}
		> img:last-child {
			width: 56px;
			height: 14px;
		}
	}
`
export default EntranceCom
