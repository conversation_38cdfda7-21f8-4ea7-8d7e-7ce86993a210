import React, { CSSProperties, useEffect, useState } from 'react'
import * as echarts from 'echarts'
import { numberToThousands } from '@src/utils/timeFilter'
type EChartsOption = echarts.EChartsOption

interface ChartProps {
	id: string
	style: CSSProperties
	option?: EChartsOption
	data: any[]
	xAxisData: string[]
}

const BarChart = React.forwardRef((props: ChartProps, ref: any) => {
	const [myChart, setMyChart] = useState<echarts.EChartsType>()
	React.useImperativeHandle(ref, () => ({
		onResize: () => myChart?.resize(),
	}))
	const { id, style, data, xAxisData } = props
	const defaultOption = {
		tooltip: {
			show: true,
			trigger: 'axis',
			padding: 0,
			borderColor: 'rgba(0,0,0,0.75)',
			formatter: function (params) {
				let res = `<div style="
              background: rgba(0,0,0,0.75);
              color: #fff;
              padding: 10px;
              border-radius:4px;
              font-size:12px;
          ">
          <div>${params[0].axisValue} </div>
          <div style="display:flex;align-items:center;"><div style="width:6px;height:6px;border-radius:50%;background:#1890FF;margin-right:5px;"></div> 金额（元）: ${numberToThousands(
						params[0].value
					)} </div>
          </div>`
				return res
			},
		},
		grid: {
			top: 40,
			left: 80,
			right: 40,
			bottom: 40,
		},
		color: ['#1890ff'],
		xAxis: {
			type: 'category',
			data: xAxisData,
		},
		yAxis: {
			type: 'value',
			name: '（元）', // 单位
			position: 'top',
		},
		series: [
			{
				data: data,
				type: 'bar',
			},
		],
		barWidth: 20,
	}
	const { option = defaultOption } = props

	if (data) option.series[0].data = data

	useEffect(() => {
		initChart(option)
	}, [data, option])

	const initChart = option => {
		const chartDom = document.getElementById(id)!
		const myChart = echarts.init(chartDom)
		setMyChart(myChart)
		option && myChart.setOption(option)
	}
	return <div id={id} style={style}></div>
})

export default BarChart
