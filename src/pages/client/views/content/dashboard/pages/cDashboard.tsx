import DashboardApi from '@src/pages/client/api/dashboard'
import { getCurrentRole } from '@src/pages/client/biz/bizIndex'
import { numberToThousands, numberToThousandsWithoutPoint, timeToStamp } from '@src/utils/timeFilter'
import { filterSensitiveData } from '@src/utils/util'
import { Button, Col, Radio, RadioChangeEvent, Row } from 'antd'
import moment from 'moment'
import React, { useEffect, useRef, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import styled from 'styled-components'
import BarChart from '../components/barChart'
import CashDateCom from '../components/cashDate'
import EntranceCom from '../components/entrance'
import IconLabelContent from '../components/iconLabelContent'
import LabelContent from '../components/labelContent'
import PieChart from '../components/pie-chart'
import TitleCom from '../components/title'
import WaitDo, { ToDoItem } from '../components/waitDo'

const CDashboard = () => {
	const navigate = useNavigate()
	const currentRole = getCurrentRole()
	const [todoData, setTodoData] = useState([])
	const [openingData, setOpeningData] = useState<any>()
	const [couDueData, setCouDueData] = useState<any>()
	const [accountInfo, setAccountInfo] = useState<any>()
	const [openingCredit, setOpeningCredit] = useState<any>()
	const [openMonth, setOpenMonth] = useState<string>()
	const [couCreateData, setCouCreateData] = useState<any>()
	const barCharts = useRef(null)
	const pieCharts = useRef(null)

	useEffect(() => {
		getTodoData()
		getAccInfo()
		getOpeningData()
		getCouDueData()
		getOpeningCredit()
		onChange('1')
	}, [])

	const changeSize = () => {
		pieCharts?.current?.onResize()
		barCharts?.current?.onResize()
	}
	useEffect(() => {
		window.addEventListener('resize', changeSize)
		return () => {
			window.removeEventListener('resize', changeSize)
		}
	}, [])

	const getTodoData = () => {
		const data: ToDoItem[] = []
		DashboardApi.getTodos()
			.then(res => {
				data.push({
					label: '开立审核',
					number: res.createAuditCount || 0,
					action:
						res.createAuditCount > 0 && currentRole?.roleCode?.includes('Auditor')
							? () => {
									navigate('/content/couTransfer/waitAuditorList', {})
							  }
							: null,
				})
				data.push({
					label: '今日兑付（笔)',
					number: res.todayCashCount || 0,
					action:
						res.todayCashCount > 0
							? () => {
									navigate('/content/couTransfer/transferCash', { state: { dueDate: [timeToStamp(new Date(), 'begin'), timeToStamp(new Date(), 'begin')] } })
							  }
							: null,
				})
				data.push({
					label: '退款审核',
					number: res.refundCount || 0,
					action:
						res.refundCount > 0
							? () => {
									navigate('/content/refundManage/auditRefund')
							  }
							: null,
				})
				setTodoData(data)
				console.log(data)
			})
			.catch(() => {})
	}
	const getOpeningData = () => {
		DashboardApi.getOpenings()
			.then(res => {
				setOpeningData(res)
				console.log(res)
			})
			.catch(() => {})
	}
	const getCouDueData = () => {
		DashboardApi.getCouDueStatistical()
			.then(res => {
				setCouDueData(res)
				console.log(res)
			})
			.catch(() => {})
	}

	const getAccInfo = () => {
		const ext = JSON.parse(localStorage.getItem('ext') || '[]')
		if (ext?.bankCards?.length) {
			const account = ext.bankCards[0]
			setAccountInfo(account)
		}
	}
	const formatDate = (start: string, end: string): string => {
		const startStr = moment(start).format('YYYY-MM-DD')
		const endStr = moment(end).format('MM-DD')
		return `${startStr} ~ ${endStr}`
	}

	const getOpeningCredit = () => {
		DashboardApi.getOpeningCredit()
			.then(res => {
				console.log(res)
				setOpeningCredit(res)
			})
			.catch(() => {})
	}

	const onChange = (str: string) => {
		setCouCreateData({ date: [], value: [] })
		setOpenMonth(str)
		let startTime = ''
		let endTime = ''
		if (str == '1') {
			startTime = moment().startOf('year').format('YYYY-MM-DD')
			endTime = moment().endOf('year').format('YYYY-MM-DD')
		} else {
			//近一年= 本月+往前数11个月
			endTime = moment().endOf('month').format('YYYY-MM-DD')
			startTime = moment(endTime).subtract(11, 'months').startOf('month').format('YYYY-MM-DD')
		}
		getCouCreateStatsData(startTime, endTime)
	}
	const getCouCreateStatsData = (start, end) => {
		DashboardApi.getCouCreateStatsData({ createDateStart: start, createDateEnd: end })
			.then(res => {
				let dateArr = []
				let valArr = []
				if (res?.length == 12) {
					dateArr = res.map(item => item.segmentDate)
					valArr = res.map(item => item.totalAmountInYuan)
				} else {
					for (let i = 0; i < 12; i++) {
						dateArr.push(moment(start).add(i, 'months').format('YYYY-MM'))
					}
					dateArr.forEach(date => {
						res = res.map(item => {
							if (date == item.segmentDate) {
								return {
									segmentDate: item.segmentDate,
									totalAmountInYuan: item.totalAmountInYuan,
								}
							} else {
								return {
									segmentDate: date,
									totalAmountInYuan: 0,
								}
							}
						})
					})
					valArr = res.map(item => item.totalAmountInYuan)
					console.log(valArr, 'valArr')
				}

				setCouCreateData({ date: dateArr, value: valArr })
			})
			.catch(() => {})
	}
	return (
		<Wrapper>
			<div className="box two padding20">
				<div className="inner-box wth-50">
					<TitleCom title="待办" icon={<img width={14} height={16} src={require('@src/assets/images/dashboard/icon1.png')} />} />
					<div className="waitdo">
						<WaitDo data={todoData} />
					</div>
				</div>
				<div className="inner-box wth-50">
					<TitleCom title="结算账户" icon={<img width={14} height={16} src={require('@src/assets/images/dashboard/icon2.png')} />} />
					<div className="acc-body">
						<img src={require('@src/assets/images/dashboard/acc.png')} alt="acc" />
						<div className="acc-right">
							<div className="acc-right-item">
								<span>账号</span>：<span>{filterSensitiveData(accountInfo?.accountNum || '', 4, 4)}</span>
							</div>
							<div className="acc-right-item">
								<span>户名</span>：<span>{accountInfo?.accountName}</span>
							</div>
							<div className="acc-right-item">
								<span>开户行</span>：<span>{accountInfo?.accountBank}</span>
							</div>
						</div>
					</div>
					<div className="acc-footer">
						<div className="acc-footer-inner">
							<img src={require('@src/assets/images/dashboard/acc-icon.png')} alt="accicon" />
							<span>请在融信到期前，将还款资金打入本账户</span>
						</div>
					</div>
				</div>
			</div>

			<div className="box two grey-bg">
				<div className="inner-box wth-25 mrg-0 white-bg">
					<TitleCom
						title="开立记录"
						icon={<img width={14} height={16} src={require('@src/assets/images/dashboard/icon5.png')} />}
						action={
							<Button style={{ padding: 0, fontSize: 12 }} type="link" onClick={() => navigate('/content/couTransfer/sList')}>
								详情 &gt;&gt;
							</Button>
						}
					/>
					<div>
						<IconLabelContent
							icon={<img width={20} height={20} src={require('@src/assets/images/dashboard/company.png')} />}
							label="审核中"
							content={numberToThousandsWithoutPoint(openingData?.waitingCount)}
							action={() => {
								navigate('/content/couTransfer/sList', { state: { status: 'WAITING' } })
							}}
						/>
						<IconLabelContent
							icon={<img width={20} height={20} src={require('@src/assets/images/dashboard/company.png')} />}
							label="开立成功"
							content={numberToThousandsWithoutPoint(openingData?.confirmedCount)}
							action={() => {
								navigate('/content/couTransfer/sList', { state: { status: 'CONFIRMED' } })
							}}
						/>
						<IconLabelContent
							icon={<img width={20} height={20} src={require('@src/assets/images/dashboard/company.png')} />}
							label="已拒绝"
							content={numberToThousandsWithoutPoint(openingData?.rejectedCount)}
							action={() => {
								navigate('/content/couTransfer/sList', { state: { status: 'REJECTED' } })
							}}
						/>
					</div>
				</div>
				<div className="inner-box wth-75 mrg-0 white-bg">
					<TitleCom title="快捷入口" icon={<img width={14} height={16} src={require('@src/assets/images/dashboard/icon3.png')} />} />
					<Row gutter={16} className="entrance">
						<Col span={6}>
							<EntranceCom
								type="open"
								action={
									currentRole?.roleCode?.includes('Operator')
										? () => {
												navigate('/content/couManage/COperatorRongXinCreate', {})
										  }
										: null
								}
							/>
						</Col>
						<Col span={6}>
							<EntranceCom
								type="invoice"
								action={
									currentRole?.roleCode?.includes('Operator')
										? () => {
												navigate('/content/couAssets/uploadInvoice', {})
										  }
										: null
								}
							/>
						</Col>
						<Col span={6}>
							<EntranceCom
								type="contract"
								action={
									currentRole?.roleCode?.includes('Operator')
										? () => {
												navigate('/content/couAssets/contract', { state: { add: true } })
										  }
										: null
								}
							/>
						</Col>
						<Col span={6}>
							<EntranceCom
								type="cash"
								action={() => {
									navigate('/content/couTransfer/transferCash', {})
								}}
							/>
						</Col>
					</Row>
				</div>
			</div>
			<div className="box">
				<TitleCom title="开立额度" icon={<img width={14} height={16} src={require('@src/assets/images/dashboard/icon4.png')} />} />
				<div className="open-box">
					<div>
						<LabelContent className="label" label="开立额度" content={`¥ ${numberToThousands(openingCredit?.quotaInYuan)}`} />
						<LabelContent className="label" label="可用额度" content={`¥ ${numberToThousands(openingCredit?.availableQuotaInYuan)}`} />
						<LabelContent className="label" label="到期日" content={moment(openingCredit?.creditDueDate).format('YYYY-MM-DD')} />
						<LabelContent className="label" label="质押率" content={`${Number(openingCredit?.financeDiscount) * 100} %`} />
					</div>
					<div className="pie-chart">
						<PieChart
							id="pie-01"
							ref={pieCharts}
							style={{ width: '100%', height: '255px' }}
							data={[
								{
									value: openingCredit?.pledgeCouAmountInYuan,
									name: `上游质押中 `,
									action: () => {
										navigate('/content/couManage/holderDetail', { state: { cashStatusList: ['WAIT_CASH', 'NO_CASH'], pledgeStatus: '1' } })
									},
								},
								{
									value: openingCredit?.availableQuotaInYuan,
									name: `可用额度`,
								},

								{
									value: openingCredit?.holderCouAmountInYuan,
									name: `上游持有中`,
									action: () => {
										navigate('/content/couManage/holderDetail', { state: { cashStatusList: ['WAIT_CASH', 'NO_CASH'], pledgeStatus: '0' } })
									},
								},
								{
									value: openingCredit?.auditCouAmountInYuan,
									name: `开立审核中`,
									action: currentRole?.roleCode?.includes('Operator')
										? () => {
												navigate('/content/couManage/COperatorRongXinCreate', { state: { transferStatus: 'WAITING' } })
										  }
										: null,
								},
							]}
							colorList={['#FF9E23', '#53D2D2', '#7578EE', '#3AA0FF']}
						/>
					</div>
					<div>
						<LabelContent
							className="label"
							label="合计开立"
							content={`¥ ${numberToThousands(openingCredit?.totalCouCreateAmountInYuan)}`}
							clickable
							action={() => navigate('/content/couTransfer/transferCash', {})}
						/>
						<LabelContent
							className="label"
							label="已兑付"
							content={`¥ ${numberToThousands(openingCredit?.cashOkCouAmountInYuan)}`}
							clickable
							action={() => navigate('/content/couTransfer/transferCash', { state: { cashStatusList: ['CASH_OK'] } })}
						/>
						<LabelContent
							className="label"
							label="未兑付"
							content={`¥ ${numberToThousands(openingCredit?.unCashCouAmountInYuan)}`}
							clickable
							action={() => navigate('/content/couTransfer/transferCash', { state: { cashStatusList: ['WAIT_CASH', 'NO_CASH'] } })}
						/>
					</div>
				</div>
			</div>

			<div className="box">
				<TitleCom title="到期兑付统计" icon={<img width={14} height={16} src={require('@src/assets/images/dashboard/icon4.png')} />} />
				<div className="cash-date">
					<div>
						<CashDateCom
							title="未来3个工作日到期"
							amount={numberToThousands(couDueData?.next3BusinessDays?.totalAmountInYuan)}
							count={couDueData?.next3BusinessDays?.count}
							date={formatDate(couDueData?.next3BusinessDays?.startDate, couDueData?.next3BusinessDays?.endDate)}
							action={() => {
								navigate('/content/couTransfer/transferCash', {
									state: {
										dueDate: [
											timeToStamp(new Date(couDueData?.next3BusinessDays?.startDate), 'begin'),
											timeToStamp(new Date(couDueData?.next3BusinessDays?.endDate), 'begin'),
										],
										cashStatusList: ['WAIT_CASH', 'NO_CASH'],
									},
								})
							}}
						/>
					</div>
					<div>
						<CashDateCom
							title="本月到期"
							amount={numberToThousands(couDueData?.currentMonth?.totalAmountInYuan)}
							count={couDueData?.currentMonth?.count}
							date={formatDate(couDueData?.currentMonth?.startDate, couDueData?.currentMonth?.endDate)}
							action={() => {
								navigate('/content/couTransfer/transferCash', {
									state: {
										dueDate: [
											timeToStamp(new Date(couDueData?.currentMonth?.startDate), 'begin'),
											timeToStamp(new Date(couDueData?.currentMonth?.endDate), 'begin'),
										],
										cashStatusList: ['WAIT_CASH', 'NO_CASH'],
									},
								})
							}}
						/>
					</div>
					<div>
						<CashDateCom
							title="未来30天到期"
							amount={numberToThousands(couDueData?.next30Days?.totalAmountInYuan)}
							count={couDueData?.next30Days?.count}
							date={formatDate(couDueData?.next30Days?.startDate, couDueData?.next30Days?.endDate)}
							action={() => {
								navigate('/content/couTransfer/transferCash', {
									state: {
										dueDate: [
											timeToStamp(new Date(couDueData?.next30Days?.startDate), 'begin'),
											timeToStamp(new Date(couDueData?.next30Days?.endDate), 'begin'),
										],
										cashStatusList: ['WAIT_CASH', 'NO_CASH'],
									},
								})
							}}
						/>
					</div>
					<div>
						<CashDateCom
							title="未来所有到期"
							amount={numberToThousands(couDueData?.afterToday?.totalAmountInYuan)}
							count={couDueData?.afterToday?.count}
							action={() => {
								const dtStart = moment().add(1, 'days').format('YYYY-MM-DD')
								navigate('/content/couTransfer/transferCash', {
									state: { dueDate: [timeToStamp(new Date(dtStart), 'begin'), undefined], cashStatusList: ['WAIT_CASH', 'NO_CASH'] },
								})
							}}
						/>
					</div>
					<div>
						<CashDateCom
							title="已逾期"
							amount={numberToThousands(couDueData?.beforeToday?.totalAmountInYuan)}
							count={couDueData?.beforeToday?.count}
							isDue
							action={() => {
								const dtEnd = moment().subtract(1, 'days').format('YYYY-MM-DD')
								navigate('/content/couTransfer/transferCash', {
									state: { dueDate: [undefined, timeToStamp(new Date(dtEnd), 'begin')], cashStatusList: ['WAIT_CASH', 'NO_CASH'] },
								})
							}}
						/>
					</div>
				</div>
			</div>

			<div className="box">
				<TitleCom
					title="开立月统计"
					icon={<img width={14} height={16} src={require('@src/assets/images/dashboard/icon4.png')} />}
					action={
						<Radio.Group onChange={e => onChange(e.target.value)} value={openMonth} size="small">
							<Radio.Button className="btnt" value="1" key="本年">
								本年
							</Radio.Button>
							<Radio.Button className="btnt" value="2" key="近一年">
								近一年
							</Radio.Button>
						</Radio.Group>
					}
				/>
				<div>
					<BarChart ref={barCharts} id="bar" data={couCreateData?.value} xAxisData={couCreateData?.date} style={{ width: '100%', height: 300 }} />
				</div>
			</div>
		</Wrapper>
	)
}
const Wrapper = styled.div`
	.box {
		width: 100%;
		min-height: 238px;
		background: #ffffff;
		margin-bottom: 24px;
		border-radius: 10px;
	}

	.two {
		display: flex;
		box-sizing: border-box;
	}

	.padding20 {
		padding: 20px;
	}

	.inner-box {
		background: #f6faff;
		border-radius: 14px;
	}

	.inner-box:nth-child(1) {
		margin-right: 20px;
	}

	.wth-25 {
		width: 25%;
	}

	.wth-50 {
		width: calc((100% - 20px) / 2);
	}

	.wth-75 {
		width: 75%;
	}

	.grey-bg {
		background: #f1f1f1;
	}

	.white-bg {
		background: #ffffff;
	}

	.mrg-0 {
		margin: 0;
	}

	.open-box {
		width: 100%;
		height: 290px;
		display: flex;
		align-items: center;
		padding: 15px 20px;

		> div {
			width: 28%;
			height: 100%;
		}
		> div:nth-child(2) {
			width: 44%;
		}
		> div:nth-child(1) {
			.label {
				margin: 22px 0px;
			}
		}
		> div:nth-child(3) {
			.label {
				margin: 35px 0px;
			}
		}

		.pie-chart {
			background: #f6faff;
			margin: 14px;
			border-radius: 14px;
			padding: 5px;
		}
	}

	.entrance {
		padding: 20px;
		.entrance-box {
			height: 140px;
		}
	}
	.waitdo {
		width: 100%;
		height: 225px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.acc-body {
		display: flex;
		align-items: center;
		padding: 23px 30px;
		> img {
			max-width: 110px;
			max-height: 128px;
			margin-right: 20px;
			aspect-ratio: 110 / 128;
			width: 25%;
			min-width: 75px;
		}
		.acc-right {
			flex-grow: 1;
			width: calc(100% - 130px);
		}
		.acc-right-item {
			height: 40px;
			background: #edf2fa;
			border-radius: 2px;
			margin-bottom: 12px;
			font-size: 14px;
			padding: 0 20px;
			display: flex;
			align-items: center;

			> span:first-child {
				color: rgba(0, 0, 0, 0.85);
				width: 60px;
				height: 40px;
				line-height: 40px;
				text-align-last: justify;
			}
			> span:last-child {
				color: rgba(0, 0, 0, 0.65);
				padding-left: 12px;
			}
		}
		.acc-right-item:last-child {
			margin-bottom: 0;
		}
	}
	.acc-footer {
		/* height: 50px; */
		padding: 5px 30px;
		box-sizing: border-box;
		background: rgba(255, 254, 248, 0.8);
		border-bottom-left-radius: 10px;
		border-bottom-right-radius: 10px;
		display: flex;
		align-items: center;
		.acc-footer-inner {
			width: 100%;
			background: #fffef8;
			padding: 0 20px;
			border-radius: 8px;
			display: flex;
			align-items: center;
			> img {
				width: 16px;
				height: 16px;
			}
			> span {
				display: inline-block;
				font-size: 14px;
				color: #ff8a17;
				padding: 8px;
			}
		}
	}
	.cash-date {
		display: flex;
		padding: 20px 10px;
		height: 235px;
		> div {
			width: 25%;
			box-sizing: border-box;
			margin-right: 10px;
			height: 100%;
		}
		> div:last-child {
			margin-right: 0;
		}
	}
	.ant-radio-button-wrapper {
		margin-right: 5px;
	}
	.ant-radio-button-wrapper:last-child {
		margin-right: 0px;
	}
	.ant-radio-group {
		display: flex;
	}
	.btnt {
		width: 70px;
		display: flex;
		justify-content: center;
		align-items: center;
	}
`
export default CDashboard
