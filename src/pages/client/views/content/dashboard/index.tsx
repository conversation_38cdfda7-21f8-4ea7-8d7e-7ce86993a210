import { getCurrentRole } from '@src/pages/client/biz/bizIndex'
import React from 'react'
import styled from 'styled-components'
import CDashboard from './pages/cDashboard'
import SDashboard from './pages/sDashboard'

const DashboardPage = () => {
	const currentRole = getCurrentRole()
	return (
		<DashboardPageWrapper className="dashboard">
			{currentRole?.roleCode?.startsWith('C') && !currentRole?.roleCode?.includes('Admin') ? <CDashboard /> : <SDashboard />}
		</DashboardPageWrapper>
	)
}
const DashboardPageWrapper = styled.div``
export default DashboardPage
