@import '../../../..//assets//styles/common.less';
@src: '../../../../assets';

// 蓝色
.link {
	color: #49a9ee;
	cursor: pointer;
	margin-right: 5px;

	&:last-child {
		margin-right: 0;
	}

	&.delete {
		color: rgb(255, 0, 31);
	}
}

// 红色
.red {
	color: red;
	cursor: pointer;
}

.money-list {
	border-radius: 4px;
	background: #fff;
	padding: 20px;
	display: flex;
	justify-content: space-between;
	margin-bottom: 20px;

	.money-item {
		display: flex;
		flex-direction: column;

		.money-title {
			font-size: 14px;
			color: rgba(0, 0, 0, 0.45);
			margin-bottom: 5px;
		}

		.money {
			color: rgba(0, 0, 0, 0.85);
			font-size: 24px;
		}
	}
}

.tabs-wrapper {
	background: rgba(255, 255, 255, 1);
	border-radius: 4px;
	box-sizing: border-box;
	overflow: hidden;

	.ant-tabs-nav-wrap {
		padding-left: 20px;

		.ant-tabs-nav .ant-tabs-tab {
			padding: 15px 20px 10px 20px;
		}
		//
	}

	.ant-tabs-top > .ant-tabs-nav {
		margin: 0;
	}

	.operate {
		padding-top: 0 !important;
	}
}

/*card style*/
.card-wrapper {
	width: 100%;
	background: rgba(255, 255, 255, 1);
	border-radius: 4px;
	box-sizing: border-box;

	// margin-bottom: 20px;
	.confirmPageButton {
		display: flex;
		margin-bottom: 20px;

		button {
			margin-right: 10px;
		}
	}

	/*big title without a left vertical line*/
	.card-title {
		font-size: 20px;
		font-weight: 600;
		color: rgba(38, 38, 38, 0.8);
		height: 30px;
		line-height: 30px;
		margin-bottom: 20px;
		// user-select: none;
		cursor: default;
	}

	/*small title with a left vertical line*/
	.small-title {
		display: block;
		position: relative;
		padding-left: 13px;
		font-size: 18px;
		font-weight: 500;
		color: rgba(38, 38, 38, 0.9);
		line-height: 24px;
		margin-bottom: 15px;
		// user-select: none;
		cursor: default;

		&::before {
			position: absolute;
			content: ' ';
			top: 4px;
			left: 0;
			width: 3px;
			height: 16px;
			background-color: #4454a5;
		}
	}

	/*link style inside the card list*/
	.card-link {
		font-size: 14px;
		font-weight: 400;
		color: rgba(24, 144, 255, 0.7) !important;
		line-height: 14px;

		&:hover {
			color: rgba(24, 144, 255, 0.95) !important;
			cursor: pointer !important;
		}
	}

	.card-table {
		margin-bottom: 30px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	/*文件列表样式*/
	.file-list {
		width: 100%;
		min-width: 360px;
		padding: 15px 0;
		background-color: #fafafa;
		color: rgba(87, 87, 87, 1);
		border-radius: 4px;
		display: flex;
		flex-direction: row;
		align-items: center;
		flex-wrap: wrap;

		/*每个li上添加file-item*/
		.file-item {
			height: 32px;
			position: relative;
			display: flex;
			align-items: center;
			padding-left: 40px;
			cursor: pointer;

			&:after {
				position: absolute;
				display: inline-block;
				content: ' ';
				right: 0;
				width: 1px;
				height: 24px;
				background-color: #d9d9d9;
			}

			&:last-child {
				&:after {
					display: none;
				}
			}
		}
	}

	/*卡片中列表的包裹*/
	.card-list {
		padding: 12px 20px;
		background-color: #fafafa;
		color: rgba(87, 87, 87, 1);
		border-radius: 4px;
		display: flex;
		flex-direction: row;
		/* align-items: center; */
		flex-wrap: wrap;
		margin-bottom: 30px;

		&:last-child {
			margin-bottom: 0;
		}

		.card-item {
			width: 50%;
			padding: 8px 0;
			text-align: left;

			&:nth-child(2n + 1) {
				padding-right: 10px;
			}

			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;

			.card-item-content {
				width: 270px;
				font-size: 14px;
				text-align: left;
				word-break: break-all;
				display: flex;
			}

			.left {
				margin-right: 20px;
				font-weight: 600;
				line-height: 28px;
				color: rgba(18, 23, 27, 0.65);
				// user-select: none;
				cursor: default;
				display: inline-block;
			}

			.right {
				display: inline;
				flex: 1;
				line-height: 28px;

				i {
					padding-left: 5px;
					color: orange;
					cursor: pointer;
				}
			}
		}
	}
}

.form-list {
	width: 100%;
	padding: 16px 20px;
	display: flex;
	flex-wrap: wrap;
	background: rgba(250, 250, 250, 1);
	border-radius: 4px;
	margin-bottom: 20px;

	.form-item {
		width: 50%;
		text-align: left;
		display: inline-block;
		margin-bottom: 0px !important;

		&:last-child {
			width: 100%;
			text-align: left !important;
			padding-right: 0;
			.ant-form-item {
				margin-bottom: 0 !important;
			}
		}

		&:nth-child(2n + 1) {
			padding-right: 10px;
		}

		.ant-form-item-control {
			width: 100%;
			min-height: 56px;
		}
	}
}

/**tips气泡样式 */
.tips {
	padding-left: 16px;
	width: 100%;
	min-width: 500px;
	height: 40px;
	line-height: 40px;
	background: rgba(230, 247, 255, 1);
	border-radius: 4px;
	border: 1px solid rgba(186, 231, 255, 1);
	margin-bottom: 16px;

	.icon {
		color: #1890ff;
		margin-right: 8px;
	}

	.desc {
		font-size: 14px;
		color: rgba(0, 0, 0, 0.65);
		margin-right: 10px;

		span {
			color: #1890ff;
			padding: 0 4px;
		}
	}
}

.ant-modal-header {
	padding: 8px 24px;
}

.ant-modal-close-x {
	line-height: 40px;
	height: 40px !important;
}

.ant-modal-body {
	// padding: 0;
}

.ant-drawer-body {
	padding: 0;
}

.ant-input {
	background-color: #fff;
	border: 1px solid #d9d9d9;
}

.ant-input-affix-wrapper {
	background-color: #fff;
	border: 1px solid #d9d9d9;
}

//右侧content 布局

.client_content {
	// padding: 41px 46px !important;

	.card-wrapper {
		// border-radius: 20px 20px 2px 2px !important;
	}
}

//顶部面包屑以及个人信息
.top-bar {
	// box-shadow: none !important;
}

.wx_Modal {
	//弹窗样式
	.ant-modal-body {
		padding: 0 !important;
	}

	.ant-modal-content {
		width: 520px;
		overflow: hidden;
		border-radius: 20px !important;
		background-image: url('@{src}/images/content/confirm/top.png');
		background-size: 520px 146px;
		background-repeat: no-repeat;
		background-position: center 0;

		//弹窗头部
		.ant-modal-header {
			padding: 6px 22px 0px !important;
			background-color: transparent !important;
			border-bottom: none !important;

			.ant-modal-title {
				display: block;
				width: 144px;
				height: 34px;
				color: #fff !important;
				text-align: center;
				line-height: 34px;
				// background-image: url('@{src}/images/content/confirm/watermark.png');
				background-size: cover;
				background-repeat: no-repeat;
				background-position: center;
			}
		}

		//弹窗需要置顶居中显示的
		.wx_Modal_top_center {
			display: flex;
			justify-content: center;
			text-align: center;
			color: #fff !important;
			line-height: 24px;
			height: 106px;
			padding: 0px 40px !important;
			margin-bottom: 17px;
			position: relative;

			.wx_Modal_top_center_line {
				position: absolute;
				top: 30px;
				left: 0;
				right: 0;
				margin: auto;
				width: 2px;
				height: 27px;
				border: 1px solid #ffffff;
			}

			.wx_Modal_top_center_item {
				flex: 1;
				text-align: center;

				.text {
					font-size: 18px;
					margin-bottom: 4px;
					margin-top: 18px;
					font-weight: 500;
				}

				.label {
					font-size: 14px;
					font-weight: 400;
				}
			}
		}

		.wx_Modal_Main {
			padding: 0 70px;

			.wx_Modal_Main_tips {
				font-size: 16px;
			}
		}

		//描述信息 单元格
		.ant-descriptions-row > th,
		.ant-descriptions-row > td {
			padding-bottom: 12px !important;
		}

		//弹窗 按钮样式
		.ant-modal-confirm-btns {
			// display: flex !important;
			height: 52px !important;
			line-height: 52px !important;
			// justify-content: right !important;
			// align-items: center !important;
			padding: 0 16px !important;
			margin-top: 13px !important;
			width: 100%;
			border-top: 1px solid rgba(0, 0, 0, 0.06);
			text-align: right !important;
		}

		//函数式调用 MODEL
		.ant-modal-confirm-title {
			display: block;
			width: 144px;
			height: 34px;
			margin: 6px 0 0px 22px !important;
			color: #fff !important;
			text-align: center;
			line-height: 34px;
			// background-image: url('@{src}/images/content/confirm/watermark.png');
			background-size: cover;
			background-repeat: no-repeat;
			background-position: center;
		}

		.ant-modal-confirm-content {
			margin-top: 0 !important;
		}

		.wx_Modal_Warning_text {
			.ant-descriptions-item-content {
				color: #efa000 !important;
			}

			.ant-descriptions-item-label {
				color: #efa000 !important;
			}
		}
	}
}

.fi_audit_Modal {
	.ant-modal-body {
		height: auto !important;
	}

	.wx_Modal_Main_tips {
		margin: 24px 50px 22px !important;
		text-align: left !important;
	}
}

.wx_Modal_confirm {
	.ant-modal-content {
		background-image: url('@{src}/images/content/confirm/top_S.png');
		background-size: 520px 76px;
	}

	.wx_Modal_Main {
		margin-top: 36px !important;
		padding: 16px !important;
	}

	//拒绝意见文本域
	.ant-input-textarea {
		height: 100px;
		border: 1px solid #d4dced;
		border-radius: 4px;

		textarea {
			resize: none;
		}
	}

	//拒绝意见文本域 字数
	.ant-input-textarea-show-count::after {
		position: absolute;
		bottom: 2px;
		width: 100%;
		right: 0px;
	}
}

.remove-padding {
	padding: 0;
}

.add-padding {
	padding: 15px;
}

.list-bg {
	background-image: url('@{src}/images/content/list/bg.png');
	background-size: 100% 500px;
	// background-size: 1581px 1064px;
	// background-size: 1000px 1064px;
	background-repeat: no-repeat;
	background-position: center bottom;
	flex: 1;
	padding: 15px;
	display: flex;
	flex-direction: column;

	.ant-table {
		background-color: transparent;
	}

	.ant-table-tbody > tr > td {
		border-bottom: 1px solid rgba(168, 180, 205, 0.5);
	}

	// 记录悬停的样式
	.ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td {
		background: rgba(180, 202, 237, 0.15);
	}

	// 自定义字段固定在左边，右边时的背景色
	.ant-table-cell-fix-left,
	.ant-table-cell-fix-right {
		background-color: transparent;
	}

	.ant-table-tbody > tr {
		&.highLight {
			background-color: #fff0e8;
		}
	}

	// .bg-img {
	// 	height: 520px;
	// 	margin-top: -350px;
	// 	background-image: url('@{src}/images/content/list/bg.png');
	// 	background-size: 100% 100%;
	// 	flex: 1;
	// 	padding: 15px;
	// }
	.bg-height {
		// min-height: 130px;
		min-height: 120px;
		flex: 1;
	}
}

// 解决卡片的padding-bottom问题
.layout-wrapper {
	// padding-bottom: 25px;
}

// 给卡片视图增加顶部背景图片
.list-bar::before {
	content: '';
	background-image: url('@{src}/images/content/list/bar.png');
	background-repeat: no-repeat;
	background-size: 100% 60px;
	position: relative;
	// top: -40px;
	top: -15px;
	width: 100%;
	height: 25px;
}

//列表标题
.wx_List_title {
	display: block;
	width: 137px;
	height: 52px;
	text-align: center;
	font-size: 20px;
	line-height: 52px;
	background-image: url('@{src}/images/content/list/image.png');
	background-size: 100% 100%;
	background-repeat: no-repeat;
	color: #11182a !important;
	background-position: center;
	position: relative;
	// border-bottom: 1px solid linear-gradient(90deg, undefined, #4162ff 49%, undefined);
}

.wx_contract {
	.ant-modal-content {
		width: 680px !important;
		background-size: 100% 76px !important;
	}
}

.resetBox {
	a {
		color: #1890ff;
		background-color: transparent !important;
	}

	padding: 0px 50px;

	head {
		display: none;
	}

	body {
		margin: 8px;
		line-height: 1.12;
	}

	button,
	textarea,
	input,
	object,
	select {
		display: inline-block;
	}

	ol,
	ul,
	dir,
	menu,
	dd {
		margin-left: 40px;
	}

	i,
	cite,
	em,
	var,
	address {
		font-style: italic;
	}

	//块级元素
	html,
	body,
	div,
	ol,
	p,
	ul,
	h1,
	h2,
	h3,
	h4,
	h5,
	h6,
	address,
	blockquote,
	form,
	dd,
	dl,
	dt,
	fieldset,
	frame,
	frameset,
	center,
	dir,
	hr,
	menu,
	pre {
		display: block;
	}

	//列表元素
	ul li {
		display: list-item;
		list-style: disc;
	}

	ol li {
		display: list-item;
		list-style: decimal;
	}

	ol {
		list-style-type: decimal;
	}

	ol ul,
	ul ol,
	ul ul,
	ol ol {
		margin-top: 0;
		margin-bottom: 0;
	}

	//标题
	h1 {
		font-size: 2em;
		margin: 0.67em 0;
	}

	h2 {
		font-size: 1.5em;
		margin: 0.75em 0;
	}

	h3 {
		font-size: 1.17em;
		margin: 0.83em 0;
	}

	h4,
	p,
	blockquote,
	ul,
	fieldset,
	form,
	ol,
	dl,
	dir,
	menu {
		margin: 1.12em 0;
	}

	h5 {
		font-size: 0.83em;
		margin: 1.5em 0;
	}

	h6 {
		font-size: 0.75em;
		margin: 1.67em 0;
	}

	h1,
	h2,
	h3,
	h4,
	h5,
	h6,
	b,
	strong {
		font-weight: bolder;
	}

	//伪类
	br:before {
		content: '\A';
	}

	:before,
	:after {
		white-space: pre-line;
	}

	:link,
	:visited {
		text-decoration: underline;
	}

	:focus {
		outline: thin dotted invert;
	}

	//表格
	table {
		display: table;
		border-left: 1px solid #ccc;
		border-spacing: 2px;
	}

	tr {
		display: table-row;
	}

	thead {
		display: table-header-group;
	}

	tbody {
		display: table-row-group;
	}

	tfoot {
		display: table-footer-group;
	}

	col {
		display: table-column;
	}

	colgroup {
		display: table-column-group;
	}

	td,
	th {
		display: table-cell;
		vertical-align: inherit;
		border-bottom: 1px solid #ccc;
		border-right: 1px solid #ccc;
		padding: 3px 5px;
		min-height: 30px;
		height: 30px;
	}

	caption {
		display: table-caption;
		text-align: center;
	}

	th {
		font-weight: bolder;
		text-align: center;
		background-color: #f1f1f1;
		border-bottom: 1px solid #ccc;
		border-right: 1px solid #ccc;
		padding: 3px 5px;
		vertical-align: inherit;
		min-height: 30px;
		height: 30px;
	}

	thead,
	tbody,
	tfoot {
		vertical-align: middle;
	}

	//其它元素
	blockquote {
		margin-left: 40px;
		margin-right: 40px;
	}

	pre,
	tt,
	code,
	kbd,
	samp {
		font-family: monospace;
	}

	pre {
		white-space: pre;
	}

	big {
		font-size: 1.17em;
	}

	small,
	sub,
	sup {
		font-size: 0.83em;
	}

	sub {
		vertical-align: sub;
	}

	sup {
		vertical-align: super;
	}

	s,
	strike,
	del {
		text-decoration: line-through;
	}

	hr {
		border: 1px inset;
	}

	u,
	ins {
		text-decoration: underline;
	}

	center {
		text-align: center;
	}

	abbr,
	acronym {
		font-variant: small-caps;
		letter-spacing: 0.1em;
	}
}

// 公告样式
.announcement {
	.ant-modal-content {
		color: rgba(255, 255, 255);
		box-shadow: none;
		background: none;
		.ant-modal-close {
			top: 30px;
		}
		.ant-modal-close-x {
			color: #fff;
			font-size: 20px;
		}
		.resetBox {
			color: #6e7276;
			h1,
			h2,
			h3,
			h4,
			h5,
			h6 {
				color: #000000 !important;
			}
			a {
				color: #f8b13a;
			}
		}
		.ant-modal-header {
			height: 100px;
			border-bottom: none;
			background:url('@src/assets/images/announcementTitle.png');
			background-repeat: no-repeat;
			background-size: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.ant-modal-title {
				color: #fff;
			}
			text-indent: 15px;
		}
		.ant-modal-body {
			background: #FFFFFF;
			border-radius:0 0 24px 24px;
		}
		.ant-modal-footer {
			background: #FFFFFF;
		}
	}
}

.announcement_mask {
	filter: blur(5px);
}

// added by john
.warnmodal {
	text-align: left;

	.ant-modal-body {
		padding: 30px 50px !important;

		.headtxt {
			font-weight: bold;

			.warnicon {
				margin-right: 20px;
			}
		}
	}

	p {
		margin: 15px 0;
	}
}

.mr10 {
	margin-right: 10px;
}
.mb10 {
	margin-bottom: 10px;
}

.mb20 {
	margin-bottom: 20px;
}

.ant-select-dropdown {
	width: 140px !important;
}
.ant-select-dropdown-hidden {
	width: 140px !important;
}

//江阴table的样式
.ant-table-tbody > tr > td {
	padding: 20px 6px !important;
	border-bottom: none !important;
}
.ant-table-thead > tr > th {
	background: transparent;
	border-bottom: 1px solid transparent;
	border-top: 1px solid #e8e8e8;
	font-weight: 400;
	text-align: left;
	color: rgba(0, 0, 0, 0.85);
	padding: 20px 6px !important;
}
.ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
	background-color: transparent;
}

.ant-table-row:nth-child(odd) {
	background-color: #fafafa;
}
.ant-table-row:nth-child(even) {
	background-color: #ffffff;
}
.ant-table-pagination.ant-pagination {
	color: rgba(0, 0, 0, 0.45);
	.ant-pagination-total-text {
		flex-grow: 1;
	}
	.ant-pagination-item a {
		color: rgba(0, 0, 0, 0.45);
	}
	.ant-pagination-item-active a {
		color: #1890ff;
	}
	.ant-select-selection-item {
		color: rgba(0, 0, 0, 0.45);
	}
}

// select选择选中的不显示
#businessChange_list ~ div {
	.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
		display: none !important;
	}
}
.ant-menu.ant-menu-dark,
.ant-menu-dark .ant-menu-sub,
.ant-menu.ant-menu-dark .ant-menu-sub {
	background: #fff !important;
	padding: 3px 0 !important;
}
.ant-menu-dark .ant-menu-item > span > a {
	color: rgba(0, 0, 0, 0.65) !important;
}
.ant-menu-dark.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item-selected {
	background-color: #ffffff !important;
	width: 92% !important;
	// margin: 0 auto !important;
	border-radius: 6px !important;
}
.ant-menu-dark .ant-menu-item-selected > a,
.ant-menu-dark .ant-menu-item-selected > span > a,
.ant-menu-dark .ant-menu-item-selected > a:hover,
.ant-menu-dark .ant-menu-item-selected > span > a:hover {
	color: #1890ff !important;
}

// 增加table列特定内容的样式省略
.ellipsis-table-clon {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	width: 100%;
}
.ChainInfoWrap {
	.ant-modal-body {
		padding: 0 !important;
	}
}
.ellipsis-table-clon {
	display: flex;
	align-items: center;
}
.modal-btn-center {
	.ant-modal-confirm .ant-modal-confirm-btns {
		margin-top: 50px;
		text-align: center;
	}
}

.title-box {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.txt-title {
	margin-left: 12px;
	height: 24px;
	font-size: 16px;
	font-family: PingFangSC, PingFangSC-Medium;
	font-weight: bold;
	text-align: left;
	color: rgba(0, 0, 0, 0.85);
	line-height: 24px;
}

.txt-title_sub {
	margin-left: 12px;
	height: 22px;
	font-size: 14px;
	font-family: PingFangSC, PingFangSC-Medium;
	font-weight: 500;
	text-align: left;
	color: rgba(0, 0, 0, 0.85);
	line-height: 22px;
}

.statistic-container {
	display: flex;
	flex-direction: column;
	text-align: center;
}

.statistic-box > div:first-child {
	color: rgba(0, 0, 0, 0.65);
}

.card-border {
	background: #f6faff !important;
	border-radius: 14px !important;
	border: 0px !important;
}

.card-border-nocolor {
	border-radius: 14px !important;
}

.card-grid-divider-rborder {
	border-right: 1px solid #c5cedb !important;
}

.card-grid-divider-nborder {
	border-right: 0px;
}

.card-grid-divider-unshadow {
	box-shadow: none !important;
}

.content-backgroudcolor {
	background-color: #f6faff;
}

.cash-fuse-box {
	background: #f6faff;
	border-radius: 16px;
}

.cash-fuse-box-label {
	display: flex;
	align-items: center;
	justify-content: center;
	min-width: 168px;
	height: 46px;
	border-top: 1px solid #dbe5f2;
	font-size: 14px;
	font-family: 'PingFangSC, PingFangSC-Regular';
	font-weight: 400;
	color: rgba(0, 0, 0, 0.65);
}

.cash-fuse-box-value {
	display: flex;
	align-items: center;
	justify-content: center;
	min-width: 168px;
	height: 78px;
	font-size: 22px;
	font-family: 'PingFangSC, PingFangSC-Regular';
	font-weight: 400;
	color: rgba(0, 0, 0, 0.85);
}

//.statistic-container > div:last-child > span {
//	text-decoration: underline;
//}

.clear-padding {
	padding: 0px !important;
}

.record-value {
	width: 85px;
	margin-left: 12px;
}

.credit-value {
	//width: 110px;
	margin-left: 10px;
}

.month-line-box {
	width: 140px;
	//height: 90px;
	background-color: rgba(0, 0, 0, 0.75);
	border-radius: 8px;
	box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
}

.month-line-round {
	width: 6px;
	height: 6px;
	background-color: #1890ff;
	border-right: 6px;
}

.month-finance-btn {
	width: 74px;
	height: 24px;
	border-radius: 2px;
	font-size: 14px;
	line-height: 22px;
	text-align: center;
	font-family: 'PingFangSC, PingFangSC-Regular';
	font-weight: 400;
}

.ant-table-tbody {
	.ant-table-measure-row {
		visibility: collapse !important;
	}
}
